<template>
  <!-- 完善病情描述 -->
  <view class="page">
    <view class="page-container">
      <view class="base-info">
        <view class="input-box">
          <text>标题</text>
          <textarea
            v-model="info.recordsTitle"
            :class="old_info.recordsTitle ? '' : ''"
            placeholder="填写您的症状或特征、性质以及持续时间"
            maxlength="20"
            :focus="true"
            :disabled="old_info.recordsTitle ? false : false"
            style="text-align: right"
          />
        </view>
        <view class="input-box">

          <text>首诊诊断</text>
          <input
            :disabled="old_info.diagnosisDisease ? false : false"
            type="text"
            :class="old_info.diagnosisDisease ? '' : ''"
            v-model="info.diagnosisDisease"
            placeholder="请输入已确认诊断"
            maxlength="50"
            style="text-align: right"
          />
        </view>

        <view class="input-box">
          <text>首诊医院</text>
<!--          <input-->
<!--            :disabled="old_info.firstVisitHos ? false : false"-->
<!--            type="text"-->
<!--            :class="old_info.firstVisitHos ? '' : ''"-->
<!--            v-model="info.firstVisitHos"-->
<!--            placeholder="请输入首诊医院"-->
<!--            maxlength="50"-->
<!--          />-->
          <view class="select-box" @click="isFirstVisitHos=true">
            <view style="margin-right: 10px;flex: 1">{{ info.firstVisitHos || "请选择" }}</view>
            <u-icon name="arrow-down" />
          </view>
          <u-popup v-model="isFirstVisitHos" mode="bottom">
            <view>
              <view class="popup-top">
                <view class="cancel" @click="isFirstVisitHos=false">取消</view>
                <view class="success" @click="sureEvent">完成</view>
              </view>
              <view style="padding: 20px">
                <u-search placeholder="请输入" v-model="keyword" :show-action="false" shape="round" @search="getSearch"></u-search>
              </view>
              <scroll-view
                scroll-y
                class="hospital-scroll-view"
                @scrolltolower="loadMoreHospitals"
              >
                <view class="hospital-list">
                  <view
                    class="hospital-item"
                    v-for="(item, index) in hosList"
                    :key="index"
                    :class="{ 'hospital-item-active': selectedHospitalIndex === index }"
                    @click="selectHospital(index)"
                  >
                    {{ item.name }}
                  </view>
                </view>
                <view v-if="loading" class="loading-more">加载中...</view>
                <view v-if="finished && hosList.length > 0" class="loading-more">没有更多了</view>
              </scroll-view>
            </view>
          </u-popup>
        </view>

        <view class="input-box">
          <text>患病时长</text>
          <input
            type="number"
            v-model="info.sickTime"
            :class="old_info.sickTime ? '' : ''"
            placeholder="请输入整数"
            :disabled="old_info.sickTime ? false : false"
            maxlength="6"
          />
          <!-- 选择天 -->
          <view class="select-day" @click="selectUnit">
            <view class="select_time">{{ info.timeUnit }}</view>
            <image src="/static/images/sx.png" class="icon_sx"></image>
          </view>
        </view>

        <view class="title_format">病情描述（详细描述您的病情，症状，治疗经过）</view>
        <view class="content">
          <textarea
            :class="old_info.diseaseDescription ? '' : ''"
            placeholder="详细描述您的病情，症状，治疗经过"
            :disabled="old_info.diseaseDescription ? false : false"
            maxlength="200"
            v-model="info.diseaseDescription"
            class="other-content"
          />
          <view class="char-count" style="bottom:-22px">{{ info.diseaseDescription.length }}/200</view>
        </view>

        <view class="title_format">期望获得的帮助</view>
        <view class="content">
          <textarea
            placeholder="详细描述您的期望帮助"
            :class="old_info.expectHelp ? '' : ''"
            maxlength="200"
            v-model="info.expectHelp"
            class="other-content"
            :disabled="old_info.expectHelp ? false : false"
          />
          <view class="char-count" style="bottom:0px">{{ info.expectHelp.length }}/200</view>
          <view class="shortcut">
            <view style="color: #999; font-size: 24rpx; line-height: 34rpx"
              >快捷输入</view
            >
            <view class="shortcut_box">
              <template v-for="item in shortcutList">
                <view
                  class="shortcut_little"
                  @click="getShortcut(item.quickInputName)"
                  >{{ item.quickInputName }}</view
                >
              </template>
            </view>
          </view>
        </view>
      </view>
      <view style="height: 16rpx; background: #f2f2f2"></view>
      <view class="img-info">
        <view class="title_format"
          >病情照片<text class="font_hint"
            >（仅限本人和接诊医生可看）</text
          ></view
        >
        <view class="describe_check_image">
          <view class="font_hint_pb">请上传患者历史就诊病例图片，最多9张
          </view>
          <view class="uploader">
            <template v-for="(item, index) in fileListLi">
              <view class="img-box">
                <image
                  class="img-list"
                  :src="item.url"
                  mode="aspectFill"
                  @click="previewImg(item.url)"
                ></image>
                <!-- 删除按钮 -->
                <image
                  src="/static/images/question/image-delete.png"
                  v-if="item.base64"
                  class="delete-img"
                  @click="delectImg1(index)"
                ></image>
              </view>
            </template>
            <view
              v-if="fileListLi.length < 9"
              class="img-box img_box_shang"
              @click="chooseImage1"
            >
              <view>
                <image src="/static/images/pic.png"></image>
                <view>上传图片</view>
              </view>
            </view>
          </view>
        </view>
        <view class="title_format" v-if="false"
          >其他照片<text class="font_hint"
            >（仅限本人和接诊医生可看）</text
          ></view
        >
        <view class="describe_check_image" v-if="false">
          <view class="font_hint_pb">请上传患者历史就诊病例图片，最多9张
          </view>
          <view class="uploader">
            <template v-for="(item, index) in fileListQin">
              <view class="img-box">
                <image
                  class="img-list"
                  :src="item.url"
                  mode="aspectFill"
                  @click="previewImg(item.url)"
                ></image>
                <!-- 删除按钮 -->
                <image
                  src="/static/images/question/image-delete.png"
                  v-if="item.base64"
                  class="delete-img"
                  @click="delectImg2(index)"
                ></image>
              </view>
            </template>
            <view
              v-if="fileListQin.length < 9"
              class="img-box img_box_shang"
              @click="chooseImage2"
            >
              <view>
                <image src="/static/images/pic.png"></image>
                <view>上传图片</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 选择患病时长单位 -->
    <propBottom
      v-if="unitShow"
      :actions="columns"
      @propConfirm="propConfirm"
      @propCancel="propCancel"
    ></propBottom>

    <!-- 按钮 -->
    <FooterButton @click="getSubmit">保存</FooterButton>
  </view>
</template>

<script>
import { Toast } from "@/common/js/pay.js";
import FooterButton from "@/components/footer_button/button.vue";
import propBottom from "@/components/propBottom/propBottom.vue";
import { getQuickInputInfo, savePatientRecords } from "@/api/base.js";
import { uploadImg } from "@/api/oss.js";
import myJsTools from "@/common/js/myJsTools.js";
import { getPatientRecords } from "@/api/chat";
import {dicSzHospitalList} from "../../api/base";

function downImg(v) {
  return new Promise((resolve, reject) => {
    try {
      myJsTools.downAndSaveImg(v, resolve);
    } catch (error) {
      reject(error);
    }
  });
}

export default {
  components: {
    propBottom,
    FooterButton,
  },
  data() {
    return {
      info: {
        recordsTitle: "", // 标题
        diagnosisDisease: "", // 已确诊疾病
        firstVisitHos: "", // 首诊医院
        sickTime: "", // 患病时长
        timeUnit: "天", // 患病时长单位
        diseaseDescription: "", // 病情描述
        expectHelp: "", // 期望获得的帮助
      },
      taskTypeActions:[],
      old_info: {},
      // 病情，其他照片
      fileListLi: [],
      isFirstVisitHos:false,
      fileListQin: [],
      disease: [], // 提交时，用以缓存图片上传oss后返回的文件名
      diseaseIndex: 0,
      medical: [], // 提交时，用以缓存图片上传oss后返回的文件名
      medicalIndex: 0,
      diseaseImgLen: -1,
      medicalImgLen: -1,
      diseaseImgOne: [], // 第二次进入页面，页面中缓存的病情图片（oss下载下来的图片路径）
      medicalImgOne: [], // 第二次进入页面，页面中缓存的其他照片，或者修改进入，页面中之前保存的其他照片(oss)
      unitShow: false,
      columns: [
        {
          name: "天",
          value: "0",
        },
        {
          name: "周",
          value: "1",
        },
        {
          name: "月",
          value: "2",
        },
        {
          name: "年",
          value: "3",
        },
      ],
      shortcutList: [], // 快捷输入
      prId: "",
      patientId: "",
      patientName: "",
      // 新增还是修改
      newFlag: true,
      patientRecordsInfo: {}, // 缓存提交的文字数据
      action: "", // 从哪页进入
      btnActive: true,
      regId: "",
      keyword:'',
      indicatorStyle: `height: 50px;`,
      hosList:[],
      checkHos:[],
      checkHosInfo:{},
      allList:[],
      selectedHospitalIndex: -1,
      // 分页相关
      currentPage: 1,
      pageSize: 20,
      totalPages: 1,
      loading: false,
      finished: false
    };
  },
  watch: {
    hosList: {
      handler(newVal) {
        // If we have a firstVisitHos value but no selected index yet, try to find it in the list
        if (this.info.firstVisitHos && this.selectedHospitalIndex === -1 && newVal.length > 0) {
          const index = newVal.findIndex(item => item.name === this.info.firstVisitHos);
          if (index !== -1) {
            this.selectedHospitalIndex = index;
          }
        }
      },
      deep: true
    }
  },
  onLoad(options) {
    this.regId = options.regId;
    this.getQuickInputInfoFun();
    this.getPatientRecordsFun();
    this.getHosList()
  },
  methods: {
    getHosList() {
      this.currentPage = 1;
      this.hosList = [];
      this.loading = true;
      this.finished = false;

      const data = {
        page: this.currentPage,
        limit: this.pageSize,
      };

      if (this.keyword) {
        data.name = this.keyword;
      }

      dicSzHospitalList(data).then(res => {
        this.hosList = res.data.rows || [];
        this.totalPages = Math.ceil(res.data.total / this.pageSize);
        this.loading = false;
        
        if (this.currentPage >= this.totalPages) {
          this.finished = true;
        }
      }).catch(() => {
        this.loading = false;
      });
    },
    
    loadMoreHospitals() {
      if (this.loading || this.finished) return;

      this.loading = true;
      this.currentPage++;

      const data = {
        page: this.currentPage,
        limit: this.pageSize,
      };

      if (this.keyword) {
        data.name = this.keyword;
      }

      dicSzHospitalList(data).then(res => {
        const newItems = res.data.rows || [];
        this.hosList = [...this.hosList, ...newItems];
        this.loading = false;
        
        if (this.currentPage >= this.totalPages || newItems.length === 0) {
          
          this.finished = true;
        }
      }).catch(() => {
        this.loading = false;
        this.currentPage--;
      });
    },
    
    getSearch(value) {
      this.keyword = value;
      this.selectedHospitalIndex = -1;
      this.getHosList();
    },
    
    selectHospital(index) {
      this.selectedHospitalIndex = index;
    },
    
    sureEvent() {
      this.isFirstVisitHos = false;
      if (this.selectedHospitalIndex === -1) {
        this.info.firstVisitHos = this.hosList[0]?.name || '';
        return;
      }
      this.info.firstVisitHos = this.hosList[this.selectedHospitalIndex]?.name || '';
    },
    // 预览图片
    previewImg(img) {
      myJsTools.previewImg(img);
    },

    async getPatientRecordsFun() {
      let res = await getPatientRecords({
        regId: this.regId,
      });

      if (res.code != 20000) return;

      let data = res.data;

      if (!data) return;

      // 定义旧数据
      let old_obj = {
        recordsTitle: data.recordsTitle || "",
        diagnosisDisease: data.diagnosisDisease || "",
        firstVisitHos: data.firstVisitHos || "",
        sickTime: data.sickTime || 0,
        // 后台返回null 增加默认值天
        timeUnit: data.timeUnit || "天",
        diseaseDescription: data.diseaseDescription || "",
        expectHelp: data.expectHelp || "",
      };
      // 防止引用
      let obj = JSON.parse(JSON.stringify(old_obj));

      let diseaseImgOne = [];

      let medicalImgOne = [];
      if (data.diseaseImg) {
        let diseaseImg = (JSON.parse(res.data.diseaseImg)||[]).filter((v) => v);
        this.diseaseImgOne = diseaseImg.filter((v) => v);

        if (diseaseImg.length > 0) {
          diseaseImg.map(async (v) => {
            let url = await downImg(v);
            diseaseImgOne.push({
              url,
              name: v,
            });
          });

          this.diseaseImgLen = diseaseImgOne.length - 1;
        }
      }
      if (data.medicalImg) {
        let medicalImg = JSON.parse(res.data.medicalImg);
        this.medicalImgOne = medicalImgOne;
        if (medicalImg.length > 0) {
          medicalImg.forEach((element) => {
            // 获取图片
            myJsTools.downAndSaveImg(element, (url) => {
              medicalImgOne.push({
                url,
                name: element,
              });
            });
          });
          this.fileListQin = medicalImgOne;
          this.medicalImgLen = medicalImgOne.length - 1;
        }
      }
      setTimeout(() => {
        this.prId = data.prId;
        this.patientName = data.patientName;
        this.patientId = data.patientId;
        // 旧数据
        this.old_info = old_obj;
        // 当前数据
        this.info = obj;
        this.fileListLi = diseaseImgOne;
        this.fileListQin = medicalImgOne;
        this.diseaseImgLen = diseaseImgOne.length - 1;
        this.medicalImgLen = medicalImgOne.length - 1;
        
        // 如果有首诊医院数据，等待获取医院列表后找到匹配的医院索引
        if (this.info.firstVisitHos) {
          this.getHosList();
        }
      }, 300);
    },
    // 选择患病时长,默认"天"
    selectUnit() {
      if (this.old_info.sickTime) return;
      this.unitShow = true;
    },
    // 接收选择患病时长组件的返回值
    propConfirm(evt) {
      this.unitShow = false;
      this.info.timeUnit = evt.name;
    },
    // 取消选择患病时长单位
    propCancel() {
      this.unitShow = false;
    },
    // 查询快捷输入标签
    async getQuickInputInfoFun() {
      let res = await getQuickInputInfo({
        page: 1,
        limit: 20,
        quickType: 1,
      });
      this.shortcutList = res.data.rows;
    },
    // 操作快捷输入
    getShortcut(evt) {
      this.info.expectHelp = this.info.expectHelp + evt;
    },

    chooseImage1() {
      let _this = this;
      uni.chooseImage({
        count: 9, //默认9
        sizeType: ["compressed"],
        sourceType: ["album"], //从相册选择
        success: (res) => {
          let tempFiles = res.tempFiles;
          if (tempFiles.length > 0) {
            for (let i = 0; i < tempFiles.length; i++) {
              let el = tempFiles[i];
              myJsTools.setImgZip(el, (dataUrl) => {
                if (_this.fileListLi.length < 9) {
                  _this.fileListLi.push({
                    base64: dataUrl.split(",")[1],
                    url: dataUrl,
                    name: "",
                  });
                }

                if (i == tempFiles.length - 1) {
                  _this.uploadDiseaseImg();
                }
              });
            }
          }
        },
      });
    },

    // 上传其他照片
    chooseImage2() {
      let _this = this;
      uni.chooseImage({
        count: 9, //默认9
        sizeType: ["original", "compressed"],
        sourceType: ["album"], //从相册选择
        success: (res) => {
          let tempFiles = res.tempFiles;
          for (let i = 0; i < tempFiles.length; i++) {
            let imgFile = tempFiles[i];
            myJsTools.setImgZip(imgFile, (dataUrl) => {
              if (_this.fileListQin.length < 9) {
                _this.fileListQin.push({
                  base64: dataUrl.split(",")[1],
                  url: dataUrl,
                  name: "",
                });
              }

              if (i == tempFiles.length - 1) {
                _this.uploadMedicalImg();
              }
            });
          }
        },
      });
    },

    // 下一步
    getSubmit() {
      let _this = this;
      this.btnActive = false;
      let obj = this.info;
      obj.patientId = this.patientId;
      obj.patientName = this.patientName;
      let recordsTitle = this.info.recordsTitle.trim();
      let diseaseDescription = this.info.diseaseDescription.trim();
      // 验证标题
      if (!recordsTitle) {
        Toast("请填写标题");
        this.btnActive = true;
        return;
      }
      // 患病时长
      if (obj.sickTime) {
        let reg = /^[0-9]{1,}$/;
        if (!reg.test(obj.sickTime)) {
          Toast("患病时长请填写正数");
          return;
        }
      }
      if (!diseaseDescription) {
        Toast("请填写病情描述");
        this.btnActive = true;
        return;
      }
      this.patientRecordsInfo = obj;

      this.savePatientRecordsFun();
    },
    async uploadDiseaseImg() {
      let fileListLi = this.fileListLi;
      if (fileListLi.length > 0) {
        // 图片上传加载loading
        uni.showLoading({
          title: "图片上传中",
        });
        let disease = this.disease;
        for (let i = 0; i < fileListLi.length; i++) {
          console.log(fileListLi)
          if (fileListLi[i].base64) {
            let res = await this.uploadImgFun(fileListLi[i]);
            // 清除掉
            fileListLi[i].base64 = "";
            disease.push(res.data.url);
          } else {
            disease.push(fileListLi[i].name);
          }
        }
        // 隐藏loading
        uni.hideLoading();
        this.disease = disease;
      } else {
        this.patientRecordsInfo.diseaseImg = "";
      }

      // 2.遍历其他照片,上传oss,缓存oss返回的文件名
      // this.uploadMedicalImg();
    },
    uploadImgFun(element) {
      let para = {
        folderType: 11,
        imgBody: element.base64,
        otherId: uni.getStorageSync("patientId"),
      };
      return uploadImg(para);
    },
    async uploadMedicalImg() {
      let fileListQin = this.fileListQin;
      if (fileListQin.length == 0) {
        // 没有其他照片,直接调用提交接口

        this.patientRecordsInfo.medicalImg = "";
        // this.savePatientRecordsFun();
      } else {
        let medical = this.medical;
        for (let j = 0; j < fileListQin.length; j++) {
          if (fileListQin[j].base64) {
            let res = await this.uploadImgFun(fileListQin[j]);
            fileListQin[j].base64 = "";
            medical.push(res.data.url);
          } else {
            medical.push(fileListQin[j].name);
          }
        }
        this.medical = medical;

        // 3.调用提交接口

        // this.savePatientRecordsFun();
      }
    },

    // 提交
    async savePatientRecordsFun() {
      console.log(this.disease,'this.disease')
      // 病情图片
      this.patientRecordsInfo.diseaseImg = this.disease.length
        ? JSON.stringify(this.disease.filter((item) => item))
        : "";
      // 其他图片
      this.patientRecordsInfo.medicalImg = this.medical.length
        ? JSON.stringify(this.medical)
        : "";
      let patientRecordsInfo = this.patientRecordsInfo;
      patientRecordsInfo.prId = this.prId;
      await savePatientRecords(patientRecordsInfo);
      uni.navigateBack({
        delta: 1,
      });
    },

    // 删除指定图片
    delectImg1(index) {
      this.fileListLi.splice(index, 1);
      this.disease.splice(index, 1);
    },
    delectImg2(index) {
      this.fileListQin.splice(index, 1);
      this.medical.splice(index, 1);
    },
  },
};
</script>

<style scoped lang="scss">
.input-box{
  .textarea-placeholder,.input-placeholder{
    font-size: 12px;
    display: flex;
    align-items: center;
    color: rgba(153, 153, 153, 1) !important;
    justify-content: right;
    margin-right: 10px;
  }
}
.textarea-placeholder,.input-placeholder{
  font-size: 12px;
  display: flex;
  color: rgba(153, 153, 153, 1) !important;
}
.x {
  font-size: 14px;

  &::before {
    content: "*";
    color: red;
    margin-right: 10rpx;
  }
}

.page-container {
  padding-bottom: 120rpx;
  box-sizing: border-box;
  overflow-y: auto;
  background-color: #ffffff;
}
.base-info,
.img-info {
  padding: 0 30rpx 30rpx 30rpx;
}

.input-box {
  width: 100%;
  display: flex;
  height: 95rpx;
  padding: 16rpx 0;
  align-items: center;
  box-sizing: border-box;
  border-bottom: 1rpx solid #ebebeb;
  font-size: 32rpx;
  font-weight: 400;
}

.input-box text {
  display: inline-block;
  width: 180rpx;
  line-height: 88rpx;
  flex: none;
  color: rgba(51, 51, 51, 1);
}

.input-box input {
  flex: 1;
  /* height: 88rpx; */
  /* line-height: 88rpx; */
  padding-left: 28rpx;
  color: rgba(102, 102, 102, 1);
}

.input-box textarea {
  flex: 1;
  height: 88rpx;
  /* line-height: 44rpx; */
  padding-left: 28rpx;
  color: rgba(102, 102, 102, 1);
}

.input-box .select-day {
  width: 102rpx;
  @include flex(center);
  border: 1px solid $k-hr-color;
  border-radius: 4upx;
  box-sizing: border-box;
  height: 60upx;

  .select_time {
    font-size: 24upx;
    color: $k-title;
    font-weight: 500;
    padding-right: 10upx;
  }

  .icon_sx {
    width: 30upx;
    height: 30upx;
  }
}

.title_format {
  font-size: 14px;
  font-weight: 400;
  color: #333333;
  line-height: 22px;
  margin: 38rpx 0 20rpx 0;

}
.content{
  position: relative;
  .char-count {
    position: absolute;
    right: 20rpx;
    bottom: 30rpx;
    font-size: 24rpx;
    color: #999;
    background: rgba(255, 255, 255, 0.9);
    padding: 0 10rpx;
    border-radius: 4rpx;
  }
}
.other-content {
  width: 100%;
  box-sizing: border-box;
  border-radius: 8rpx;
  border: 1px solid rgba(229, 229, 229, 1);
  font-size: 32rpx;
  line-height: 44rpx;
  padding: 30rpx 16rpx;
  height: 240rpx;
}

.shortcut {
  padding: 20rpx 0;
}

.shortcut_box {
  display: flex;
  align-items: center;
  flex-direction: row;
  flex-wrap: wrap;
}

.shortcut_little {
  color: #75777a;
  background-color: #eeeeee;
  border-radius: 19rpx;
  font-size: 24rpx;
  text-align: center;
  padding: 4rpx 32rpx;
  margin-right: 28rpx;
  margin-top: 20rpx;
}

.font_hint {
  font-size: 12px;
  color: rgba(131, 106, 255, 1);
  font-weight: 500;
}

/* 病情/其他图片 */
.describe_check_image {
  color: #666666;
  border: 1px solid #ebedf0;
  border-radius: 8rpx;
  padding: 18rpx;
  font-size: 28rpx;
}

.font_hint_pb {
  font-size: 12px;
  color: #999;
}

.uploader {
  display: flex;
  align-items: center;
  flex-direction: row;
  flex-wrap: wrap;
}

.img_box_shang,
.img-box {
  width: 198rpx;
  height: 198rpx;
  border-radius: 8px;
  border: 1px dashed rgba(131, 106, 255, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18rpx;
  text-align: center;
  color: #666666;
  margin-top: 20rpx;
  position: relative;
}

.img-box:not(:nth-child(3n + 1)) {
  margin-left: 22rpx;
}

.img-list {
  display: block;
  width: 100%;
  height: 100%;
}

.img_box_shang image {
  width: 40rpx;
  height: 38rpx;
}

.delete-img {
  width: 40rpx;
  height: 40rpx;
  position: absolute;
  top: -10rpx;
  right: -10rpx;
}

/* 底部按钮 */
.footer {
  width: 100%;
  position: fixed;
  bottom: 0;
  left: 0;

  .btns {
    height: 108rpx;
    @include flex(lr);
    padding: 0 32upx;
    box-sizing: border-box;
    background: #ffffff;
    font-size: 32rpx;

    .samll-btn {
      width: 334rpx;
      height: 84rpx;
      color: #333;
      background: #f5f5f5;
      border-radius: 42rpx;
      font-weight: 500;
      line-height: 84rpx;
      text-align: center;
      box-sizing: border-box;

      &.active {
        @include bg_theme;
        color: #ffffff;
      }

      &.cancel {
        @include border_theme;
        background: #ffffff;
        @include font_theme;
      }
    }
  }
}

.footer .btn {
  width: 100%;
  height: 84rpx;
  text-align: center;
  line-height: 84rpx;
  font-size: 32rpx;
  font-weight: 400;
  color: #333;
  background: #f5f5f5;
}
.footer .active {
  @include bg_theme;
  color: #ffffff;
}
.select-box{
  display: flex;
  align-items: center;
  text-align: right;
  flex: 1;
}
.picker-view {
  width: 750rpx;
  height: 400rpx;
  margin-top: 20rpx;
}
.item {
  line-height: 100rpx;
  text-align: center;
}
.popup-top{
  display: flex;
  align-items: center;
  line-height: 50px;
  justify-content: space-between;
  padding: 0 20px;
  .success{
    color: #15a0e6;
  }
}
::v-deep .footer_button uni-button{
  width: 353px;
  height: 36px;
  opacity: 1;
  border-radius: 20px;
  background: linear-gradient(135deg, rgba(81, 106, 251, 1) 0%, rgba(133, 155, 255, 1) 99.97%);
  color: white;
  font-size: 14px !important ;
}
.hospital-scroll-view {
  max-height: 400rpx;
  width: 100%;
}

.hospital-list {
  padding: 0 20rpx;
}

.hospital-item {
  height: 84rpx;
  line-height: 84rpx;
  padding: 0 20rpx;
  border-bottom: 1px solid #eee;
  font-size: 28rpx;
  color: #333;
  text-align: center;
}

.hospital-item-active {
  color: #836AFF;
  background-color: #f5f5f5;
}

.loading-more {
  text-align: center;
  padding: 10rpx 0;
  color: #999;
  font-size: 24rpx;
}
</style>
