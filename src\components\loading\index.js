import Loading from './loading.vue'

// 注册为全局组件
export default {
  install(Vue) {
    Vue.component('hz-loading', Loading)

    // 添加一个全局可用的$loading方法
    Vue.prototype.$loading = {
      show(options = {}) {
        // 创建加载实例
        const LoadingConstructor = Vue.extend(Loading)
        const instance = new LoadingConstructor({
          propsData: {
            ...options,
            show: true,
            fullscreen: true,
          },
        })

        // 挂载到body
        instance.$mount()
        document.body.appendChild(instance.$el)

        // 返回关闭方法
        return {
          close() {
            instance.show = false
            setTimeout(() => {
              if (instance.$el && instance.$el.parentNode) {
                instance.$el.parentNode.removeChild(instance.$el)
              }
              instance.$destroy()
            }, 300)
          },
        }
      },
    }
  },
}
