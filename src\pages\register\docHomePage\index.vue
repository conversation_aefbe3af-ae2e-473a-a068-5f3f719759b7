<template>
  <view class="home">
    <!-- 浮动图标 -->
    <GOHOME />

    <HEAD
      :info="docDetail"
      :collectFlag="collectFlag"
      @collect="collectOperation"
      @share="toShare"
    />
    <view style="box-shadow: 0px 2px 4px  rgba(131, 106, 255, 0.2);">
      <!-- 排班 -->
      <DNTLIST
          :act="num"
          @setNum="getTabNum"
          :list="visitInfo"
          @click="getAdvisory"
          :isFz="isFz"
          :isZx="isZx"
          :isDesc="isDesc"
          v-if="showTab"
      />

      <!-- 空白 -->
      <view class="empty" v-if="!showTab">
        <image src="/static/images/doc/empty.png" class="empty_img" />
        <text>暂无出诊信息</text>
      </view>

      <!-- 线下排班 -->
      <OFFLINE v-if="offlineList.length" :list="offlineList" @click="toOffline" />
      <!-- 评价 -->
      <view class="evaluate">
        <!-- 评价标签 -->
        <view class="evaluate_top">
          <view style="display: flex;align-items: center">
            <text class="font_col_333">患者评价</text>
            <text style="color: rgba(153, 153, 153, 1)">({{ prTotal }})</text>
          </view>
          <view>
            <text class="font_col_666">好评率：</text>
            <text class="font_col_blue">{{ docDetail.percentage }}%</text>
          </view>
        </view>

        <!-- 评论列表 -->
        <view v-if="listPr.length">
          <!-- 评价标签 -->
          <!-- <view class="pr_label">
            <text
              class="pr_label_item"
              v-for="(item, index) in evaluate"
              :key="index"
              >{{ item.title }}({{ item.cont }})</text
            >
          </view> -->
          <!-- <scroll-view scroll-y lower-threshold="100" class="doc_list_box"> -->
          <!-- 具体评价 -->
          <block v-for="(item, index) in listPr" :key="index">
            <view class="evaluate_list">
              <view class="rate-content">
                <!-- 名称 -->
                <text class="nickname">{{
                    item.remark.patientName | userName
                  }}</text>
                <!-- 就诊满意度 -->
                <rate
                    size="24"
                    :value="item.t3"
                    disabled
                    star_empty="/static/images/question/assess.png"
                    star_fill="/static/images/question/assess_active.png"
                    v-if="false"
                ></rate>
                <uni-rate
                    allowHalf
                    :value="item.num"
                    size="14"
                    :readonly="true"
                    disabled
                ></uni-rate>
              </view>
              <!-- 内容 -->
              <view class="eval-content">
                <!-- 文本内容 -->
                <view class="eval-cont-text">{{ item.t1 }}</view>
                <!-- 图片内容 -->
                <view class="eval-cont-imgs" v-if="item.t2 && item.t2.length">
                  <image
                      @click="previewImage(img)"
                      mode="aspectFill"
                      class="eval-cont-imgs-item"
                      :src="img"
                      v-for="(img, i) in item.t2"
                      :key="i"
                  ></image>
                </view>
              </view>
              <view class="diagnose">
                <text
                >[{{ item.remark.visitTypeName }}问诊] 诊断：{{
                    item.remark.diagNames || "暂无诊断"
                  }}</text
                >
                <text>{{ item.evaluateTime | time }}</text>
              </view>
            </view>
          </block>
          <view class="more-text" v-if="loadingFlag">评价已全部加载完成</view>
          <!-- </scroll-view> -->
        </view>

        <!-- 没有评论 -->
        <view class="no_eval" v-else> 咨询问诊后请您对医生进行评价 </view>
      </view>
    </view>


    <!-- 如果分页，大于1页，则显示“查看全部评价” -->
    <view v-if="evaluateFlag" class="evaluate_btn" @click="getAllEvaluate">
      查看全部评价
    </view>

    <!-- 选择科室 -->
    <propBottom
      v-if="deptFlag"
      :actions="deptList"
      @propConfirm="propConfirm"
      @propCancel="propCancel"
    ></propBottom>

    <!-- 分享 -->
    <view class="share-container" v-if="isShareShow" @click="cancelShare">
      <image
        src="/static/images/index/share-toast.png"
        mode=""
        class="share-bg"
      ></image>
    </view>

    <!--    申请更换绑定医生-->
    <view class="pay_buts" v-if="btnChange">
      <text @click.stop="changeCollectOperation">绑定医生</text>
    </view>
  </view>
</template>

<script>
import { Toast } from "@/common/js/pay.js";
import HEAD from "./com/head.vue";
import DNTLIST from "./com/dntList.vue";
import OFFLINE from "./com/offline.vue";
import GOHOME from "@/components/home/<USER>";
import uniIcons from "@/components/uni-icons/uni-icons.vue";
import {
  findDoctorByID,
  findAllByType,
  findDocEvaluateListPage,
  getIsExist,
  cancelKeepDoc,
  addCollectDoctor,
  findVisitTypePrice,
  findDocDept,
  getConfigInfo,
  getSysPlatformConfigByKeyList,
  getDocOfflineRoster,
} from "@/api/base.js";
import myJsTools from "@/common/js/myJsTools.js";
import { getJSSDKSign } from "@/api/share.js";
var jweixin = require("jweixin-module");

import urlConfig from "@/common/request/config.js";
import Login from "@/mixins/login.js";

export default {
  mixins: [Login],
  components: {
    uniIcons,
    GOHOME,
    DNTLIST,
    OFFLINE,
    HEAD,
  },
  data() {
    return {
      docInfo: {},
      deptFlag: false,
      deptList: [],
      docDetail: {
        specialties: "",
        docSynopsis: "",
      },
      collectFlag: false,
      initialFlag: false,
      num: 0,
      inquiryWay: [],
      subsequent: [],
      visitInfo: [],
      listPr: [],
      prTotal: "",
      evaluateQuery: {
        docId: "",
        limit: 10,
        page: 1,
      },
      // 评价的标签
      evaluate: [
        {
          title: "接诊神速",
          cont: 5,
        },
        {
          title: "接诊神速",
          cont: 7,
        },
        {
          title: "接诊神速",
          cont: 3,
        },
      ],
      evaluateFlag: false, // 是否显示查看全部评价按钮
      loadingFlag: false,
      isShareShow: false, // 分享引导图
      // 是否开启咨询
      isZx: false,
      // 是否开启复诊
      isFz: false,
      // 是否倒序 默认 咨询在前
      isDesc: false,
      // 是否显示排班
      showTab: false,
      // 线下排班
      offlineList: [],
      pageAction: "", // 页面来源
    };
  },
  onLoad(option) {
    let docId = option.docId;
    this.pageAction = option.pageAction || "";
    this.docInfo.docId = docId;
  },
  onShow() {
    this.getConfig();
    this.getDoc();
    this.getJSSDKSignFun();
  },
  methods: {
    async getDoc() {
      let docId = this.docInfo.docId;
      let res = await findDocDept({
        docId,
      });
      if (res.data.length <= 1) {
        let docInfo = {
          docId,
          deptId: res.data[0].deptId,
        };
        this.docInfo = docInfo;
        this.initData();
      } else {
        let deptList = [];
        for (let i = 0; i < res.data.length; i++) {
          deptList.push({
            name: res.data[i].deptName,
            deptId: res.data[i].deptId,
          });
        }
        this.deptList = deptList;
        this.deptFlag = true;
      }
    },
    // 获取医生线下排班
    async getOfflineVisit() {
      let { data } = await getDocOfflineRoster({
        docId: this.docInfo.docId,
      });
      this.offlineList = data;
    },
    // 查询配置
    async getConfig() {
      let { data } = await getSysPlatformConfigByKeyList([
        "display_consultative",
        "display_fllow",
        "consultation_follow_up_order",
      ]);
      data.forEach((v) => {
        // 开启咨询
        if (v.configKey == "display_consultative") {
          this.isZx = v.configValue == 0 ? false : true;
        }
        // 开启复诊
        if (v.configKey == "display_fllow") {
          this.isFz = v.configValue == 0 ? false : true;
        }
        // 1 咨询前 2 复诊前
        if (v.configKey == "consultation_follow_up_order") {
          let str = v.configValue.substring(0, 1);
          if (str == 1 || str == 2) {
            this.isDesc = str == 1 ? false : true;
          }
        }
      });
    },
    // 调起分享sdk
    async getJSSDKSignFun() {
      let para = {
        appid: uni.getStorageSync("appId"),
        url: window.location.href.split("#")[0],
      };
      let res = await getJSSDKSign(para);
      var info = res.data;
      jweixin.config({
        debug: false,
        appId: uni.getStorageSync("appId"), // 必填，公众号的唯一标识
        timestamp: info.timestamp, // 必填，生成签名的时间戳
        nonceStr: info.nonceStr, // 必填，生成签名的随机串
        signature: info.signature, // 必填，签名
        jsApiList: ["updateAppMessageShareData", "updateTimelineShareData"], // 必填，需要使用的JS接口列表
      });
    },
    // 点击线下排班
    toOffline(v) {
      if (!this.hasInfo()) return;

      // 号别
      let { dntId, dntName } = v;
      // 存本地
      let obj = {
        dntId,
        dntName,
        docId: this.docDetail.docId,
      };

      uni.setStorageSync("offlineAppoint", obj);

      uni.navigateTo({
        url: "/pages/personalCenter/patientManage/index?action=offline",
      });
    },
    // 分享
    toShare() {
      let evt = this.docDetail;
      let docInfo = {
        docId: evt.docId,
        docImg: evt.docImg,
        docName: evt.docName,
        docProf: evt.docProf,
        deptName: evt.deptName,
        hosName: evt.hosName,
        docLable: evt.docLable,
        hosId: evt.hosId,
      };

      const shareLogoUrl = require("@/common/request/config.js").shareLogoUrl;

      let userId = uni.getStorageSync("userId") || "";
      let openid = uni.getStorageSync("wxInfo").openId;
      let url = `${urlConfig.rootUrl}#/pages/shareDocCard/index?docId=${docInfo.docId}&hosId=${docInfo.hosId}&userId=${userId}&openid=${openid}&doCodeType=D_C_2`;

      // alert("调用分享微信好友");
      jweixin.updateAppMessageShareData({
        title: `医生-${evt.docName}`, // 分享标题
        desc: evt.docName + "医生医术高明、服务贴心、点击去问诊沟通>>>", // 分享描述
        link: url,
        imgUrl: shareLogoUrl,
      });
      // alert("调用分享朋友圈");
      jweixin.updateTimelineShareData({
        title: evt.docName + "医生名片，点击沟通交流>>>", // 分享标题
        desc: evt.docName + "医生医术高明、服务贴心，点击去问诊沟通>>>",
        link: url, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
        imgUrl: shareLogoUrl, // 分享图标
      });

      this.isShareShow = true;
    },
    // 取消分享
    cancelShare() {
      this.isShareShow = false;
    },
    // 获取全局参数
    async getWholeArg() {
      // 获取全局参数
      let res = await getConfigInfo();
      this.$store.commit("setWholeArg", res.data);
    },

    // 初始化页面
    async initData() {
      uni.showLoading({
        mask: true,
      });
      this.evaluateQuery.docId = this.docInfo.docId;
      // 获取线下排班
      this.getOfflineVisit();
      // 医生详情
      await this.getDocDetail();
      // 开启图文
      if (this.isZx) await this.getVisitInfo();
      // 开启复诊
      if (this.isFz) await this.getFzVisit();
      // 设置默认
      if (this.isDesc && this.isFz) {
        this.getTabNum(1);
        if (!this.visitInfo.length) {
          this.getTabNum(0);
        }
      } else {
        this.getTabNum(0);
        if (!this.visitInfo.length) {
          this.getTabNum(1);
        }
      }
      // 判断是否显示tab栏
      if (this.subsequent.length || this.inquiryWay.length) {
        this.showTab = true;
      }
      if (!this.subsequent.length && !this.inquiryWay.length) {
        this.showTab = false;
      }
      this.getIsExistFun();
      this.getEvaluateList();
      uni.hideLoading();
    },
    // 获取医生信息
    async getDocDetail() {
      let res = await findDoctorByID({
        docId: this.docInfo.docId,
        deptId: this.docInfo.deptId,
      });
      this.docDetail = res.data;
      this.docDetail.docImgCopy = res.data.docImg;
      if (res.data.docImg) {
        myJsTools.downAndSaveImg(res.data.docImg, (url) => {
          this.docDetail.docImg = url;
        });
      }
      uni.setStorageSync("hosId", res.data.hosId);
    },
    // 接收选择医生科室的结果
    propConfirm(evt) {
      this.deptFlag = false;
      this.docInfo.deptId = evt.deptId;
      this.initData();
    },
    // 取消选择医生
    propCancel() {
      this.deptFlag = false;
      // 获取当前页面栈数量
      let length = getCurrentPages().length;
      // 可以后退
      if (length > 1) {
        uni.navigateBack({
          delta: 1,
        });
      } else {
        // 刷新页面导致页面栈丢失
        uni.reLaunch({
          url: "/pages/index/index",
        });
      }
    },
    // 查询该医生是否被关注
    async getIsExistFun() {
      if (uni.getStorageSync("userId")) {
        let res = await getIsExist({
          docId: this.docInfo.docId,
          openid: uni.getStorageSync("wxInfo").openId,
        });
        this.collectFlag = res.data;
      }
    },
    // 取消关注/关注 该医生
    async collectOperation() {
      if (!this.hasInfo()) return;

      let obj = {
        docId: this.docInfo.docId,
        userId: uni.getStorageSync("userId"),
        openid: uni.getStorageSync("wxInfo").openId,
        appid: uni.getStorageSync("appId"),
      };
      this.collectFlag = !this.collectFlag;
      if (this.collectFlag) {
        await addCollectDoctor(obj);
        Toast("关注成功");
      } else {
        await cancelKeepDoc(obj);
        Toast("取消成功");
      }
    },
    // 获取咨询排班
    async getVisitInfo() {
      // 问诊
      let para = {
        docId: this.docInfo.docId,
        consultationCode: 0,
        deptId: this.docInfo.deptId,
        hosId: uni.getStorageSync("hosId"),
      };
      let res = await findAllByType(para);
      this.inquiryWay = res.data;
    },
    // 获取复诊排班
    async getFzVisit() {
      // 问诊
      let para = {
        docId: this.docInfo.docId,
        consultationCode: 1,
        deptId: this.docInfo.deptId,
        hosId: uni.getStorageSync("hosId"),
      };
      let res = await findAllByType(para);
      this.subsequent = res.data;
    },
    // 根据不同的num,显示不同的排班内容
    setVisitInfo() {
      let num = this.num;
      let obj;
      if (num == 0) {
        // 咨询
        obj = this.inquiryWay;
      } else {
        // 复诊
        obj = this.subsequent;
      }
      this.visitInfo = obj;
    },
    // 切换咨询,复诊
    getTabNum(e) {
      this.num = e;
      this.setVisitInfo();
    },
    // 获取评价信息
    async getEvaluateList() {
      let res = await findDocEvaluateListPage(this.evaluateQuery);

      this.prTotal = res.data.total;
      let list = res.data.rows;

      // 循环此次数据
      list.forEach((v) => {
        v.remark = JSON.parse(v.remark);
        // 1 描述 2 评价图片 3 就诊满意度 4 物流满意度 5 取药满意度
        let n = 0;
        let num = 0;
        if (v.t3) {
          n += Number(v.t3);
          num += 1;
        }
        if (v.t4) {
          n += Number(v.t4);
          num += 1;
        }
        if (v.t5) {
          n += Number(v.t5);
          num += 1;
        }
        // 评价分
        let rate = (n / num).toFixed(1);

        if (v.t2) {
          let imgs = [];
          let arr = JSON.parse(v.t2);

          arr.forEach((k) => {
            myJsTools.downAndSaveImg(k, (url) => {
              imgs.push(url);
            });
          });
          v.t2 = imgs;
        }
        // 评分
        v.num = rate;
      });

      // 拼接数据
      this.listPr = [...this.listPr, ...list];

      // 判断是否还有下一页
      if (res.data.total > this.listPr.length) {
        this.loadingFlag = false;
        // 展示查看更多
        this.evaluateFlag = true;
      } else {
        // 没有更多
        this.loadingFlag = true;
      }
    },
    // 查看全部评价
    getAllEvaluate() {
      // 加载剩余的全部评价
      this.evaluateQuery.page = 2; // 第2页
      this.evaluateQuery.limit = this.prTotal - this.listPr.length; // 剩余条数
      this.getEvaluateList();
      this.evaluateFlag = false;
      this.loadingFlag = true;
    },

    // 点击去问诊
    async getAdvisory(msg) {
      if (!this.hasInfo()) return;
      this.getWholeArg(); // 此处统一获取全局参数
      let {
        docName,
        docProf,
        deptName,
        deptId,
        docImg,
        docImgCopy,
        telNo: docTel,
        docLable,
      } = this.docDetail;
      let {
        visitDuration,
        visitPrice,
        visitTypeCode,
        visitTypeName,
        visitDate,
        isSwitch,
        vrId,
        dntId,
        dntName,
        week,
        apw,
        surplusNnm,
      } = msg;
      let num = this.num;
      let date = myJsTools.formatTimeDate(new Date());
      if (surplusNnm == 0) {
        Toast("该医生已无号");
      } else {
        // 判断如果为医院统一价格，获取费用明细
        if (isSwitch == 0) {
          if (
            visitTypeCode == "2" ||
            visitTypeCode == "3" ||
            visitTypeCode == "4"
          ) {
            date = "";
          }
          let para = {
            date,
            docId: this.docInfo.docId,
            type: visitTypeCode,
            consultationCode: num,
            hosId: uni.getStorageSync("hosId"),
            dntId,
          };
          let res = await findVisitTypePrice(para);
          if (res.data.length == 0) {
            Toast("该医生已无号");
          } else {
            let infoDetail = {
              dntName: res.data[0].dntName,
              docId: this.docInfo.docId,
              type: num,
              docName,
              docProf,
              deptName,
              deptId,
              docImg,
              docImgCopy,
              docTel,
              docLable,
              visitDuration,
              visitPrice,
              visitTypeCode,
              visitTypeName,
              week: week || "",
              apw: apw || "",
              visitDate: visitDate || "",
              details: res.data,
              isSwitch,
              vrId: vrId || "",
              dntId,
            };
            if (
              visitTypeCode == "2" ||
              visitTypeCode == "3" ||
              visitTypeCode == "4"
            ) {
              infoDetail.consultationCode = infoDetail.type;
              uni.setStorageSync("appointInfoDetail", infoDetail);
              uni.navigateTo({
                url: "/pages/register/appointRegister/reserve/index",
              });
            } else {
              uni.setStorageSync("infoDetail", infoDetail);
              uni.navigateTo({
                url:
                  "/pages/personalCenter/patientManage/index?action=" +
                  "selectPatient",
              });
            }
          }
        } else {
          let infoDetail = {
            type: num,
            docName,
            docProf,
            deptName,
            deptId,
            docImg,
            docImgCopy,
            docTel,
            docLable,
            docId: this.docInfo.docId,
            visitDuration,
            visitPrice,
            visitTypeCode,
            // visitTypeId: msg.visitTypeId, // 后台未返回
            visitTypeName,
            week: msg.week || "",
            apw: msg.apw || "",
            visitDate: visitDate || "",
            details: "",
            isSwitch,
            vrId: vrId || "",
            dntId: dntId,
            dntName,
          };
          if (
            visitTypeCode == "2" ||
            visitTypeCode == "3" ||
            visitTypeCode == "4"
          ) {
            infoDetail.consultationCode = infoDetail.type;
            uni.setStorageSync("appointInfoDetail", infoDetail);
            uni.navigateTo({
              url: "/pages/register/appointRegister/reserve/index",
            });
          } else {
            uni.setStorageSync("infoDetail", infoDetail);
            uni.navigateTo({
              url:
                "/pages/personalCenter/patientManage/index?action=" +
                "selectPatient",
            });
          }
        }
      }
    },
    // 预览评价图片
    previewImage(img) {
      myJsTools.previewImg(img);
    },
  },
  filters: {
    time(val) {
      if (!val) return "**";
      let str = val.split(" ")[0];
      str = str.replace(/-/g, ".");
      return str;
    },
    userName(val) {
      if (!val) return "";
      let str = val[0];
      str += "**";
      return str;
    },
  },
};
</script>

<style lang="scss" scoped>
.home {
  position: relative;
  padding: 20rpx 32rpx;
  min-height: 100vh;
  background: linear-gradient(225deg, #eaf8ff 0%, #f3f4f6 100%);
}
.pay_buts {
  @include flex(right);

  text {
    @include flex;
    width: 100%;
    height: 86rpx;
    border-radius: 100rpx;
    @include bg_theme;
    color: #fff;
    font-size: 34rpx;
  }
}

* {
  box-sizing: border-box;
}

.offline {
  margin-top: 24rpx;
}

.dnt_list {
  margin-top: 24rpx;
}

/* 空排班 */
.empty {
  height: 488rpx;
  border-radius: 16rpx;
  background-color: #fff;
  margin-top: 24rpx;
  box-shadow: 0px 4rpx 40rpx 0px rgba(0, 0, 0, 0.1);
  @include flex(left);
  flex-direction: column;

  &_img {
    width: 386rpx;
    height: 324rpx;
    margin-bottom: 30rpx;
  }

  text {
    font-size: 28rpx;
    color: #999;
  }
}

/* 评价 */
.evaluate {
  padding: 24rpx;
  //margin-top: 24rpx;
  background-color: #fff;
  border-radius:0px 0px 16rpx 16rpx;
}

.evaluate_top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 22rpx;
  color: #999999;
}

.evaluate_top .font_col_333 {
  font-size: 28rpx;
  color: rgba(51, 51, 51, 1);
  font-weight: bold;
  padding-right: 20rpx;
}

.evaluate_top .font_col_666 {
  color: #666666;
  font-size: 24rpx;
  margin-right: 10rpx;
  font-weight: bold;
}

.evaluate_top .font_col_blue {
  @include font_theme;
  font-weight: bold;
}

.evaluate_list {
  font-size: 26rpx;
  padding: 20rpx 0;
}

/* 更多文案 */
.more-text {
  text-align: center;
  font-size: 24rpx;
  color: #999;
  padding: 30rpx 0 0;
  border-top: 1px solid #eee;
  margin-top: 30rpx;
}

.rate-content {
  display: flex;
  align-items: center;

  /deep/ .uni-rate {
    position: relative;
    top: 6upx;
  }
}

/deep/.rate-media .rate-media-body {
  margin-top: 0;
}

.evaluate_list .nickname {
  color: #666666;
  margin-right: 20rpx;
}

.evaluate_list .eval-content {
  color: #333333;
  font-size: 26rpx;
  padding: 10rpx 0;
}

.evaluate_list .diagnose {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #999999;
  font-size: 26rpx;
}

.evaluate_btn {
  width: 100%;
  height: 92rpx;
  border-radius: 46rpx;
  @include border_theme;
  line-height: 92rpx;
  text-align: center;
  font-size: 36rpx;
  @include font_theme;
  box-sizing: border-box;
}

/* 分享 */
.share-container {
  position: fixed;
  width: 100%;
  height: 100vh;
  left: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  z-index: 999;
}

.share-bg {
  width: 100%;
  height: 100%;
}

.content {
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  background: #ffffff;
  border-radius: 8px 8px 0px 0px;
  padding: 42rpx 32rpx 32rpx;
  box-sizing: border-box;
}

.share-style {
  display: flex;
  align-items: center;
  justify-content: space-around;
  font-size: 22rpx;
  font-weight: 400;
  color: #999999;
  line-height: 32rpx;
}

.share-style .list {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.share-style .list image {
  display: block;
  width: 100rpx;
  height: 100rpx;
  margin-bottom: 6rpx;
}

.cancel {
  width: 100%;
  height: 98rpx;
  margin-top: 62rpx;
}

.cancel .btn {
  width: 100%;
  height: 100%;
  @include bg_theme;
  border-radius: 48rpx;
  text-align: center;
  line-height: 98rpx;
  font-size: 36rpx;
  font-weight: 400;
  color: #ffffff;
}

// 评价内容
.eval-content {
  .eval-cont-imgs {
    @include flex(left);
    flex-wrap: wrap;
    padding: 20rpx 0;

    .eval-cont-imgs-item {
      width: 200rpx;
      height: 200rpx;
      margin-right: 20rpx;
      margin-bottom: 15rpx;
      border: 1px solid #efefef;
      box-sizing: border-box;
      border-radius: 8rpx;

      &:nth-child(3n) {
        margin-right: 0;
      }
    }
  }
}

// 没有评价文案
.no_eval {
  line-height: 180upx;
  text-align: center;
  font-size: 28upx;
  color: $k-info-title;
}

// 评价标签
.pr_label {
  @include flex(left);
  flex-wrap: wrap;
  padding-top: 26upx;

  .pr_label_item {
    height: 36upx;
    background-color: #e8f6fd;
    padding: 0 12upx;
    margin-right: 16upx;
    margin-bottom: 16upx;
    font-size: 22upx;
    @include font_theme;
    @include flex;
    border-radius: 18upx;
  }
}
</style>
