/*
  Author: 王可 (<EMAIL>)
  store.js (c) 2021
  Desc: 发票信息本地存储
  Created:  2021/11/8上午10:08:37
  Modified: 2021/11/8下午5:55:23
*/

const INVOICE = 'invoice';

const SWITCH = 'invoice_switch';

/**
 * 获取发票列表
 */

export function getList() {
  return uni.getStorageSync(INVOICE) || [];
}

/**
 * 添加发票信息
 * @param {Object} obj 发票信息
 */
export function addItem(obj) {
  let list = getList();
  list.unshift(obj);
  uni.setStorageSync(INVOICE, list);
}

/**
 * 获取开关
 */
export function getIsNext() {
  let onOff = uni.getStorageSync(SWITCH);
  if (typeof onOff === 'boolean') {
    return onOff;
  }
  return true;
}

/**
 * 设置开关
 */
export function setIsNext(val) {
  uni.setStorageSync(SWITCH, val);
}

/**
 * 清空
 */
export function clear() {
  uni.removeStorageSync(INVOICE);
}
