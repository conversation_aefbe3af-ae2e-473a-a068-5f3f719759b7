<template>
  <view class="swiper">
    <!-- #BAE3F7 -->
    <swiper
      v-if="list.length > 0"
      class="swiper_warp"
      indicator-dots
      autoplay
      circular
      indicator-color="rgba(255, 255, 255, 0.29)"
      indicator-active-color="#FFFFFF"
      :interval="swiperTime"
      :duration="300"
    >
      <swiper-item v-for="(item, index) in list" :key="index">
        <img
          class="img-box"
          v-if="item.carouselImg"
          v-img="item.carouselImg"
          @click="goDetail(item)"
        />
      </swiper-item>
    </swiper>
    <swiper
      v-else
      class="swiper_warp"
      indicator-dots
      autoplay
      circular
      indicator-color="rgba(255, 255, 255, 0.29)"
      indicator-active-color="#FFFFFF"
      :interval="3000"
      :duration="300"
    >
      <swiper-item>
        <image class="img-box" src="/static/images/topbanner.png"></image>
      </swiper-item>
    </swiper>
  </view>
</template>

<script>
import { getConfigInfoByKey } from '@/api/base.js'
export default {
  props: {
    list: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      errUrl: require('../../../static/images/topbanner.png'),
      swiperTime: 5000,
    }
  },
  created() {
    this.getConfigInfoByKeys()
  },
  methods: {
    //获取轮播图参数
    async getConfigInfoByKeys() {
      let res = await getConfigInfoByKey({
        configKey: 'interval_time_for_patient',
      })
      let newArr = res.data
      this.swiperTime =
        newArr?.configValue > 0 ? Number(newArr?.configValue) * 1000 : 5000
      console.log(newArr)
    },
    goDetail(item) {
      console.log('999', item)
      if (item.title === 'AI医生') {
        uni.navigateTo({
          url: '/pages/aiAssistant/aiIndex',
        })
        return
      }
      if (item.title === 'AI健康咨询') {
        uni.navigateTo({
          url: '/pages/aiAssistant/previewImages?flag=2&title=' + item.title,
        })
        return
      }
      if (item.carouselType == 1) {
        uni.navigateTo({
          url:
            '/pages/index/com/swiperDetail?obj=' +
            JSON.stringify(item.contentInfo) +
            '&title=' +
            item.title,
        })
      } else {
        let linkUrl = item.contentInfo
        window.location.href = linkUrl
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.swiper {
  background: #fff;
  border-radius: 8rpx;
}

.swiper_warp {
  height: 240rpx;
}

.img-box {
  width: 100%;
  height: 240rpx;
  object-fit: cover;
  border-bottom-left-radius: 28rpx;
  border-bottom-right-radius: 28rpx;
}
</style>
