import http from '../common/request/request.js';

// 聊天列表(post请求)
export function basicgetPatientChatList(param = {}) {
  return http({
    url: 'basic/proPatientBusiness/getPatientChatList',
    param,
    method: 'post',
  });
}

// 查询该次挂号是否已关闭问诊倒计时的显示(post请求)
export function getIsCountdown(param = {}) {
  return http({
    url: 'business/proreceive/getIsCloseChatTime',
    param,
    method: 'post',
  });
}

// 关闭该次挂号的倒计时显示(post请求)
export function setCloseCountdown(param = {}) {
  return http({
    url: 'business/proreceive/updateCloseChatTime',
    param,
    method: 'post',
  });
}

// 获取处方卡片信息(post请求)
export function getPrescriptionCard(param = {}) {
  return http({
    url: 'business/proPrescriptionController/getPrescriptionCardInfo',
    param,
    method: 'post',
  });
}

// 查询量表是否已填写
export function getQuestionStatus(param = {}) {
  return http({
    url: 'basic/proflocksend/getPatientFeedback',
    param,
    method: 'post',
  });
}

// 获取转诊记录状态
export function getReferralRecord(param = {}) {
  return http({
    url: 'business/proReferralRecord/getReferralRecord',
    param,
    method: 'post',
  });
}

// 查询聊天页面卡片-自定义服务支付状态
export function findCustomServicePayStatus(param = {}) {
  return http({
    url: 'business/proCustomBusiness/findCustomServicePayStatus',
    param,
    method: 'post',
  });
}

// 查询聊天页面卡片-查询病历授权状态
export function getMedicalAuthorizeStatus(param = {}) {
  return http({
    url: 'basic/proMedicalAuthorize/getMedicalAuthorizeStatus',
    param,
    method: 'post',
  });
}

// 查询聊天页面卡片-修改病历授权状态
export function updateMedicalAuthorizeStatus(param = {}) {
  return http({
    url: 'basic/proMedicalAuthorize/updateMedicalAuthorizeStatus',
    param,
    method: 'post',
  });
}

// 查询聊天页面卡片-保存病历授权信息转诊
export function saveMedicalAuthorizeReferral(param = {}) {
  return http({
    url: 'basic/proMedicalAuthorize/saveMedicalAuthorizeReferral\n',
    param,
    method: 'post',
  });
}

//操作聊天列表(post请求)
export function updateChatList(param = {}) {
  return http({
    url: 'business/proregister/updateChatOperation',
    param,
    method: 'post',
  });
}

// 保存投诉原因(post请求)
export function saveProComplain(param = {}) {
  return http({
    url: 'business/procomplain/saveProComplain',
    param,
    method: 'post',
  });
}

// 查询医生给患者赠送的剩余次数
export function getGiveTimesById(param = {}) {
  return http({
    url: 'business/progivechattimes/getGiveTimesById',
    param,
    method: 'post',
  });
}

// 更新剩余次数
export function updateSurplusTimes(param = {}) {
  return http({
    url: 'business/progivechattimes/updateSurplusTimes',
    param,
    method: 'post',
  });
}

// 添加病情描述(post请求)
export function getPatientRecords(param = {}) {
  return http({
    url: 'business/patientrecords/getPatientRecords',
    param,
    method: 'post',
  });
}

// 患者给医生，向mq推送消息
export function patientToDoctorSendMessageToMq(param = {}) {
  return http({
    url: 'basic/sendMessageToMq/patientToDoctorSendMessageToMq',
    param,
    method: 'post',
  });
}

// 查询聊天页面卡片-检查单状态
export function getProPacsStatus(param = {}) {
  return http({
    url: 'basic/proPacsInfoController/getProPacsStatus',
    param,
    method: 'post',
  });
}
