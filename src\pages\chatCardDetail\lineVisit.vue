<template>
  <view>
    <doc-info
      :doc-id="pageParam.docId"
      pageType="visit"
      @docInfo="getDocInfo"
    ></doc-info>

    <lineVisit :pageParam="pageParam" />
  </view>
</template>

<script>
import DocInfo from '../../components/docInfo/docInfo';
import lineVisit from './components/lineVisit.vue';
export default {
  components: {
    DocInfo,
    lineVisit,
  },
  data() {
    return {
      pageParam: {},
      // 医生信息
      docInfo: '',
    };
  },
  onLoad(option) {
    this.pageParam = JSON.parse(option.param);
  },
  methods: {
    getDocInfo(item) {
      this.docInfo = item;
    },
  },
};
</script>
