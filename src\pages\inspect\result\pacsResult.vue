<template>
  <!-- 检验预约结果 -->
  <view class="pacs_result">
    <!-- 状态 -->
    <RESULT :time="time" :isSuc="status == 1" />

    <!-- 二维码 -->
    <EWM
      v-if="status == 1"
      :code="code"
      :time="time"
      :pliId="ppiId"
      :dloId="dpoId"
    />
  </view>
</template>

<script>
import { getProPacsAllInfoByID } from '@/api/inspect';
import RESULT from '../com/result.vue';
import EWM from '../com/resultEwm.vue';

export default {
  name: 'PacsResult',
  components: {
    RESULT,
    EWM,
  },
  data() {
    return {
      status: -1,
      address: {},
      time: '',
      code: '',
      pliId: '',
      dloId: '',
    };
  },
  onLoad(opt) {
    this.status = opt.type || 1;
    this.getDetail(opt.id);
  },
  methods: {
    async getDetail(id) {
      let { data } = await getProPacsAllInfoByID(id);
      let {
        appointDate,
        appointEndTime,
        appointStartTime,
        ppiCode,
        ppiId,
        dpoId,
      } = data;
      this.time =
        appointDate +
        ' ' +
        appointStartTime.slice(0, 5) +
        '-' +
        appointEndTime.slice(0, 5);
      this.code = ppiCode;
      this.ppiId = ppiId;
      this.dpoId = dpoId;
    },
  },
};
</script>

<style lang="scss" scoped>
.pacs_result {
  padding: 32rpx;

  .ewm {
    margin-top: 24rpx;
  }
}
</style>
