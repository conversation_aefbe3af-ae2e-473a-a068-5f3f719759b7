<!--
 * @Descripttion: 
 * @version: 
 * @Author: zhengyangyang
 * @Date: 2025-05-22 15:28:28
 * @LastEditors: zhengyangyang
 * @LastEditTime: 2025-06-26 16:40:04
-->
<template>
  <!-- 文本消息 -->
  <view class="chat_text">
    <!-- 头像 -->
    <image @click="head" :src="imgUrl || '/static/images/docHead.png'" mode="aspectFill" class="chat_user_img" />
    <view>
      <view class="chatName">{{chatName}}</view>
      <!-- 文本 -->
      <view class="text_cont" @click="click">
        <text v-html="content"></text>
        <!-- 确定按钮 -->
        <view 
          class="confirm_btn" 
          :class="{ disabled: isButtonDisabled }"
          @click="confirmClick"
        >
          <text>确认</text>
        </view>
      </view>

    </view>
  </view>
</template>

<script>
import { isConfirmDrug } from '@/api/qcAi.js'

export default {
  props: {
    content: {
      type: String,
      default: '',
    },
    imgUrl: {
      type: String,
      default: '',
    },
    chatName: {},
    pageParam: {
      type: Object,
      default: () => {},
    },
    drugId: {
      type: String,
      default: '',
    },
    patientId: {
      type: String,
      default: '',
    },
    groupId: {
      type: String,
      default: '',
    }
  },
  data () { 
    return {
      n: 0,
      isButtonDisabled: false
    };
  },
  created() {
    this.checkConfirmStatus();
  },
  methods: {
    head () {
      this.$emit('head');
    },
    click () {
      this.n++;
      if (this.n >= 2) {
        this.$emit('double', this.content);
      }
      setTimeout(() => {
        this.n = 0;
      }, 500);
    },
    confirmClick() {
      if (this.isButtonDisabled) return;
      this.isButtonDisabled = true;
      this.$emit('confirm');
    },
    // 检查用药确认状态
    async checkConfirmStatus() {
      try {
        // 如果没有传入必要参数，不执行检查
        if (!this.groupId || !this.pageParam.patientId) return;
        
        const { data } = await isConfirmDrug({
          groupId: this.groupId,
          patientId: this.pageParam.patientId,
          projectId: '18'
        });
        
        // 根据接口返回值判断按钮是否禁用
        // 假设接口返回true表示已确认，按钮应该禁用
        this.isButtonDisabled = data === false;
      } catch (error) {
        console.error('检查确认状态出错:', error);
      }
    }
  },
  watch: {
    groupId() {
      this.checkConfirmStatus();
    }
  }
};
</script>

<style scoped lang="scss">
.chat_text {
  max-width: 600upx;
  box-sizing: border-box;
  @include flex(left);
  padding-bottom: 30upx;
  align-items: flex-start;

  // 头像
  .chat_user_img {
    width: 64upx;
    height: 64upx;
    border-radius: 4upx;
    margin-right: 16upx;
    flex: none;
  }

  .text_cont {
    max-width: 516upx;
    padding: 10upx 20upx;
    background-color: #fff;
    color: #333;
    word-break: break-all;
    font-size: 28upx;
    border: 1px solid #ececec;
    border-radius: 8upx 32upx 32upx 32upx;
    
    .confirm_btn {
      margin-top: 20upx;
      background-color: #1989fa;
      color: #fff;
      text-align: center;
      padding: 10upx 0;
      border-radius: 8upx;
      width: 200upx;
      
      &.disabled {
        background-color: #c8c9cc;
        opacity: 0.5;
        cursor: not-allowed;
      }
      
      text {
        color: #fff;
      }
    }
  }
}
</style>
