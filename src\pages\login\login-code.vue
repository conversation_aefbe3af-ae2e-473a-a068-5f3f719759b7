<template>
  <view class="login">
    <!--   -->
    <!-- 内容 -->
    <view class="cont" v-if="simpName == '0'">
      <view class="input_item">
        <text class="label">姓名</text>
        <input
          type="text"
          placeholder-class="place"
          v-model="form.name"
          placeholder="请输入姓名"
        />
      </view>
      <view class="input_item">
        <text class="label">身份证号</text>
        <input
          type="text"
          placeholder-class="place"
          v-model="form.cardId"
          :placeholder="'请输入身份证号' + (isRealName ? '' : '')"
          @blur="cardIdBlur"
        />
      </view>

      <view class="input_item">
        <text class="label">性别</text>
        <input
          type="text"
          placeholder-class="place"
          v-model="form.sex"
          placeholder="-"
          disabled
        />
      </view>

      <view class="input_item">
        <text class="label">年龄</text>
        <input
          type="text"
          placeholder-class="place"
          v-model="form.age"
          placeholder="-"
          disabled
        />
      </view>
      <view class="input_item juleft">
        <text class="label">民族</text>
        <view @click="openNation">
          <input
            class="disabled"
            type="text"
            placeholder-class="place"
            v-model="form.nation"
            :placeholder="'点击选择民族' + (isRealName ? '' : '')"
            disabled
          />
        </view>
      </view>

      <view class="input_item" v-if="form.age && form.age < 18">
        <text class="label">监护人</text>
        <input
          type="text"
          placeholder-class="place"
          v-model="form.guardianIdNo"
          placeholder="请输入监护人身份证号"
          @blur="guardianIdNoBlur"
        />
      </view>
      <view class="input_item">
        <text class="label">手机号</text>
        <input
          placeholder-class="place"
          type="text"
          maxlength="11"
          v-model="form.telNo"
          placeholder="请输入手机号"
        />
        <button
          v-if="isRealName"
          :class="{ act: codeinfo.auth_time > 0 }"
          @click="sendCode"
        >
          {{ codeinfo.btnText }}
        </button>
      </view>

      <view class="input_item" v-if="isRealName">
        <text class="label">验证码</text>
        <input
          type="text"
          placeholder-class="place"
          v-model="form.code"
          placeholder="请输入验证码"
          maxlength="6"
        />
      </view>

      <view class="input_item" @click="selectYbCardFun">
        <text class="label">医保卡</text>
        <input
          type="text"
          placeholder-class="place"
          :value="form.isMedicareType"
          v-model="medicareTypeName"
          class="disabled"
          :placeholder="'请选择医保卡类型' + (isRealName ? '' : '(选填)')"
          disabled
        />
        <uni-icons class="icon" type="arrowright" size="20"></uni-icons>
      </view>

      <view class="input_item" v-if="medicareNumShow">
        <text class="label"></text>
        <input
          placeholder-class="place"
          type="text"
          :value="form.medicareNum"
          v-model="form.medicareNum"
          placeholder="请填写医保卡卡号"
        />
      </view>

      <view class="input_item juleft">
        <text class="label">家人关系</text>
        <view @click="openLbPicker">
          <input
            type="text"
            placeholder-class="place"
            placeholder="点击选择家人关系"
            disabled
            class="disabled"
            v-model="form.spuRelation"
          />
        </view>
        <uni-icons class="icon" type="arrowright" size="20"></uni-icons>
      </view>
      <view>
        <view class="input_item">
          <text class="label">身高</text>
          <input
            type="number"
            placeholder-class="place"
            maxlength="3"
            :value="form.height"
            v-model="form.height"
            placeholder="请输入就诊人身高(选填)"
          />
          <view class="unit"> CM </view>
        </view>
        <view class="input_item">
          <text class="label">体重</text>
          <input
            type="number"
            maxlength="3"
            placeholder-class="place"
            :value="form.weight"
            v-model="form.weight"
            placeholder="请输入就诊人体重(选填)"
          />
          <view class="unit"> KG </view>
        </view>

        <view class="title_format">药物过敏史</view>
        <view class="content">
          <textarea
            placeholder="请输入就诊人药物过敏史(选填)"
            maxlength="200"
            :value="form.drugAllergyHistory"
            v-model="form.drugAllergyHistory"
            class="other-content"
          />
        </view>
        <view class="title_format">既往病史</view>
        <view class="content">
          <textarea
            placeholder="请输入就诊人既往病史(选填)"
            maxlength="200"
            value="form.previousHistory"
            v-model="form.previousHistory"
            class="other-content"
          />
        </view>
        <view class="title_format">遗传病史</view>
        <view class="content">
          <textarea
            placeholder="请输入就诊人遗传病史(选填)"
            maxlength="200"
            :value="form.geneticHistory"
            v-model="form.geneticHistory"
            class="other-content"
          />
        </view>
        <view class="title_format">家族病史</view>
        <view class="content">
          <textarea
            placeholder="请输入就诊人家族病史(选填)"
            maxlength="200"
            :value="form.familyMedicalHistory"
            v-model="form.familyMedicalHistory"
            class="other-content"
          />
        </view>
      </view>
    </view>

    <view class="cont" v-if="simpName == '1'">
      <view class="input_item">
        <text class="label">姓名</text>
        <input
          type="text"
          placeholder-class="place"
          v-model="form.name"
          placeholder="请输入姓名"
        />
      </view>
      <view class="input_item">
        <text class="label">身份证号</text>
        <input
          type="text"
          placeholder-class="place"
          v-model="form.cardId"
          placeholder="请输入身份证号"
          @blur="cardIdBlur"
        />
      </view>
      <view class="input_item">
        <text class="label">手机号</text>
        <input
          placeholder-class="place"
          type="text"
          maxlength="11"
          v-model="form.telNo"
          placeholder="请输入手机号"
        />
        <button :class="{ act: codeinfo.auth_time > 0 }" @click="sendCode">
          {{ codeinfo.btnText }}
        </button>
      </view>
      <view class="input_item">
        <text class="label">验证码</text>
        <input
          type="text"
          placeholder-class="place"
          v-model="form.code"
          placeholder="请输入验证码"
          maxlength="6"
        />
      </view>
    </view>

    <!-- 弹窗二次验证码 -->
    <view class="wrapper" v-if="showOld">
      <view class="old_phone">
        <!-- 20014 20015 -->
        <view class="old_title" v-if="!other">
          <view
            >系统检测到您的用户信息已被注册，已向{{
              setPhone(oldTelNo)
            }}发送验证码</view
          >
          <view>请填写验证码，核实您的信息</view>
        </view>
        <!-- 20019 -->
        <view class="old_title" v-else>
          <view
            >系统检测到您的就诊人信息已被其他用户绑定或与您填写的信息不一致，已向{{
              setPhone(oldTelNo)
            }}发送验证码</view
          >
          <view>请填写验证码，核实您的信息</view>
        </view>
        <view class="old_input">
          <text class="input_text">验证码</text>
          <!-- 输入框 -->
          <input
            type="number"
            :value="newCode.length >= index + 1 ? newCode[index] : ''"
            v-for="(item, index) in 6"
            disabled
            @click="isFocus = true"
            :key="index"
          />
        </view>
        <!-- 隐藏输入框 -->
        <input
          type="number"
          @blur="isFocus = false"
          class="hide_input"
          v-model="newCode"
          maxlength="6"
          :focus="isFocus"
        />
        <!-- 按钮 -->
        <view class="old_but">
          <button @click="sendBind">提交</button>
        </view>
      </view>
    </view>

    <!-- 注意 -->
    <view class="tip">
      <view class="text">
        <text>注：</text>若要在互联网医院进行问诊或线上购药
      </view>
      <view class="text"> 请先完善您的个人信息 </view>
    </view>
    <propBottom
      v-if="selectYbType"
      :actions="actions"
      @propConfirm="propConfirm"
      @propCancel="propCancel"
    ></propBottom>
    <lb-picker
      :list="nationList"
      ref="nation"
      v-model="form.nation"
      mode="multiSelector"
      :level="1"
      @confirm="handleConfirmnation"
    ></lb-picker>
    <lb-picker
      :list="relationship"
      ref="lbpicker"
      v-model="form.spuRelation"
      mode="multiSelector"
      :level="2"
      @confirm="handleConfirm"
    ></lb-picker>
    <view class="wrapper1" v-if="showTips">
      <view class="hx-block">
        <view class="title"> {{ patient_age_limit_tip }} </view>
        <view class="btn_footer">
          <view @click="showTips = false">确定</view>
        </view>
      </view>
    </view>
    <FOOTER @click="isClickSave && bindData()">确定</FOOTER>
  </view>
</template>

<script>
// 引入视频通话
const emedia = require('@/utils/WebIM.js')['emedia']
import regex from '@/common/js/regex.js'
import myJsTools from '@/common/js/myJsTools.js'
import FOOTER from '@/components/footer_button/button.vue'
import LbPicker from '@/components/lb-picker'
import { Toast } from '@/common/js/pay.js'
import { getSysCodeByType } from '@/api/shop'
import {
  sendCaptcha,
  hisPatientLoginBindingUserInfo,
  updateHxidIsregistStatus,
  getUsertInfo,
  findPatientRelationDict,
} from '@/api/user.js'
import {
  patientLogOneDataChangeTelNo,
  patientLogTwoData,
  verifyPatient,
  getSysPlatformConfigByKeyList,
} from '@/api/base.js'

export default {
  components: {
    FOOTER,
    LbPicker,
  },
  data() {
    return {
      //简化版显示
      simpName: '0',
      showTips: false,
      //年龄限制提示
      patient_age_limit: '',
      patient_age_limit_tip: '',
      // 旧手机号
      oldTelNo: '',
      // 新验证码
      newCode: '',
      // 用户id
      userId: '',
      // 重复数据
      existedAppid: '',

      // 接口返回的身份证号
      enterIdNo: '',
      // 不保留的id
      repeatIdNoUserId: '',
      // 要保留的id
      repeatTelNoUserId: '',
      isFocus: false,
      showOld: false,
      form: {
        name: '',
        cardId: '',
        sex: '',
        age: '',
        telNo: '',
        code: '',
        sexCode: '',
        nation: '',
        guardianIdNo: '', //监护人身份证号
        medicareNum: '', //医保卡号
        isMedicareType: '', //是否医保类型 (0无医保 1本地医保 2异地医保)
        spuRelation: '', //家人关系
        height: '', //身高cm
        weight: '', //体重kg
        drugAllergyHistory: '', //药物过敏史
        previousHistory: '', //既往史
        geneticHistory: '', //遗传病史
        familyMedicalHistory: '', //家族病史
      },
      nationList: [], //民族列表
      relationship: [], //家人关系列表
      selectYbType: false, //医保弹窗
      medicareNumShow: false, //医保卡号显示
      medicareTypeName: '', //医保卡类型回显
      actions: [
        {
          name: '就诊人无本市医保卡',
          ybCardType: 0,
        },
        {
          name: '就诊人持有本市医保卡',
          ybCardType: 1,
        },
        {
          name: '就诊人持有外地医保卡',
          ybCardType: 2,
        },
      ],
      codeinfo: {
        sendAuthCode: true,
        auth_time: 0,
        btnText: '发送验证码',
      }, //验证码时间及是否可点击
      isClickSave: true,
      // 20019
      other: '',
      // 是否实名
      isRealName: true,
      SMGYactions: '',
      docId: '',
      projectId: '',
    }
  },
  onLoad(options) {
    console.log('查看未注册用户参数', options)
    if (options) {
      if (options.action) {
        this.SMGYactions = options.action
        this.docId = options.docId
        this.projectId = options.projectId
      }
    }
    // 医诺卫 强制实名
    if (uni.getStorageSync('ynw_telNo')) {
      this.form.telNo = uni.getStorageSync('ynw_telNo')
      this.form.name = uni.getStorageSync('ynw_patientName')
      this.isRealName = true
      return
    }
    this.getConfig()
    this.form.spuRelation = '本人'
  },
  onShow() {
    this.getOptions()
    this.getSysCodeByType()
    this.findPatientRelationDict()
  },
  methods: {
    //获取配置项
    async getOptions() {
      this.simpName = 0
      let { data } = await getSysPlatformConfigByKeyList([
        'simplify_patient_real_name',
        'patient_age_limit',
        'patient_age_limit_tip',
      ])
      data.map((el) => {
        if (el.configKey == 'simplify_patient_real_name') {
          this.simpName = el.configValue
        } else if (el.configKey == 'patient_age_limit') {
          this.patient_age_limit = el.configValue
        } else if (el.configKey == 'patient_age_limit_tip') {
          this.patient_age_limit_tip = el.configValue
        }
      })
    },
    async getSysCodeByType() {
      let { data } = await getSysCodeByType('085')
      let list = []
      data.forEach((item) => {
        list.push({ label: item.meaning, value: item.code, children: [] })
      })
      this.nationList = list
    },
    async findPatientRelationDict() {
      let { data } = await findPatientRelationDict()
      this.relationship = data
    },

    // 修改就诊人医保类型
    selectYbCardFun() {
      this.selectYbType = true
    },
    propCancel() {
      this.selectYbType = false
    },
    propConfirm(data) {
      console.log(data)
      this.selectYbType = false
      let ybCardType = data.ybCardType

      this.medicareTypeName = data.name
      this.form.isMedicareType = data.ybCardType

      if (ybCardType == '1' || ybCardType == '2') {
        this.medicareNumShow = true
      } else {
        this.medicareNumShow = false
      }
    },
    // 获取配置 是否实名
    async getConfig() {
      let { data } = await getSysPlatformConfigByKeyList(['patient_real_name'])
      // 0 不实名 1 实名
      console.log(data[0].configValue)
      if (data[0].configValue == 0) this.isRealName = false
    },
    setPhone(str) {
      return myJsTools.phone(str)
    },
    // 校验身份证号,并获取用户性别,年龄
    cardIdBlur(e) {
      let cardId = e.detail.value
      if (!cardId) return
      let form = this.form
      if (regex.idNoBlur(cardId)) {
        if (regex.disCriCard(cardId, 'sex') == 2) {
          form.sex = '女'
        } else {
          form.sex = '男'
        }
        form.sexCode = regex.disCriCard(cardId, 'sex')
        form.age = regex.disCriCard(cardId, 'age')
      }
    },
    //校验监护人身份证
    guardianIdNoBlur(e) {
      let cardId = e.detail.value
      if (!cardId) return
      regex.guardianIdNoBlur(cardId)
      if (regex.disCriCard(cardId, 'age') < 18) {
        Toast('监护人应为成年人')
        return
      }
    },
    // 获取验证码
    async sendCode() {
      let telNo = this.form.telNo
      if (regex.telBlur(telNo)) {
        // 如果正在倒计时 防止点击
        if (this.codeinfo.auth_time > 0) return
        await sendCaptcha({
          telNo,
        })
        // 发送验证码,倒数读秒
        this.codeinfo.auth_time = 60
        var auth_timetimer = setInterval(() => {
          this.codeinfo.auth_time--
          this.codeinfo.btnText = this.codeinfo.auth_time + 's后重新发送'
          if (this.codeinfo.auth_time <= 0) {
            this.codeinfo.sendAuthCode = true
            // 重置为0
            this.codeinfo.auth_time = 0
            this.codeinfo.btnText = '重新发送'
            clearInterval(auth_timetimer)
          }
        }, 1000)
      }
    },
    //提交表单，如需实名进行信息验证
    async bindData() {
      if (!regex.nameBlur(this.form.name)) return
      if (this.isRealName || this.form.cardId || this.simpName == '1') {
        if (!regex.idNoBlur(this.form.cardId)) return
      }
      if (!regex.telBlur(this.form.telNo)) return
      if (this.isRealName) {
        if (!regex.codeBlur(this.form.code)) return
      }
      if (
        this.patient_age_limit &&
        this.form.age &&
        this.form.age <= this.patient_age_limit
      ) {
        this.showTips = true
        return
      }
      if (this.simpName == '0') {
        if (!this.form.nation && this.isRealName) {
          Toast('请选择名族')
          return
        }
        if (this.isRealName && !this.form.guardianIdNo && this.form.age < 18) {
          Toast('请填写监护人身份证号')
          return
        }
        if (this.form.guardianIdNo) {
          if (!regex.guardianIdNoBlur(this.form.guardianIdNo)) return
          if (regex.disCriCard(this.form.guardianIdNo, 'age') < 18) {
            Toast('监护人应为成年人')
            return
          }
        }
        if (!this.form.medicareNum && this.form.isMedicareType) {
          Toast('请输入医保卡号')
          return
        }
        if (this.isRealName) {
          if (typeof this.form.isMedicareType !== 'number') {
            Toast('请选择医保类型')
            return
          }
          // if (!this.form.medicareNum && this.form.isMedicareType) {
          //   Toast("请输入医保卡号");
          //   return;
          // }
          if (!this.form.spuRelation) {
            Toast('请选择家人关系')
            return
          }
          // console.log("全填写了");
        }
      }
      this.isClickSave = false
      uni.showLoading({
        mask: true,
      })

      let {
        age,
        cardId,
        code,
        sexCode,
        sex,
        name,
        telNo,
        nation,
        guardianIdNo,
        medicareNum,
        isMedicareType,
        spuRelation,
        height,
        weight,
        drugAllergyHistory,
        previousHistory,
        geneticHistory,
        familyMedicalHistory,
      } = this.form

      let obj = {
        appid: uni.getStorageSync('appId'),
        openid: uni.getStorageSync('wxInfo').openId,
        headImgUrl: uni.getStorageSync('wxInfo').headimgurl,
        age,
        idNo: cardId,
        captcha: code,
        sexCode,
        sex,
        userName: name,
        telNo,
        nation,
        guardianIdNo,
        medicareNum,
        isMedicareType,
        spuRelation,
        height,
        weight,
        drugAllergyHistory,
        previousHistory,
        geneticHistory,
        familyMedicalHistory,
        patientImg: '',
      }
      obj.callSource = uni.getStorageSync('callSource')
      if (uni.getStorageSync('ynw_telNo')) {
        obj.isThirdPart = 1
        obj.thirdAppid = 'ynw'
        obj.thirdPatientId = uni.getStorageSync('ynw_patientId')
        obj.scanid = uni.getStorageSync('ynw_scanid')
        obj.thirdPartyOpenid = uni.getStorageSync('ynw_openid')
      } else {
        obj.isThirdPart = 0
      }
      try {
        console.log(obj)
        let res = await hisPatientLoginBindingUserInfo(obj)
        console.log(res)

        this.isClickSave = true
        uni.hideLoading()
        let data = res.data
        // 正常流程
        if (res.code == 20000) {
          this.bindSuccess(res)
          return
        }
        // 需要实名
        if (res.code == 20020) {
          Toast(res.message)
          this.isRealName = true
          return
        }

        let {
          oldTelNo,
          userId,
          enterIdNo,
          repeatIdNoUserId,
          existedAppid,
          repeatTelNoUserId,
        } = data

        // 身份一样，手机号不一样
        if (res.code == 20014) {
          this.oldTelNo = oldTelNo
          this.userId = userId
          this.existedAppid = existedAppid
          // 向旧手机发送验证码
          this.sendPhoneCode(oldTelNo)
          this.showOld = true
          return
        }

        // 两条重复信息
        if (res.code == 20015) {
          this.oldTelNo = oldTelNo
          this.userId = userId
          this.repeatIdNoUserId = repeatIdNoUserId
          this.existedAppid = existedAppid
          this.repeatTelNoUserId = repeatTelNoUserId
          // 用户输入的身份证号
          this.enterIdNo = enterIdNo
          // 向旧手机发送验证码
          this.sendPhoneCode(oldTelNo)
          this.newCode = ''
          this.showOld = true
          return
        }

        // 20019情况
        if (res.code == 20019) {
          this.oldTelNo = oldTelNo
          this.userId = userId
          // 向旧手机发送验证码
          this.sendPhoneCode(oldTelNo)
          this.newCode = ''
          this.showOld = true
        }
      } catch (error) {
        uni.hideLoading()
        this.isClickSave = true
      }
    },
    handleConfirm(e) {
      this.form.spuRelation = e.item[1].label
    },
    handleConfirmnation(e) {
      this.form.nation = e.item[0].label
    },
    openNation() {
      this.$refs.nation.show()
    },
    openLbPicker() {
      this.$refs.lbpicker.show()
    },
    // 状态 20000 正常流程
    bindSuccess(res) {
      uni.showLoading({
        mask: true,
      })
      // 存储userId
      uni.setStorageSync('userId', res.data.userId)
      let myUsername = res.data.userId.toLowerCase()
      uni.setStorageSync('myUsername', myUsername)
      this.$store.commit('setProPfInfo', res.data)
      // 隐藏弹窗
      this.showOld = false
      let hxidIsregist = res.data.hxidIsregist
      if (hxidIsregist == 1) {
        this.WebIMLogin()
      } else {
        this.WebIMRegister()
      }
    },

    // 状态 20014 执行
    async bindErrorOne() {
      // 参数
      let obj = {
        appid: uni.getStorageSync('appId'),
        openid: uni.getStorageSync('wxInfo').openId,
        headImgUrl: uni.getStorageSync('wxInfo').headimgurl,
        newTelNo: this.form.telNo,
        existedAppid: this.existedAppid,
        telNo: this.oldTelNo,
        userId: this.userId,
        captcha: this.newCode,
      }

      if (uni.getStorageSync('ynw_telNo')) {
        obj.isThirdPart = 1
        obj.thirdAppid = 'ynw'
        obj.thirdPatientId = uni.getStorageSync('ynw_patientId')
        obj.scanid = uni.getStorageSync('ynw_scanid')
        obj.thirdPartyOpenid = uni.getStorageSync('ynw_openid')
      } else {
        obj.isThirdPart = 0
      }

      uni.showLoading({
        mask: true,
      })

      try {
        let res = await patientLogOneDataChangeTelNo(obj)
        uni.hideLoading()
        if (res.code == 20000) {
          this.bindSuccess(res)
          return
        }
        if (res.code == 20019) {
          this.other = res.data
          this.oldTelNo = res.data.oldTelNo
          // 向旧手机发送验证码
          this.sendPhoneCode(res.data.oldTelNo)
          this.newCode = ''
          this.showOld = true
        }
      } catch (error) {
        uni.hideLoading()
      }
    },

    // 状态 20015 执行
    async bindErrorTwo() {
      // 参数
      let obj = {
        appid: uni.getStorageSync('appId'),
        openid: uni.getStorageSync('wxInfo').openId,
        headImgUrl: uni.getStorageSync('wxInfo').headimgurl,
        existedAppid: this.existedAppid,
        telNo: this.oldTelNo,
        captcha: this.newCode,
        enterIdNo: this.enterIdNo,
        repeatIdNoUserId: this.repeatIdNoUserId,
        repeatTelNoUserId: this.repeatTelNoUserId,
      }
      uni.showLoading({
        mask: true,
      })
      try {
        if (uni.getStorageSync('ynw_telNo')) {
          obj.isThirdPart = 1
          obj.thirdAppid = 'ynw'
          obj.thirdPatientId = uni.getStorageSync('ynw_patientId')
          obj.scanid = uni.getStorageSync('ynw_scanid')
          obj.thirdPartyOpenid = uni.getStorageSync('ynw_openid')
        } else {
          obj.isThirdPart = 0
        }
        let res = await patientLogTwoData(obj)
        uni.hideLoading()
        if (res.code == 20000) {
          this.bindSuccess(res)
          return
        }
        if (res.code == 20019) {
          this.other = res.data
          this.oldTelNo = res.data.oldTelNo
          // 向旧手机发送验证码
          this.sendPhoneCode(res.data.oldTelNo)
          this.newCode = ''
          this.showOld = true
        }
      } catch (error) {
        uni.hideLoading()
      }
    },

    // 状态 20019 执行
    async bindErrorThere() {
      this.other.appid = uni.getStorageSync('appId')
      this.other.captcha = this.newCode
      const obj = JSON.parse(JSON.stringify(this.other))

      if (uni.getStorageSync('ynw_telNo')) {
        obj.isThirdPart = 1
        obj.thirdAppid = 'ynw'
        obj.thirdPatientId = uni.getStorageSync('ynw_patientId')
        obj.scanid = uni.getStorageSync('ynw_scanid')
        obj.thirdPartyOpenid = uni.getStorageSync('ynw_openid')
      } else {
        obj.isThirdPart = 0
      }

      uni.showLoading({
        mask: true,
      })
      try {
        let res = await verifyPatient(obj)
        if (res.code != 20000) return
        this.bindSuccess(res)
      } catch (error) {}
    },

    // 按钮触发
    sendBind() {
      if (!regex.codeBlur(this.newCode)) return
      // 20019
      if (this.other) {
        this.bindErrorThere()
        // 20015
      } else if (this.enterIdNo && this.repeatIdNoUserId) {
        this.bindErrorTwo()
        // 20014
      } else {
        this.bindErrorOne()
      }
    },

    // 环信注册
    WebIMRegister() {
      console.log('环信注册', uni.getStorageSync('userId'))
      let _this = this
      let options = {
        username: uni.getStorageSync('userId'),
        password: uni.getStorageSync('userId'),
        nickname: uni.getStorageSync('userId'),
        appKey: _this.$im.config.appkey,
        // success: function () {
        //   console.log("环信注册成功", uni.getStorageSync("userId"));
        //   _this.updateHxidIsregistStatusFun();
        // },
        // error: function (err) {
        //   console.log("环信注册失败", err);
        //   // 关闭绑定信息弹框,打开环信提示框
        //   uni.hideLoading();
        //   WeixinJSBridge.call("closeWindow");
        // },
        apiUrl: _this.$im.config.apiURL,
      }
      _this.$im.conn
        .registerUser(options)
        .then((res) => {
          console.log('环信注册成功', uni.getStorageSync('userId'))
          _this.updateHxidIsregistStatusFun()
        })
        .catch((err) => {
          console.log('环信注册失败', err)
          // 关闭绑定信息弹框,打开环信提示框
          uni.hideLoading()
          WeixinJSBridge.call('closeWindow')
        })
    },

    async updateHxidIsregistStatusFun() {
      let userId = uni.getStorageSync('userId')
      await updateHxidIsregistStatus({
        userId,
      })
      console.log('第二步更改状态')
      // 更改用户环信状态成功
      this.WebIMLogin()
    },

    // 环信登录
    async WebIMLogin() {
      console.log('第三步环信登录')
      await this.getUserInfoFun()
      let _this = this
      let options = {
        apiUrl: _this.$im.config.apiURL,
        user: uni.getStorageSync('userId'),
        pwd: uni.getStorageSync('userId'),
        grant_type: uni.getStorageSync('userId'),
        appKey: _this.$im.config.appkey,
        // success: function (res) {
        //   // _this.getWholeArg();
        //   console.log("第四步环信登录成功回调");
        //   let memName =
        //     _this.$im.config.appkey + "_" + uni.getStorageSync("userId");
        //   // 进入会议前 必须调用
        //   emedia.mgr.setIdentity(memName, res.access_token);
        //   uni.hideLoading();
        //   if (uni.getStorageSync("ynw_telNo")) {
        //     uni.reLaunch({
        //       url: "/pages/shop/index",
        //     });
        //   } else if (this.action == "offline") {
        //     uni.reLaunch({
        //       url: "/pages/shop/scan?docId=" + this.docId,
        //     });
        //   } else {
        //     uni.reLaunch({
        //       url: "/pages/index/index",
        //     });
        //     // uni.navigateBack();
        //   }
        // },
        // error: function (err) {
        //   console.log("环信登录失败", err);
        //   uni.hideLoading();
        // },
      }
      _this.$im.conn
        .open(options)
        .then((res) => {
          console.log('第四步环信登录成功回调')
          let memName =
            _this.$im.config.appkey + '_' + uni.getStorageSync('userId')
          // 进入会议前 必须调用
          emedia.mgr.setIdentity(memName, res.access_token)
          uni.hideLoading()
          console.log('222222222', this.SMGYactions)
          // debugger;
          if (uni.getStorageSync('ynw_telNo')) {
            uni.reLaunch({
              url: '/pages/shop/index',
            })
          } else if (this.SMGYactions == 'offline') {
            uni.reLaunch({
              url: '/pages/shop/scan?docId=' + this.docId,
            })
          } else if (this.SMGYactions == 'qcOffline') {
            uni.reLaunch({
              url:
                '/pages/scanCode/nutritionAssessment?docId=' +
                this.docId +
                '&projectId=' +
                this.projectId,
            })
          } else if (this.SMGYactions == '618') {
            uni.reLaunch({
              url: '/pages/shop/index',
            })
          } else {
            uni.reLaunch({
              url: '/pages/index/index',
            })
            // uni.navigateBack();
          }
        })
        .catch((err) => {
          console.log('环信登录失败', err)
          uni.hideLoading()
        })
    },

    // 获取用户信息
    async getUserInfoFun() {
      console.log('第五步环信登录成功回调')
      let para = {
        appid: uni.getStorageSync('appId'),
        openid: uni.getStorageSync('wxInfo').openId,
      }
      // 请求用户信息
      let res = await getUsertInfo(para)

      let proPfInfo = res.data
      // 存储用户信息
      this.$store.commit('setProPfInfo', proPfInfo)

      // userId
      uni.setStorageSync('userId', proPfInfo.userId)

      uni.setStorageSync('tel', res.data.telNo || '')

      // 该用户下所有的患者id
      uni.setStorageSync('patientIdList', proPfInfo.patientIdList || [])
    },

    // 单独发送验证码
    sendPhoneCode(tel) {
      sendCaptcha({ telNo: tel })
    },
  },
}
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}

page {
  background-color: #fff;
}
.disabled {
  pointer-events: none;
}

.tip {
  // position: fixed;
  // bottom: 140rpx;
  height: 200rpx;
  margin-top: 40rpx;

  width: 100%;
  color: #999;
  font-size: 26rpx;

  .text {
    text-align: center;
    line-height: 44rpx;

    text {
      color: red;
    }
  }
}

.login {
  padding-top: 32rpx;

  .cont {
    width: 690rpx;
    margin: 0 auto;
    background-color: #fff;
    border-radius: 16rpx;
    overflow: hidden;

    .input_item {
      @include flex(lr);
      font-size: 30rpx;
      color: #333;
      height: 88rpx;
      border-bottom: 1px solid #f5f5f5;

      .label {
        width: 160rpx;
        flex: none;
      }

      input {
        flex: 1;
      }

      .place {
        font-size: 28rpx;
      }

      button {
        min-width: 180rpx;
        flex: none;
        height: 80rpx;
        @include flex;
        @include font_theme;
        font-size: 30rpx;
        padding: 0;

        &.act {
          color: #999;
        }
      }
    }
    .juleft {
      justify-content: left;
      view {
        width: 80%;
      }
    }
  }

  .wrapper {
    position: fixed;
    width: 100%;
    height: 100vh;
    left: 0;
    top: 0;
    background-color: rgba(0, 0, 0, 0.5);
    @include flex(center);
    z-index: 999;

    // 容器
    .block_box {
      width: 600upx;
      border-radius: 16upx;
      background-color: #fff;
      padding: 0 24upx;
      box-sizing: border-box;

      // 头像
      .avatar {
        width: 170upx;
        height: 170upx;
        display: block;
        margin: -85upx auto 0;
      }

      // 内容
      .info {
        .text {
          color: $k-title;
          font-weight: 500;
          font-size: 28upx;
          text-align: center;
        }
      }
    }
  }

  // 旧手机验证码
  .old_phone {
    width: 600upx;
    padding: 32upx;
    background-color: #fff;
    border-radius: 16upx;
    box-sizing: border-box;

    .old_title {
      font-size: 32upx;
      color: $k-title;
      text-align: center;
      padding: 20upx 0;
      line-height: 48upx;
    }

    .old_input {
      height: 84upx;
      border-radius: 8upx;
      @include flex;

      .input_text {
        font-size: 28upx;
        color: $k-title;
        font-weight: bold;
        padding-right: 20upx;
      }

      input {
        width: 36upx;
        height: 44upx;
        font-size: 32upx;
        color: $k-title;
        line-height: 44upx;
        font-weight: bold;
        text-align: center;
        margin-right: 20upx;
        border-radius: 4upx;
        background-color: $k-page-bg-color;
      }
    }

    .hide_input {
      height: 0;
      opacity: 0;
      position: relative;
      top: -60upx;
    }

    .old_but {
      padding-top: 32upx;

      button {
        width: 248upx;
        height: 68upx;
        border-radius: 34upx;
        @include bg_theme;
        color: #fff;
        @include flex;
        font-size: 32upx;
      }
    }
  }
}
.title_format {
  font-size: 28rpx;
  color: rgba(51, 51, 51, 1);
  line-height: 92rpx;
}
.content {
  padding-right: 32upx;
}
.other-content {
  width: 100%;
  box-sizing: border-box;
  border-radius: 8rpx;
  border: 1px solid rgba(229, 229, 229, 1);
  font-size: 28rpx;
  line-height: 40rpx;
  padding: 16rpx;
  height: 240rpx;
}
.unit {
  font-size: 28rpx;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
}

.wrapper1 {
  position: fixed;
  width: 100%;
  height: 100vh;
  left: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.5);
  @include flex(center);
  z-index: 999;

  // 容器
  .block_box {
    width: 600upx;
    border-radius: 16upx;
    background-color: #fff;
    padding: 0 24upx;
    box-sizing: border-box;

    // 头像
    .avatar {
      width: 170upx;
      height: 170upx;
      display: block;
      margin: -85upx auto 0;
    }

    // 内容
    .info {
      .text {
        color: $k-title;
        font-weight: 500;
        font-size: 28upx;
        text-align: center;
      }
    }
  }
}

/* 环信弹框 */
.wrapper1 .hx-block {
  width: 606rpx;
  box-sizing: border-box;
  background: #fff;
  border-radius: 16upx;
  overflow: hidden;
  padding: 94rpx 40rpx 40rpx 40rpx;
}

.hx-block .title {
  padding-bottom: 64rpx;
  color: $k-title;
  font-size: 32upx;
  font-weight: 600;
  white-space: normal;
  text-align: center;
}

.hx-block .btn_footer {
  width: 100%;
  @include flex(center);
  justify-content: space-around;
}

.hx-block .btn_footer view {
  width: 242rpx;
  height: 68rpx;
  border-radius: 46rpx;
  color: #ffffff;
  font-weight: 600;
  @include bg_theme;
  font-size: 28rpx;
  text-align: center;
  line-height: 68rpx;
  box-sizing: border-box;
}

.hx-block .btn_footer .cancel {
  @include border_theme;
  @include font_theme;
  background: transparent;
  margin-right: 36rpx;
}
</style>
