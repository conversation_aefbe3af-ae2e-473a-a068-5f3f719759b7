<template>
  <div class="row" @click="click">
    <span>{{ label }}</span>

    <p class="right">
      <span>{{ value }}</span>
      <uni-icons
        type="arrowright"
        color="#666"
        size="16"
        v-if="showIcon"
      ></uni-icons>
    </p>
  </div>
</template>

<script>
export default {
  name: 'Row',
  props: {
    // 左侧
    label: String,
    // 右侧
    value: String,
    // 箭头
    showIcon: Boolean,
  },
  methods: {
    click() {
      if (this.showIcon) this.$emit('click');
    },
  },
};
</script>

<style lang="scss" scoped>
.row {
  @include flex(lr);
  height: 88rpx;
  padding: 0 32rpx;
  font-size: 28rpx;
  margin-top: 24rpx;
  background-color: #fff;

  .right {
    @include flex;

    span {
      padding-right: 10rpx;
    }
  }
}
</style>
