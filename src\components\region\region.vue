<template>
	<view class="region">
		<picker mode="selector" class="picker" :range="range" range-key="name" @change="change" @columnchange="columnchange" :value="value" :disabled="disabled">
			<view class="content" :class="{ placeholder: !defaultValue }">
				<text class="ellipsis-1">{{ defaultValue ? defaultValue : placeholder }}</text>
			</view>
		</picker>
	</view>
</template>

<script>
const regionData = require('./region.json');
export default {
	/**
	 * 数据
	 */
	props: {
		// 是否禁用
		disabled: {
			type: Boolean,
			default: false
		},

		// placeholder
		placeholder: {
			type: String,
			default: '请选择地区'
		},

		// 默认值
		defaultValue: {
			type: String,
			default: ''
		}
	},

	/**
	 * 数据
	 */
	data() {
		return {
			range: [], // 省/市/区
			value: 0, // value 的值表示选择了 range 中的第几个（下标从 0 开始）,默认为 [0, 0, 0]，即 range 为 ['第一个省', '第一个省的第一个市', '第一个省的第一个市的第一个区']
			regionStr: '' // 选中的省市区字符串
		};
	},

	/**
	 * 监听数据
	 */

	// watch: {
	// 	// 默认值
	// 	defaultValue() {
	// 		// 设置/更新省市区数据
	// 		this.defaultValue ? this.setRange() : '';
	// 	}
	// },

	/**
	 * 组件初次加载完成
	 */
	mounted() {
		// 设置/更新省市区数据
		this.setRange();
	},

	/**
	 * 函数
	 */
	methods: {
		/**
		 * 设置/更新省市区数据
		 */
		setRange() {
			// 省市区数据
			let range = [];

			// 省
			regionData.forEach(el => {
				range.push({
					id: el.id,
					level: el.level,
					name: el.name,
					pid: el.pid
				});
			});

			// 更新省市区数据
			this.range = range;
		},

		
		/**
		 * 确认选择
		 */
		change(event) {
			this.value = event.detail.value;
			// 更新选中的省市区字符串
			this.regionStr = this.range[this.value].name;

			// 传递事件
			this.$emit('change', this.regionStr);
		}
	}
};
</script>

<style lang="scss" scoped>
.region {
	flex: 1 !important;
}
.ellipsis-1 {
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 1;
	overflow: hidden;
	font-size: 28upx;
}

.picker {
	width: 100%;
}

.content {
	color: #333;
	text-align: left;
}

.placeholder {
	color: #949596;
	font-size: 26upx;
}
</style>
