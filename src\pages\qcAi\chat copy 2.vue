<template>
  <view class="ai-chat-container">
    <!-- 操作按钮区域 - 单行显示 -->
    <view
      v-if="type != 'ywz' && !isHideAction"
      class="action-buttons-container"
    >
      <view class="action-btn" @click="!isSendDisabled && goToHistory()">
        <image src="/static/ai/ai-1.png" class="btn-icon"></image>
        <text>历史</text>
      </view>
      <view
        class="action-btn"
        @click="!isSendDisabled && startNewConversation()"
      >
        <image src="/static/ai/ai-2.png" class="btn-icon"></image>
        <text>创建新对话</text>
      </view>
      <view class="action-btn" @click="!isSendDisabled && goToDailyCheckIn()">
        <image src="/static/ai/ai-3.png" class="btn-icon"></image>
        <text>营养报告</text>
      </view>
    </view>

    <!-- 聊天内容区域 -->
    <scroll-view
      scroll-y
      class="chat-content"
      :scroll-top="scrollTop"
      :scroll-with-animation="true"
      @scrolltoupper="loadMoreMessages"
      ref="chatContent"
    >
      <view class="chat-list">
        <view
          v-for="(item, index) in chatMessages"
          :key="index"
          class="chat-item"
          :class="{ 'chat-item-ai': item.isAi, 'chat-item-user': !item.isAi }"
        >
          <view class="avatar">
            <image
              :src="
                item.isAi
                  ? '/static/ai-avatar/ai-营养师.png'
                  : '/static/ai/patient.png'
              "
            ></image>
          </view>
          <view class="message">
            <!-- Determine if message is markdown and render accordingly -->
            <view
              class="msg-content"
              :class="{ typing: item.isAi && !item.isComplete }"
              v-if="item.isAi && isMarkdown(item.content)"
              v-html="compiledMarkdown(item.content)"
            ></view>
            <view class="msg-content" v-else>{{ item.content }}</view>
            <!--            <view class="msg-time">{{ item.time }}</view>-->
          </view>
        </view>
        <hz-loading
          v-if="!isHideAction"
          :show="isAiLoading"
          type="dots"
          :fullscreen="false"
          :size="40"
          color="#874ff0"
        />
      </view>
    </scroll-view>

    <!-- 底部输入区域 -->
    <view class="chat-footer" v-if="!isFinish">
      <view
        class="patient-selector"
        v-if="
          chatMessages.filter((item) => !item.isAi).length > 0 &&
          chatMessages.filter((item) => !item.isAi).length <= 2 &&
          !selectedPatient
        "
      >
        <view class="selector-title">选择就诊人</view>
        <view class="patient-list">
          <view
            v-for="(patient, index) in patientList"
            :key="index"
            class="patient-item"
            @click="selectPatient(patient)"
          >
            <text>{{ patient.patientNameStr }}</text>
          </view>
          <view class="patient-item add-new" @click="goToAddPatient">
            <text>添加新就诊人</text>
          </view>
        </view>
      </view>
      <view v-else>
        <!-- 输入区域 - 选择就诊人后显示 -->
        <view class="input-area" v-if="isInputArea">
          <!-- 文本输入模式 -->
          <template v-if="inputMode === 'text'">
            <input
              type="text"
              v-model="inputMessage"
              :disabled="isSendDisabled"
              placeholder="请输入您的问题"
              confirm-type="send"
              @confirm="sendMessage"
            />
            <view class="voice-btn" @click="switchInputMode">
              <u-icon name="mic" color="#874ff0" size="28"></u-icon>
            </view>
            <view class="send-btn" @click="sendMessage">
              <u-icon name="arrow-right" color="#fff" size="22"></u-icon>
            </view>
          </template>
          
          <!-- 语音输入模式 -->
          <template v-else>
            <view class="voice-input-container">
              <view class="keyboard-btn" @click="switchInputMode">
                <u-icon name="keyboard" color="#874ff0" size="28"></u-icon>
              </view>
              <view 
                class="voice-record-btn" 
                @touchstart.prevent="startVoiceRecord" 
                @touchend.prevent="stopVoiceRecord"
                @touchcancel.prevent="cancelVoiceRecord"
              >
                <text>{{ isRecording ? '松开结束' : '按住说话' }}</text>
              </view>
            </view>
            
            <!-- 语音识别结果显示 -->
            <view class="voice-result" v-if="voiceText">
              <text class="voice-text">{{voiceText}}</text>
              <view class="voice-actions">
                <view class="voice-action-btn send" @click="sendVoiceMessage">
                  <u-icon name="checkmark" color="#fff" size="16"></u-icon>
                </view>
                <view class="voice-action-btn cancel" @click="cancelVoiceMessage">
                  <u-icon name="close" color="#fff" size="16"></u-icon>
                </view>
              </view>
            </view>
          </template>
        </view>
      </view>
    </view>

    <!-- 语音录制遮罩层 -->
    <view class="voice-recording-mask" v-if="isRecording">
      <view class="voice-recording-indicator">
        <image src="/static/ai/voice-record.gif" class="recording-icon"></image>
        <text>松开发送，上滑取消</text>
      </view>
    </view>
  </view>
</template>

<script>
import { fetchEventSource } from '@microsoft/fetch-event-source'
import { getPatientList } from '@/api/user.js'
import {
  createSession,
  getAiCompletions,
  getChatList,
  saveUserMessage,
  invokeWorkflow,
  stopWorkflow,
} from '@/api/qcAi.js'
import urlConfig from '@/common/request/config.js'
import { syncHealthRecord } from '../../api/qcAi'
import { compiledMarkdown } from '@/utils/compiled-markDown.js'
import WebAudioSpeechRecognizer from '@/audio/webaudiospeechrecognizer.js'
import config from '@/audio/config.js'

export default {
  data() {
    return {
      compiledMarkdown,
      chatMessages: [],
      inputMessage: '',
      scrollTop: 0,
      conversationId: null,
      selectedPatient: null,
      patientList: [],
      isSendDisabled: false,
      quickActions: [],
      type: '',
      isFinish: false,
      sessionId: null, // 会话ID
      userId: '', // 用户ID
      patientId: '', // 就诊人ID
      patientName: '', // 就诊人姓名
      isLoading: false,
      workflowSessionId: null,
      isWorkflowRunning: false,
      input_schema: null,
      message_id: null,
      diagnosis: '',
      isInputArea: true,
      isHideAction: false,
      isMarkdownEnabled: true, // Enable markdown rendering by default
      reportCode: null,
      isAiLoading: true, // AI回答中
      isEnd: false, // 判断第一次开启工作流是否结束
      isStreamStarted: false,
      currentStreamMessage: null,
      pendingText: '', // 存储待显示的文本
      isTyping: false, // 是否正在执行打字机效果
      typewriterSpeed: 25, // 打字机速度(ms)
      saveDebounceTimer: null,
      isAIAnswer: true, // 是不是AI智能对话回答
      // 语音识别相关变量
      inputMode: 'text', // 'text' 或 'voice'
      isRecording: false, // 是否正在录音
      voiceText: '', // 语音识别的文本
      speechRecognizer: null, // 语音识别器
      replyText: '', // 识别结果
      audioText: '', // 最终识别结果
      recognizOpen: false, // 是否开启识别
      lastRecognitionTime: null, // 上次识别结果的时间
      clearRecognitionTimeout: null, // 清除识别结果的定时器
    }
  },
  onLoad(options) {
    console.log(fetchEventSource, '===========fetchEventSource')

    this.type = options.type
    this.userId = uni.getStorageSync('userId')
    this.getPatientList()
    this.isHideAction = false
    // Check if we have a session ID parameter
    if (options.sessionId) {
      this.isFinish = true
      this.isHideAction = true
      this.sessionId = options.sessionId
      this.loadSessionMessages(options.sessionId)
    } else {
      this.startNewConversation()
    }
  },
  onShow() {
    this.getPatientList()
  },
  methods: {
    async getQuickActions(userMessage) {
      // 构建历史消息数组，用于AI模型上下文
      let messageHistory = []
      // 只取最近的5条消息作为上下文
      const recentMessages = this.chatMessages
      recentMessages.forEach((msg) => {
        messageHistory.push({
          role: msg.isAi ? 'assistant' : 'user',
          content: msg.content,
        })
      })
      let { data } = await getAiCompletions({
        messages: messageHistory,
        content: userMessage,
        model: '13d3455d-6250-41bb-9599-e17310a85fab',
      })
      console.log(data, '===========AI回答提示词接口返回结果')
      this.quickActions = (data.choices[0].message.content || []).map(
        (item) => {
          return {
            name: item,
          }
        }
      )
    },
    // 获取就诊人列表
    async getPatientList() {
      let userId = uni.getStorageSync('userId')
      let { data } = await getPatientList({
        userId,
      })
      if (!data) {
        this.patientList = []
        return
      }
      let disease = []
      data.forEach((element) => {
        disease.push(element.patientId)
      })
      uni.setStorageSync('patientIdList', disease)
      this.patientList = (data || []).map((v) => {
        return {
          ...v,
          patientNameStr: `${v.patientName}(${v.sex}-${v.age})`,
        }
      })
    },
    goBack() {
      uni.navigateBack()
    },
    goToHistory() {
      uni.navigateTo({
        url: '/pages/qcAi/history',
      })
    },
    formatTime() {
      const now = new Date()
      const hours = now.getHours().toString().padStart(2, '0')
      const minutes = now.getMinutes().toString().padStart(2, '0')
      return `${hours}:${minutes}`
    },
    async sendMessage() {
      if (!this.inputMessage.trim()) return
      if (this.isSendDisabled) return
      this.isSendDisabled = true

      // 添加用户消息
      this.chatMessages.push({
        isAi: false,
        content: this.inputMessage,
        time: this.formatTime(),
      })

      const userMessage = this.inputMessage
      this.inputMessage = ''
      // AI回答中 loading
      this.isAiLoading = true
      this.scrollToBottom()

      if (this.chatMessages.filter((item) => !item.isAi).length == 1) {
        this.isSendDisabled = false
        // 在用户发送后，小助手推送提示语【为了建议准确性和档案完整性，请您选择您本次的咨询人是？】并显示 选择就诊人浮层
        // 保存消息到服务器
        await saveUserMessage({
          sessionId: this.sessionId,
          content: userMessage,
          userId: this.userId,
          patientId: this.patientId,
          isAi: false,
          role: 'user',
        })
        // 保存消息到服务器
        await saveUserMessage({
          sessionId: this.sessionId,
          content: '为了建议准确性和档案完整性，请您选择您本次的咨询人是？',
          userId: this.userId,
          patientId: this.patientId,
          isAi: true,
          role: 'assistant',
        })
        this.receiveAiMessage(
          '为了建议准确性和档案完整性，请您选择您本次的咨询人是？'
        )
        return
      }

      try {
        // 保存用户消息到服务器
        await saveUserMessage({
          sessionId: this.sessionId,
          content: userMessage,
          userId: this.userId,
          patientId: this.patientId,
          isAi: false, // 用户消息
          role: 'user',
        })

        // 构建历史消息数组，用于AI模型上下文
        let messageHistory = []
        // 只取最近的5条消息作为上下文
        const recentMessages = this.chatMessages.slice(-10)
        recentMessages.forEach((msg) => {
          messageHistory.push({
            role: msg.isAi ? 'assistant' : 'user',
            content: msg.content,
          })
        })
        // AI智能对话回答流式输出
        this.createByAiWorkFlow(userMessage)
        return
        // 调用AI回答接口
        const response = await getAiCompletions({
          messages: messageHistory,
          model: 'c1bb8c359b8044ad8160a52fbf712494',
          content: userMessage, // 最新消息，如果未提供历史消息时使用
        })
        console.log(response, '===========AI回答接口返回结果')

        let aiReply = ''
        if (
          response.data &&
          response.data.choices &&
          response.data.choices.length > 0
        ) {
          // OpenAI风格的返回格式
          aiReply = response.data.choices[0].message.content
        } else if (response.data && response.data.aiReply) {
          // 自定义返回格式
          aiReply = response.data.aiReply
        } else {
          aiReply = '很抱歉，我暂时无法回答您的问题，请稍后再试。'
        }
        console.log(aiReply, 'aiReply')

        // 保存AI回复到服务器
        await saveUserMessage({
          sessionId: this.sessionId,
          content: aiReply,
          userId: this.userId,
          patientId: this.patientId,
          isAi: true, // AI消息
          role: 'assistant',
        })
        console.log(this.isSendDisabled, 'this.isSendDisabled')
        // 添加AI回复到界面 添加100ms的延迟有个慢慢出来的效果
        await this.receiveAiMessage(aiReply)
        this.isSendDisabled = false
        console.log(this.isSendDisabled, 'this.isSendDisabled')
        this.getQuickActions(userMessage)
      } catch (error) {
        console.error('AI回答异常:', error)
        this.isSendDisabled = false
        this.isAiLoading = false
        this.receiveAiMessage('网络异常，请稍后再试。')
      } finally {
        this.isSendDisabled = false
      }
    },
    async receiveAiMessage(content) {
      // 非流式输出的情况下使用打字机效果
      this.isAiLoading = false

      // 重置当前流式消息状态
      this.currentStreamMessage = null
      this.pendingText = content
      this.isTyping = false

      // 创建一个新的AI消息
      this.currentStreamMessage = {
        isAi: true,
        content: '',
        time: this.formatTime(),
        isComplete: false,
      }

      // 添加到消息列表
      this.chatMessages.push(this.currentStreamMessage)

      // 启动打字机效果
      this.startTypewriter()

      // 等待打字机效果完成
      await new Promise((resolve) => {
        const checkComplete = () => {
          console.log(
            this.pendingText.length,
            this.isTyping,
            'this.pendingText.length, this.isTyping'
          )
          if (this.pendingText.length === 0 && !this.isTyping) {
            resolve()
          } else {
            console.log(this.isSendDisabled, 'this.isSendDisabledxx')
            setTimeout(checkComplete, 100)
          }
        }
        checkComplete()
      })
      this.isSendDisabled = false
      // 标记消息完成并保存到数据库
      if (this.currentStreamMessage) {
        this.currentStreamMessage.isComplete = true
        this.currentStreamMessage = null
      }
    },

    // 重新实现流式消息处理方法，确保打字机效果正常工作
    receiveStreamMessage(content) {
      console.log(content, 'content',this.currentStreamMessage)
      // 检查是否是第一次收到流式消息
      if (!this.currentStreamMessage) {
        console.log('this.currentStreamMessage',this.currentStreamMessage);
        
        // 创建新消息
        this.currentStreamMessage = {
          isAi: true,
          content: '',
          time: this.formatTime(),
          isComplete: false,
        }

        // 添加到消息列表
        this.chatMessages.push(this.currentStreamMessage)
        this.isAiLoading = false

        // 启动打字机效果处理器
        this.startTypewriter()
      }

      // 将收到的内容添加到待显示队列
      if (content) {
        // if(!this.isTyping){
        //   this.isTyping = true
        //   this.processTypewriter()
        // }
        this.pendingText += content
      }
    },

    // 启动打字机效果处理器
    startTypewriter() {
      if (this.isTyping) return // 如果已经在执行，不重复启动

      this.isTyping = true
      this.processTypewriter()
    },

    // 打字机效果处理器 - 持续处理队列中的内容
    async processTypewriter() {
      // 只要还在打字状态，就持续处理
      while (this.isTyping) {
        // 如果有待显示的内容，且有当前消息
        if (this.pendingText.length > 0 && this.currentStreamMessage) {
          // 取出第一个字符
          const char = this.pendingText.charAt(0)
          this.pendingText = this.pendingText.substring(1)

          // 添加到当前消息
          this.currentStreamMessage.content += char

          // 根据标点符号决定延迟时间
          let delay = this.typewriterSpeed
          if (/[。.!?！？]/.test(char)) {
            delay = 150 // 句末停顿
          } else if (/[，,、;；]/.test(char)) {
            delay = 60 // 中间停顿
          } else if (/[\n]/.test(char)) {
            delay = 100 // 换行停顿
          }

          // 每添加10个字符或遇到换行符时滚动到底部
          if (
            this.currentStreamMessage.content.length % 10 === 0 ||
            /[\n]/.test(char)
          ) {
            this.scrollToBottom()
          }

          // 等待一段时间，产生打字效果
          await new Promise((resolve) => setTimeout(resolve, delay))
        } else {
          // 如果没有待显示内容，等待一段时间再检查
          await new Promise((resolve) => setTimeout(resolve, 50))

          // 如果消息已标记为完成，且没有待显示内容，结束打字
          if (this.pendingText.length === 0) {
            console.log('this.pendingText.length === 0',this.pendingText.length,this.isTyping)
            this.isTyping = false
            break
          }
        }
      }
      // 确保最后滚动到底部
      this.scrollToBottom()
    },

    // 标记流式消息完成
    completeStreamMessage() {
      if (this.currentStreamMessage) {
        // 将所有待显示内容立即添加到消息中
        if (this.pendingText.length > 0) {
          // 不立即添加，让打字机效果处理完所有内容
          // 只是标记消息将要完成
          this.currentStreamMessage.willComplete = true

          // 设置一个检查器，确保所有内容最终都会显示
          this.ensureCompletion()
        } else {
          this.currentStreamMessage.isComplete = true

          // 消息完成时保存到数据库
          // this.saveMessageToDatabase(this.currentStreamMessage.content)

          this.currentStreamMessage = null
        }
      }
      this.isAiLoading = false
    },

    // 确保所有内容最终都会显示
    async ensureCompletion() {
      // 等待足够长的时间，让打字机效果处理完待显示内容
      const maxWaitTime = 5000 // 最长等待5秒
      const checkInterval = 100 // 每100ms检查一次
      let waitedTime = 0

      while (this.pendingText.length > 0 && waitedTime < maxWaitTime) {
        await new Promise((resolve) => setTimeout(resolve, checkInterval))
        waitedTime += checkInterval
      }

      // 如果还有待显示内容，立即全部显示出来
      if (this.currentStreamMessage && this.pendingText.length > 0) {
        this.currentStreamMessage.content += this.pendingText
        this.pendingText = ''
      }

      // 完成消息时保存到数据库
      if (this.currentStreamMessage) {
        // 保存完整内容到数据库
        // this.saveMessageToDatabase(this.currentStreamMessage.content)
        if (this.isAIAnswer) {
          // 保存AI回复到服务器
          await saveUserMessage({
            sessionId: this.sessionId,
            content: this.currentStreamMessage.content,
            userId: this.userId,
            patientId: this.patientId,
            isAi: true, // AI消息
            role: 'assistant',
          })
        }
        this.currentStreamMessage.isComplete = true
        this.currentStreamMessage = null
      }

      this.isTyping = false
      this.scrollToBottom()
    },

    // 保存消息到数据库
    async saveMessageToDatabase(content) {
      // 避免保存空消息
      if (!content || content.trim() === '') return

      try {
        // 使用防抖来避免频繁保存
        if (this.saveDebounceTimer) {
          clearTimeout(this.saveDebounceTimer)
        }

        this.saveDebounceTimer = setTimeout(async () => {
          // 调用API保存消息
          await saveUserMessage({
            sessionId: this.sessionId,
            content: content,
            userId: this.userId,
            patientId: this.patientId,
            isAi: true, // AI消息
            role: 'assistant',
          })
          console.log('消息已保存到数据库')
        }, 300) // 300ms的防抖延迟
      } catch (error) {
        console.error('保存消息到数据库失败:', error)
      }
    },
    scrollToBottom() {
      this.$nextTick(() => {
        // 获取消息列表高度并滚动到底部
        const query = uni.createSelectorQuery().in(this)
        query
          .select('.chat-list')
          .boundingClientRect((data) => {
            if (data) {
              this.scrollTop = data.height
            }
          })
          .exec()
      })
    },
    async startNewConversation() {
      // 清空当前聊天
      this.chatMessages = []
      this.isSendDisabled = false
      this.selectedPatient = null
      this.isInputArea = true
      this.selectedPatient = null
      this.patientId = null
      this.isFinish = false
      try {
        // 创建新会话
        const { data } = await createSession({
          userId: this.userId,
          sessionType: 3,
        })

        if (data) {
          this.sessionId = data
          // 发送欢迎消息
          this.receiveAiMessage('很高兴为您服务，您可以随时向我提问')
        } else {
          uni.showToast({
            title: '创建会话失败',
            icon: 'none',
          })
        }
      } catch (error) {
        console.error('创建会话异常:', error)
        uni.showToast({
          title: '创建会话失败，请稍后再试',
          icon: 'none',
        })
      }
    },
    async loadMoreMessages() {
      // 实际应用中，这里应该加载更多历史消息
      console.log('加载更多消息')
    },
    async loadSessionMessages(sessionId) {
      try {
        this.isLoading = true
        const { data } = await getChatList({
          sessionId: sessionId,
        })

        if (data && data.length > 0) {
          // 清空现有消息
          this.chatMessages = []

          // 将后端返回的消息格式转换为前端显示格式
          data.forEach((msg) => {
            this.chatMessages.push({
              isAi: msg.senderObjectType === '1', // 1: AI消息，2: 用户消息
              content: msg.textContent,
              time: this.formatTimeFromTimestamp(msg.createTime),
            })
          })

          this.scrollToBottom()
        }
      } catch (error) {
        console.error('获取会话消息异常:', error)
        uni.showToast({
          title: '获取历史消息失败',
          icon: 'none',
        })
      } finally {
        this.isLoading = false
      }
    },
    formatTimeFromTimestamp(timestamp) {
      // 将时间戳转换为时间字符串
      if (!timestamp) return this.formatTime()

      const date = new Date(timestamp)
      const hours = date.getHours().toString().padStart(2, '0')
      const minutes = date.getMinutes().toString().padStart(2, '0')
      return `${hours}:${minutes}`
    },
    goToCloudClinic() {
      uni.navigateTo({
        url: '/pages/register/docList/index',
      })
    },
    async goToSmartClinic(item) {
      if (item.name === '去问诊') {
        await this.getDiagnosis()

        return
      }
      if (item.name === '补充健康档案') {
        try {
          await syncHealthRecord({
            aiReportCode: this.reportCode,
          })
        } catch (error) {
          this.scrollToBottom()
        }
        await this.receiveAiMessage(
          '好的，我已将您当前的病情记录补充到您的健康档案中。请注意，这些建议不能替代专业医生的诊断和治疗。如果症状持续或加重，请及时就医'
        )
        // 保存ai消息到服务器
        await saveUserMessage({
          sessionId: this.sessionId,
          content:
            '好的，我已将您当前的病情记录补充到您的健康档案中。请注意，这些建议不能替代专业医生的诊断和治疗。如果症状持续或加重，请及时就医',
          userId: this.userId,
          patientId: this.patientId,
          isAi: true, // AI消息
          role: 'assistant',
        })
        this.finishConversation()
        return
      }
      if (item.name === '没有其他问题了') {
        this.finishConversation()
        return
      }
      this.inputMessage = this.inputMessage + ' ' + item.name
    },
    // 诊断
    async getDiagnosis() {
      uni.showLoading({
        title: '生成诊断中...',
        mask: true,
      })
      try {
        // 将聊天记录转换为字符串格式
        const chatHistory = this.chatMessages
          .map((msg) => {
            return `${msg.isAi ? '医生' : '患者'}: ${msg.content}`
          })
          .join('\n')

        // 第一步：发起工作流执行
        const { data } = await invokeWorkflow({})
        console.log(data, '===========workflowData')
        const workflowData = data.data
        if (workflowData && workflowData.session_id) {
          this.workflowSessionId = workflowData.session_id
          this.isWorkflowRunning = true
          // 处理工作流返回的事件
          if (workflowData.events && workflowData.events.length > 0) {
            for (const event of workflowData.events) {
              if (event.event === 'guide_word' && event.output_schema) {
                // 显示工作流的引导语
                // this.receiveAiMessage(event.output_schema.message);
              }
              if (event.event === 'input') {
                this.message_id = event.message_id
                this.input_schema = event.input_schema
                this.lastNodeId = event.node_id
              }
            }
          }
          console.log(this.input_schema, '===========input_schema')
          uni.showLoading({
            title: '生成诊断中...',
            mask: true,
          })
          // 第四步：再次调用工作流，传入完整的聊天记录
          const { data: summaryData } = await invokeWorkflow({
            sessionId: this.workflowSessionId,
            input: {
              [this.lastNodeId]: {
                [this.input_schema.value[0].key]: chatHistory,
              },
            },
            message_id: this.message_id,
          })
          // 处理返回的总结内容
          if (summaryData.data && summaryData.data.events) {
            for (const event of summaryData.data.events) {
              if (event.output_schema && event.output_schema.message) {
                // this.receiveAiMessage(event.output_schema.message);
              }
              if (event.event === 'stream_msg' && event.status === 'end') {
                console.log(
                  event.output_schema.message,
                  '===========event.output_schema.message'
                )
                // this.receiveAiMessage(event.output_schema.message);
                // ```json\n{\n  \"R51.x00\": \"头痛\"\n}\n```
                const json = JSON.parse(
                  event.output_schema.message
                    .replace(/```json\n/g, '')
                    .replace(/\n```/g, '')
                )
                this.diagnosis = Object.values(json)[0]
              }
            }
          }
          // 第五步：停止工作流
          await stopWorkflow({
            sessionId: this.workflowSessionId,
          })
          uni.hideLoading()
          uni.navigateTo({
            url:
              '/pages/register/docList2/index?flag=2&diagnosis=' +
              this.diagnosis,
          })
        }
      } catch (error) {
        console.error('诊断异常:', error)
        uni.hideLoading()
      } finally {
      }
    },
    // 结束对话
    finishConversation() {
      this.isFinish = true
    },
    async selectPatient(patient) {
      this.selectedPatient = patient
      this.patientId = patient.patientId
      // 可以将选中的患者信息添加到会话中
      // 保存消息到服务器
      await saveUserMessage({
        sessionId: this.sessionId,
        content: `已为您选择就诊人: 姓名：${patient.patientName} ，性别：${patient.sex} ， 年龄：${patient.age}`,
        userId: this.userId,
        patientId: this.patientId,
        isAi: true,
        role: 'assistant',
      })
      this.receiveAiMessage(
        `已为您选择就诊人: 姓名：${patient.patientName} ，性别：${patient.sex} ， 年龄：${patient.age}`
      )
    },
    goToAddPatient() {
      // 跳转到添加患者页面
      uni.navigateTo({
        url: `/pages/personalCenter/patientManage/addPatient/index`,
      })
    },
    // Check if text is likely markdown
    isMarkdown(text) {
      if (!text) return false

      // Common markdown patterns
      const markdownPatterns = [
        /^#{1,6}\s+.+$/m, // Headers
        /\*\*(.+?)\*\*/, // Bold
        /\*(.+?)\*/, // Italic
        /\[.+?\]\(.+?\)/, // Links
        /^>\s+.+$/m, // Blockquotes
        /^-\s+.+$/m, // List items
        /^[0-9]+\.\s+.+$/m, // Numbered lists
        /^```[\s\S]+?```$/m, // Code blocks
        /^###\s+.+$/m, // Specifically check for ### headers
      ]

      // If any markdown pattern is found, consider it markdown
      return markdownPatterns.some((pattern) => pattern.test(text))
    },

    // Render markdown to HTML - 增强版，更好地支持流式输出
    renderMarkdown(text) {
      try {
        if (!text) return ''
        if (!this.isMarkdown(text)) return text

        // 流式输出可能导致Markdown语法未完成，需要处理这种情况
        // 例如：一个 "**" 后面没有匹配的 "**"，或者链接 "[text](" 没有闭合

        // 处理未闭合的加粗语法
        let processedText = text.replace(/\*\*([^*]*?)$/g, '**$1**')

        // 处理未闭合的斜体语法
        processedText = processedText.replace(/\*([^*]*?)$/g, '*$1*')

        // 处理未闭合的链接
        processedText = processedText.replace(
          /\[([^\]]*?)\]\(([^)]*?)$/g,
          '[$1]($2)'
        )

        // 处理未闭合的代码块
        if ((processedText.match(/```/g) || []).length % 2 !== 0) {
          processedText += '\n```'
        }

        // 进行Markdown到HTML的转换
        return (
          processedText
            .replace(/^### (.*$)/gm, '<h3>$1</h3>')
            .replace(/^## (.*$)/gm, '<h2>$1</h2>')
            .replace(/^# (.*$)/gm, '<h1>$1</h1>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>')
            .replace(/^> (.*$)/gm, '<blockquote>$1</blockquote>')
            .replace(/^- (.*$)/gm, '<ul><li>$1</li></ul>')
            .replace(/^([0-9]+)\. (.*$)/gm, '<ol><li>$2</li></ol>')
            .replace(/```([\s\S]+?)```/g, '<pre><code>$1</code></pre>')
            .replace(/`([^`]+)`/g, '<code>$1</code>')
            // 合并连续的列表
            .replace(/<\/ul>\s*<ul>/g, '')
            .replace(/<\/ol>\s*<ol>/g, '')
        )
      } catch (error) {
        console.error('Markdown渲染错误:', error)
        return text
      }
    },
    // 跳转营养报告量表填写
    async goToDailyCheckIn() {
      uni.navigateTo({
        url: `/pages/scanCode/nutritionForm?flag=ai&projectId=18`,
      })
    },
    onShow() {
      uni.$once('startWorkFlow', (v) => {
        console.log(v, '===========v')
        if (v.isAi) {
          this.createSJReportByAiWorkFlow(v.params, v.patient)
        }
      })
    },
    // AI大模型营养报告
    async createSJReportByAiWorkFlow(params, patient) {
      try {
        // 前端调用方案  暂时不使用这个方案
        // 第一步：发起工作流执行
        this.streamWorkFlow(
          {
            workflow_id: 'e7a6505cdef14802b10c5597341f7588',
            stream: true,
          },
          'first'
        )
        const values = params
        if (patient) {
          this.patientName = patient.patientName
          this.patientId = patient.patientId
          this.userId = patient.userId
        }

        const startNextWorkFlow = () => {
          console.log(this.isEnd, '===========isEnd')
          if (this.isEnd) {
            clearTimeout(timer)
            // 第二步：再次调用工作流，传入完整的聊天记录
            this.streamWorkFlow(
              {
                stream: true,
                session_id: this.workflowSessionId,
                input: {
                  [this.lastNodeId]: {
                    name: patient.patientName,
                    sex: patient.sex,
                    age: patient.age,
                    height: values[0].value,
                    weight: values[1].value,
                    month_ago_weight: values[2].value,
                    dietary_situation_1month: values[3].value,
                    Current_eating_status: values[4].value,
                    Reasons_affecting_eating: values[5].value,
                  },
                },
                message_id: this.message_id,
              },
              'second'
            )
            this.isAiLoading = true
            return
          }
          startNextWorkFlow()
        }

        const timer = setTimeout(() => {
          startNextWorkFlow()
        }, 5000)

        // 第五步：停止工作流
        // await stopWorkflow({
        //   sessionId: this.workflowSessionId,
        // })
      } catch (error) {
        console.error('营养评估表报告生成异常:', error)
        this.isAiLoading = false
        uni.hideLoading()
      } finally {
      }
    },
    // 工作流流式输出 封装 前端调用
    streamWorkFlow(params, type) {
      console.log(params, '===========params')
      const _this = this
      // 中止之前可能存在的请求
      if (this.abortController) {
        this.abortController.abort()
      }

      // 重置所有流式输出相关状态
      this.isStreamStarted = false
      this.currentStreamMessage = null
      this.pendingText = ''
      this.isTyping = false

      // 显示加载指示器，等待第一个数据块到达
      this.isAiLoading = true

      // 记录当前工作流ID，用于后续验证消息所属
      const currentWorkflowId = 'e7a6505cdef14802b10c5597341f7588'
      // 当前工作流ID
      this.activeWorkflowId = currentWorkflowId
      // 请求参数
      const queryData = {
        workflow_id: currentWorkflowId,
        ...params,
        szAiReportVo: {
          reportType: '5',
          userId: _this.userId,
          patientId: _this.patientId,
          patientName: _this.patientName,
          projectId: '18',
          sessionId: _this.sessionId,
        },
      }

      this.abortController = new AbortController()
      const url =
        type === 'first'
          ? '/api/v2/workflow/invoke'
          : urlConfig.serverUrl + 'basic/ai/report/dailyLbStreamGenerate'
      fetchEventSource(url, {
        method: 'POST',
        openWhenHidden: true,
        headers: {
          'Content-Type': 'application/json',
          firmId: urlConfig.firmId,
          Authorization: uni.getStorageSync('proPfInfo').token || '',
          hosId: uni.getStorageSync('hosId') || '',
          version: urlConfig.version,
          clientType: urlConfig.clientType,
        },
        body: JSON.stringify(queryData),
        signal: this.abortController.signal,

        async onopen(response) {
          // 验证当前工作流是否仍然活跃
          // if (_this.activeWorkflowId === currentWorkflowId) {
          //   _this.isFinish = true; //会导致禁用输入框失败
          // }
          console.log(response, '===========response')
        },
        onmessage(res) {
          // 验证消息是否属于当前活动的工作流
          if (_this.activeWorkflowId !== currentWorkflowId) {
            console.log('收到非活动工作流的消息，已忽略')
            return // 忽略非当前活动工作流的消息
          }

          try {
            const resData = JSON.parse(res.data)
            console.log(resData, '===========resData.data')
            // 工作流流式输出
            if (
              resData.data.event === 'stream_msg' &&
              resData.data.status === 'stream'
            ) {
              // 收到消息内容，使用流式消息处理方法
              if (
                resData.data.output_schema &&
                resData.data.output_schema.message
              ) {
                _this.receiveStreamMessage(resData.data.output_schema.message)
              }
            }
            // 工作流结束
            else if (
              resData.data.event === 'stream_msg' &&
              resData.data.status === 'end'
            ) {
              // 保存最终消息到服务器，额外增加一层保护
              // 这里不需要额外调用保存方法，因为completeStreamMessage中已经处理了保存

              // 标记流式消息完成
              _this.completeStreamMessage()

              // 记录输出完成
              console.log('流式输出完成，消息已保存到数据库')
            }
            // 工作流输入
            else if (resData.data.event === 'input') {
              if (type === 'first' && resData.data.status === 'end') {
                _this.message_id = resData.data.message_id
                _this.lastNodeId = resData.data.node_id
                _this.workflowSessionId = resData.session_id
                _this.isEnd = true // 判断第一次开启工作流是否结束
              }
            }
            // 工作流关闭
            else if (resData.data.event === 'close') {
              // 确保关闭前已保存所有内容
              _this.completeStreamMessage()
              _this.isAiLoading = false
            }
          } catch (error) {
            console.error('处理消息时出错:', error)
            // 发生错误时，尝试保存已有内容
            if (
              _this.currentStreamMessage &&
              _this.currentStreamMessage.content
            ) {
              // _this.saveMessageToDatabase(_this.currentStreamMessage.content)
            }
          }
        },
        onerror(err) {
          console.error('请求出错', err)
          throw err
        },
      })
    },
    // AI智能回答 工作流调用
    async createByAiWorkFlow(params) {
      try {
        // 前端调用方案  暂时不使用这个方案
        // 第一步：发起工作流执行
        this.AIAnswerWorkFlow(
          {
            workflow_id: '238a57c56da044abad231b12dfc9691a',
            stream: true,
          },
          'first'
        )
        const values = params

        const startNextWorkFlow = () => {
          console.log(this.isEnd, '===========isEnd')
          if (this.isEnd) {
            clearTimeout(timer)
            // 第二步：再次调用工作流，传入完整的聊天记录
            this.AIAnswerWorkFlow(
              {
                stream: true,
                session_id: this.workflowSessionId,
                input: {
                  [this.lastNodeId]: {
                    user_input: values,
                  },
                },
                message_id: this.message_id,
              },
              'second'
            )
            this.isAiLoading = true
            return
          }
          startNextWorkFlow()
        }

        const timer = setTimeout(() => {
          startNextWorkFlow()
        }, 5000)

        // 第五步：停止工作流
        // await stopWorkflow({
        //   sessionId: this.workflowSessionId,
        // })
      } catch (error) {
        console.error('AI智能回答异常:', error)
        this.isAiLoading = false
        uni.hideLoading()
      } finally {
      }
    },
    // AI智能对话回答 工作流
    AIAnswerWorkFlow(params, type) {
      this.isAIAnswer = true // 是不是AI智能对话回答
      const url = '/api/v2/workflow/invoke'
      const _this = this
      // 中止之前可能存在的请求
      if (this.abortController) {
        this.abortController.abort()
      }

      // 重置所有流式输出相关状态
      this.isStreamStarted = false
      this.currentStreamMessage = null
      this.pendingText = ''
      this.isTyping = false

      // 显示加载指示器，等待第一个数据块到达
      this.isAiLoading = true

      // 记录当前工作流ID，用于后续验证消息所属
      const currentWorkflowId = '238a57c56da044abad231b12dfc9691a'
      // 当前工作流ID
      this.activeWorkflowId = currentWorkflowId
      // 请求参数
      const queryData = {
        workflow_id: currentWorkflowId,
        ...params,
      }

      this.abortController = new AbortController()
      fetchEventSource(url, {
        method: 'POST',
        openWhenHidden: true,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(queryData),
        signal: this.abortController.signal,

        async onopen(response) {
          // 验证当前工作流是否仍然活跃
          // if (_this.activeWorkflowId === currentWorkflowId) {
          //   _this.isFinish = true; //会导致禁用输入框失败
          // }
          console.log(response, '===========response')
        },
        onmessage(res) {
          // 验证消息是否属于当前活动的工作流
          if (_this.activeWorkflowId !== currentWorkflowId) {
            console.log('收到非活动工作流的消息，已忽略')
            return // 忽略非当前活动工作流的消息
          }

          try {
            const resData = JSON.parse(res.data)
            console.log(resData, '===========resData.data')
            // 工作流流式输出
            if (
              resData.data.event === 'stream_msg' &&
              resData.data.status === 'stream'
            ) {
              // 收到消息内容，使用流式消息处理方法
              if (
                resData.data.output_schema &&
                resData.data.output_schema.message
              ) {
                // 收到AI智能回答消息
                _this.receiveStreamMessage(resData.data.output_schema.message)
              }
            }
            // 工作流结束
            else if (
              resData.data.event === 'stream_msg' &&
              resData.data.status === 'end'
            ) {
              // 保存最终消息到服务器，额外增加一层保护
              // 这里不需要额外调用保存方法，因为completeStreamMessage中已经处理了保存

              // 标记流式消息完成
              _this.completeStreamMessage()

              // 记录输出完成
              console.log('流式输出完成，消息已保存到数据库')
            }
            // 工作流输入
            else if (resData.data.event === 'input') {
              if (type === 'first' && resData.data.status === 'end') {
                _this.message_id = resData.data.message_id
                _this.lastNodeId = resData.data.node_id
                _this.workflowSessionId = resData.session_id
                _this.isEnd = true // 判断第一次开启工作流是否结束
              }
            }
            // 工作流关闭
            else if (resData.data.event === 'close') {
              // 确保关闭前已保存所有内容
              _this.completeStreamMessage()
              _this.isAiLoading = false
            }
          } catch (error) {
            console.error('处理消息时出错:', error)
            // 发生错误时，尝试保存已有内容
            if (
              _this.currentStreamMessage &&
              _this.currentStreamMessage.content
            ) {
              // _this.saveMessageToDatabase(_this.currentStreamMessage.content)
            }
          }
        },
        onerror(err) {
          console.error('请求出错', err)
          throw err
        },
      })
    },
    // 在组件销毁前保存未完成的消息
    beforeDestroy() {
      // 如果有正在进行的打字机效果，确保内容被保存
      if (this.currentStreamMessage && this.currentStreamMessage.content) {
        // this.saveMessageToDatabase(this.currentStreamMessage.content)
      }

      // 清除所有定时器
      if (this.saveDebounceTimer) {
        clearTimeout(this.saveDebounceTimer)
      }
    },
    // 切换输入模式
    switchInputMode() {
      this.inputMode = this.inputMode === 'text' ? 'voice' : 'text'
      this.voiceText = ''
      if (this.inputMode === 'text') {
        // 切换到文本模式，停止语音识别
        this.stopSpeechRecognition()
      }
    },
    // 开始语音录制
    startVoiceRecord() {
      // 如果禁用发送，不执行录音
      if (this.isSendDisabled) return
      
      this.isRecording = true
      this.voiceText = ''
      
      // 初始化语音识别器
      const params = {
        secretid: config.secretId, // 这里可能需要从配置中获取
        secretkey: config.secretKey,
        appid: config.appId,
        engine_model_type: '16k_zh',
      }
      this.speechRecognizer = new WebAudioSpeechRecognizer(params, false)
      console.log(this.speechRecognizer, '===========this.speechRecognizer')
      // 设置回调函数
      this.speechRecognizer.OnRecognitionStart = (res) => {
        console.log("开始识别", res)
      }
      
      // 一句话开始
      this.speechRecognizer.OnSentenceBegin = (res) => {
        console.log("一句话开始", res)
      }
      
      // 识别变化的时候
      this.speechRecognizer.OnRecognitionResultChange = (res) => {
        console.log("识别变化时", res)
        this.replyText = res.result.voice_text_str
        this.lastRecognitionTime = new Date().now() // 更新最后识别时间
        
        // 清除之前的定时器
        if (this.clearRecognitionTimeout) {
          clearTimeout(this.clearRecognitionTimeout)
        }
        
        // 设置新的定时器，1秒内没有新的识别结果就停止识别
        this.clearRecognitionTimeout = setTimeout(() => {
          this.stopVoiceRecord()
        }, 1000)
      }
      
      // 一句话结束
      this.speechRecognizer.OnSentenceEnd = (res) => {
        console.log("一句话结束", res)
        this.audioText = res.result.voice_text_str
        if (this.audioText && this.recognizOpen) {
          this.voiceText = this.audioText
        }
      }
      
      // 识别结束
      this.speechRecognizer.OnRecognitionComplete = (res) => {
        console.log("识别结束", res)
      }
      
      // 识别错误
      this.speechRecognizer.OnError = (res) => {
        console.log("识别失败", res)
        this.isRecording = false
        uni.showToast({
          title: '语音识别失败'+res,
          icon: 'none'
        })
      }
      
      // 开始识别
      this.recognizOpen = true
      this.speechRecognizer.start()
    },
    
    // 停止语音录制
    stopVoiceRecord() {
      this.isRecording = false
      if (this.speechRecognizer) {
        this.speechRecognizer.stop()
        this.recognizOpen = false
        
        // 如果有识别结果，显示在语音结果区域
        if (this.replyText) {
          this.voiceText = this.replyText
        }
      }
    },
    
    // 取消语音录制
    cancelVoiceRecord() {
      this.isRecording = false
      this.voiceText = ''
      this.stopSpeechRecognition()
    },
    
    // 发送语音消息
    sendVoiceMessage() {
      if (this.voiceText) {
        this.inputMessage = this.voiceText
        this.sendMessage()
        this.voiceText = ''
      }
    },
    
    // 取消语音消息
    cancelVoiceMessage() {
      this.voiceText = ''
    },
    
    // 停止语音识别
    stopSpeechRecognition() {
      if (this.speechRecognizer) {
        this.speechRecognizer.stop()
        this.speechRecognizer = null
      }
      
      if (this.clearRecognitionTimeout) {
        clearTimeout(this.clearRecognitionTimeout)
        this.clearRecognitionTimeout = null
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.ai-chat-container {
  background-color: #f5f7fa;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.chat-header {
  background-color: #fff;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;

  .tab-bar {
    display: flex;
    justify-content: space-around;

    .tab-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 10rpx 0;

      .tab-icon {
        width: 50rpx;
        height: 50rpx;
        margin-bottom: 8rpx;
      }

      text {
        font-size: 24rpx;
        color: #666;
      }

      &.active {
        color: #836aff;

        text {
          color: #836aff;
        }

        &::after {
          content: '';
          display: block;
          width: 40rpx;
          height: 4rpx;
          background-color: #836aff;
          margin-top: 8rpx;
        }
      }
    }
  }
}

.action-buttons-container {
  display: flex;
  justify-content: space-around;
  background-color: #fff;
  padding: 10rpx 0 20rpx;
  border-bottom: 1rpx solid #eee;

  .action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 25%;

    .btn-icon {
      width: 70rpx;
      height: 70rpx;
      margin-bottom: 10rpx;
      padding: 12rpx;
      box-sizing: border-box;
    }

    text {
      font-size: 24rpx;
      color: #666;
      text-align: center;
      white-space: normal;
      width: 100%;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
  }
}

.chat-content {
  flex: 1;
  padding: 20rpx;
  overflow-y: auto;
  background-color: #f9f6ff;
  box-sizing: border-box;
  .chat-list {
    padding-bottom: 20rpx;
  }

  .chat-item {
    display: flex;
    margin-bottom: 30rpx;

    .avatar {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      overflow: hidden;
      margin-right: 20rpx;

      image {
        width: 100%;
        height: 100%;
      }
    }

    .message {
      max-width: 70%;

      .msg-content {
        padding: 20rpx;
        border-radius: 12rpx;
        font-size: 28rpx;
        line-height: 1.5;
        position: relative;

        &::after {
          content: '';
          display: inline-block;
          width: 2px;
          height: 24rpx;
          background-color: #874ff0;
          margin-left: 2px;
          vertical-align: text-bottom;
          opacity: 0;
        }
      }

      // 添加打字机光标动画
      .typing::after {
        opacity: 1;
        animation: cursor-blink 0.8s infinite;
      }
    }
  }

  .chat-item-ai {
    .message {
      .msg-content {
        background-color: #fff;
        color: #333;

        /* Markdown styles */
        :deep(h1) {
          font-size: 32rpx;
          margin: 10rpx 0;
          font-weight: bold;
        }

        :deep(h2) {
          font-size: 30rpx;
          margin: 10rpx 0;
          font-weight: bold;
        }

        :deep(h3) {
          font-size: 28rpx;
          margin: 10rpx 0;
          font-weight: bold;
        }

        :deep(ul),
        :deep(ol) {
          padding-left: 20rpx;
          margin: 10rpx 0;
        }

        :deep(li) {
          margin: 5rpx 0;
        }

        :deep(blockquote) {
          border-left: 3px solid #ddd;
          padding-left: 10rpx;
          color: #666;
          margin: 10rpx 0;
        }

        :deep(code) {
          background-color: #f3f3f3;
          padding: 2rpx 5rpx;
          border-radius: 3rpx;
          font-family: monospace;
        }

        :deep(pre) {
          background-color: #f3f3f3;
          padding: 10rpx;
          border-radius: 5rpx;
          overflow-x: auto;
          margin: 10rpx 0;
        }
      }
    }
  }

  .chat-item-user {
    flex-direction: row-reverse;

    .avatar {
      margin-right: 0;
      margin-left: 20rpx;
    }

    .message {
      .msg-content {
        background: linear-gradient(
          135deg,
          rgba(81, 106, 251, 1) 0%,
          rgba(133, 155, 255, 1) 99.97%
        );
        color: #fff;
      }

      .msg-time {
        text-align: right;
      }
    }
  }
}

.chat-footer {
  background-color: #f9f6ff;
  border-top: 1rpx solid #eeeeee;
  padding: 10rpx 20rpx 20rpx;

  .input-hint {
    font-size: 24rpx;
    color: #999;
    margin-bottom: 10rpx;
    padding-left: 10rpx;
  }

  .input-area {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;

    input {
      flex: 1;
      height: 80rpx;
      background-color: #fff;
      border-radius: 40rpx;
      padding: 0 30rpx;
      font-size: 28rpx;
      border: 1px solid #e6e6fa;
    }

    .voice-btn, .keyboard-btn {
      width: 70rpx;
      height: 70rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: 10rpx;
      background-color: #fff;
      border-radius: 50%;
      border: 1px solid #e6e6fa;
    }

    .send-btn {
      width: 70rpx;
      height: 70rpx;
      background: linear-gradient(
        135deg,
        rgba(81, 106, 251, 1) 0%,
        rgba(133, 155, 255, 1) 99.97%
      );
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: 20rpx;
    }
    
    .voice-input-container {
      flex: 1;
      display: flex;
      align-items: center;
      
      .voice-record-btn {
        flex: 1;
        height: 80rpx;
        background-color: #fff;
        border-radius: 40rpx;
        margin: 0 10rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1px solid #e6e6fa;
        
        text {
          font-size: 28rpx;
          color: #666;
        }
      }
    }
    
    .voice-result {
      margin-top: 10rpx;
      background-color: #fff;
      border-radius: 8rpx;
      padding: 15rpx;
      position: relative;
      
      .voice-text {
        font-size: 28rpx;
        color: #333;
        line-height: 1.5;
        margin-right: 80rpx;
      }
      
      .voice-actions {
        position: absolute;
        right: 10rpx;
        top: 50%;
        transform: translateY(-50%);
        display: flex;
        
        .voice-action-btn {
          width: 50rpx;
          height: 50rpx;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-left: 10rpx;
          
          &.send {
            background-color: #4CAF50;
          }
          
          &.cancel {
            background-color: #F44336;
          }
        }
      }
    }
  }

  .bottom-actions {
    display: flex;
    border-top: 1px solid #eee;
    padding: 15rpx 0;
    align-items: center;

    .bottom-title {
      font-size: 28rpx;
      font-weight: bold;
      color: #836aff;
      margin-right: 20rpx;
      white-space: nowrap;
      padding-left: 20rpx;
    }

    .quick-actions-scroll {
      flex: 1;
      width: 100%;
      white-space: nowrap;
      overflow: hidden;
    }

    .quick-actions {
      display: inline-flex;
      padding-right: 20rpx;
    }

    .quick-btn {
      display: inline-flex;
      align-items: center;
      background-color: #f0f2f5;
      border-radius: 36rpx;
      padding: 10rpx 20rpx;
      margin-right: 15rpx;

      text {
        font-size: 24rpx;
        color: #666;
      }

      &.active {
        background-color: #e6e6fa;

        text {
          color: #836aff;
        }
      }
    }
  }

  .patient-selector {
    margin: 10rpx 0 20rpx;

    .selector-title {
      color: #333;
      font-size: 32rpx;
      font-weight: bold;
      padding: 20rpx 0;
      border-bottom: 1px solid #ececec;
      margin-bottom: 20rpx;
    }

    .patient-list {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;

      .patient-item {
        width: 48%;
        background-color: #f0f0f0;
        border-radius: 8rpx;
        padding: 20rpx;
        margin-bottom: 15rpx;
        text-align: center;

        text {
          font-size: 28rpx;
          color: #333;
        }

        &.add-new {
          background-color: #f9f9f9;
          border: 1px dashed #ccc;

          text {
            color: #666;
          }
        }
      }
    }
  }
}

// 添加光标闪烁动画
@keyframes cursor-blink {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
}

// 语音录制遮罩层
.voice-recording-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;

  .voice-recording-indicator {
    background-color: #fff;
    padding: 20rpx;
    border-radius: 10rpx;
    text-align: center;

    .recording-icon {
      width: 100rpx;
      height: 100rpx;
      margin-bottom: 20rpx;
    }

    text {
      font-size: 28rpx;
      color: #333;
    }
  }
}
</style>
