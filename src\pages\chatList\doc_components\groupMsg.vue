<template>
  <!-- 群发消息 -->
  <view class="chat_group">
    <!-- 头像 -->
    <image
      @click="head"
      :src="imgUrl || '/static/images/docHead.png'"
      mode="aspectFill"
      class="chat_user_img"
    />
    <!-- 卡片 -->
    <view class="group_content" @click="click">
      <!-- 标题 -->
      <view class="content_title">群发</view>
      <!-- 内容 -->
      <view class="content_cont">
        <!-- 左侧图标 -->
        <image src="/static/images/chat/qf.png" class="cont_icon" />
        <!-- 右侧文案 -->
        <view class="cont_right">
          <!-- 标题 -->
          <view class="cont_right_title">{{ title }}</view>
          <!-- 描述 -->
          <view class="cont_right_info">
            <text>医生给你发送了一条群发消息，点击查看</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    // 头像
    imgUrl: {
      type: String,
      default: '',
    },
    // 标题
    title: {
      type: String,
      default: '',
    },
  },
  methods: {
    head() {
      this.$emit('head');
    },
    click() {
      this.$emit('click');
    },
  },
};
</script>

<style scoped lang="scss">
.chat_group {
  max-width: 600upx;
  box-sizing: border-box;
  @include flex(left);
  padding-bottom: 30upx;
  align-items: flex-start;

  // 头像
  .chat_user_img {
    width: 64upx;
    height: 64upx;
    border-radius: 4upx;
    margin-right: 16upx;
    flex: none;
  }

  // 内容
  .group_content {
    width: 516upx;
    padding: 24upx;
    background-color: #fff;
    color: #333;
    border: 1px solid #ececec;
    border-radius: 8upx 32upx 32upx 32upx;

    // 标题
    .content_title {
      font-size: 28upx;
      color: $k-title;
      font-weight: bold;
    }

    // 底部内容
    .content_cont {
      @include flex(left);
      align-items: flex-start;
      margin-top: 20upx;

      // 左侧图标
      .cont_icon {
        width: 80upx;
        height: 80upx;
        border-radius: 8upx;
        margin-right: 24upx;
        flex: none;
      }

      // 右侧文案
      .cont_right {
        flex: 1;
        min-height: 80upx;
        @include flex(lr);
        flex-direction: column;
        align-items: stretch;

        // 标题
        .cont_right_title {
          font-size: 28upx;
          color: $k-title;
          font-weight: bold;
        }

        // 描述
        .cont_right_info {
          font-size: 24upx;
          color: $k-info-title;
        }
      }
    }
  }
}
</style>
