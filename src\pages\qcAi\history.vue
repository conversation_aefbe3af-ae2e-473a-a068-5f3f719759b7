<template>
  <view class="history-container">
    <view class="page-header">
      <view class="back-btn" @click="goBack">
        <u-icon name="arrow-left" color="#333" size="40"></u-icon>
      </view>
      <text class="page-title">聊天历史</text>
    </view>
    
    <scroll-view scroll-y class="history-list" v-if="sessionList.length > 0">
      <view 
        v-for="(item, index) in sessionList" 
        :key="index" 
        class="history-item"
        @click="openConversation(item)"
      >
        <view class="item-left">
          <image src="/static/images/ai-avatar.png" class="avatar"></image>
        </view>
        <view class="item-content">
          <view class="item-title">
            <text class="title">
              对话{{ index + 1 }}：{{ (item.textContent || '').length > 10 ? (item.textContent || '').substring(0, 10) + '...' : (item.textContent || '') }}
              <br>
              <text class="time">{{ item.addTime||'' }}</text>
            </text>
          </view>
        </view>
        <view class="item-right">
          <view class="delete-btn" @click.stop="confirmDelete(item)">
            <u-icon name="arrow-right" color="#ccc" size="30"></u-icon>
          </view>
        </view>
      </view>
    </scroll-view>
    
    <view class="empty-state" v-else>
      <image src="/static/images/empty.png" class="empty-icon"></image>
      <text class="empty-text">暂无聊天记录</text>
      <!-- <button class="start-btn" @click="startNewChat">开始新对话</button> -->
    </view>
  </view>
</template>

<script>
import { getSessionList, deleteSession } from '@/api/qcAi.js';

export default {
  data() {
    return {
      sessionList: [],
      userId: '',
      isLoading: false
    };
  },
  onLoad() {
    this.userId = uni.getStorageSync('userId');
    this.getSessionList();
  },
  methods: {
    async getSessionList() {
      try {
        this.isLoading = true;
        const { data } = await getSessionList({
          userId: this.userId,
          sessionType: 3
        });
        
        if (data ) {
          this.sessionList = data.map(session => {
            return {
              ...session,
              // 提取最后一条消息的内容作为预览
              lastContent: this.extractLastContent(session),
              // 使用会话标题或默认标题
              title: session.title || '智能问诊对话'
            };
          });
        } else {
          this.sessionList = [];
        }
      } catch (error) {
        console.error('获取会话列表异常:', error);
        uni.showToast({
          title: '获取历史记录失败',
          icon: 'none'
        });
      } finally {
        this.isLoading = false;
      }
    },
    // 从会话中提取最后一条消息内容
    extractLastContent(session) {
      if (session.lastMessage && session.lastMessage.messages && session.lastMessage.messages.length > 0) {
        return session.lastMessage.messages[0].content;
      } else if (session.lastContent) {
        return session.lastContent;
      }
      return '无消息内容';
    },
    goBack() {
      uni.navigateBack();
    },
    openConversation(item) {
      uni.navigateTo({
        url: `/pages/qcAi/chat?sessionId=${item.sessionId}`
      });
    },
    startNewChat() {
      uni.navigateTo({
        url: '/pages/qcAi/chat'
      });
    },
    confirmDelete(item) {
      uni.showModal({
        title: '提示',
        content: '确定要删除此对话吗？',
        success: async (res) => {
          if (res.confirm) {
            await this.deleteSession(item);
          }
        }
      });
    },
    async deleteSession(item) {
      try {
        const { data } = await deleteSession({
          sessionId: item.sessionId,
          userId: this.userId
        });
        
        if (data) {
          uni.showToast({
            title: '删除成功',
            icon: 'success'
          });
          
          // 重新获取会话列表
          this.getSessionList();
        }
      } catch (error) {
        console.error('删除会话异常:', error);
        uni.showToast({
          title: '删除失败',
          icon: 'none'
        });
      }
    },
    formatDate(timestamp) {
      if (!timestamp) return '';
      
      const date = new Date(timestamp);
      const now = new Date();
      const diffDays = Math.floor((now - date) / (1000 * 60 * 60 * 24));
      
      if (diffDays === 0) {
        // Today, show time
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');
        return `${hours}:${minutes}`;
      } else if (diffDays === 1) {
        // Yesterday
        return '昨天';
      } else if (diffDays < 7) {
        // This week
        const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
        return weekdays[date.getDay()];
      } else {
        // More than a week ago
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        return `${month}-${day}`;
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.history-container {
  height: 100vh;
  background-color: #f5f7fa;
  display: flex;
  flex-direction: column;
}

.page-header {
  display: flex;
  align-items: center;
  padding: 30rpx 30rpx; // 上下间距增加
  background-color: #fff;
  border-bottom: 1px solid #eee;
  position: relative;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05); // 添加轻微投影
  
  .back-btn {
    padding: 10rpx;
  }
  
  .page-title {
    flex: 1;
    text-align: center;
    font-size: 32rpx;
    font-weight: bold;
    margin-right: 40rpx; // To offset the back button
  }
}

.history-list {
  flex: 1;
  overflow: hidden;
  padding: 20rpx;
  box-sizing: border-box;
  .history-item {
    display: flex;
    padding: 20rpx;
    background-color: #fff;
    margin-bottom: 20rpx;
    margin-top: 20rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05); // 添加轻微投影
    .item-left {
      margin-right: 20rpx;
      
      .avatar {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
      }
    }
    
    .item-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      
      .item-title {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10rpx;
        
        .title {
          font-size: 30rpx;
          color: #333;
          font-weight: 500;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        
        .time {
          font-size: 24rpx;
          color: #999;
        }
      }
      
      .item-message {
        font-size: 26rpx;
        color: #666;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 450rpx;
      }
    }
    
    .item-right {
      display: flex;
      align-items: center;
      
      .delete-btn {
        padding: 10rpx 20rpx;
      }
    }
  }
}

.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40rpx;
  
  .empty-icon {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 20rpx;
  }
  
  .empty-text {
    font-size: 28rpx;
    color: #999;
    margin-bottom: 40rpx;
  }
  
  .start-btn {
    background: linear-gradient(135deg, rgba(81, 106, 251, 1) 0%, rgba(133, 155, 255, 1) 99.97%);
    color: #fff;
    border-radius: 40rpx;
    padding: 16rpx 60rpx;
    font-size: 28rpx;
    border: none;
  }
}
</style> 