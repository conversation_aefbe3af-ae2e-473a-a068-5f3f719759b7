import urlConfig from '../common/request/config.js';
import { Toast } from '@/common/js/pay.js';
let baseUrl = urlConfig.serverUrl;
const getUrl = (url) => {
  if (url.indexOf('://') == -1) {
    url = baseUrl + url;
  }
  return url;
};
const http = ({ url = '', param = {}, ...other } = {}) => {
  return new Promise((resolve, reject) => {
    uni.request({
      url: getUrl(url),
      data: param,
      ...other,
      header: {
        firmId: urlConfig.firmId,
        version: urlConfig.version,
        clientType: urlConfig.clientType,
      },
      success: (res) => {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          if (res.data.code == 20000 || res.data.code == 20011) {
            resolve(res.data);
          } else {
            Toast(res.data.message);
            reject(res);
          }
        } else {
          reject(res);
        }
      },
      fail: (err) => {
        uni.hideLoading();
        reject(err);
      },
    });
  });
};

//查询预约排班标志
export function add(data = {}) {
  return http({
    url: '/buriedPoint/add',
    data,
    method: 'post',
  });
}
