<template>
  <view class="zdcom-container">
    <view class="diagnose-container">
      <view v-if='selectedDiagList.length > 0' style="display: flex;flex-wrap: wrap;">
        <view v-for="(item, index) in selectedDiagList" :key="index" class="selectDiagnose">
          <view style="color:#999999;" @click="editDiagName(item, index)">{{ item.diaNamePrefix }}</view>
          <view class="selectDiagnoseName" @click="editDiagName(item, index)">{{ item.diagName }}</view>
          <view style="color:#999999;" @click="editDiagName(item, index)">{{ item.diaNameSuffix }}</view>
          <u-icon name="close-circle" color="red" style="margin-left: 6px" @click="deleteDiag(item, index)"></u-icon>
          <image src="/static/image/cf/editZd.png" class="editZd" @click="editDiagName(item, index)"
                 v-if="!item.editZdShow" />
        </view>
      </view>
      <view class="diagnose-input-wrapper">
        <view v-if="!isShowInput" @click="handleDiagnose" class="add-input flex items-center">
          <text>{{ inputPlaceholder }}</text>
        </view>
        <view v-else class="diagnose-input-container">
          <u-input v-model="zdName" :placeholder="inputPlaceholder" class="diagnose-input" :focus="true" @focus="searchZd('focus')" @blur="blurInput" style="height: 100%;"/>
        </view>
        <!-- 诊断下拉列表 -->
        <scroll-view 
          class="diagnose-dropdown-list" 
          v-if="prescriptionList.length > 0 && !popupDrugVisable"
          scroll-y
          @scrolltolower="loadMoreData"
        >
          <view class="diagnose-dropdown-item" v-for="(item, index) in prescriptionList" :id="index" :key="index"
                @click="clickDiagName(item)">
            {{ item.diagName }}
          </view>
          <view class="loading-indicator" v-if="prescriptionList.length < totalNum && loadmore === 'loading'">
            <u-loading size="24" mode="circle"></u-loading>
            <text>加载中...</text>
          </view>
          <view class="no-more" v-else-if="prescriptionList.length >= totalNum">
            <text>没有更多数据了</text>
          </view>
        </scroll-view>
      </view>
    </view>
    
    <!-- 药添加前缀后缀 -->
    <u-popup v-model="drugPreOrSufVisable" mode="bottom" width="80%"  @close="closeDrugPopup">
      <view class="custom-popup">
        <view class="custom-popup-title"> 临床诊断 </view>
        <view class="custom-popup-content">
          <u-input type="textarea" class="drug-config-input" :height="120" :auto-height="true" placeholder="请输入诊断前缀（可跳过）" :border="true"
                   v-model="currentSelect.diaNamePrefix"></u-input>
          <view class="custom-popup-title drug-config-name">{{ currentSelect.diagName }}</view>
          <u-input type="textarea" class="drug-config-input" :height="120" :auto-height="true" placeholder="请输入诊断后缀（可跳过）" :border="true"
                   v-model="currentSelect.diaNameSuffix"></u-input>
        </view>
      </view>
      <view style="margin-bottom: 20px;display: flex;flex-direction: row;align-items: center;justify-content: space-around">
        <view class="custom-btn custom-btn-y" @click="sumitDiaName" >确定</view>
        <view class="custom-btn custom-cancle" @click="deleteDiagName">删除</view>
      </view>
    </u-popup>
    
    <!-- 药列表 -->
    <u-popup v-model="popupDrugVisable" mode="bottom" height="80%" bg-color="#F5F5F5" @close="closeDrugPopup">
      <scroll-view scroll-y @scrolltolower="loadMoreData" style="height:100%">
        <view class="popup-drug-list">
          <block v-for="(item, index) in prescriptionList" :key="index">
            <view class="popup-drug-item" @click="clickDiagName(item)"> {{ item.diagName }}</view>
          </block>
          <view>
            <u-loadmore :status="loadmore"></u-loadmore>
          </view>
        </view>
      </scroll-view>
    </u-popup>
  </view>
</template>

<script>
import { findAll, findAllListPage } from '@/api/user.js'
import {mapState} from "vuex";
export default {
  props:{
    drugstoreId:{
      type:String,
      default:''
    }
  },
  data() {
    return {
      isShowInput: false,
      historyPopup: false,
      drugPreOrSufVisable: false,
      zdName: '',
      prescriptionList: [],
      listQuery:{
        page: 1,
        limit: 50,
      },
      totalNum: 10,
      loadmore:'loading',
      popupDrugVisable:false,
      isEditDiaName:false,
      focusInput:false,
      currentSelect:{
        diaNamePrefix: "",
        diagName: "",
        diaNameSuffix: "",
        index:null
      }
    }
  },
  computed:{
    ...mapState({
      diagList: 'diagList'
    }),
    selectedDiagList:{
      get(){
        return this.$store.getters.diagList
      },
      set(val){
        this.$emit("commitDiagList", val)
        this.$store.commit('setDiagList', val)
      }
    },
    inputPlaceholder() {
      return this.drugstoreId === "574a40ea01424f7a8b62762b37ff58e2" 
        ? "输入疾病名称:如湿疹、荨麻疹、带状疱疹、免疫力低下，或其他免疫缺陷病等"
        : "输入西医诊断或中医诊断";
    }
  },
  watch:{
    zdName(newVal, oldVal){
      this.searchZd("input");
    },
  },
  created(){
    uni.onKeyboardHeightChange((res) => {
      console.log("键盘参数", res);
    });
    this.selectedDiagList = this.$store.getters.diagList
  },
  methods:{
    loadMoreData(){
      if (this.prescriptionList.length >= this.totalNum) return;
      this.listQuery.page += 1;
      this.getAllDrugList();
    },
    getAllDrugList(){
      let data = {
        ...this.listQuery,
        diagName: this.zdName,
        diagType: 1
      }
      findAllListPage(data).then((res) => {
        let data = res.data.rows;
        let total = res.data.total;
        this.totalNum = total
        if (this.listQuery.page > 1) {
          this.prescriptionList = [...this.prescriptionList, ...res.data.rows];
        } else {
          this.prescriptionList = res.data.rows;
        }
        if (this.prescriptionList.length >= res.data.total) {
          this.loadmore = 'nomore';
        } else {
          this.loadmore = 'loading';
        }
      })
    },
    handleHistory(){
      this.historyPopup = true;
    },
    handleDiagnose(){
      this.isShowInput = !this.isShowInput;
    },
    closeDrugPopup(){
      this.popupDrugVisable = false;
    },
    clickDiagName(item){
      for (let i = 0; i < this.selectedDiagList.length; i++) {
        if (this.selectedDiagList[i].diagName == item.diagName) {
          uni.showToast({
            title: "诊断已存在，请勿重复选择",
            icon: "none"
          })
          return;
        }
      }
      this.isEditDiaName = false
      this.prescriptionList = []
      this.currentSelect.diagType = item.diagType;
      this.currentSelect.diagId = item.diagId;
      this.currentSelect.diagCode = item.diagCode;
      this.currentSelect.diagName = item.diagName;
      this.popupDrugVisable = false
      this.drugPreOrSufVisable = true
    },
    sumitDiaName(){
      this.prescriptionList = [];
      this.zdName = ''
      let obj = Object.assign({}, this.currentSelect);
      if (this.isEditDiaName) { //编辑诊断
        this.selectedDiagList.splice(this.currentSelect.index, 1, this.currentSelect);
      } else {//新增诊断
        this.selectedDiagList.push(obj)
      }
      this.currentSelect = {
        diaNamePrefix: "",
        diagName: "",
        diaNameSuffix: ""
      }
      this.drugPreOrSufVisable = false
    },
    deleteDiagName(item, index){
      if(index){}
      this.currentSelect = {
        diaNamePrefix: "",
        diagName: "",
        diaNameSuffix: "",
        index:this.currentSelect.index
      }
      if(!this.currentSelect.index){
        this.drugPreOrSufVisable = false
        this.reset()
        return
      }
      this.selectedDiagList.splice(this.currentSelect.index, 1);
      this.drugPreOrSufVisable = false
      this.reset()
    },
    deleteDiag(item, index){
      this.currentSelect = {
        diaNamePrefix: "",
        diagName: "",
        diaNameSuffix: ""
      }
      this.selectedDiagList.splice(index, 1);
      this.drugPreOrSufVisable = false
      this.reset()
    },
    reset(){
      this.prescriptionList = []
      this.zdName = ''
      this.popupDrugVisable = false
    },
    editDiagName(item,index){
      item.index = index
      item.type = 'edit'
      this.currentSelect.index = index
      this.selectedDiagList[index] = item
      this.isEditDiaName = true
      this.currentSelect.diaNamePrefix = item.diaNamePrefix
      this.currentSelect.diagName = item.diagName
      this.currentSelect.diaNameSuffix = item.diaNameSuffix
      this.drugPreOrSufVisable = true
    },
    searchZd(type){
      let time = 0;
      if (type == "focus") {
        time = 0;
        this.focusInput = true;
      } else {
        time = 200;
      }
      if (this.zdName.length > 0) {
        clearTimeout(timeOut);
        if (!this.focusInput) {
          return;
        }
        let timeOut = setTimeout(() => {
          // 搜索时重置为第一页
          this.listQuery.page = 1;
          this.prescriptionList = [];
          
          findAllListPage({
            ...this.listQuery,
            diagName: this.zdName,
            diagType: '1' //根据切换处方，修改处方类型入参
          }).then(res => {
            this.prescriptionList = res.data.rows || [];
            this.totalNum = res.data.total || 0;
            if(this.prescriptionList.length === 0){
              this.prescriptionList = [{
                diagName: '其它',
                diagId: '05098dbed30d4a72a1bf6e4888632e63',
                diagType: '1'
              }];
            }
            if (this.prescriptionList.length >= this.totalNum) {
              this.loadmore = 'nomore';
            } else {
              this.loadmore = 'loading';
            }
          });
        }, time);
      } else {
        this.prescriptionList = []
      }
    },
    blurInput(){
      this.focusInput = false
    }
  }
}



</script>

<style scoped lang='scss'>
.zdcom-container {
  width: 100%;
  position: relative;
}

.diagnose-input-wrapper {
  position: relative;
  width: 100%;
  margin-bottom: 10px;
}

.diagnose-dropdown-list {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  max-height: 300px;
  background: #FFFFFF;
  border: 1px solid #EEEEEE;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 999;
  overflow-y: auto;
}

.diagnose-dropdown-item {
  padding: 12px 16px;
  border-bottom: 1px solid #F5F5F5;
  font-size: 28rpx;
  color: #333333;
}

.diagnose-dropdown-item:active {
  background-color: #F5F5F5;
}

.loading-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx 0;
  font-size: 24rpx;
  color: #999999;
}

.no-more {
  text-align: center;
  padding: 16rpx 0;
  font-size: 24rpx;
  color: #999999;
}

.custom-cancle {
  background: #fff;
  border: 1px solid #14A0E6;
  color: #14A0E6;
}

::v-deep .uni-input-placeholder{
  text-align: left;
}

.selectDiagnose {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  padding: 16rpx;
  background: #F5F5F5;
  border-radius: 4rpx;
  margin-top: 16rpx;
  margin-right: 16rpx;

  .editZd {
    width: 32rpx;
    height: 32rpx;
    margin-left: 6px;
  }
}

.drug-config-name {
  color: #14a0e6 !important;
  font-size: 28rpx !important;
}

.add-input {
  border-radius: 8rpx;
  color: rgba(153, 153, 153, 1);
  font-size: 28rpx;
  padding: 16rpx;
  display: flex;
  width: 100%;
  height: 80px;
  opacity: 1;
  background: rgba(255, 255, 255, 1);
  border: 0.5px solid rgba(131, 106, 255, 1);
}

.diagnose-input {
  height: 80rpx !important;
  background-color: #fff;
  padding: 0 16rpx;
  border-radius: 8rpx;
  border: 0.5px solid rgba(131, 106, 255, 1);
  line-height: 80rpx;
  display: flex;
  text-align: left !important;
  ::v-deep .uni-input-wrapper {
    text-align: left !important;
    text-indent: 10px !important;
  }
}

.diagnose-input-container {
  width: 100%;
}

.diagnose-container {
  width: 100%;
}

.custom-popup {
  padding: 20rpx 30rpx;
  text-align: center;
  .custom-popup-title {
    font-size: 28rpx;
    color: rgba(51, 51, 51, 1);
    height: 80rpx;
    line-height: 100rpx;
    text-align: left;

    .custom-popup-content {
      text-align: center;
      font-size: 28rpx;
      margin-bottom: 20px;
    }
  }
}
.custom-popup-content {
  padding: 10px;
  margin-bottom: 20px;
}
::v-deep .u-drawer-bottom{
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
}
.custom-btn {
  width: 40%;
  height: 36px;
  opacity: 1;
  border-radius: 422px;
  background: rgba(255, 255, 255, 1);
  border: 1px solid rgba(131, 106, 255, 1);
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(131, 106, 255, 1);
}
.custom-btn-y{
  color: rgba(255, 87, 51, 1);
  border: 1px solid rgba(255, 87, 51, 1);
}
.btn-style {
  width: 140rpx;
  height: 52rpx;
  line-height: 52rpx;
  border: 2rpx solid #999999;
  border-radius: 40rpx;
  min-width: 60px;
  font-size: 24rpx;
  color: #999999;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;

  &.btn-add {
    background: #14A0E6;
    color: #fff;
    border: none;
  }
  &.shoucang{
    border:1px solid #14A0E6;
    color:#14A0E6
  }
}
</style>