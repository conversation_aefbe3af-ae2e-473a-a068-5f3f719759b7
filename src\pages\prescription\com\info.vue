<template>
  <!-- 订单信息 -->
  <view class="order_info">
    <!-- 重要 -->
    <view class="order_number">
      <!-- 单号 -->
      <view class="order_bold">处方单号：{{ detail.businessCode }}</view>
      <!-- 医院 -->
      <view class="order_bold">医院名称：{{ detail.hosName }}</view>
    </view>

    <!-- 患者信息 -->
    <view class="user">
      <text>{{ detail.patientName }}</text>
      <text>{{ detail.sex }}</text>
      <text>{{ detail.age }}岁</text>
    </view>
    <!-- 开单医生 -->
    <view class="info_item">
      <text>医生姓名：{{ detail.platformDocName || detail.docName }}</text>
    </view>
    <!-- 医生科室 -->
    <view class="info_item">
      <text>医生科室：{{ detail.deptName }}</text>
    </view>
    <!-- 药师姓名 -->
    <view class="info_item">
      <text>药师姓名：{{ detail.checkUserName }}</text>
    </view>
    <!-- 处方诊断 -->
    <view class="info_item">
      <text>处方诊断：{{ detail.diagName }}</text>
    </view>

    <!-- 二维码 -->
    <view class="ewm" v-if="code">
      <QRCODE
        :val="code"
        :size="300"
        background="#ffffff"
        foreground="#000000"
        pdground="#000000"
        icon="/static/images/logo-img.png"
        :iconSize="45"
        onval
        loadMake
      />

      <!-- 单号 -->
      <view class="ewm_num">{{ code }}</view>
    </view>
  </view>
</template>

<script>
import QRCODE from '@/components/tki-qrcode/tki-qrcode.vue';
export default {
  name: 'OrderInfo',
  components: {
    QRCODE,
  },
  props: {
    detail: {
      type: Object,
      default: () => {},
    },
    code: String,
  },
};
</script>

<style lang="scss" scoped>
.order_info {
  padding: 0 32rpx 32rpx;
  background: #fff;
  border-radius: 8rpx;
  font-size: 28rpx;

  .order_number {
    //height: 148rpx;
    border-bottom: 1px dashed #eee;
    @include flex;
    align-items: stretch;
    flex-direction: column;
    padding: 14rpx 0;

    .order_bold {
      font-size: 30rpx;
      color: #333;
      line-height: 60rpx;
      font-weight: bold;
    }
  }

  .user {
    @include flex(lr);
    padding-top: 16rpx;

    text {
      font-size: 30rpx;
      color: #333;
      font-weight: bold;
    }
  }

  .info_item {
    font-size: 28rpx;
    color: #666;
    margin-top: 16rpx;
    line-height: 40rpx;
  }

  .ewm {
    @include flex;
    flex-direction: column;
    padding: 38rpx 0 0;

    .ewm_num {
      font-weight: bold;
      color: #333;
      padding-top: 28rpx;
      max-width: 90%;
      word-break: break-all;
      text-align: center;
    }
  }
}
</style>
