<template>
  <view class="meadicalRecords">
    <view class="medRecordCard">
      <!-- 医生卡片 -->
      <Docter :infoDetail="docInfo" />

      <!-- 预约时间 -->
      <view class="patientCard">
        <view class="cardInfo">
          <view class="text-l"> 预约时间 </view>
          <!-- 从列表来 -->
          <view class="text-r" v-if="visitTime"
            >{{ visitTime }} {{ startTime.slice(0, 5) }}-{{
              endTime.slice(0, 5)
            }}</view
          >
        </view>
      </view>

      <!-- 预约说明 -->
      <view class="patientCard">
        <view class="cardInfo">
          <view class="text-l"> 预约说明 </view>
          <view class="text-r"> 预约成功后，请与预约时间内去医院就诊 </view>
        </view>
      </view>
    </view>

    <!-- 确认 -->
    <FooterButton @click="confirm">确认预约</FooterButton>
  </view>
</template>

<script>
import FooterButton from "@/components/footer_button/button.vue";
import Docter from "@/components/doctor_header/doctor_header.vue";

import myJsTools from "@/common/js/myJsTools.js";
import { toMakeAnOffLineAppointment } from "@/api/appoint";
import { findDoctorByID } from "@/api/base.js";
import { findPatientByPatientId } from "@/api/chatCardDetail.js";

import PropCenter from "@/components/propCenter/propCenter.vue";

export default {
  components: {
    FooterButton,
    Docter,
    PropCenter,
  },
  data() {
    return {
      // 医生id
      docId: "",
      docInfo: {},
      huanzeInfo: {},
      // 预约日期
      date: "",
      // 询问
      showModal: false,
      // 预约日期
      visitTime: "",
      // 开始时间
      startTime: "",
      // 结束时间
      endTime: "",
      // 时段id
      vrId: "",
    };
  },
  onLoad(e) {
    let { docId, patientId, visitTime, startTime, endTime, vrId } = e;
    this.docId = docId;
    this.patientId = patientId;
    this.visitTime = visitTime;
    this.startTime = startTime;
    this.endTime = endTime;
    this.vrId = vrId;
    // 获取医生信息
    this.getDocInfo();
    // 患者信息
    this.getPatientInfo();
  },
  methods: {
    // 获取患者病情详情
    async getPatientInfo() {
      let { data } = await findPatientByPatientId({
        patientId: this.patientId,
      });
      this.huanzeInfo = data;
    },
    // 获取医生详情
    async getDocInfo() {
      let { data } = await findDoctorByID({ docId: this.docId });
      if (data.docImg) {
        data.docImgUrl = data.docImg;
        myJsTools.downAndSaveImg(data.docImg, (url) => {
          data.docImg = url;
        });
      }
      if (data.lableName) {
        data.docLable = data.lableName.split(",");
      }
      this.docInfo = data;
    },
    // 确认预约
    async confirm() {
      let appointDate = this.visitTime;

      let appointStartTime = this.startTime;

      let appointEndTime = this.endTime;

      let vrId = this.vrId;

      // 医生信息
      let {
        deptId,
        deptName,
        dntName,
        docId,
        docImgUrl: docImg,
        docName,
        telNo: docTel,
        hosId,
      } = this.docInfo;

      // 患者信息
      let {
        age,
        ageUnit,
        birthDate,
        idNo,
        patientId,
        patientImg,
        patientName,
        sex,
        telNo,
        sexCode,
      } = this.huanzeInfo;

      // 业务来源 1app 2自助机 3窗口
      const regSource = 1;

      const openid = uni.getStorageSync("wxInfo").openId;

      const appid = uni.getStorageSync("appId");

      const details = [
        {
          isMedicare: "",
          priceDetailName: "线下服务费",
          totalPay: 0,
          priceDetailId: "",
        },
      ];

      let param = {
        isOffLine: 1,
        deptId,
        deptName,
        dntName,
        docId,
        docImg,
        docName,
        telNo,
        docTel,
        hosId,
        age,
        ageUnit,
        birthDate,
        idNo,
        patientId,
        patientImg,
        patientName,
        sex,
        sexCode,
        regSource,
        openid,
        appid,
        appointDate,
        appointStartTime,
        appointEndTime,
        details,
        vrId,
      };

      uni.showLoading({
        mask: true,
      });

      try {
        let {
          data: { signTime, appointCode },
        } = await toMakeAnOffLineAppointment(param);

        uni.hideLoading();

        // 拼接预约时间
        let str =
          appointDate +
          " " +
          appointStartTime.slice(0, 5) +
          "-" +
          appointEndTime.slice(0, 5);

        uni.redirectTo({
          url:
            "/pages/chatCardDetail/appointResult?code=" +
            appointCode +
            "&hosId=" +
            hosId +
            "&appointTime=" +
            str +
            "&signTime=" +
            signTime,
        });
      } catch (error) {
        uni.hideLoading();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.doctor_box_top {
  padding: 24upx 32upx;
  margin-top: 24upx;
}

.medRecordCard {
  margin: 0 32rpx;
}

.patientCard {
  margin-top: 22rpx;
  background-color: #ffffff;
  color: #333;
  font-size: 28rpx;
  padding: 32rpx;

  .cardInfo {
    display: flex;

    .text-l {
      width: 150upx;
      flex: none;
    }

    .text-r {
      flex: 1;
      text {
        padding-right: 10upx;
      }
    }
  }
}
</style>
