<template>
  <!-- 医生列表 -->
  <view class="doc_list">
    <!-- 标题 -->
    <view class="doc_title">
      <TITLE title="我的医生" />
    </view>

    <!-- 列表 -->
    <view class="list">
      <DOC v-for="(item , index) in list" :item="item"  :key="index"/>
    </view>
  </view>
</template>

<script>
import TITLE from "@/pages/inspect/com/itemTitle.vue";
import DOC from "./doc.vue";

export default {
  components: {
    TITLE,
    DOC,
  },
  props: {
    list: {
      type: Array,
      default: () => [],
    },
  },
};
</script>

<style lang="scss" scoped>
.doc_list {
  background: #fff;
  border-radius: 8rpx;

  .doc_title {
    padding: 0 10rpx;
    height: 80rpx;
    @include flex(left);
  }

  .rec_item {
    border-bottom: 1px solid #ebebeb;
    border-radius: 0;

    &:last-child {
      border-bottom: none;
    }
  }
}
.list{
  display: grid;
  grid-template-columns: repeat(2, 1fr); /* 每行4个元素 */
  gap: 20rpx; /* 固定间距 */
  justify-items: center; /* 居中对齐内容 */
  align-items: center; /* 垂直居中内容 */
  box-sizing: border-box;
  margin-top: 20rpx;
}
</style>
