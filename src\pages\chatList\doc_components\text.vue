<template>
  <!-- 文本消息 -->
  <view class="chat_text">
    <!-- 头像 -->
    <image
      @click="head"
      :src="imgUrl || '/static/images/docHead.png'"
      mode="aspectFill"
      class="chat_user_img"
    />
    <view>
      <view class="chatName">{{chatName}}</view>
      <!-- 文本 -->
      <view class="text_cont" @click="click">
        <text v-html="content"></text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    content: {
      type: String,
      default: '',
    },
    imgUrl: {
      type: String,
      default: '',
    },
    chatName:{}
  },
  data() {
    return {
      n: 0,
    };
  },
  methods: {
    head() {
      this.$emit('head');
    },
    click() {
      this.n++;
      if (this.n >= 2) {
        this.$emit('double', this.content);
      }
      setTimeout(() => {
        this.n = 0;
      }, 500);
    },
  },
};
</script>

<style scoped lang="scss">
.chat_text {
  max-width: 600upx;
  box-sizing: border-box;
  @include flex(left);
  padding-bottom: 30upx;
  align-items: flex-start;

  // 头像
  .chat_user_img {
    width: 64upx;
    height: 64upx;
    border-radius: 4upx;
    margin-right: 16upx;
    flex: none;
  }

  .text_cont {
    max-width: 516upx;
    padding: 10upx 20upx;
    background-color: #fff;
    color: #333;
    word-break: break-all;
    font-size: 28upx;
    border: 1px solid #ececec;
    border-radius: 8upx 32upx 32upx 32upx;
  }
}
</style>
