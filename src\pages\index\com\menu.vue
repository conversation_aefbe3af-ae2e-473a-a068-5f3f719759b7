<template>
  <!-- 功能按钮 -->
  <view class="menu">
    <block v-for="(item, index) in list">
      <view class="menu_item" :class="{ three: !isAll }" @click="toPath(item)" v-if="isAll || item.id != 1" :key="index">
        <view class="menu_item_image" :style="{background: item.background}">
           <image class="icon" :src="item.icon" :style="{
             width:item.width||'60rpx',
             height:item.height||'46rpx'
           }"/>
        </view>
        <view class="text">
          <text class="bold">{{ item.title }}</text>
<!--          <text>{{ item.info }}</text>-->
        </view>
      </view>
    </block>
  </view>
</template>

<script>
import Login from '@/mixins/login.js';

export default {
  name: 'Menu',
  mixins: [Login],
  props: {
    isAll: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    let allList = [
      {
        id: 3,
        title: '互医复诊',
        info: '互医复诊',
        icon: '/static/doc/8.png',
        background:"linear-gradient(180deg, rgba(254, 160, 76, 1) 0%, rgba(255, 209, 156, 1) 100%)",
        path: '/pages/register/docList2/index?flag=2',
      },
      {
        id: 2,
        title: '健康咨询',
        info: '健康咨询',
        icon: '/static/doc/路径 5.png',
        background:"linear-gradient(170.85deg, rgba(135, 79, 240, 1) 0%, rgba(197, 187, 242, 1) 100%)",
        path: '/pages/register/docList2/index?flag=1',
      },
      {
        id: 4,
        title: '快速续方',
        info: '快速续方',
        icon: '/static/doc/路径 9.png',
        path: '/pages/quickContinuation/index',
        background: "linear-gradient(180deg, rgba(46, 145, 232, 1) 0%, rgba(179, 219, 255, 1) 100%)"
      },
      {
        id: 5,
        title: '正元门诊',
        info: '正元门诊',
        icon: '/static/doc/路径 8.png',
        background:"linear-gradient(180deg, rgba(60, 184, 163, 1) 0%, rgba(176, 237, 208, 1) 100%)",
        path: '',
        isInDev: true
      },
      // {
      //   id: 1,
      //   title: '智能导诊',
      //   info: '智能快速答复',
      //   icon: '/static/images/index/dz.png',
      //   path: '/pages/guidance/index',
      // },
      // {
      //   id: 1,
      //   title: '健康科普',
      //   info: '健康科普',
      //   background:"linear-gradient(180deg, rgba(60, 184, 163, 1) 0%, rgba(176, 237, 208, 1) 100%)",
      //   icon: '/static/doc/wz.png',
      //   path: '/pages/article/index',
      //   width:'42rpx',
      //   height:'48rpx'
      // },
      // {
      //   id: 4,
      //   title: '医疗瘦身',
      //   info: '在线快捷续方',
      //   icon: '/static/images/index/mbxf.png',
      //   path: '/pages/disease/index',
      // },
    ];
    return {
      list: allList,
    };
  },
  methods: {
    toPath(item) {
      uni.removeStorageSync('checkCf')
      uni.removeStorageSync('addPatientFlag')
      this.$store.commit('setDiagList', [])
      this.$store.commit('setDrugList', [])
      
      if (item.isInDev) {
        uni.showToast({
          title: '正在开发中',
          icon: 'none',
          duration: 2000
        });
        return;
      }
      
      if (item.id < 4) {
        uni.navigateTo({ url: item.path });
        return;
      }

      if (!this.hasInfo()) return;

      uni.navigateTo({ url: item.path });
    },
  },
};
</script>

<style lang="scss" scoped>
.menu_item_image{
  width: 100rpx;
  height: 100rpx;
  background: linear-gradient(170.85deg, rgba(135, 79, 240, 1) 0%, rgba(197, 187, 242, 1) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  .icon{
    width: 60rpx;
    height: 46rpx;
  }
}
.menu {
  width: 100%;
  @include flex;
  justify-content: space-around;
  flex-wrap: wrap;
  margin-top: 24rpx;

  .menu_item {
    @include flex;
    flex-direction: column;
    border-radius: 16rpx;
    background-color: #fff;
    width: 50px;
    margin-bottom: 20rpx;
    box-sizing: border-box;

    &.three {
      flex-direction: column;
      width: 212rpx;
      height: 212rpx;
      justify-content: space-between;
      padding: 20rpx 28rpx;

      .icon {
        width: 100rpx;
        height: 100rpx;
      }

      .text {
        align-items: stretch;
        flex: 1;
        font-size: 14px;
        .bold {
          text-align: center;
        }
      }
    }

    .icon {
       width: 100rpx;
        height: 100rpx;
    }

    .text {
      @include flex;
      flex-direction: column;
      margin-top: 16rpx;
      font-size: 20rpx;
      color: #c1c1c1;

      .bold {
        font-size: 28rpx;
        color: rgba(80, 90, 105, 1);
        margin-bottom: 6rpx;
        white-space: nowrap;
      }
    }
  }
}
</style>
