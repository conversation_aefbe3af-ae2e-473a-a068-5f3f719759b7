<template>
  <view class="preview-page">
    <!-- PDF预览区域 -->
    <web-view
      :src="previewUrl"
      class="pdf-viewer"
    />
    <div id="pdfH5" class="pdf-viewer"></div>
    <!-- 下载按钮 -->
    <view class="download-btn" >
      <button @click="showDownloadDialog">下载</button>
    </view>

    <!-- 下载链接弹窗 -->
    <uni-popup ref="downloadPopup" type="dialog">
      <uni-popup-dialog
          type="info"
          title="下载链接"
          confirmText="复制"
          :before-close="true"
          @confirm="copyDownloadUrl"
          @close="hideDownloadDialog"
      >
        <text class="copyDownloadUrl" @click="copyDownloadUrl">
          {{downloadUrl}}
        </text>
      </uni-popup-dialog>
    </uni-popup>
  </view>
</template>

<script>
import Pdfh5 from 'pdfh5'
import 'pdfh5/css/pdfh5.css'
import uniPopupDialog from "../../components/uni-popup/uni-popup-dialog.vue";
export default {
  components: {
    uniPopupDialog
  },
  data() {
    return {
      previewUrl: '',
      downloadUrl: '',
      pdfh5:''
    }
  },

  onLoad(options) {
    if(options.url) {
      this.previewUrl = decodeURIComponent(options.url)
      this.downloadUrl = this.previewUrl // 假设下载链接与预览链接相同
      this.initPdf()

    }
  },

  methods: {
    initPdf() {
      this.pdfh5 = new Pdfh5('#pdfH5', {
        pdfurl: this.downloadUrl,
        lazy: true,
        scale: 1
      })
      const _this = this
      this.pdfh5.on('ready', function () {
      })
      this.pdfh5.on('scroll', function (scrollTop, currentNum) {
      })
    },
    // 显示下载弹窗
    showDownloadDialog() {
      console.log(99)
      this.$refs.downloadPopup.open()
    },

    // 隐藏下载弹窗
    hideDownloadDialog() {
      this.$refs.downloadPopup.close()
    },

    // 复制下载链接
    copyDownloadUrl() {
      uni.setClipboardData({
        data: this.downloadUrl,
        success: () => {
          uni.showToast({
            title: '复制成功',
            icon: 'success'
          })
        }
      })
      this.hideDownloadDialog()
    }
  }
}
</script>

<style lang="scss" scoped>
.preview-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #fff;
}

.pdf-viewer {
  flex: 1;
  width: 100%;
}

.download-btn {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 16rpx 32rpx;
  background: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
z-index: 999;
  button {
    width: 100%;
    height: 88rpx;
    line-height: 88rpx;
    background: linear-gradient(135deg, #516afb 0%, #859bff 99.97%);
    color: #fff;
    font-size: 32rpx;
    border-radius: 44rpx;
  }
}
.copyDownloadUrl{
  // 自动换行
  display: inline-block;
  color: #15a0e6;
  // 3行超出省略号
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp:5;
  -webkit-box-orient: vertical;
  word-break: break-all;
  padding: 20rpx;
}
</style>