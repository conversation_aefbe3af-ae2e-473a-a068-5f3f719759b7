<template>
  <footer>
    <!-- 左侧 -->
    <view class="left">
      共{{ length }}项 合计：
      <text>￥{{ cost }}</text>
    </view>

    <!-- 右侧 -->
    <view class="right">
      <button @click="click">
        <slot />
      </button>
    </view>
  </footer>
</template>

<script>
export default {
  name: 'Footer',
  props: {
    length: {
      type: Number,
      default: 0,
    },
    cost: {
      type: Number,
      default: 0.0,
    },
  },
  methods: {
    click() {
      this.$emit('click');
    },
  },
};
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}

footer {
  width: 100%;
  padding: 0 32rpx;
  height: 104rpx;
  box-shadow: 0px 4rpx 8rpx 0px rgba(0, 0, 0, 0.5);
  @include flex(lr);
  background: #fff;
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 2;

  .left {
    font-size: 28rpx;
    color: #333;

    text {
      color: #ff0000;
    }
  }

  .right {
    button {
      min-width: 160rpx;
      line-height: 0;
      padding: 0 28rpx;
      height: 60rpx;
      border-radius: 30rpx;
      color: #fff;
      font-size: 28rpx;
      @include flex;
      @include bg_theme;
    }
  }
}
</style>
