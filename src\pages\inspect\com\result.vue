<template>
  <!-- 结果 -->
  <view class="result">
    <!-- 成功图标 -->
    <image class="icon" v-if="isSuc" src="/static/inspection/suc.png" />
    <image class="icon" v-else src="/static/inspection/err.png" />

    <!-- 文案 -->
    <view class="text">
      <text class="suc" v-if="isSuc">预约成功</text>
      <text class="err" v-else>支付失败</text>
    </view>

    <!-- 其他文案 -->
    <view class="other">
      <!-- 成功 -->
      <block v-if="isSuc">
        <text>您已成功预约</text>
        <text>{{ time }}的检{{ isLis ? '验' : '查' }}</text>
      </block>

      <!-- 失败 -->
      <block v-else>
        <text>抱歉支付失败</text>
        <text>请检查网络是否正常，或者您的余额是否不足</text>
      </block>
    </view>

    <!-- 返回 -->
    <view class="but">
      <button class="back" @click="back">返回</button>
      <button class="reload" v-if="!isSuc">再次支付</button>
    </view>
  </view>
</template>

<script>
export default {
  name: 'Result',
  props: {
    // 是否成功
    isSuc: {
      type: Boolean,
      default: true,
    },
    time: String,
    isLis: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    back() {
      let length = getCurrentPages().length;
      if (length == 1) {
        window.history.back();
      } else {
        uni.navigateBack();
      }
    },
    reload() {},
  },
};
</script>

<style lang="scss" scoped>
.result {
  background: #fff;
  border-radius: 8rpx;
  @include flex(lr);
  flex-direction: column;
  padding: 48rpx 0;

  .icon {
    width: 104rpx;
    height: 104rpx;
    border-radius: 50%;
  }

  .text {
    font-size: 36rpx;
    font-weight: bold;
    line-height: 50rpx;
    margin-top: 20rpx;

    .suc {
      @include font_theme;
    }

    .err {
      color: #ff5050;
    }
  }

  .other {
    font-size: 28rpx;
    color: #333;
    text-align: center;
    line-height: 40rpx;
    margin-top: 24rpx;

    text {
      display: block;
    }
  }

  .but {
    margin-top: 32rpx;
    @include flex;

    button {
      width: 248rpx;
      height: 68rpx;
      border-radius: 34rpx;
      font-size: 32rpx;
      @include flex;
      margin: 0 16rpx;

      &.reload {
        background: #ff5050;
        color: #fff;
      }

      &.back {
        @include border_theme;
        @include font_theme;
      }
    }
  }
}
</style>
