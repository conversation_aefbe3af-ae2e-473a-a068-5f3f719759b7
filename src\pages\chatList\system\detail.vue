<template>
  <view class="system_detail">
    <!-- 标题 -->
    <view class="title">{{ planName }}</view>
    <!-- 内容 -->
    <view class="cont">{{ sendContent }}</view>
    <!-- 图片列表 -->
    <view class="img_list">
      <image
        mode="widthFix"
        :src="item"
        class="img_item"
        v-for="(item, index) in sendImg"
        :key="index"
        @click="prevImg(item)"
      />
    </view>
  </view>
</template>

<script>
import { getSendContentBySendId } from '@/api/base';

import myJsTools from '@/common/js/myJsTools.js';

export default {
  data() {
    return {
      id: '',
      // 标题
      planName: '',
      // 内容
      sendContent: '',
      // 图片
      sendImg: [],
    };
  },
  onLoad(e) {
    this.id = e.id;
    this.getDetail();
  },
  methods: {
    // 预览原图
    prevImg(url) {
      myJsTools.previewImg(url);
    },
    // 获取详情
    getDetail() {
      getSendContentBySendId(this.id).then((res) => {
        let { planName, sendContent, sendImg } = res.data;
        this.planName = planName;
        this.sendContent = sendContent;
        let arr = [];
        if (sendImg) {
          sendImg = JSON.parse(sendImg);
          // 循环获取
          sendImg.forEach((v) => {
            myJsTools.downAndSaveImg(v, (url) => {
              url = url.split('?')[0];
              arr.push(url);
            });
          });
          this.sendImg = arr;
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.system_detail {
  width: 100%;
  padding: 32upx;
  box-sizing: border-box;

  .title {
    font-size: 32upx;
    color: #333;
    text-align: center;
    font-weight: bold;
    line-height: 60upx;
    box-sizing: border-box;
    padding-bottom: 20upx;
  }

  .cont {
    font-size: 28upx;
    color: #666;
    line-height: 42upx;
    box-sizing: border-box;
  }

  .img_list {
    width: 100%;
    padding-top: 32upx;
    box-sizing: border-box;

    .img_item {
      width: 100%;
      display: block;
      margin-bottom: 32upx;
      box-sizing: border-box;
    }
  }
}
</style>
