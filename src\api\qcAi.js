import http from '../common/request/request.js'

/**
 * 智能问诊 - 创建会话
 * @param {Object} param 参数对象
 * @returns {Promise} 请求结果
 */
export function createSession(param = {}) {
    return http({
        url: 'basic/szAiConsultation/createSession',
        param: {
            userId: param.userId || '',
            title: param.title || '智能问诊对话',
            sessionType: param.sessionType || '',
        },
        method: 'post',
    })
}

/**
 * 发送群组消息
 * @param {String} messageData 消息数据，JSON字符串格式
 * @returns {Promise} 请求结果
 */
export function sendGroupMessage(messageData) {
    return http({
        url: 'chat/hxMessages/sendGroupMessage',
        param: messageData,
        method: 'post',
        contentType: 'application/json;charset=UTF-8',
    })
}

/**
 * 智能问诊 - AI回答接口
 * @param {Object} param 参数对象，包含模型ID和消息内容等
 * @returns {Promise} 请求结果
 */
export function getAiCompletions(param = {}) {
    // 构建符合OpenAI接口的请求格式
    const data = {
        model: param.model || '9b6d9b73-d342-489d-81d3-a7f5b58cd0be', // 允许传入模型ID，默认使用原模型
        messages: [{
            role: 'user',
            content: param.content || '',
        }, ],
        temperature: 0,
        stream: false,
    }

    // 如果有历史消息，添加到messages数组
    if (param.messages && Array.isArray(param.messages)) {
        data.messages = param.messages
    }

    return http({
        url: '/api/v2/assistant/chat/completions',
        param: data,
        method: 'post',
    })
}

/**
 * 智能问诊 - 存储聊天消息
 * @param {Object} param 参数对象
 * @returns {Promise} 请求结果
 */
export function saveUserMessage(param = {}) {
    // 构建符合后端接口的请求格式
    const data = {
        userId: param.userId || '',
        sessionId: param.sessionId || '',
        requireAi: param.isAi ? '1' : '2', // 1: AI消息，2: 用户消息
        messages: [],
        content: param.content || '',
        patientId: param.patientId || '',
    }

    return http({
        url: 'basic/szAiConsultation/completions',
        param: data,
        method: 'post',
    })
}

/**
 * 智能问诊 - 获取会话聊天记录
 * @param {Object} param 参数对象，包含sessionId
 * @returns {Promise} 请求结果
 */
export function getChatList(param = {}) {
    return http({
        url: 'basic/szAiConsultation/chatList',
        param,
        method: 'post',
    })
}

/**
 * 智能问诊 - 根据userId获取历史会话列表
 * @param {Object} param 参数对象，包含userId
 * @returns {Promise} 请求结果
 */
export function getSessionList(param = {}) {
    return http({
        url: 'basic/szAiConsultation/sessionList',
        param: {
            userId: param.userId || '',
            page: param.page || 1,
            pageSize: param.pageSize || 20,
            sessionType: param.sessionType || '',
        },
        method: 'post',
    })
}

/**
 * 智能问诊 - 删除会话
 * @param {Object} param 参数对象，包含sessionId和userId
 * @returns {Promise} 请求结果
 */
export function deleteSession(param = {}) {
    return http({
        url: 'basic/szAiConsultation/deleteSession',
        param: {
            sessionId: param.sessionId || '',
            userId: param.userId || '',
        },
        method: 'post',
    })
}

/**
 * 智能问诊 - 调用工作流
 * @param {Object} param 参数对象
 * @returns {Promise} 请求结果
 */
export function invokeWorkflow(param = {}) {
    return http({
        url: '/api/v2/workflow/invoke',
        param: {
            ...param,
            session_id: param.sessionId,
            workflow_id: param.workflow_id || '4d089987-be14-4c01-b188-489f00357e66',
            stream: param.stream || false,
        },
        method: 'post',
    })
}

/**
 * 智能问诊 - 停止工作流
 * @param {Object} param 参数对象
 * @returns {Promise} 请求结果
 */
export function stopWorkflow(param = {}) {
    return http({
        url: '/api/v2/workflow/stop',
        param: {
            session_id: param.sessionId,
            workflow_id: param.workflow_id || '',
        },
        method: 'post',
    })
}

/**
 * 智能问诊 - 同步健康记录
 * @param {Object} param 参数对象
 * @returns {Promise} 请求结果
 */
export function syncHealthRecord(param = {}) {
    return http({
        url: 'basic/szAiReport/syncHealthRecord',
        param,
        method: 'post',
    })
}
export function getInterrogationSummaryById(param = {}) {
    return http({
        url: 'business/proreceive/getInterrogationSummaryById',
        param,
        method: 'post',
    })
}
export function getAiReportInfo(param = {}) {
    return http({
        url: 'basic/szAiReport/info',
        param,
        method: 'post',
    })
}

/**
 * 获取AI医生小助手推送记录
 * @param {Object} param 参数对象，包含docId(用户ID)、page、limit
 * @returns {Promise} 请求结果
 */
export function getSysPushResultRecord(param = {}) {
    return http({
        url: 'basic/pushResultRecord/getSysPushResultRecordAi',
        param,
        method: 'post',
    })
}

// 根据医生ID查询营养师ID 是否有关联
export function getDoctorInfoByDocId(param = {}) {
    return http({
        url: 'basic/doctor/getDoctorInfoByDocId',
        param,
        method: 'post',
    })
}

// 请求工作流-后台生成营养评估报告
export function generateNutritionReport(param = {}) {
    return http({
        url: 'basic/ai/nutritionReport/generate',
        param,
        method: 'post',
    })
}

// 请求后台 -后台调用大模型实现流式输出
export function getAiReportByAiWorkFlow(param = {}) {
    return http({
        url: 'basic/ai/report/dailyLbStreamGenerate',
        param,
        method: 'post',
    })
}

// 请求工作流-后台生成营养评估报告
export function nutritionStreamGenerate(param = {}) {
    return http({
        url: 'basic/ai/report/nutritionStreamGenerate',
        param,
        method: 'post',
    })
}
// 请求工作流-后台生成每日提醒报告
export function dailyReminderLbGenerate(param = {}) {
    return http({
        url: 'basic/ai/report/dailyReminderLbGenerate',
        param,
        method: 'post',
        isNeedToStringParam: false
    })
}
// 请求工作流-后台生成每周循环报告
export function weeklyCycleGenerate(param = {}) {
    return http({
        url: 'basic/ai/report/weeklyCycleLbGenerate',
        param,
        method: 'post',
        isNeedToStringParam: false
    })
}
// 请求工作流-后台生成肠道功能量表报告
export function gutFunctionLbLbGenerate(param = {}) {
    return http({
        url: 'basic/ai/report/gutFunctionLbLbGenerate',
        param,
        method: 'post',
        isNeedToStringParam: false
    })
}
// 是否确认用药
export function isConfirmDrug(param = {}) {
    return http({
        url: 'business/patientMedicationPushLog/isConfirmDrug',
        param,
        method: 'post',
    })
}