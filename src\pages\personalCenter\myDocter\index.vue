<template>
  <view class="my_docter">
    <!-- 顶部搜索 -->
    <!--    <view class="docter_search">-->
    <!--      &lt;!&ndash; 搜索 &ndash;&gt;-->
    <!--      <uni-icons type="search" size="20" color="#C1C1C1" />-->
    <!--      &lt;!&ndash; 输入框 &ndash;&gt;-->
    <!--      <input-->
    <!--        type="text"-->
    <!--        v-model="keyworld"-->
    <!--        placeholder-style="color: #c1c1c1;"-->
    <!--        placeholder="搜索医生"-->
    <!--        confirm-type="search"-->
    <!--        @confirm="search"-->
    <!--      />-->
    <!--      &lt;!&ndash; 清除 &ndash;&gt;-->
    <!--      <uni-icons-->
    <!--        type="clear"-->
    <!--        v-show="keyworld"-->
    <!--        @click="clear"-->
    <!--        size="20"-->
    <!--        color="#C1C1C1"-->
    <!--      />-->
    <!--    </view>-->
    <!-- 列表 -->
    <view class="doc_list" v-if="list.length">
      <!-- 组件 -->
      <uni-indexed-list :options="list" :showSelect="false" @click="bindClick"></uni-indexed-list>
    </view>

    <!-- 空列表 -->
    <EMPT v-else title="暂无相关医生" />
  </view>
</template>

<script>
// 获取汉字首字母
import makePy from "@/utils/str.js";
import { getMyDocList, getMyBangDocList } from "@/api/base.js";
import myJsTools from "@/common/js/myJsTools.js";
// 空组件
import EMPT from "./empt/empt.vue";

export default {
  components: {
    EMPT,
  },
  data () {
    return {
      // 就诊人id
      patientId: "",
      keyworld: "",
      old_list: [],
      list: [],
    };
  },
  onLoad (e) {
    this.patientId = e.patientId;
    this.patientImg = e.patientImg;
    this.pageType = e.pageType;
    this.getDocList();
  },
  methods: {
    bindClick (e) {
      console.log(e, 'e')
      this.toDoc(e);
      return
      let { docId, docImg, hosId } = e;
      uni.setStorageSync("hosId", hosId);
      let obj = {
        docId,
        patientId: this.patientId,
        patientImg: this.patientImg,
        docImg,
      };
      uni.navigateTo({
        url: "/pages/chatList/hisChatList?param=" + JSON.stringify(obj),
      });
    },
    // 去医生主页
    toDoc (item) {
      // 医院id
      uni.setStorageSync("hosId", item.hosId);
      uni.navigateTo({
        url: "/pages/register/docHomePage/index?docId=" + item.docId,
      });
    },
    // 搜索
    search () {
      if (this.keyworld == "") {
        // 重置
        this.list = JSON.parse(this.old_list);
        return;
      }
      let str = this.keyworld;
      let arr = [];

      this.list.forEach((v) => {
        let obj = {};
        obj.letter = v.letter;
        let data = [];
        v.data.forEach((k) => {
          if (k.docName.indexOf(str) > -1) {
            data.push(k);
          }
        });
        obj.data = data;
        if (data.length) arr.push(obj);
      });
      this.list = arr;
    },
    clear () {
      this.keyworld = "";
      // 恢复搜索前状态
      this.list = JSON.parse(this.old_list);
    },
    async getDocList () {
      let res;
      if (this.pageType === "myDocter") {
        res = await getMyBangDocList({ patientId: this.patientId });
      } else {
        res = await getMyDocList({ patientId: this.patientId });
      }

      // 如果数组为空
      if (!res.data.docList.length) return;
      // 索引
      let label = [];
      let list = res.data.docList;

      // 第一次循环 获取首字母
      list.forEach((v, index) => {
        // 首字母
        let key = makePy(v.docName);
        console.log(key, 'key')
        // 如果不存在
        if (!label.includes(key)) {
          label.push(key);
        }
        v.label = key;
        // 获取头像地址
        if (v.docImg) {
          myJsTools.downAndSaveImg(v.docImg, (url) => {
            v.docImgUrl = url;
          });
        }
      });
      // 排序 a - z
      label.sort((a, b) => a.charCodeAt(0) - b.charCodeAt(0));
      // 医生姓名排序
      list.sort((a, b) => a.docName.charCodeAt(0) > b.docName.charCodeAt(0));
      // 最终数组
      let arr = [];
      // 循环首字母
      label.forEach((v) => {
        // 当前首字母下所有列表
        let data = [];
        let obj = {
          letter: v,
        };
        console.log(v)
        // 循环医生列表
        list.forEach((k) => {
          console.log(k)
          if (k.label == v) {
            data.push(k);
          }
        });
        obj.data = data;
        arr.push(obj);
      });
      this.list = arr;
      console.log(this.list)
      setTimeout(() => {
        // 旧的列表
        this.old_list = JSON.stringify(arr);
      }, 3000);
    },
  },
};
</script>
<style scoped>
.doc_list /deep/ .uni-indexed-list__title-wrapper {
  background-color: #f0f2fc !important;
  color: #333333;
  font-size: 14px;
  font-weight: bold;
  margin-top: 5px;
}
.doc_list /deep/ .uni-indexed-list__list {
  border-radius: 5px;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px 1px 4px rgba(121, 72, 234, 0.15);
  margin-top: 5px;
}

.doc_list /deep/ .item_head {
  border-radius: 100px;
}
</style>

<style lang="scss" scoped>
.my_docter {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: stretch;

  .docter_search {
    @include flex(left);
    padding: 0 32upx;
    height: 88upx;
    background-color: #fff;
    box-sizing: border-box;
    flex: none;

    /deep/ .uni-icons {
      flex: none;
    }

    input {
      flex: 1;
      font-size: 28upx;
      padding-left: 20upx;
    }
  }

  // 列表
  .doc_list {
    width: 100%;
    flex: 1;
    position: relative;
    .uni-indexed-list {
      background-color: #f0f2fc;
      padding: 20rpx 32rpx;
      box-sizing: border-box;
      :deep(.uni-indexed-list__title-wrapper) {
        background-color: #f0f2fc !important;
      }
    }
  }
}
</style>
