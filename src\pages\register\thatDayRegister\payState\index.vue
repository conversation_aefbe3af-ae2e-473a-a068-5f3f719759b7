<template>
  <!-- 支付结果 -->
  <view>
    <view class="page-container">
      <view v-if="flag" class="container_box">
        <view class="img_box">
          <image src="/static/images/index/succeed.png"></image>
          <text v-if="type == 1">支付成功，请等待医生接诊通知</text>
          <text v-else>支付成功，请记得在预约时间签到</text>

          <text class="block t">为便于医生与您沟通</text>
          <text class="block">您可以在接诊前上传病历信息</text>
        </view>
        <view class="btn">
          <!-- 如果支付之前填写过 则不展示 -->
          <view class="cancel" @click="toPath" v-if="isShowDisease"
            >完善病历资料</view
          >
          <view @click="goHome" class="home">返回主页</view>
        </view>
      </view>
      <view v-else class="container_box">
        <view class="img_box">
          <image src="/static/images/index/defeated.png"></image>
          <text
            >抱歉，您支付失败，请检验网络是否正常，或者您的余额是否充足</text
          >
        </view>
        <view class="btn">
          <view class="cancel" @click="onCancel">取消支付</view>
          <view @click="getEnsure" class="again">再次支付</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      pageTitle: "",
      flag: "",
      type: 1,
      prId: "",
      // 是否展示按钮
      isShowDisease: false,
    };
  },
  onLoad(option) {
    this.prId = uni.getStorageSync("prId");
    let flag = option.flag;
    this.isShowDisease = option.isShowDisease ? true : false;

    this.type = option.type || 1;
    if (flag == 1) {
      let infoDetail = uni.getStorageSync("infoDetail");
      this.pageTitle = "支付成功";
      this.flag = true;
      let { docId, docImgCopy: docImg, docName } = infoDetail;
      let para = {
        patientId: uni.getStorageSync("patientId"),
        docId,
        docImg,
        docName,
      };
      this.$store.commit("setEmptyList", para);
    } else {
      this.pageTitle = "支付失败";
      this.flag = false;
    }
    uni.removeStorageSync("prId");
  },
  methods: {
    // 查看订单详情
    onExamine() {
      let { docId, deptId, regId } = uni.getStorageSync("infoDetail");

      let obj = {
        docId,
        deptId,
        regId,
      };

      uni.navigateTo({
        url:
          "/pages/register/thatDayRegister/examineOrder/index?orderQueryInfo=" +
          JSON.stringify(obj),
      });
    },
    // 返回首页
    goHome() {
      uni.reLaunch({
        url: "/pages/index/index",
      });
    },
    // 取消支付
    onCancel() {
      uni.navigateTo({
        url: "/pages/register/docHomePage/index",
      });
    },
    // 再次支付
    getEnsure() {
      uni.navigateTo({
        url:
          "/pages/register/thatDayRegister/diseaseDetail/index?prId=" +
          this.prId,
      });
    },
    // 去填写病情资料
    toPath() {
      uni.redirectTo({
        url:
          "/pages/register/thatDayRegister/diseaseDetail/index?prId=" +
          this.prId,
      });
    },
  },
};
</script>

<style scoped lang="scss">
.page-container {
  box-sizing: border-box;
  overflow-y: auto;
}

.container_box {
  background: #f5f5f5;
  height: 100vh;
  box-sizing: border-box;
}

.block {
  line-height: 40rpx;
  font-size: 28rpx;
  @include font_theme;

  &.t {
    margin-top: 38rpx;
  }
}

.img_box {
  width: 500rpx;
  margin: 0 auto;
  display: flex;
  text-align: center;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #333333;
  padding-top: 58rpx;
  margin-bottom: 160rpx;
}

.img_box image {
  width: 386rpx;
  height: 324rpx;
  margin-bottom: 40rpx;
}

.btn {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 40rpx;
}

.btn view {
  flex: 1;
  height: 88rpx;
  border-radius: 46rpx;
  color: #ffffff;
  font-weight: 600;
  font-size: 32rpx;
  @include flex;
  box-sizing: border-box;
}

.btn .home {
  @include bg_theme;
}

.btn .cancel {
  @include border_theme;
  @include font_theme;
  background: transparent;
  margin-right: 42rpx;
}

.btn .again {
  background: #ff5050;
}
</style>
