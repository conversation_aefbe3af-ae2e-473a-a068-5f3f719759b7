import { invokeWorkflow, stopWorkflow } from '@/api/qcAi.js'
import { saveOriginal, insertRecord } from '@/api/user.js'
import { fetchEventSource } from '@microsoft/fetch-event-source'
import urlConfig from '@/common/request/config.js'
let baseUrl = urlConfig.serverUrl
export default {
    namespaced: true,
    state: {
        workflowSessionId: '',
        isWorkflowRunning: false,
        message_id: '',
        input_schema: '',
        lastNodeId: '',
        streamContent: '',
        isFinish: true,
        streams: [],
        messageList: [],
        isAnimating: false,
        animationSpeed: 30,
        currentNodeId: '',
        currentMessageId: '',
        messageQueue: [],
        isProcessingQueue: false,
        mine_system_session_id: '',
    },

    mutations: {
        SET_WORKFLOW_SESSION(state, sessionId) {
            state.workflowSessionId = sessionId
        },
        SET_WORKFLOW_RUNNING(state, status) {
            state.isWorkflowRunning = status
        },
        SET_MESSAGE_ID(state, id) {
            state.message_id = id
        },
        SET_INPUT_SCHEMA(state, schema) {
            state.input_schema = schema
        },
        SET_LAST_NODE_ID(state, nodeId) {
            state.lastNodeId = nodeId
        },
        ADD_STREAM_CONTENT(state, data) {
            state.currentNodeId = data.node_id
            state.currentMessageId = data.message_id

            const message = state.messageList.find(item => item.node_id === data.node_id)
            if (!message) {
                state.messageList.push({
                    content: '',
                    sender: 'ai',
                    timestamp: new Date().toLocaleString(),
                    node_id: data.node_id,
                    message_id: data.message_id,
                })
            }
            console.log(state.messageList, 'state.messageList');
        },
        ADD_TO_MESSAGE_QUEUE(state, messageChunk) {
            state.messageQueue.push(messageChunk)
        },
        ADD_CHAR_TO_MESSAGE(state, { nodeId, char }) {
            const message = state.messageList.find(item => item.node_id === nodeId)
            if (message) {
                message.content += char
            }
        },
        RESET_STREAM_CONTENT(state) {
            state.streamContent = ''
        },
        SET_FINISH(state, status) {
            state.isFinish = status
        },
        ADD_STREAM(state, stream) {
            state.streams.push(stream)
        },
        ADD_MESSAGE_LIST(state, message) {
            state.messageList.push(message)
        },
        ADD_CHAR_TO_STREAM(state, char) {
            state.streamContent += char
        },
        SET_ANIMATING(state, status) {
            state.isAnimating = status
        },
        SET_PROCESSING_QUEUE(state, status) {
            state.isProcessingQueue = status
        },
        SET_ANIMATION_SPEED(state, speed) {
            state.animationSpeed = speed
        },
        SET_MINE_SYSTEM_SESSION_ID(state, sessionId) {
            state.mine_system_session_id = sessionId
        },
    },

    actions: {
        initMessageList({ commit, state }, payload) {
            state.messageList = []
        },
        //  初始化
        async initAi({ commit, state }, payload) {
            state.workflowSessionId = ''
            state.isWorkflowRunning = false
            state.message_id = ''
            state.input_schema = ''
            state.lastNodeId = ''
            state.messageList = []
            state.messageQueue = []
            state.streams = []
            state.streamContent = ''
            state.isFinish = true
            state.isAnimating = false
            state.isProcessingQueue = false
            state.animationSpeed = 30
            state.mine_system_session_id = ''
        },
        // 创建营养评估表报告 - 工作流方式
        async createReportByAiWorkFlow({ commit, state }, payload) {
            const {
                patientInfo,
                docId,
                patientId,
                projectId,
                valuesData,
                callback,
                workflow_id,
                szAiReportVo
            } = payload

            commit('RESET_STREAM_CONTENT')
            commit('SET_FINISH', false)

            try {
                uni.showLoading({
                    title: '生成报告中...',
                    mask: true,
                })

                // 使用fetchEventSource发起工作流执行（流式方式）
                await new Promise((resolve, reject) => {
                    // 构建第一步请求数据
                    const requestBody = {
                        workflow_id: workflow_id,
                        stream: true
                    }

                    // 使用fetchEventSource进行流式请求
                    fetchEventSource('/api/v2/workflow/invoke', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            Accept: '*/*',
                        },
                        openWhenHidden: true, // 允许后台保持连接
                        body: JSON.stringify(requestBody),

                        async onopen(response) {
                            console.log('流连接已打开:', response)
                            if (response.status === 200) {
                                return
                            }
                            throw new Error('连接失败')
                        },

                        async onmessage(event) {
                            console.log(event, 'event');

                            if (event.data === '[DONE]') {
                                resolve()
                                return
                            }

                            try {
                                const data = JSON.parse(event.data)
                                console.log('工作流初始化消息:', data)

                                // 处理工作流返回的事件
                                if (data.data.event === 'input') {
                                    commit('SET_MESSAGE_ID', data.data.message_id)
                                    commit('SET_INPUT_SCHEMA', data.data.input_schema)
                                    commit('SET_LAST_NODE_ID', data.data.node_id)
                                    resolve()
                                }
                                // 设置工作流会话ID
                                if (data.session_id) {
                                    commit('SET_WORKFLOW_SESSION', data.session_id)
                                    commit('SET_WORKFLOW_RUNNING', true)
                                }
                            } catch (error) {
                                console.error('处理消息错误:', error)
                            }
                        },

                        onerror(error) {
                            console.error('流数据错误:', error)
                            reject(error)
                        },
                    })
                });

                // 只有在获取到工作流会话ID后才继续
                if (state.workflowSessionId) {
                    // 第二步：使用流式API获取营养报告
                    await this.dispatch('ai/fetchStreamReport', {
                        valuesData,
                        workflow_id,
                        docId,
                        szAiReportVo
                    })

                    // 报告生成完成后的处理
                    if (state.streamContent) {
                        // 保存营养评估表报告
                        // const res = await saveOriginal({
                        //     reportType: '6',
                        //     userId: uni.getStorageSync('userId'),
                        //     docId: docId,
                        //     patientId: patientId,
                        //     reportContent: state.streamContent,
                        //     ifNeedDocAudit: 0,
                        //     projectId: projectId,
                        //     patientName: patientInfo.patientName,
                        // })

                        // // 插入通知记录
                        // await insertRecord({
                        //     wxTitle: '您的营养评估报告已生成，请查看',
                        //     receiverId: uni.getStorageSync('userId'),
                        //     patientId: patientId,
                        //     ext: JSON.stringify({
                        //         reportId: res.data.pkId,
                        //     }),
                        //     hxMessage: res.data.pkId,
                        // })

                        // 调用回调函数
                        if (typeof callback === 'function') {
                            callback()
                        }
                    }
                }

                uni.hideLoading()
            } catch (error) {
                console.error('营养评估表报告生成异常:', error)
                uni.hideLoading()
                commit('SET_FINISH', true)
            } finally {
                commit('SET_FINISH', true)
                    // 停止工作流
                if (state.workflowSessionId) {
                    try {
                        await stopWorkflow({
                            sessionId: state.workflowSessionId,
                            workflow_id: workflow_id,
                        })
                    } catch (err) {
                        console.error('停止工作流失败:', err)
                    }
                }
            }
        },

        // 使用流式API获取营养报告
        async fetchStreamReport({ commit, state, dispatch }, { valuesData, workflow_id, szAiReportVo }) {
            return new Promise((resolve, reject) => {
                // 构建请求数据
                const requestBody = {
                    session_id: state.workflowSessionId,
                    input: {
                        [state.lastNodeId]: {
                            ...valuesData
                        }
                    },
                    message_id: state.message_id,
                    temperature: 0,
                    stream: true,
                    workflow_id: workflow_id,
                    szAiReportVo: {
                        ...(szAiReportVo || {}),
                        sessionId: state.mine_system_session_id
                    }
                }
                commit('ADD_STREAM_CONTENT', {
                        content: '',
                        sender: 'ai',
                        timestamp: new Date().toLocaleString(),
                        node_id: state.messageList.length,
                        message_id: state.message_id,
                        index: state.messageList.length
                    })
                    // 使用fetchEventSource进行流式请求 /api/v2/workflow/invoke
                fetchEventSource(baseUrl + '/basic/ai/report/nutritionStreamGenerate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        Accept: '*/*',
                        Authorization: uni.getStorageSync('proPfInfo').token || '',
                        hosId: uni.getStorageSync('hosId') || '',
                        firmId: urlConfig.firmId,
                        version: urlConfig.version,
                        clientType: urlConfig.clientType,
                    },
                    openWhenHidden: true, // 允许后台保持连接
                    body: JSON.stringify(requestBody),

                    async onopen(response) {
                        console.log('流连接已打开:', response)
                        if (response.status === 200) {
                            return
                        }
                        throw new Error('连接失败')
                    },

                    async onmessage(event) {
                        console.log(event, 'event');

                        if (event.data === '[DONE]') {
                            resolve()
                            return
                        }

                        try {
                            const data = JSON.parse(event.data)
                            console.log('收到消息:', data)
                            if (data.data.event === 'close' && data.data.output_schema && data.data.output_schema.message && data.data.output_schema.message.code === 500) {
                                commit('SET_FINISH', true)
                                reject(data.data.output_schema.message.message)
                                return
                            }
                            if (data.data.event === 'stream_msg' && data.data.status === 'stream') {
                                console.log(data.data.output_schema.message, 'data.data.output_schema.message');
                                // 添加消息到队列中
                                commit('ADD_TO_MESSAGE_QUEUE', {
                                    text: data.data.output_schema.message,
                                    node_id: state.messageList.length - 1
                                })

                                // 立即为streamContent添加内容
                                for (let char of data.data.output_schema.message) {
                                    commit('ADD_CHAR_TO_STREAM', char)
                                }

                                // 启动消息队列处理
                                dispatch('processMessageQueue')

                                commit('ADD_STREAM', data)
                            }
                            if (data.data.event === 'close' && data.data.status === 'end') {
                                commit('SET_FINISH', true)
                                resolve()
                            }
                        } catch (error) {
                            console.error('处理消息错误:', error)
                        }
                    },

                    onerror(error) {
                        console.error('流数据错误:', error)
                        reject(error)
                    },
                })
            })
        },

        // 随机营养评估报告 (保留原有功能，可能在其他地方使用)
        async createSJReportByAiWorkFlow({ commit, state }, payload) {
            // 这里可以实现类似的随机营养评估报告功能
            // 如果当前不需要，可以先留空或实现基本框架
        },

        // 处理消息队列
        async processMessageQueue({ commit, state, dispatch }) {
            // 如果已经在处理队列，则不需要再启动
            if (state.isProcessingQueue) return

            commit('SET_PROCESSING_QUEUE', true)
            commit('SET_ANIMATING', true)

            try {
                while (state.messageQueue.length > 0) {
                    // 从队列中取出一条消息
                    const messageChunk = state.messageQueue[0]

                    // 逐字符播放动画
                    for (let char of messageChunk.text) {
                        commit('ADD_CHAR_TO_MESSAGE', {
                            nodeId: messageChunk.node_id,
                            char: char
                        })
                        await new Promise(resolve => setTimeout(resolve, state.animationSpeed))
                    }

                    // 处理完成后从队列中移除
                    state.messageQueue.shift()
                }
            } catch (error) {
                console.error('处理消息队列错误:', error)
            } finally {
                commit('SET_PROCESSING_QUEUE', false)
                commit('SET_ANIMATING', false)
            }
        },
    },

    getters: {
        getStreamContent: state => state.streamContent,
        isFinished: state => state.isFinish,
    }
}