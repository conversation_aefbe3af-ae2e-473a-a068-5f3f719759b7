# Loading 加载动画组件

一个灵活且美观的加载动画组件，提供多种动画样式和自定义选项。

## 特性

- 多种动画类型：圆形旋转、点跳动、旋转
- 可自定义颜色、大小、文字
- 支持全屏和内联两种显示方式
- 全局方法调用和组件调用两种使用方式

## 使用方法

### 组件用法

在页面中直接使用组件：

```vue
<template>
  <view>
    <hz-loading 
      :show="isLoading" 
      type="circle" 
      text="数据加载中..." 
      :fullscreen="false"
      :size="40"
      color="#1989fa"
    />
    
    <!-- 页面其他内容 -->
  </view>
</template>

<script>
export default {
  data() {
    return {
      isLoading: true
    }
  },
  onLoad() {
    // 模拟加载完成
    setTimeout(() => {
      this.isLoading = false;
    }, 2000);
  }
}
</script>
```

### 全局方法用法

在任意JS代码中调用：

```javascript
// 显示加载
const loading = this.$loading.show({
  type: 'spinner',
  text: '正在提交...',
  backgroundColor: 'rgba(0, 0, 0, 0.7)',
  color: '#ffffff'
});

// 执行一些异步操作
someAsyncTask().then(() => {
  // 关闭加载
  loading.close();
}).catch(err => {
  // 出错时也关闭加载
  loading.close();
});
```

### 在异步请求中使用

```javascript
async fetchData() {
  const loading = this.$loading.show({ text: '加载中...' });
  
  try {
    await this.api.getData();
    // 成功处理
  } catch (error) {
    // 错误处理
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    });
  } finally {
    loading.close();
  }
}
```

## API

### Props

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| show | 是否显示加载动画 | Boolean | true |
| type | 加载动画类型，可选值：circle、dots、spinner | String | circle |
| fullscreen | 是否全屏显示 | Boolean | false |
| backgroundColor | 背景颜色 | String | rgba(0, 0, 0, 0.6) |
| color | 加载动画颜色 | String | #ffffff |
| size | 加载动画大小(px) | Number | 36 |
| text | 加载文字内容 | String | 加载中... |
| textColor | 加载文字颜色 | String | #ffffff |
| textSize | 加载文字大小(px) | Number | 14 |

### 全局方法

#### this.$loading.show(options)

显示全屏加载动画

- options: 配置对象，支持所有 Props 选项
- 返回值: { close: Function } 关闭加载的方法 
