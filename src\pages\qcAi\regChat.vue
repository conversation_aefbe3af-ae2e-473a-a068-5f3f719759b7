<template>
  <view class="ai-chat-container">
    <!-- 聊天内容区域 -->
    <scroll-view scroll-y class="chat-content" :scroll-top="scrollTop" :scroll-with-animation="true" @scrolltoupper="loadMoreMessages" ref="chatContent">
      <view class="chat-list">
        <view v-for="(item, index) in chatMessages" :key="index" class="chat-item" :class="{'chat-item-ai': item.isAi, 'chat-item-user': !item.isAi}">
          <view class="avatar">
            <image :src="item.isAi ? '/static/ai/ai-logo.png' : '/static/ai/patient.png'"></image>
          </view>
          <view class="message">
            <view class="msg-content" v-html="item.content"></view>
            <view class="msg-time">{{ item.time }}</view>
          </view>
        </view>

        <!-- AI正在思考的加载指示器 -->
        <view v-if="isAiThinking" class="chat-item chat-item-ai">
          <view class="avatar">
            <image src="/static/images/ai-logo.png"></image>
          </view>
          <view class="message">
            <view class="msg-content">
              AI正在思考...
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 底部输入区域 -->
    <view class="chat-footer" v-if="!isFinish">
      <!-- 选择就诊人区域 - 初始显示 -->
      <view>
        <!-- 底部快捷按钮 -->
        <view class="bottom-actions">
          <view class="bottom-title">
            <text>AI小助手</text>
          </view>
          <scroll-view scroll-x class="quick-actions-scroll" show-scrollbar="false">
            <view class="quick-actions">
              <view v-for="(item,index) in quickActions" :key="index" class="quick-btn" @click="goToSmartClinic(item)">
                <text>{{item.name}}</text>
              </view>
            </view>
          </scroll-view>
        </view>
        <!-- 输入区域 -->
        <view class="input-area">
          <view style="flex:1;" v-if="lastInputSchema && lastInputSchema.input_type === 'form_input' && lastInputSchema.value && lastInputSchema.value.length > 0">
            <view v-for="(item,index) in lastInputSchema.value" :key="index" class="form-item">
              <text class="form-item-label">{{item.label}}</text>
              <input type="text" v-model="item.value" :disabled="isSendDisabled" placeholder="请输入您的问题" confirm-type="send" @confirm="sendMessage" />
            </view>
          </view>
          <view style="flex:1;" v-else>
            <input type="text" v-model="inputMessage" :disabled="isSendDisabled" placeholder="请输入您的问题" confirm-type="send" @confirm="sendMessage" />
          </view>
          <view class="send-btn" @click="sendMessage">
            <u-icon name="arrow-right" color="#fff" size="22"></u-icon>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import {
  getPatientList,
} from "@/api/user.js";
import {
  getAiCompletions,
  invokeWorkflow,
  stopWorkflow,
  saveUserMessage
} from "@/api/aiConsultation.js";
import {
  endRegChat
} from "@/utils/ai.js";
import {getPatientChat2} from "../../api/order";
export default {
  data () {
    return {
      chatMessages: [],
      inputMessage: '',
      scrollTop: 0,
      conversationId: null,
      selectedPatient: null,
      patientList: [

      ],
      isSendDisabled: false,
      quickActions: [
      ],
      type: '',
      isFinish: false,
      // AI workflow API 相关数据
      workflowId: '9a669d21-1749-4150-bb10-a24e702183ab',//'9994046c-7fc9-49d4-8efb-7458fdd4c01b',
      workflowApiUrl: '/api/v2/workflow/invoke',
      sessionId: null,
      messageId: null,
      lastNodeId: null,
      lastInputSchema: {
        input_type: 'dialog_input',
        value: []
      }, // 初始化为默认对象
      isAiThinking: false, // AI是否正在思考
      workflowError: false, // 工作流是否出错
      regId: '',
      userId: '',
      patientId: '',
      dataVal:{}
    };
  },
  async onLoad (options) {
    this.type = options.type
    this.regId = options.regId
    await this.getRegChatInfo()
    this.userId = uni.getStorageSync("userId");
    this.getPatientList()
    if (options.id) {
      this.loadConversation(options.id);
    } else {
      this.startNewConversation();
    }
  },
  methods: {
    async getQuickActions (userMessage) {
      // 构建历史消息数组，用于AI模型上下文
      let messageHistory = [];
      // 只取最近的5条消息作为上下文
      const recentMessages = this.chatMessages
      recentMessages.forEach(msg => {
        messageHistory.push({
          role: msg.isAi ? "assistant" : "user",
          content: msg.content
        });
      });
      let { data } = await getAiCompletions({
        messages: messageHistory,
        content: userMessage,
        model: "13d3455d-6250-41bb-9599-e17310a85fab"
      });
      console.log(data, "===========AI回答提示词接口返回结果");
      this.quickActions = (data.choices[0].message.content || []).map(item => {
        return {
          name: item,
        }
      });
    },
    // AI 工作流相关方法
    async initWorkflow() {
      try {
        this.isAiThinking = true;
        // 发起工作流执行
        const response = await uni.request({
          url: this.workflowApiUrl,
          method: 'POST',
          data:JSON.stringify({
            workflow_id: this.workflowId,
            stream: false // 初始化时不使用流式响应
          }),
          header: {
            'Content-Type': 'application/json'
          }
        });
        console.log(response,'response');

        if (response[1].statusCode === 200 && response[1].data.status_code === 200) {
          const workflowData = response[1].data.data;

          // 保存会话ID
          this.sessionId = workflowData.session_id;

          // 处理工作流返回的事件
          this.handleWorkflowEvents(workflowData.events);
          this.workflowError = false;
        } else {
          console.error('工作流初始化失败:', response);
          this.receiveAiMessage('AI助手服务暂时不可用，请稍后再试');
          this.workflowError = true;
        }
      } catch (error) {
        console.error('工作流API调用出错:', error);
        this.receiveAiMessage('AI助手服务连接失败，请检查网络后重试');
        this.workflowError = true;
      } finally {
        this.isAiThinking = false;
      }
    },

    handleWorkflowEvents(events) {
      if (!events || events.length === 0) return;
      // 提示词
      const guide_question = events.find(event => event.event === 'guide_question');
      if (guide_question) {
        this.quickActions = guide_question.output_schema.message.map(option => ({
          name: option,
          value: option
        }))
      }
      // 找出所有end状态的输出事件
      const endOutputEvents = events.filter(event =>
          (event.event === 'guide_word') &&
          event.status === 'end' &&
          event.output_schema &&
          event.output_schema.message
      );

      // 找出输入事件
      const inputEvents = events.filter(event => event.event === 'input');

      // 找出关闭事件
      const closeEvents = events.filter(event => event.event === 'close');
      console.log(closeEvents,'closeEvents');

      // 如果有多个end状态的输出事件，则每个都显示为独立消息
      if (endOutputEvents.length > 0) {
        endOutputEvents.forEach(event => {
          // 保存AI消息到数据库
          this.saveMessageToDatabase(event.output_schema.message, true);
          this.receiveAiMessage(event.output_schema.message);

          // 处理文件输出
          if (event.output_schema.files && event.output_schema.files.length > 0) {
            this.handleFileOutput(event.output_schema.files);
          }
        });
      }

      // 处理最后一个输入事件（如果有）
      if (inputEvents.length > 0) {
        const lastInputEvent = inputEvents[inputEvents.length - 1];
        this.lastNodeId = lastInputEvent.node_id;
        this.lastInputSchema = lastInputEvent.input_schema;
        this.messageId = lastInputEvent.message_id;

        // 更新UI
        this.updateUIForInputType(lastInputEvent.input_schema);
      }
      console.log(closeEvents,'closeEvents');

      // 处理关闭事件
      if (closeEvents.length > 0) {
        this.finishConversation();
      }
    },

    // 根据输入类型更新UI
    updateUIForInputType(inputSchema) {
      if (!inputSchema) return;

      // 处理不同类型的输入
      if (inputSchema.input_type === 'dialog_input') {
        // 标准对话输入，无需特殊处理
        this.isSendDisabled = false;
      } else if (inputSchema.input_type === 'choose_input') {
        // 选择型输入，生成快捷操作按钮
        this.generateQuickActions(inputSchema.value);
      } else if (inputSchema.input_type === 'form_input') {
        // 表单输入，可能需要特殊UI
        // 目前先作为普通对话处理
        this.isSendDisabled = false;
      }
    },

    // 根据选项生成快捷操作按钮
    generateQuickActions(options) {
      if (!options || !Array.isArray(options)) {
        return;
      }

      // 找到包含选项的字段
      const optionsField = options.find(field => field.options && Array.isArray(field.options));

      if (optionsField && optionsField.options) {
        // 将选项转换为快捷操作按钮
        this.quickActions = optionsField.options.map(option => ({
          name: option.label || option.value,
          value: option.value
        }));
      } else {
        // 如果没有选项，恢复默认快捷操作
        this.quickActions = [
          {
            name: '什么是智能诊室',
          }
        ];
      }
    },

    async sendToWorkflow(userInput) {
      if (!this.sessionId || !this.lastNodeId) {
        console.error('缺少会话信息，无法发送消息');
        return;
      }

      try {
        this.isAiThinking = true;
        // 构建输入数据
        const inputData = {};
        const keys = {};

        // 确保lastInputSchema.value存在并且是数组
        if (this.lastInputSchema && this.lastInputSchema.value && Array.isArray(this.lastInputSchema.value)) {
          this.lastInputSchema.value.forEach(item => {
            if (item && item.key) {
              keys[item.key] = item.value || userInput;
            }
          });
        } else {
          // 如果没有特定的输入结构，使用默认的user_input键
          keys['user_input'] = userInput;
        }

        inputData[this.lastNodeId] = keys;

        // 在界面上添加一个空的AI消息，用于流式更新
        const streamMessageIndex = this.chatMessages.length;
        this.receiveAiMessage('');

        // 是否启用流式响应
        const useStream = true;

        if (useStream) {
          // 使用流式响应API
          await this.sendToWorkflowStream(userInput, inputData, streamMessageIndex);
        } else {
          // 使用普通响应API
          await this.sendToWorkflowNormal(inputData, streamMessageIndex);
        }
      } catch (error) {
        console.error('工作流API调用出错:', error);
        this.receiveAiMessage('AI助手服务连接失败，请检查网络后重试');
        this.isSendDisabled = false;
      } finally {
        this.isAiThinking = false;
      }
    },

    // 使用流式响应发送消息到工作流
    async sendToWorkflowStream(userInput, inputData, streamMessageIndex) {
      try {
        let fullResponse = '';
        let isFirstChunk = true;
        let isFirstEndStatus = true;

        // 创建请求数据
        const requestData = {
          workflow_id: this.workflowId,
          stream: false, // 启用流式传输
          input: inputData,
          message_id: this.messageId,
          session_id: this.sessionId
        };

        // 使用自定义的流式请求函数
        await this.streamRequest({
          url: this.workflowApiUrl,
          method: 'POST',
          data: requestData,
          onChunk: (chunkData) => {
            console.log(chunkData,'chunkData');

            try {
              // 解析每个数据块
              if (chunkData && chunkData.data) {
                const eventData = chunkData.data;

                // 确认是输出事件
                if (eventData.event && (eventData.event === 'output_msg' || eventData.event === 'guide_word'||eventData.event==='stream_msg')) {
                  if (eventData.output_schema && eventData.output_schema.message) {
                    // 检测到status==='end'的情况
                    if(eventData.status==='end'){
                      if(isFirstEndStatus) {
                        // 第一次遇到end状态，更新当前消息
                        fullResponse = eventData.output_schema.message;
                        if (this.chatMessages[streamMessageIndex]) {
                          this.chatMessages[streamMessageIndex].content = fullResponse;

                          // 保存AI最终回复到数据库
                          this.saveMessageToDatabase(fullResponse, true);

                          this.$forceUpdate(); // 强制更新视图
                        }
                        isFirstEndStatus = false;
                      } else {
                        console.log(eventData.output_schema.message,'eventData.output_schema.message');
                          // 保存AI消息到数据库
                          this.saveMessageToDatabase(eventData.output_schema.message, true);
                        // 后续每次遇到end状态，都添加新消息
                        this.receiveAiMessage(eventData.output_schema.message);
                      }
                    } else {
                      // 普通消息，累加到当前正在流式显示的消息中
                      fullResponse += eventData.output_schema.message;
                      // 更新UI上的消息
                      if (this.chatMessages[streamMessageIndex]) {
                        this.chatMessages[streamMessageIndex].content = fullResponse;
                        this.$forceUpdate(); // 强制更新视图
                      }
                    }

                    // 如果是第一个数据块，更新相关状态
                    if (isFirstChunk) {
                      isFirstChunk = false;

                      // 存储节点信息
                      if (eventData.node_id) {
                        this.lastNodeId = eventData.node_id;
                      }
                    }
                  }
                }

                // 处理输入请求事件
                if (eventData.event && eventData.event === 'input') {
                  this.lastNodeId = eventData.node_id;
                  this.lastInputSchema = eventData.input_schema;
                  this.messageId = eventData.message_id;

                  // 更新UI
                  this.updateUIForInputType(eventData.input_schema);
                }
              }
            } catch (e) {
              console.error('处理流式数据块出错:', e);
            }
          },
          onComplete: (finalData) => {
            console.log(finalData,'finalData');

            // 处理完整的响应
            if (finalData && finalData.data) {
              this.handleWorkflowEvents(finalData.data.events || []);
              this.getQuickActions('')
            }
          },
          onError: (error) => {
            console.error('流式请求错误:', error);
            // 更新错误消息
            this.chatMessages[streamMessageIndex].content = '抱歉，AI助手响应异常，请稍后再试';

            this.$forceUpdate();
          }
        });
        this.isSendDisabled = false
      } catch (error) {
        console.error('流式请求出错:', error);
        this.chatMessages[streamMessageIndex].content = '抱歉，AI助手响应异常，请稍后再试';
        this.$forceUpdate();
      }
    },

    // 使用普通响应发送消息到工作流
    async sendToWorkflowNormal(inputData, streamMessageIndex) {
      try {
        // 发送用户输入到工作流
        const response = await uni.request({
          url: this.workflowApiUrl,
          method: 'POST',
          data: {
            workflow_id: this.workflowId,
            input: inputData,
            message_id: this.messageId,
            session_id: this.sessionId
          },
          header: {
            'Content-Type': 'application/json'
          }
        });

        if (response.statusCode === 200 && response.data.status_code === 200) {
          const workflowData = response.data.data;

          // 如果没有任何响应内容，删除之前添加的空消息
          if (!workflowData.events || workflowData.events.length === 0) {
            this.chatMessages.splice(streamMessageIndex, 1);
            this.$forceUpdate();
          } else {
            // 处理工作流返回的事件
            this.handleWorkflowEvents(workflowData.events);
          }
        } else {
          console.error('工作流调用失败:', response);
          this.chatMessages[streamMessageIndex].content = 'AI助手服务暂时不可用，请稍后再试';
          this.$forceUpdate();
        }
      } catch (error) {
        console.error('工作流API调用出错:', error);
        this.chatMessages[streamMessageIndex].content = 'AI助手服务连接失败，请检查网络后重试';
        this.$forceUpdate();
      }
    },

    // 自定义流式请求函数
    async streamRequest({ url, method, data, onChunk, onComplete, onError }) {
      try {
        // 注意：这里使用的是简化版实现，实际上uni-app可能不支持流式请求
        // 在真实环境中，需要根据平台支持情况选择合适的实现方式

        // 发送普通请求，模拟分块处理
        const response = await uni.request({
          url,
          method,
          data: {
            ...data,
            stream: false // 强制使用非流式，因为uni-app默认不支持流式响应
          },
          header: {
            'Content-Type': 'application/json'
          }
        });

        if (response[1].statusCode === 200 && response[1].data.status_code === 200) {
          const workflowData = response[1].data.data;

          // 如果有事件数据，按顺序处理每个事件
          if (workflowData.events && workflowData.events.length > 0) {
            // 按顺序分类处理事件
            // 1. 先处理所有非end状态的输出事件
            const streamingEvents = workflowData.events.filter(event =>
                (event.event === 'output_msg' || event.event === 'guide_word' || event.event === 'stream_msg') &&
                event.status !== 'end'
            );

            // 2. 然后处理所有end状态的输出事件
            const endEvents = workflowData.events.filter(event =>
                (event.event === 'output_msg' || event.event === 'guide_word' || event.event === 'stream_msg') &&
                event.status === 'end'
            );

            // 3. 最后处理输入事件
            const inputEvents = workflowData.events.filter(event =>
                event.event === 'input'
            );

            // 处理非end状态的输出事件
            for (const event of streamingEvents) {
              if (onChunk) {
                // 延迟一点处理，模拟流式效果
                await new Promise(resolve => setTimeout(resolve, 50));
                onChunk({ data: event });
              }
            }

            // 处理end状态的输出事件
            for (const event of endEvents) {
              if (onChunk) {
                // 延迟一点处理，模拟流式效果
                await new Promise(resolve => setTimeout(resolve, 100));
                onChunk({ data: event });
              }
            }

            // 处理输入事件
            for (const event of inputEvents) {
              if (onChunk) {
                onChunk({ data: event });
              }
            }
          }

          // 处理完整响应
          if (onComplete) {
            onComplete({ data: workflowData });
          }
        } else {
          if (onError) {
            onError(response);
          }
        }
      } catch (error) {
        if (onError) {
          onError(error);
        }
      }
    },
    // 获取就诊人列表
    async getPatientList () {
      let userId = uni.getStorageSync("userId");
      let { data } = await getPatientList({
        userId,
      });
      if (!data) {
        this.patientList = [];
        return;
      }
      let disease = [];
      data.forEach((element) => {
        disease.push(element.patientId);
      });
      uni.setStorageSync("patientIdList", disease);
      this.patientList = (data || []).map((v) => {
        return {
          ...v,
          patientNameStr: `${v.patientName}(${v.sex}-${v.age})`,
        };
      });
      // this.patientList.push({
      //   patientNameStr: "添加就诊人",
      //   patientId: "add",
      // });
    },
    goBack () {
      uni.navigateBack();
    },
    goToHistory () {
      uni.navigateTo({
        url: '/pages/aiAssistant/history'
      });
    },
    goToNucleic () {
      if (this.isFinish) return
      this.chatMessages.push({
        isAi: true,
        content: '生成小结中...',
        time: this.formatTime()
      });
      // 生成小结后，候选项区显示【去问诊】【补充健康档案】【没有其他问题了】
      this.quickActions = [
        {
          name: '去问诊',
        },
        {
          name: '补充健康档案',
        },
        {
          name: '没有其他问题了',
        }
      ]

    },
    goToHealthRecord () {
      // 智慧康养说明页面
      uni.navigateTo({
        url: '/pages/setting/about'
      });
    },
    formatTime () {
      const now = new Date();
      const hours = now.getHours().toString().padStart(2, '0');
      const minutes = now.getMinutes().toString().padStart(2, '0');
      return `${hours}:${minutes}`;
    },
    sendMessage () {
      if (!this.inputMessage.trim()&&this.lastInputSchema.input_type!=='form_input') return;
      if (this.isSendDisabled) return;
      this.isSendDisabled = true;
      if(this.lastInputSchema.input_type==='form_input'){
        this.inputMessage = this.lastInputSchema.value.map(item=>`${item.label}:${item.value}`).join('\n');
      }

      // 添加用户消息
      this.chatMessages.push({
        isAi: false,
        content: this.inputMessage,
        time: this.formatTime()
      });

      const userMessage = this.inputMessage;
      this.inputMessage = '';
      this.scrollToBottom();

      // 保存用户消息到数据库
      this.saveMessageToDatabase(userMessage, false);

      // 保存消息到本地存储
      this.saveConversation();

      // 使用AI工作流处理用户消息
      this.sendToWorkflow(userMessage);
    },
    receiveAiMessage (content) {
      // 添加AI消息到聊天界面
      this.chatMessages.push({
        isAi: true,
        content: content,
        time: this.formatTime()
      });
      this.scrollToBottom();
      this.saveConversation();
    },
    getAiResponse (userMessage) {
      // 简单的模拟AI回复，实际应用中应调用真实AI API
      const responses = [
        "我理解您的问题，可以这样解决...",
        "关于这个问题，我建议您...",
        "很高兴能帮到您，您可以尝试...",
        "根据您的描述，我认为..."
      ];
      return responses[Math.floor(Math.random() * responses.length)];
    },
    scrollToBottom () {
      this.$nextTick(() => {
        // 获取消息列表高度并滚动到底部
        const query = uni.createSelectorQuery().in(this);
        query.select('.chat-list').boundingClientRect(data => {
          if (data) {
            this.scrollTop = data.height;
          }
        }).exec();
      });
    },
    startNewConversation () {
      // 清空当前聊天记录
      this.chatMessages = [];
      this.conversationId = Date.now().toString();
      this.isSendDisabled = false;

      // 重置工作流相关数据
      this.sessionId = null;
      this.messageId = null;
      this.lastNodeId = null;
      this.lastInputSchema = {
        input_type: 'dialog_input',
        value: []
      };

      // 启动AI工作流
      this.initWorkflow();
    },
    loadMoreMessages () {
      // 实际应用中，这里应该加载更多历史消息
      console.log('加载更多消息');
    },
    saveConversation () {
      // 保存当前会话到本地存储
      if (!this.conversationId) return;

      const conversations = uni.getStorageSync('ai_conversations') || {};
      conversations[this.conversationId] = {
        id: this.conversationId,
        lastMessage: this.chatMessages[this.chatMessages.length - 1]?.content || '',
        lastTime: new Date().toISOString(),
        messages: this.chatMessages,
        // 保存工作流状态
        workflowState: {
          sessionId: this.sessionId,
          messageId: this.messageId,
          lastNodeId: this.lastNodeId,
          workflowId: this.workflowId
        },
        selectedPatient: this.selectedPatient
      };

      uni.setStorageSync('ai_conversations', conversations);
    },
    loadConversation (id) {
      const conversations = uni.getStorageSync('ai_conversations') || {};
      const conversation = conversations[id];

      if (conversation) {
        this.conversationId = id;
        this.chatMessages = conversation.messages || [];

        // 恢复工作流状态
        if (conversation.workflowState) {
          this.sessionId = conversation.workflowState.sessionId;
          this.messageId = conversation.workflowState.messageId;
          this.lastNodeId = conversation.workflowState.lastNodeId;

          // 如果工作流ID不同，使用保存的ID
          if (conversation.workflowState.workflowId) {
            this.workflowId = conversation.workflowState.workflowId;
          }
        }

        // 恢复选中的患者
        if (conversation.selectedPatient) {
          this.selectedPatient = conversation.selectedPatient;
        }

        this.scrollToBottom();
      } else {
        // If conversation not found, start a new one
        this.startNewConversation();
      }
    },
    goToCloudClinic () {
      uni.navigateTo({
        url: '/pages/register/docList/index'
      });
    },
    goToSmartClinic (item) {
      if (item.name === '去问诊') {
        uni.navigateTo({
          url: '/pages/register/docList2/index'
        })
        return
      }
      if (item.name === '补充健康档案') {
        this.receiveAiMessage('好的，我已将您当前的病情记录补充到您的健康档案中。请注意，这些建议不能替代专业医生的诊断和治疗。如果症状持续或加重，请及时就医')
        this.finishConversation()
        return
      }
      if (item.name === '没有其他问题了') {
        this.finishConversation()
        return
      }

      // 如果是AI工作流生成的选项，传递实际的value值
      if (item.value) {
        this.inputMessage = item.value;
      } else {
        // 否则按原来的方式处理
        this.inputMessage = this.inputMessage + ' ' + item.name;
      }
    },
    async getRegChatInfo(){
      if(!this.regId){
        return
      }
      if(this.type === 'reg'){
        const dataVal = await getPatientChat2({ regId: this.regId })
        this.dataVal=dataVal.data
      }
    },
    // 结束对话
   async finishConversation () {
      this.isFinish = true;
      if(this.type === 'reg'){
        const dataVal = await getPatientChat2({ regId: this.regId })
        wx.switchTab({
           url: "/pages/index/index",
        });
        // endRegChat({
        //   sessionId: this.sessionId,
        //   ...dataVal.data
        // })
      }
      // 如果有活跃的工作流会话，尝试停止它
      if (this.sessionId) {
        this.stopWorkflow();
      }
    },
    // 停止工作流
    async stopWorkflow() {
      try {
        const response = await uni.request({
          url: '/api/v2/workflow/stop',
          method: 'POST',
          data: {
            session_id: this.sessionId
          },
          header: {
            'Content-Type': 'application/json'
          }
        });

        console.log('工作流已停止:', response);

        // 重置工作流状态
        this.sessionId = null;
        this.messageId = null;
        this.lastNodeId = null;
        this.lastInputSchema = {
          input_type: 'dialog_input',
          value: []
        };
        this.isFinish = true;
      } catch (error) {
        console.error('停止工作流出错:', error);
      }
    },
    selectPatient (patient) {
      this.selectedPatient = patient;
      this.patientId = patient.patientId;
      // 可以将选中的患者信息添加到会话中
      this.receiveAiMessage(`已为您选择就诊人: ${patient.patientName}`);

      // 如果已经有工作流会话，并且有待处理的用户输入
      if (this.sessionId && this.lastNodeId && this.lastInputSchema) {
        // 将选择的患者信息作为上下文发送到工作流
        const patientContext = {
          patientId: patient.patientId,
          patientName: patient.patientName,
          sex: patient.sex,
          age: patient.age
        };

        // 可以将患者信息作为特殊指令发送到工作流
        this.sendToWorkflow(`选择就诊人：${patient.patientName}(${patient.sex}-${patient.age})`);
      }
    },
    goToAddPatient () {
      // 跳转到添加患者页面d
      uni.navigateTo({
        url: `/pages/personalCenter/patientManage/addPatient/index`
      });
    },
    // 处理文件输出
    handleFileOutput(files) {
      if (!files || !Array.isArray(files)) return;

      files.forEach(file => {
        // 根据文件类型处理
        if (file.type === 'image') {
          // 图片类型
          this.receiveAiMessage(`<img src="${file.url}" alt="${file.name || '图片'}" style="max-width: 100%; border-radius: 8px;" />`);
        } else if (file.type === 'pdf' || file.type === 'doc' || file.type === 'docx' || file.type === 'xlsx') {
          // 文档类型
          this.receiveAiMessage(`<a href="${file.url}" target="_blank" style="color: #4285f4;">${file.name || '查看文档'}</a>`);
        } else {
          // 其他类型文件
          this.receiveAiMessage(`<a href="${file.url}" target="_blank" style="color: #4285f4;">${file.name || '下载文件'}</a>`);
        }
      });
    },
    // 保存消息到数据库
    async saveMessageToDatabase(content, isAi) {
      if(!this.dataVal.sessionId){
        return
      }
      try {
        await saveUserMessage({
          sessionId: this.dataVal.sessionId,
          content: content,
          userId: this.userId,
          patientId: this.dataVal.patientId,
          isAi: isAi,
          role: isAi ? "assistant" : "user"
        });
      } catch (error) {
        console.error('保存消息到数据库失败:', error);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.ai-chat-container {
  background-color: #f5f7fa;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.chat-header {
  background-color: #fff;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;

  .tab-bar {
    display: flex;
    justify-content: space-around;

    .tab-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 10rpx 0;

      .tab-icon {
        width: 50rpx;
        height: 50rpx;
        margin-bottom: 8rpx;
      }

      text {
        font-size: 24rpx;
        color: #666;
      }

      &.active {
        color: #836AFF;

        text {
          color: #836AFF;
        }

        &::after {
          content: '';
          display: block;
          width: 40rpx;
          height: 4rpx;
          background-color: #836AFF;
          margin-top: 8rpx;
        }
      }
    }
  }
}

.action-buttons-container {
  display: flex;
  justify-content: space-around;
  background-color: #fff;
  padding: 10rpx 0 20rpx;
  border-bottom: 1rpx solid #eee;

  .action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 25%;

    .btn-icon {
      width: 70rpx;
      height: 70rpx;
      margin-bottom: 10rpx;
      padding: 12rpx;
      box-sizing: border-box;
    }

    text {
      font-size: 24rpx;
      color: #666;
      text-align: center;
      white-space: normal;
      width: 100%;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
  }
}

.chat-content {
  flex: 1;
  padding: 20rpx;
  overflow-y: auto;
  background-color: #f9f6ff;
  box-sizing: border-box;
  .chat-list {
    padding-bottom: 20rpx;
  }

  .chat-item {
    display: flex;
    margin-bottom: 30rpx;

    .avatar {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      overflow: hidden;
      margin-right: 20rpx;

      image {
        width: 100%;
        height: 100%;
      }
    }

    .message {
      max-width: 70%;

      .msg-content {
        padding: 20rpx;
        border-radius: 12rpx;
        font-size: 28rpx;
        line-height: 1.5;
        word-break: break-all;
      }

      .msg-time {
        font-size: 24rpx;
        color: #999;
        margin-top: 10rpx;
      }
    }
  }

  .chat-item-ai {
    .message {
      .msg-content {
        background-color: #fff;
        color: #333;
        white-space: pre-wrap;
      }
    }
  }

  .chat-item-user {
    flex-direction: row-reverse;

    .avatar {
      margin-right: 0;
      margin-left: 20rpx;
    }

    .message {
      .msg-content {
        background: linear-gradient(135deg, rgba(81, 106, 251, 1) 0%, rgba(133, 155, 255, 1) 99.97%);
        color: #fff;
      }

      .msg-time {
        text-align: right;
      }
    }
  }
}

.chat-footer {
  background-color: #f9f6ff;
  border-top: 1rpx solid #eeeeee;
  padding: 10rpx 20rpx 20rpx;

  .input-hint {
    font-size: 24rpx;
    color: #999;
    margin-bottom: 10rpx;
    padding-left: 10rpx;
  }

  .input-area {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;

    input {
      flex: 1;
      height: 80rpx;
      background-color: #fff;
      border-radius: 40rpx;
      padding: 0 30rpx;
      font-size: 28rpx;
      border: 1px solid #e6e6fa;
    }

    .send-btn {
      width: 70rpx;
      height: 70rpx;
      background: linear-gradient(135deg, rgba(81, 106, 251, 1) 0%, rgba(133, 155, 255, 1) 99.97%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: 20rpx;
    }
  }

  .bottom-actions {
    display: flex;
    border-top: 1px solid #eee;
    padding: 15rpx 0;
    align-items: center;

    .bottom-title {
      font-size: 28rpx;
      font-weight: bold;
      color: #836AFF;
      margin-right: 20rpx;
      white-space: nowrap;
      padding-left: 20rpx;
    }

    .quick-actions-scroll {
      flex: 1;
      width: 100%;
      white-space: nowrap;
      overflow: hidden;
    }

    .quick-actions {
      display: inline-flex;
      padding-right: 20rpx;
    }

    .quick-btn {
      display: inline-flex;
      align-items: center;
      background-color: #f0f2f5;
      border-radius: 36rpx;
      padding: 10rpx 20rpx;
      margin-right: 15rpx;

      text {
        font-size: 24rpx;
        color: #666;
      }

      &.active {
        background-color: #e6e6fa;

        text {
          color: #836AFF;
        }
      }
    }
  }

  .patient-selector {
    margin: 10rpx 0 20rpx;

    .selector-title {
      color: #333;
      font-size: 32rpx;
      font-weight: bold;
      padding: 20rpx 0;
      border-bottom: 1px solid #ececec;
      margin-bottom: 20rpx;
    }

    .patient-list {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;

      .patient-item {
        width: 48%;
        background-color: #f0f0f0;
        border-radius: 8rpx;
        padding: 20rpx;
        margin-bottom: 15rpx;
        text-align: center;

        text {
          font-size: 28rpx;
          color: #333;
        }

        &.add-new {
          background-color: #f9f9f9;
          border: 1px dashed #ccc;

          text {
            color: #666;
          }
        }
      }
    }
  }
}
.form-item{
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10rpx;
  flex:1;
}
.form-item-label{
  font-size: 28rpx;
  color: #333;
  margin-right: 20rpx;
}
</style>