<template>
  <!-- 左侧助手 -->
  <div class="guid_zs">
    <!-- 头像 -->
    <image
      class="zs_head"
      src="/static/images/guidance/tx.png"
      mode="aspectFill"
    />
    <!-- 文本内容 -->
    <view class="zs_cont">
      <slot></slot>
    </view>
  </div>
</template>

<script></script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}
.guid_zs {
  @include flex(left);
  align-items: flex-start;
  padding-bottom: 32rpx;

  .zs_head {
    width: 64rpx;
    height: 64rpx;
    margin-right: 16px;
    border-radius: 50%;
    flex: none;
  }

  .zs_cont {
    max-width: 500rpx;
    padding: 20rpx 24rpx;
    font-size: 28rpx;
    background: #ffffff;
    color: #333;
    border-radius: 8rpx 32rpx 32rpx 32rpx;
    border: 1px solid rgba(0, 0, 0, 0.1);
    word-break: break-all;
  }
}
</style>
