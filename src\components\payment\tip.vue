<template>
  <div class="share" @click="click">
    <image
      src="/static/images/index/share-toast.png"
      mode="widthFix"
      class="share-bg"
    />
  </div>
</template>

<script>
export default {
  name: 'payShare',
  methods: {
    click() {
      this.$emit('click');
    },
  },
};
</script>

<style lang="scss" scoped>
.share {
  position: fixed;
  width: 100%;
  height: 100vh;
  left: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 999;

  .share-bg {
    width: 100%;
  }
}
</style>
