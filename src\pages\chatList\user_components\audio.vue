<template>
  <!-- 图片消息 -->
  <view class="user_audio">
    <view>
      <view class="patientName">{{ patientName }}</view>
      <view class="audio_content" @click="click">
        <!-- 音频时长 -->
        <text class="audio_time">{{ time }}s </text>
        <!-- 播放 -->
        <image
            src="/static/images/chat/left.gif"
            class="audio_icon"
            v-if="play"
        />
        <!-- 暂停 -->
        <image
            src="/static/images/chat/left.png"
            class="audio_icon"
            v-if="!play"
        />
      </view>
    </view>
    <!-- 头像 -->
    <image
      @click="head"
      :src="imgUrl || '/static/images/docHead.png'"
      mode="aspectFill"
      class="user_img"
    />
  </view>
</template>

<script>
export default {
  props: {
    // 头像
    imgUrl: {
      type: String,
      default: '',
    },
    // 消息id
    id: String,
    // 音频时长
    time: {
      type: [Number, String],
      default: 0,
    },
    // 地址
    audioSrc: {
      type: String,
      default: '',
    },
    // 播放状态
    play: {
      type: Boolean,
      default: false,
    },
    // 下标
    index: {
      type: [Number, String],
      default: 0,
    },
    patientName:{}
  },
  data() {
    return {
      audio: '',
    };
  },
  methods: {
    head() {
      this.$emit('head');
    },
    // 点击事件
    click() {
      this.$emit('click', {
        id: this.id,
        src: this.audioSrc,
        index: this.index,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.user_audio {
  box-sizing: border-box;
  @include flex(right);
  padding-bottom: 30upx;
  align-items: flex-start;

  // 头像
  .user_img {
    width: 64upx;
    height: 64upx;
    border-radius: 4upx;
    margin-left: 16upx;
    flex: none;
  }

  // 内容
  .audio_content {
    max-width: 516upx;
    padding: 10upx 20upx;
    background-color: #d6f1ff;
    color: #333;
    border: 1px solid #ececec;
    border-radius: 32upx 8upx 32upx 32upx;
    @include flex(left);

    // 时长
    .audio_time {
      font-size: 28upx;
      margin-right: 20upx;
    }

    // 图标
    .audio_icon {
      width: 40upx;
      height: 40upx;
    }
  }
}
</style>
