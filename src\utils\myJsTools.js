/**
 * 公共方法封装
 *
 *
 */

const myJsTools = {};

/**
 * 存储  global_config 全局参数配置  patientInfo患者信息存储  active 切换之后的tab保存状态
 *       token_info 登录信息  showCodeLogin 登录方式状态存储  myActive 我的处方tab 状态存储
 *       chatList 聊天记录存储，未读消息记录存储
 */
myJsTools.setItem = function(key, value) {
  if (!window.localStorage) {
    alert('请检查浏览器版本，该版本不支持localStorage存储');
  } else {
    var ms = 'mystorage';
    var storage = window.localStorage;
    var mydata = storage.getItem(ms);
    if (!mydata) {
      storage.setItem(ms, '{"data":{}}');
      mydata = storage.getItem(ms);
    }
    mydata = JSON.parse(mydata);
    mydata.data[key] = value;
    storage.setItem(ms, JSON.stringify(mydata));
  }
};
myJsTools.getItem = function(key) {
  if (!window.localStorage) {
    alert('请检查浏览器版本，该版本不支持localStorage存储');
  } else {
    var ms = 'mystorage';
    var storage = window.localStorage;
    var mydata = storage.getItem(ms);
    if (!mydata) {
      return false;
    }
    mydata = JSON.parse(mydata);
    return mydata.data[key];
  }
};
myJsTools.removeItem = function(key) {
  if (!window.localStorage) {
    alert('请检查浏览器版本，该版本不支持localStorage存储');
  } else {
    var ms = 'mystorage';
    var storage = window.localStorage;
    mydata = JSON.parse(mydata);
    delete mydata.data[key];
    storage.setItem(ms, JSON.stringify(mydata));
  }
};
myJsTools.clearItem = function() {
  if (!window.localStorage) {
    alert('请检查浏览器版本，该版本不支持localStorage存储');
  } else {
    var ms = 'mystorage';
    var storage = window.localStorage;
    storage.removeItem(ms);
  }
};

myJsTools.HashMap = function() {
  //定义长度
  var length = 0;
  //创建一个对象
  var obj = new Object();

  /**
   * 判断Map是否为空
   */
  this.isEmpty = function() {
    return length == 0;
  };

  /**
   * 判断对象中是否包含给定Key
   */
  this.containsKey = function(key) {
    return key in obj;
  };

  /**
   * 判断对象中是否包含给定的Value
   */
  this.containsValue = function(value) {
    for (var key in obj) {
      if (obj[key] == value) {
        return true;
      }
    }
    return false;
  };

  /**
   *向map中添加数据
   */
  this.put = function(key, value) {
    if (!this.containsKey(key)) {
      length++;
    }
    obj[key] = value;
  };

  /**
   * 根据给定的Key获得Value
   */
  this.get = function(key) {
    return this.containsKey(key) ? obj[key] : null;
  };

  /**
   * 根据给定的Key删除一个值
   */
  this.remove = function(key) {
    if (this.containsKey(key) && delete obj[key]) {
      length--;
    }
  };

  /**
   * 获得Map中的所有Value
   */
  this.values = function() {
    var _values = new Array();
    for (var key in obj) {
      _values.push(obj[key]);
    }
    return _values;
  };

  /**
   * 获得Map中的所有Key
   */
  this.keySet = function() {
    var _keys = new Array();
    for (var key in obj) {
      _keys.push(key);
    }
    return _keys;
  };

  /**
   * 获得Map的长度
   */
  this.size = function() {
    return length;
  };

  /**
   * 清空Map
   */
  this.clear = function() {
    length = 0;
    obj = new Object();
  };
};

export default myJsTools;
