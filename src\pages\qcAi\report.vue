<template>
  <view class="container">
    <!-- <view class="header">
      <view class="flex-row items-center">
        <text class="header-title">{{ headerTitle }}</text>
      </view>
    </view> -->
    <view class="flex-column" v-if="businessInfo.length > 0">
      <view
        class="section full-width"
        v-for="(item, index) in businessInfo"
        :key="item.pkId"
      >
        <view class="flex-row items-center padding-top full-width">
          <view class="section-title">{{ item.sectionTitle }}</view>
        </view>
        <view class="flex-row padding-top full-width">
          <view
            class="section-content"
            v-html="formatContent(item.sectionContent)"
          ></view>
        </view>
      </view>
    </view>

    <!-- 数据加载中 -->
    <view class="loading" v-if="loading">
      <text>加载中...</text>
    </view>
  </view>
</template>

<script>
import { getAiReportInfo } from '@/api/qcAi'

export default {
  data() {
    return {
      headerTitle: '', // 健康报告大标题
      businessInfo: [], // 问诊小结信息集合
      reportType: '1', // 报告类型
      loading: true, // 是否加载中
      reportId: '', // 报告ID
    }
  },
  onLoad(options) {
    this.reportId = options.id
    this.getReportInfo()
  },
  methods: {
    // 获取健康报告信息
    getReportInfo() {
      this.loading = true
      getAiReportInfo({
        pkId: this.reportId,
      })
        .then((res) => {
          if (res.data) {
            this.headerTitle = res.data.reportTittle || '健康报告'
            uni.setNavigationBarTitle({
              title: res.data.reportTittle,
            })
            this.businessInfo = res.data.szAiReporsectionVo || []
            this.reportType = res.data.reportType || '1'
          } else {
            uni.showToast({
              title: '获取报告失败',
              icon: 'none',
            })
          }
          this.loading = false
        })
        .catch((err) => {
          console.error('获取报告详情失败', err)
          uni.showToast({
            title: '获取报告失败',
            icon: 'none',
          })
          this.loading = false
        })
    },
    formatContent(content) {
      if (!content) return ''
      // 处理换行
      content = content.replace(/\\n/g, '<br>').replace(/\n/g, '<br>')
      // 处理Markdown加粗语法 **文本** 转换为 <strong>文本</strong>
      content = content.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      return content
    },
  },
}
</script>

<style scoped>
.container {
  padding: 16px;
  background-color: white;
  min-height: 100vh;
  overflow: auto;
}

.header {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 16px;
}

.header-title {
  font-size: 16px;
  color: #333;
}

.section {
  margin-bottom: 16px;
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 16px;
}

.label {
  display: block;
  font-weight: bold;
  color: #666;
}

.section-title {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

.section-content {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  text-align: justify;
}

.white-space-pre-wrap {
  white-space: pre-wrap;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
}

.flex-row {
  display: flex;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.full-width {
  width: 100%;
}

.padding-top {
  padding-top: 10px;
}

.padding-right {
  padding-right: 10px;
}
</style>
