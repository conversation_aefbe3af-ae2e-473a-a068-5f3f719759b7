<template>
  <!-- 药店详情-->
  <view class="pharmacy_detail">
    <!-- 药店图标 -->
    <img
      v-img:click="detail.drugstoreImg"
      v-if="detail.drugstoreImg"
      :data-src="err"
      alt=""
      class="poster"
    />
    <image
      class="poster"
      v-else
      src="/static/images/Pharmacy-default.png"
    ></image>
    <!-- 右侧 -->
    <view class="detail">
      <view class="pharmacy_name">
        {{ detail.drugstoreName }}
        <text>自提</text>
      </view>
      <view class="pharmacy_area">
        {{ detail.provinceName }} {{ detail.cityName }}
        <text v-if="false">
          <uni-icons type="location-filled" color="#666" size="16"></uni-icons>
          809m
        </text>
      </view>
      <view class="pharmacy_address" @click="click">
        <text>{{ detail.area }} {{ detail.address }}</text>
        <uni-icons
          type="arrowright"
          color="#666"
          size="16"
          v-if="showRight"
        ></uni-icons>
      </view>
    </view>
    <!-- 底部按钮 -->
    <view class="pharmacy_buts">
      <slot></slot>
      <text @click.stop="callTel">联系药房</text>
      <text @click.stop="toAddress">去药房</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'PharmacyAddress',
  props: {
    detail: {
      type: Object,
      default: () => {
        return {};
      },
    },
    showRight: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      err: require('../../../static/images/Pharmacy-default.png'),
    };
  },
  methods: {
    callTel() {
      uni.makePhoneCall({
        phoneNumber: this.detail.telNo,
      });
    },
    click() {
      if (this.showRight) {
        this.$emit('click');
      }
    },
    toAddress() {
      // if (this.detail.drugstoreImg) {
      // 	delete this.detail.drugstoreImg;
      // }
      // uni.navigateTo({
      // 	url: `/pages/address/goAddress?address=${JSON.stringify(this.detail)}`,
      // });

      const { latitude, longitude } = this.detail;
      uni.openLocation({
        latitude: Number(latitude),
        longitude: Number(longitude),
        success: function() {},
        fail(err) {},
      });
    },
  },
};
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}

.pharmacy_detail {
  width: 100%;
  @include flex(lr);
  flex-wrap: wrap;
  padding: 0 32rpx;
  background-color: #fff;

  .poster {
    width: 140rpx;
    height: 140rpx;
    border-radius: 8rpx;
    flex: none;
  }

  .detail {
    flex: 1;
    padding-left: 26rpx;
    height: 140rpx;
    @include flex(lr);
    flex-direction: column;
    align-items: stretch;

    .pharmacy_name {
      @include flex(lr);
      font-size: 28rpx;

      text {
        width: 60rpx;
        height: 32rpx;
        @include flex;
        font-size: 22rpx;
        color: #fff;
        background-color: #666666;
        border-radius: 8rpx;
      }
    }

    .pharmacy_area {
      @include flex(lr);
      font-size: 26rpx;
      color: #666;
    }

    .pharmacy_address {
      @include flex(lr);
      font-size: 26rpx;
      color: #666;
    }
  }

  .pharmacy_buts {
    width: 100%;
    padding-top: 24rpx;
    @include flex(right);

    text {
      width: 160rpx;
      height: 60rpx;
      border-radius: 30rpx;
      @include flex;
      font-size: 28rpx;
      @include font_theme;
      @include border_theme;
      margin-left: 16rpx;
    }
  }
}
</style>
