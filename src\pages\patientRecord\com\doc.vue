<template>
  <!-- 医生 -->
  <view class="doc_info">
    <!-- 头像 -->
    <img v-img="info.docImg" alt="" class="head" v-if="info.docImg" />
    <image class="head" src="/static/images/docHead.png" v-else />
    <!-- 信息 -->
    <view class="doc_cont">
      <text class="name">{{ info.docName }}</text>
      <text>{{ info.deptName }} {{ info.docProf }}</text>
      <text>{{ info.hosName }}</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'Doc',
  props: {
    info: {
      type: Object,
      default: () => {
        docImg: '';
      },
    },
  },
};
</script>

<style lang="scss" scoped>
.doc_info {
  background-color: #fff;
  border-radius: 8rpx;
  @include flex(left);
  padding: 24rpx 28rpx;

  .head {
    width: 128rpx;
    height: 128rpx;
    border-radius: 4rpx;
    border: 1px solid rgba(255, 255, 255, 0.6);
  }

  .doc_cont {
    height: 128rpx;
    padding-left: 24rpx;
    @include flex(lr);
    flex-direction: column;
    align-items: stretch;

    text {
      font-size: 24rpx;
      color: #666;

      &.name {
        font-weight: bold;
        font-size: 32rpx;
        color: #333;
      }
    }
  }
}
</style>
