<template>
  <!-- 药品明细 -->
  <div class="drug_detail">
    <p class="title">处方信息</p>

    <!-- 药品列表 -->
    <div class="list">
      <!-- 循环 -->
      <div class="list_item" v-for="(item, index) in list" :key="index">
        <!-- 成药方 -->
        <div class="label">
          <span>Rp</span>
          <span>{{ item.proPrescriptionMasterVO.prescriptionTypeName }}</span>
        </div>
        <div class="item" v-for="(d, t) in item.details" :key="t">
          <p class="item_name">{{ t + 1 }}.{{ d.drugName }}({{ d.gg }})</p>
          <p class="item_text">
            用法：{{ d.dduName || '口服' }}，每次{{ d.eachQuan
            }}{{ d.eachUnit }}，{{ d.ddufName }}，用药{{
              item.proPrescriptionMasterVO.days
            }}天
          </p>
          <p class="item_text">
            <span>价格：¥{{ d.price }}</span>
            <span>x{{ d.quan }}</span>
          </p>
          <p class="item_text" v-if="d.memo">说明：{{ d.memo }}</p>
        </div>
        <!-- 草药 -->
        <p class="item_num" v-if="item.proPrescriptionMasterVO.herbalNum">
          共{{ item.proPrescriptionMasterVO.herbalNum }}付
        </p>
        <p class="item_num" v-if="item.proPrescriptionMasterVO.djCost">
          代煎费：¥{{ item.proPrescriptionMasterVO.djCost | toFixed }}
        </p>
        <p class="item_btm">
          合计：<span
            >¥{{ item.proPrescriptionMasterVO.totalMoney | toFixed }}</span
          >
        </p>
      </div>

      <!-- 总计 -->
      <div class="bottom">
        <p>
          合计：<span>¥{{ totalMoney | toFixed }}</span>
        </p>
        <p>
          优惠：<span>¥{{ preferentialAmount | toFixed }}</span>
        </p>
        <p>
          实付：<span>¥{{ orderMoney | toFixed }}</span>
        </p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DrugDetail',
  props: {
    list: {
      type: Array,
      default: () => [],
    },
    // 优惠
    preferentialAmount: String | Number,
    // 实际
    orderMoney: String | Number,
    // 合计
    totalMoney: String | Number,
  },
};
</script>

<style lang="scss" scoped>
.drug_detail {
  background: #fff;
  border-radius: 8rpx;
  margin-top: 24rpx;

  .title {
    height: 92rpx;
    @include flex(left);
    font-size: 28rpx;
    font-weight: bold;
    padding-left: 32rpx;
  }

  .list {
    padding: 0 32rpx 32rpx;
    font-size: 28rpx;
    color: #333;

    .list_item {
      line-height: 60rpx;
      padding-bottom: 12rpx;
      border-bottom: 1px dashed #f5f5f5;

      .label {
        @include flex(lr);
        font-weight: bold;
      }

      .item {
        .item_name {
          font-weight: bold;
        }

        .item_text {
          @include flex(lr);
        }
      }

      .item_btm {
        @include flex(right);

        span {
          color: red;
        }
      }
    }

    .bottom {
      @include flex(right);
      padding-top: 24rpx;

      p {
        padding-left: 32rpx;

        span {
          color: #ff5050;
        }
      }
    }
  }
}
</style>
