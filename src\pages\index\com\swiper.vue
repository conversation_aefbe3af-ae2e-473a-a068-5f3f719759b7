<template>
  <view class="swiper">
    <!-- 标题 -->
    <view class="swiper_title">
      <TITLE title="推荐医生" />
    </view>
    <!-- #BAE3F7 -->
    <swiper
      class="swiper_warp"
      indicator-dots
      autoplay
      circular
      indicator-color="#BAE3F7"
      indicator-active-color="#14A0E6"
      :interval="3000"
      :duration="300"
    >
      <swiper-item v-for="(item,index) in list" :key="index">
        <DOC :item="item" />
      </swiper-item>
    </swiper>
  </view>
</template>

<script>
import TITLE from '@/pages/inspect/com/itemTitle.vue';
import DOC from './doc.vue';

export default {
  components: {
    TITLE,
    DOC,
  },
  props: {
    list: {
      type: Array,
      default: () => [],
    },
  },
};
</script>

<style lang="scss" scoped>
.swiper {
  background: #fff;
  border-radius: 8rpx;

  &_title {
    height: 76rpx;
    padding-left: 32rpx;
    @include flex(left);
  }

  &_warp {
    height: 250rpx;
    overflow: hidden;

    /deep/.desc_tag {
      height: 50rpx;
      overflow: hidden !important;
    }
  }
}
</style>
