<template>
  <!-- 检验中心列表 -->
  <view class="list">
    <!-- 循环 -->
    <view
      class="item"
      v-for="(item, index) in list"
      :key="index"
      @click="itemClick(item)"
    >
      <!-- 图片 -->
      <!-- <image src="" /> -->
      <!-- 机构名 -->
      <view class="left">
        <!-- 机构 -->
        <text class="title">{{
          item.lisType == 2 ? item.samplingOrgName : item.organName
        }}</text>
        <!-- 二级 -->
        <text class="info" v-if="item.lisType == 2">{{ item.organName }}</text>
      </view>
      <!-- 右侧 -->
      <view class="right">
        <text class="type">
          {{ item.lisType == 1 ? '到检' : '送检' }}
        </text>
        <!-- 导航 -->
        <view class="address">
          <image class="icon" src="/static/inspection/dh.png" />
          <text>去这里</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getLisList<PERSON><PERSON><PERSON>rgan } from '@/api/inspect';

export default {
  name: 'List',
  data() {
    return {
      list: [],
      query: {
        latitude: '',
        longitude: '',
        pliId: '',
      },
    };
  },
  onLoad(opt) {
    if (!opt.id) return;
    this.query.pliId = opt.id;
    this.init();
  },
  methods: {
    // 初始化询问
    init() {
      let that = this;
      uni.showModal({
        title: '提示',
        content: '需要获取您当前位置',
        success(res) {
          if (res.confirm) {
          
            that.getLocation();
          } else if (res.cancel) {
            that.getList();
          }
        },
      });
    },
    // 获取经纬度
    getLocation() {
      
      let that = this;
      uni.getLocation({
        type: 'wgs84',
        complete(res) {
          let { longitude, latitude } = res;
          // 如果存在
          if (longitude) {
            that.query.longitude = longitude;
            that.query.latitude = latitude;
          }
          // 调用接口
          that.getList();
        },
      });
    },
    // 获取列表
    async getList() {
      let { data } = await getLisListToOrgan(this.query);
      this.list = data;
    },
    // 机构点击
    itemClick(item) {
      let { dloId, dsoId, samplingOrgName, lisType, organName } = item;
      let pages = getCurrentPages();
      // 获取上一页
      let prev = pages[pages.length - 2];
      if (lisType == 2) {
        prev.dloId = dsoId;
      } else {
        prev.dloId = dloId;
      }

      let obj = {
        samplingOrgName,
        lisType,
        lisOrgName: organName,
        dsoId,
        dpoId: dloId,
      };
      prev.detail = { ...prev.detail, ...obj };
      uni.navigateBack();
    },
  },
};
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}
page {
  background: #f5f5f5;
}
.list {
  width: 100%;
  background: #fff;

  .item {
    padding: 0 32rpx;
    height: 154rpx;
    @include flex(lr);
    border-bottom: 1px solid #d8d8d8;

    .left {
      flex: 1;
      padding-right: 32rpx;

      .title {
        font-size: 28rpx;
        color: #333;
        font-weight: bold;
        display: block;
      }

      .info {
        display: block;
        font-size: 24rpx;
        color: #666;
        margin-top: 16rpx;
        line-height: 34rpx;
      }
    }

    .right {
      flex: none;
      @include flex;

      .type {
        font-size: 28rpx;
        color: #333;
        margin-right: 40rpx;
        font-weight: bold;
      }

      .address {
        @include flex;
        flex-direction: column;

        .icon {
          width: 64rpx;
          height: 64rpx;
        }

        text {
          font-size: 28rpx;
          line-height: 40rpx;
          @include font_theme;
        }
      }
    }
  }
}
</style>
