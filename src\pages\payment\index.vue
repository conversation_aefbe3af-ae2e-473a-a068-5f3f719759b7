<template>
  <div class="payment" :class="{ show: status == 1 }" v-show="openId">
    <header>
      <img
        v-img:click="detail.patientImg"
        v-if="detail.patientImg"
        class="head"
      />
      <image src="/static/images/docHead.png" class="head" v-else />

      <div class="right">
        <h3>{{ detail.patientName }}</h3>
        <p v-if="status == 1">"我有一笔订单需要支付，请帮我代付吧"</p>
        <p v-if="status == 2">"订单已成功支付"</p>
        <p v-if="status == 3">"订单已失效，无法进行支付"</p>
      </div>
    </header>

    <!-- 价位 -->
    <div class="price">
      <p class="label">代付金额</p>
      <p class="item">
        <span class="bold">
          ￥<b>{{ detail.orderRealMoney | toFixed }}</b>
        </span>

        <span class="time" v-if="status == 1">剩余支付时间: {{ timeStr }}</span>
      </p>
    </div>

    <!-- 说明 -->
    <div class="tip" v-if="status == 1">
      <p class="label">代付说明</p>
      <p class="li">
        1.付款前务必和好友确认，避免诈骗行为 <br />
        2.如果发生退款，钱将原路退回到您的账户中 <br />
        3.付款金额已当前页面展示为准 <br />
      </p>
    </div>

    <!-- 订单 -->
    <div class="order">
      <p class="title">代付订单信息</p>
      <p class="title">订单编号：{{ orderNo }}</p>

      <!-- 循环 -->
      <div class="item" v-for="item in list" :key="item.drugstoreId">
        <!-- 药店名称 -->
        <p class="label">
          {{ item.drugstoreName }}
        </p>

        <div class="item_price" v-if="item.x">
          <span>西药方</span>
          <span class="n">共{{ item.x_count }}种药品</span>
          <span class="r">￥{{ item.x | toFixed }}</span>
        </div>
        <div class="item_price" v-if="item.z">
          <span>草药方</span>
          <span class="n">共{{ item.z_count }}种药品</span>
          <span class="r">￥{{ item.z | toFixed }}</span>
        </div>
        <div class="item_price" v-if="item.s">
          <span>食品级</span>
          <span class="n">共{{ item.s_count }}种药品</span>
          <span class="r">￥{{ item.s | toFixed }}</span>
        </div>
      </div>

      <!-- 统计 -->
      <div class="count">
        <span>
          合计：<b>¥{{ detail.orderShouldMoney | toFixed }}</b>
        </span>
        <span>
          优惠：<b>¥{{ detail.orderDisMoney | toFixed }}</b>
        </span>
        <span>
          实付款：<b>¥{{ detail.orderRealMoney | toFixed }}</b>
        </span>
      </div>
    </div>

    <div class="footer" v-if="status == 1">
      <p class="but" @click="toPay">立即支付</p>
    </div>
  </div>
</template>

<script>
import myJsTools from '@/common/js/myJsTools.js';
import { getSysPlatformConfigByKeyList } from '@/api/base.js';

try {
  if (process.env.NODE_ENV !== 'development') eruda.init();
} catch (error) {}

import urlConfig from '@/common/request/config.js';

import Pay from '@/modules/pay';
import {
  orderDetailNoLogin,
  queryRegisterPayStatusHelpPay,
  queryMallOrderStatusHelpPay,
  getHelpPayCreateResult,
} from '@/api/order';

import { getOpenId } from '@/api/user.js';

function setTimeStr(val) {
  if (!val) return 0;
  val = val.replace(/-/g, '/');
  let start = new Date(val).getTime();
  let end = new Date().getTime();
  let c = 1800 - (end - start) / 1000;
  if (c <= 0) return 0;
  let m = parseInt((c / 60) % 60);
  let s = parseInt(c % 60);
  if (m < 10) m = '0' + m;
  if (s < 10) s = '0' + s;
  return m + '分' + s + '秒';
}

let timer;

let PAY;

let num = 0;

export default {
  name: 'PaymentOnBehalf',
  data() {
    return {
      orderNo: '',
      status: 1,
      detail: {},
      list: [],
      timeStr: '',
      source: '',
      appid: '',
      payList: [],
      openId: '',
      ghId: '',
    };
  },
  onLoad(v) {
    this.orderNo = v.orderNo;
    this.source = v.source;
    this.appid = v.appid;
    this.ghId = v.ghId;
    const { href } = location;
    if (href.indexOf('code') == -1) {
      this.init();
      return;
    }
    const code = myJsTools.request('code');
    this.getOpenId(this.appid, code);
    this.getDetail();
  },
  methods: {
    // 授权
    init() {
      const { orderNo, source, appid, ghId } = this;

      const url =
        urlConfig.rootUrl +
        '#/pages/payment/index?orderNo=' +
        orderNo +
        '&appid=' +
        appid +
        '&source=' +
        source +
        '&ghId=' +
        ghId;

      const link =
        'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' +
        appid +
        '&redirect_uri=' +
        encodeURIComponent(url) +
        '&response_type=code&scope=snsapi_base&state=pay#wechat_redirect';
      if (process.env.NODE_ENV === 'development') return;
      location.href = link;
    },
    // 获取openid
    async getOpenId(appid, code) {
      let { data } = await getOpenId({
        appid,
        code,
      });
      const { openId } = data;
      this.openId = openId;
      sessionStorage.setItem('openId', openId);
    },
    // 获取订单详情
    async getDetail() {
      let { data } = await orderDetailNoLogin(this.orderNo);
      const { orderDrugStoreListNewVOList: list, payStatus, source } = data;

      let status = 2;

      if (payStatus == 1) {
        status = 1;
        // 正常处方 只会存在一个药店
        if (source == 1 || source == 3) {
          const { drugstoreId } = list[0];
          PAY = new Pay(drugstoreId);
        } else {
          PAY = new Pay();
          this.getPriceList();
        }
      }

      if (payStatus == 9) {
        status = 3;
      }
      this.setList(list);

      delete data.orderDrugStoreListNewVOList;
      this.detail = data;
      this.status = status;

      if (status == 1) {
        timer = setInterval(() => {
          this.timeStr = setTimeStr(data.addTime);
          if (this.timeStr == 0) {
            clearInterval(timer);
            this.status = 3;
          }
        }, 1000);
      }
    },
    // 获取平台支付方式 商城用
    async getPriceList() {
      let res = await getSysPlatformConfigByKeyList(['onlineMallPayId']);
      let callId = res.data[0].configValue;
      // 收款方
      let list = callId.split(',') || [];
      let arr = list.map((v) => {
        let obj = {
          appid: v,
        };
        if (v.indexOf('wx') > -1) {
          obj.receiptType = 1;
        } else {
          obj.receiptType = 2;
        }
        return obj;
      });
      PAY.payList = arr;
    },
    // 药品摘要
    setList(arr) {
      let list = [];
      arr.forEach((v) => {
        // 西药价位
        let x = 0;
        // 西药数量
        let x_count = 0;
        // 重要价位
        let z = 0;
        // 重要数量
        let z_count = 0;
        // 食品价位
        let s = 0;
        // 食品数量
        let s_count = 0;
        v.drugShoppingOnlineOrderList.forEach((k) => {
          // 西药
          if (k.drugType === '1' || k.drugType === '2') {
            x += Number(k.drugRealMoney) * 100;
            //x_count += Number(k.quan) * 100;
            x_count += Number(1) * 100;
          }
          // 中药
          if (k.drugType === '3') {
            z += Number(k.drugRealMoney) * 100;
            //z_count += Number(k.quan) * 100;
            z_count += Number(1) * 100;
          }
          // 食品
          if (k.drugType === '4') {
            s += Number(k.drugRealMoney) * 100;
            //s_count += Number(k.quan) * 100;
            s_count += Number(1) * 100;
          }
        });
        list.push({
          drugstoreName: v.drugstoreName,
          x: x / 100,
          z: z / 100,
          s: s / 100,
          x_count: x_count / 100,
          z_count: z_count / 100,
          s_count: s_count / 100,
        });
      });
      this.list = list;
    },
    // 点击支付
    async toPay() {
      const { index, item } = await PAY.selePay(this.detail.orderRealMoney);
      let obj = {
        callId: item.appid,
        payType: index,
        orderNo: this.orderNo,
        openid: sessionStorage.getItem('openId'),
      };

      uni.showLoading({
        mask: true,
      });

      try {
        let { data } = await getHelpPayCreateResult(obj);
        delete data.orderNo;

        if (index == 1) {
          this.wxPay(data);
        } else {
          const { orderRealMoney, orderNo } = this.detail;
          // 支付宝
          uni.navigateTo({
            url:
              '/pages/pay/pay?price=' +
              orderRealMoney +
              '&ghId=' +
              this.ghId +
              '&orderNo=' +
              orderNo +
              '&opedId=' +
              sessionStorage.getItem('openId') +
              '&source=' +
              this.source +
              '&url=' +
              btoa(data.url),
          });
        }

        uni.hideLoading();
      } catch (error) {
        uni.hideLoading();
      }
    },
    async wxPay(info) {
      try {
        await PAY.wxPay(info);
        // 查询状态
        this.getStatus();
      } catch (error) {
        PAY.Toast('取消支付');
      }
    },
    // 查询支付状态
    async getStatus() {
      if (num >= 3) return;
      num++;
      let res;
      let obj = {
        orderNo: this.orderNo,
        ghId: this.ghId,
        openid: sessionStorage.getItem('openId'),
      };

      // 正常处方
      if (this.source == 1) {
        res = await queryRegisterPayStatusHelpPay(obj);
        res.data.orderStatus = res.data.regStatus;
      } else {
        res = await queryMallOrderStatusHelpPay(obj);
      }
      const { data } = res;

      if (data.orderStatus == 2) {
        PAY.Toast('支付成功');
        clearInterval(timer);
        this.getDetail();
      } else {
        setTimeout(this.getStatus, 2000);
      }
    },
  },
  onUnload() {
    if (timer) clearInterval(timer);
  },
};
</script>

<style lang="scss" scoped>
page {
  background-color: #f5f5f5;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.payment {
  padding-bottom: 24rpx;

  &.show {
    padding-bottom: 128rpx;
  }

  header {
    height: 216rpx;
    background-color: #ff5050;
    padding: 40rpx 32rpx 0;
    @include flex;
    align-items: flex-start;

    .head {
      height: 104rpx;
      width: 104rpx;
      border-radius: 8rpx;
      margin-right: 32rpx;
      flex: none;
    }

    .right {
      font-size: 24rpx;
      color: #fff;
      flex: 1;
      height: 104rpx;
      @include flex;
      align-items: stretch;
      flex-direction: column;

      h3 {
        font-size: 40rpx;
        margin-bottom: 10rpx;
      }
    }
  }

  .price {
    border-radius: 32rpx 32rpx 0 0;
    overflow: hidden;
    margin-top: -44rpx;
    background-color: #fff;

    p {
      @include flex(lr);
      height: 88rpx;
      font-size: 28rpx;
      padding: 0 32rpx;

      &.label {
        font-weight: bold;
      }

      span {
        color: #ff5050;

        &.bold {
          font-weight: bold;

          b {
            font-size: 36rpx;
          }
        }

        &.time {
          color: #333;
        }
      }
    }
  }

  .tip {
    background-color: #fff;
    margin-top: 24rpx;
    padding: 0 32rpx 24rpx;
    font-size: 28rpx;

    .label {
      @include flex(left);
      height: 88rpx;
    }
    .li {
      line-height: 40rpx;
    }
  }

  .order {
    background-color: #fff;
    margin-top: 24rpx;
    padding: 0 32rpx;
    font-size: 28rpx;
    color: #333;

    .title {
      @include flex(left);
      height: 60rpx;
      font-weight: bold;

      &:nth-child(2) {
        height: 88rpx;
        border-bottom: 1px solid #f5f5f5;
      }
    }

    .item {
      &:nth-child(4) {
        margin-top: 24rpx;
      }

      .label {
        font-weight: bold;
        position: relative;
        padding-left: 20rpx;
        @include flex(left);
        height: 88rpx;

        &::before {
          content: '';
          display: block;
          position: absolute;
          width: 6rpx;
          height: 28rpx;
          left: 0;
          top: calc(50% - 14rpx);
          @include bg_theme;
        }
      }

      &_price {
        height: 76rpx;
        background-color: #f5f5f5;
        @include flex(lr);
        padding: 0 24rpx;
        margin-bottom: 24rpx;

        &:last-child {
          margin-bottom: 0;
        }

        span {
          flex: none;
          min-width: 180rpx;

          &.r {
            color: #ff0000;
            font-weight: bold;
            text-align: right;
          }

          &.n {
            flex: 1;
          }
        }
      }
    }

    .count {
      @include flex(right);
      height: 80rpx;

      span {
        margin-left: 20rpx;
        b {
          font-weight: bold;
          color: red;
        }
      }
    }
  }

  .footer {
    width: 100%;
    height: 108rpx;
    @include flex;
    padding: 0 32rpx;
    background-color: #fff;
    position: fixed;
    z-index: 9;
    bottom: 0;
    left: 0;
    border-radius: 16rpx 16rpx 0 0;
    box-shadow: 0 -2rpx 3rpx #e0e0e0;
    overflow: hidden;

    p {
      height: 84rpx;
      flex: 1;
      border-radius: 42rpx;
      @include flex;
      font-size: 32rpx;
      color: #fff;
      @include bg_theme;
      font-weight: bold;
    }
  }
}
</style>
