import { getSysPlatformConfigByKeyList } from './api/base'

console.log('CONFIG - - -', CONFIG_ENV)

import Vue from 'vue'
import App from './App'
import store from './store/store.js'
//跨域
import { VueJsonp } from 'vue-jsonp'
import $router, { $route } from './common/router/uni_router.js'
import date from './utils/date.js'
import { add } from './api/buriedPoint.js'
import uView from 'uview-ui'
Vue.use(uView)
import indexedDB from '@/db/main.js'
Vue.prototype.$indexedDB = indexedDB
Vue.use(VueJsonp)
Vue.config.productionTip = false
let WebIM = require('utils/WebIM')['default']
import myJsTools from './common/js/myJsTools.js'
import { initRouter, init } from './router'

// 引入加载动画组件
import Loading from '@/components/loading'
Vue.use(Loading)

initRouter()
init()
// import eruda from 'eruda';
// eruda.init();
// 自定义指令 异步获取图片
Vue.directive('img', {
  bind(el, binding) {
    console.log(binding.value)
    if (!binding.value || binding.value == 'null') return
    myJsTools.downAndSaveImg(
      binding.value,
      (url) => {
        if (!url) {
          el.src = el.dataset.src
          return
        }
        el.src = url
        // 如果存在参数 添加点击事件预览
        if (binding.arg == 'click')
          el.onclick = (e) => {
            // 阻止事件继续传播
            e.stopPropagation()
            myJsTools.previewImg(el.src)
          }
      },
      () => {
        let src = el.dataset.src
        if (src) el.src = src
      }
    )
  },
})

/**
 * 数值统一两位小数展示
 * @param {number} val
 * @returns {string} val
 */
Vue.filter('toFixed', function (val) {
  if (isNaN(Number(val))) return val
  return Number(val).toFixed(2) || val
})

let conn = {
  closed: false,
  curOpenOpt: {},
  open(opt) {
    this.curOpenOpt = opt
    WebIM.conn.open(opt)
    this.closed = false
  },
  reopen() {
    if (this.closed) {
      //this.open(this.curOpenOpt);
      WebIM.conn.open(this.curOpenOpt)
      this.closed = false
    }
  },
}

$router.onchange = (n, o) => {
  // 注册一个全局路由监听方法（可以监听小程序的物理返回），实现原理见最下方
  let data = {
    customerTime: date.format(new Date().getTime()),
    customerType: 'G',
    event: 'P1',
    page: n.fullPath,
    position: '未知',
    userId: uni.getStorageSync('userId'),
    userType: uni.getStorageSync('userId') ? '1' : '2',
    eventData: JSON.stringify({
      openId: uni.getStorageSync('wxInfo').openId,
    }),
  }
  // 发起请求 不需要回执
  add(data)
}

Vue.prototype.$iRouter = $router // 路由对象，保存了实例方法

Vue.mixin({
  onLoad() {
    this.$iRoute
  },
})

Vue.prototype.$im = WebIM
Vue.prototype.$conn = conn
App.mpType = 'app'

if (process.env.VUE_APP_FIRMID === 'dev') {
  try {
    if (process.env.NODE_ENV != 'development') {
      eruda.init()
    }
  } catch (e) {}
}
window.app = new Vue({
  ...App,
  store,
})
app.$mount()

//因为信息公示返回会重新加载页面 所以请求配置的方法放在main.js中
function reloadInfo() {
  getSysPlatformConfigByKeyList([
    'fast_prescribe',
    'patientSideDisplayOnlineMall',
    'show_doctor_practice_hospital',
    'patient_show_invoice',
  ]).then((res) => {
    let data = res.data
    data.forEach((v) => {
      // // 是否开启快捷开方
      // if (v.configKey == "fast_prescribe" && v.configValue == 0) {
      //   this.isScanCode = true;
      // }
      // 如果未开启 隐藏商城入口
      if (v.configKey == 'patientSideDisplayOnlineMall' && v.configValue == 0) {
        wx.setTabBarItem({
          index: 2,
          pagePath: 'pages/shop/index',
          text: '在线购药',
          iconPath: 'static/images/tabbar/shop.png',
          selectedIconPath: 'static/images/tabbar/shop_act.png',
          visible: false,
        })
      }
      if (
        v.configKey == 'show_doctor_practice_hospital' &&
        v.configValue == 1
      ) {
        uni.setStorageSync('isShowWorkHosName', true)
      }
      if (v.configKey == 'patient_show_invoice' && v.configValue == 1) {
        uni.setStorageSync('showInvoice', true)
      }
    })
  })
}

reloadInfo()
