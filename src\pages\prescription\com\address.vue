<template>
  <!-- 自提样式 -->
  <view class="address">
    <view class="title">
      {{ detail.addressArea }} {{ detail.addressDetail }}
    </view>
    <view class="user" @click="click">
      <text>{{ detail.deliveryName }}</text>
      <text class="phone">{{ phone(detail.deliveryTelNo) }}</text>
    </view>
  </view>
</template>

<script>
import myJsTools from '@/common/js/myJsTools.js';
export default {
  name: 'Addreess',
  props: {
    detail: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  methods: {
    click() {
      this.$emit('click');
    },
    phone(tel) {
      if (!tel) return tel;
      return myJsTools.phone(tel);
    },
  },
};
</script>

<style lang="scss" scoped>
.address {
  width: 100%;
  padding: 32rpx;
  border-radius: 8rpx;
  background-color: #fff;
  box-sizing: border-box;

  .title {
    font-size: 28rpx;
    font-weight: bold;
    color: #333;

    text {
      display: inline-block;
      height: 32rpx;
      padding: 0 6rpx;
      background-color: #666;
      color: #fff;
      font-size: 22rpx;
      border-radius: 4rpx;
      margin-right: 10rpx;
    }
  }

  .user {
    @include flex(lr);
    margin-top: 18rpx;

    text {
      font-size: 26rpx;
      color: #666;
    }

    .phone {
      flex: 1;
      padding-left: 20rpx;
    }
  }
}
</style>
