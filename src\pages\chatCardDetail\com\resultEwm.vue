<template>
  <!-- 二维码模块 -->
  <view class="ewm">
    <view class="ewm_img">
      <QRCODE
        ref="qrcode"
        :val="code"
        :size="300"
        background="#ffffff"
        foreground="#000000"
        pdground="#000000"
        icon="/static/images/logo-img.png"
        :iconSize="45"
        onval
        loadMake
      />

      <!-- 单号 -->
      <view class="ewm_info">
        <text>请您于{{ time }}</text>
        <text>到院进行就诊</text>
      </view>
    </view>

    <!-- 机构 -->
    <HOS v-if="hosId" :hosId="hosId" />
  </view>
</template>

<script>
import QRCODE from '@/components/tki-qrcode/tki-qrcode.vue';
import HOS from './hos.vue';

export default {
  name: 'Ewm',
  props: ['time', 'code', 'hosId'],
  components: {
    QRCODE,
    HOS,
  },
};
</script>

<style lang="scss" scoped>
.ewm {
  width: 100%;
  background: #fff;
  border-radius: 8rpx;

  .ewm_img {
    @include flex;
    flex-direction: column;
    padding: 38rpx 0 0;

    .ewm_info {
      @include flex;
      flex-direction: column;
      color: #999;
      margin-top: 28rpx;
      font-size: 28rpx;
    }
  }
}
</style>
