
import http from '../common/request/request';
/**
 * 查询医生端标签列表
 */
export function labelList(data) {
    return http({
        url: "basic/dicSzArticleLabel/pageList",
        method: "post",
        data: data,
    });
}

/**
 * 查询医生端文章列表
 */
export function arcTagList(data) {
    return http({
        url: "basic/dicSzArticle/pageList",
        method: "post",
        data: data,
    });
}

/**
 * 查询医生端文章详情
 */
export function arcTagInfo(data) {
    return http({
        url: "basic/dicSzArticle/info",
        method: "post",
        data: data,
    });
}
export function findRecommendDocPage(data) {
    return http({
        url: "basic/dicSzArticleRecDoc/findRecommendDocPage",
        method: "post",
        data: data,
    });
}