<template>
  <view>
    <view class="timeSelect">
      <view
        class="selectOpt"
        v-for="(item, index) in timeList"
        :key="index"
        @click="getMyList(index)"
      >
        <span>{{ item.text }}</span>
        <img
          src="../../static/images/chat/selected.png"
          class="selectedIcon"
          v-if="index == active"
        />
      </view>
    </view>
  </view>
</template>

<script>
import { Toast } from '@/common/js/pay.js';
import date from '@/utils/date';
export default {
  name: 'roamChat',
  data() {
    return {
      active: -1,
      timeList: [
        {
          text: '找回3天',
          type: 3 * 24 * 3600 * 1000,
          pagesize: 25,
        },
        {
          text: '找回7天',
          type: 7 * 24 * 3600 * 1000,
          pagesize: 100,
        },
        {
          text: '找回30天',
          type: 30 * 24 * 3600 * 1000,
          pagesize: 200,
        },
      ],
      userId: '',
      patientId: '',
      time: '', //截止时间
      hisList: [],
      docId: '',
      oneList: [],
      chatId: '',
    };
  },
  onLoad(options) {
    this.pageParam = JSON.parse(options.param);
    this.chatId =
      this.pageParam.patientId.toLowerCase() +
      ',' +
      this.pageParam.docId.toLowerCase();
  },
  created() {
    let list = this.$store.getters.getChatList;
    if (list.length) {
      list.chatRecordList.map((item) => {
        this.oneList.push(item);
      });
    }
  },
  methods: {
    getMyList(index) {
      if (index == this.active) {
        return;
      }
      this.hisList = [];
      this.active = index;
      this.$im.conn.mr_cache = [];
      uni.showLoading({
        title: '找回中...',
      });
      setTimeout(uni.hideLoading, 3000);
      this.time = date.dateDif(new Date().getTime(), this.timeList[index].type);
      // 自定义加载图标
      this.getHistoryList();
    },
    getHistoryList() {
      let me = this;
      var options = {
        queue: this.pageParam.docId.toLowerCase(),
        isGroup: false,
        count: 30,
        success: function(res) {
          if (!res.length) {
            uni.hideLoading();
            Toast('没有历史数据');
            return;
          }
          if (res.length > 0) {
            let list = res;
            let arr = [];
            if (list.length < 50) {
              for (let i = list.length - 1; i >= 0; i--) {
                let item = list[i];
                if (date.format(item.timestamp) > me.time) {
                  me.hisList.unshift(list[i]);
                } else {
                  break;
                }
              }
              me.resetListData();
            } else {
              if (date.format(list[0].timestamp) > me.time) {
                for (let i = list.length - 1; i >= 0; i--) {
                  me.hisList.unshift(list[i]);
                }
                me.getHistoryList();
              } else {
                for (let i = list.length - 1; i >= 0; i--) {
                  let item = list[i];
                  if (date.format(item.timestamp) > me.time) {
                    me.hisList.unshift(list[i]);
                  } else {
                    me.resetListData();

                    break;
                  }
                }
              }
            }
          }
        },
        fail: function() {
          uni.hideLoading();
        },
      };
      this.$im.conn.fetchHistoryMessages(options);
    },
    resetListData() {
      let arr = [];
      this.hisList.map((item) => {
        let status;
        if (item.from != this.pageParam.docId.toLowerCase()) {
          status = 'send';
          item.direction = 'send';
        } else {
          status = 'read';
          item.direction = 'receive';
        }
        let msg = {
          type: item.direction,
          messType: item.contentsType.toLowerCase(),
          content: item.data,
          from: item.from,
          to: item.to,
          time: item.time,
          duration: item.duration,
          status: status,
          ext: item.ext,
          id: item.id,
        };
        if (
          msg.messType == 'image' ||
          msg.messType == 'video' ||
          msg.messType == 'voice'
        ) {
          msg.content = item.url;
        }
        if (
          item.ext?.patientId?.toLowerCase() ==
          this.pageParam.patientId?.toLowerCase()
        ) {
          arr.push(msg);
        }
      });
      let res = {
        chatRecordList: arr,
        chatId: this.chatId,
      };
      this.$store.commit('setOneChatList', res);
      uni.hideLoading();
    },
  },
};
</script>

<style scoped lang="scss">
.selectOpt {
  color: #333333;
  font-size: 15px;
  line-height: 21px;
  background: #ffffff;
  margin-bottom: 1px;
  padding: 12px 16px;
  position: relative;

  img {
    width: 22px;
    height: 22px;
    position: absolute;
    right: 16px;
    top: 12px;
  }
}
</style>
