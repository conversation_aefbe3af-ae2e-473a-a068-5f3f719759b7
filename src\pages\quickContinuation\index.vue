<template>
  <view class="quickContinuation">
    <u-form
      class="quickContinuation-form"
      :model="form"
      ref="uForm"
      label-width="150px"
    >
      <u-form-item label="">
        <view
          style="width: 100%; display: flex; align-items: center"
          @click="!isHistory && (isPeopleToSee = true)"
        >
          <text :class="{ x: true }">就诊人(必填)</text>
          <div style="display: flex; align-items: center; flex: 1">
            <u-input
              v-model="form.patientName"
              :disabled="isHistory"
              placeholder="请选择"
              type="select"
              @click="!isHistory && (isPeopleToSee = true)"
            />
            <u-icon
              name="arrow-right"
              @click="!isHistory && (isPeopleToSee = true)"
            ></u-icon>
          </div>
          <u-select
            v-model="isPeopleToSee"
            :list="patientList"
            value-name="patientId"
            label-name="patientNameStr"
            @confirm="confirmPeople"
          ></u-select>
        </view>
      </u-form-item>
      <u-form-item v-if="source !== 'shop'" label="是否已在平台开方">
        <u-radio-group
          v-model="isPlatformKf"
          placement="row"
          @change="groupChange"
          activeColor="#874ff0"
          class="isPlatformKf"
        >
          <u-radio
            :customStyle="{ marginBottom: '8px' }"
            v-for="(item, index) in radiolist1"
            :key="index"
            :label="item.name"
            :name="item.name"
          >
            {{ item.label }}
          </u-radio>
        </u-radio-group>
      </u-form-item>
      <view>
        <view class="quickContinuation-item">
          <span :class="{ x: true }">临床诊断(必填)</span>
          <span
            class="history-icon"
            @click="historyCF"
            v-if="isPlatformKf === '1'"
          >
            选择历史处方<u-icon name="arrow-right"></u-icon>
          </span>
        </view>
        <!-- 诊断 -->
        <ZdCom @commitDiagList="commitDiagList" :drugstoreId="drugstoreId" />
      </view>
      <!-- 适应症 -->
      <view class="adaptation-list" v-if="drugAdaptationList.length > 0">
        <view
          class="adaptation-item"
          v-for="(item, index) in drugAdaptationList"
          :key="index"
          @click="selectAdaptation(item)"
        >
          {{ item.diagName }}
        </view>
      </view>
      <u-form-item
        :label="!isRequired ? '上次开方时间（非必填）' : '上次开方时间'"
        right-icon="arrow-right"
      >
        <u-input
          v-model="form.previousPrescriptionTime"
          :disabled="isHistory"
          type="select"
          placeholder="请选择"
          @click.native="!isHistory && (show = true)"
        />
        <!--        <u-picker-->
        <!--          mode="time"-->
        <!--          v-model="show"-->
        <!--          :params="params"-->
        <!--          @confirm="confirmDate"-->
        <!--        ></u-picker>-->
        <u-calendar
          v-model="show"
          mode="date"
          @change="confirmDate"
        ></u-calendar>
      </u-form-item>

      <view>
        <view
          class="quickContinuation-item"
          style="margin-top: 5px; margin-bottom: 10px"
        >
          <span>药品</span>
          <view
            v-if="source !== 'shop'"
            class="add-medicine"
            @click="isAddMedicine = true"
            >添加药品 +
          </view>
        </view>
        <!-- 药品列表 -->
        <view class="drugDiv">
          <view
            class="drug_card"
            v-for="(item, index) in drugList"
            :key="index"
          >
            <!-- 药品卡片 -->
            <view class="card">
              <view style="position: relative">
                <img
                  v-if="item.drugImg"
                  v-img="item.drugImg"
                  class="card_img"
                />
                <image
                  v-else
                  class="card_img"
                  mode="aspectFill"
                  src="/static/shop/drug.png"
                ></image>
                <!--                <image-->
                <!--                    :src="item.docImgUrl||'/static/shop/drug.png'"-->
                <!--                    mode="aspectFill"-->
                <!--                    class="card_img"-->
                <!--                ></image>-->
                <view
                  v-if="item.drugType == '025.9'"
                  class="drug-coverUp cfCover"
                >
                  处方药 <br />
                  依规定不展示包装
                </view>
              </view>
              <!-- 文字 -->
              <view class="card_text">
                <view class="text_title">{{ item.drugName }} </view>
                <view class="text_info">规格：{{ item.gg }}</view>
                <!--                  <view class="text_info"-->
                <!--                  >药店：{{ item.drugstoreName-->
                <!--                    }}{{ item.isProprietary == '1' ? '（自营）' : '' }}</view-->
                <!--                  >-->
                <view class="text_price">
                  <text>单价：</text>
                  ￥{{ item.price }}
                  <text v-if="source === 'shop'" class="num"
                    >×{{ item.quan }}</text
                  >
                </view>
              </view>
            </view>
            <!-- 库存操作 -->
            <view class="drug_action">
              <!--              <text class="count">库存{{ item.drugKc || 0 }}件</text>-->
              <!-- 操作 -->
              <u-button
                v-if="source !== 'shop'"
                type="error"
                size="mini"
                @click="deleteDrug(item, index)"
                >删除</u-button
              >

              <view class="action" v-if="source !== 'shop'">
                <image
                  @click="!item.history && reduce(item)"
                  class="icon"
                  src="/static/shop/del.png"
                ></image>
                <text class="num">{{ item.quan }}</text>
                <image
                  @click="!item.history && add(item)"
                  class="icon"
                  src="/static/shop/add.png"
                ></image>
              </view>
            </view>
          </view>
        </view>

        <u-popup v-model="isAddMedicine" mode="bottom">
          <view class="AddMedicine">
            <u-search
              placeholder="请输入药品名称"
              v-model="keyword"
              style="margin-bottom: 20px"
            ></u-search>
          </view>
        </u-popup>
      </view>
      <!--      新增字段-->
      <view class="base-info" v-if="isPlatformKf === '0'">
        <view class="input-box border-1">
          <text :class="{ x: isRequired }"
            >标题{{ !isRequired ? '（非必填）' : '' }}</text
          >
          <textarea
            :value="info.recordsTitle"
            v-model="info.recordsTitle"
            placeholder="填写您的症状或特征、性质以及持续时间"
            maxlength="20"
            :focus="true"
            style="text-align: right"
          />
        </view>

        <view class="input-box">
          <text :class="{ x: isRequired }"
            >首诊医院{{ !isRequired ? '（非必填）' : '' }}</text
          >
          <view class="select-box" @click="isFirstVisitHos = true">
            <view style="margin-right: 10px; flex: 1">{{
              info.firstVisitHos || '请选择'
            }}</view>
            <u-icon name="arrow-down" />
          </view>
          <u-popup v-model="isFirstVisitHos" mode="bottom">
            <view>
              <view class="popup-top">
                <view class="cancel" @click="isFirstVisitHos = false"
                  >取消</view
                >
                <view class="success" @click="sureEvent">完成</view>
              </view>
              <view style="padding: 20px">
                <u-search
                  placeholder="请输入"
                  v-model="keyword"
                  :show-action="false"
                  shape="round"
                  @search="getSearch"
                ></u-search>
              </view>
              <scroll-view
                scroll-y
                class="hospital-list"
                @scrolltolower="loadMoreHospitals"
              >
                <view
                  v-for="(item, index) in hosList"
                  :key="index"
                  class="hospital-item"
                  :class="{
                    'hospital-item-selected': selectedHospitalIndex === index,
                  }"
                  @click="selectHospital(index)"
                >
                  {{ item.name }}
                </view>
                <view v-if="isLoadingMore" class="loading-more">加载中...</view>
              </scroll-view>
            </view>
          </u-popup>
        </view>
        <view class="input-box">
          <text :class="{ x: isRequired }"
            >患病时长{{ !isRequired ? '（非必填）' : '' }}</text
          >
          <input
            type="number"
            :value="info.sickTime"
            v-model="info.sickTime"
            placeholder="请填整数"
            maxlength="6"
            style="text-align: right"
          />
          <view class="select-day" @click="selectUnit">
            <!-- <input type="text" :value="info.timeUnit" v-model="info.timeUnit" disabled /> -->
            <view class="select_time">{{ info.timeUnit }}</view>
            <image src="/static/images/sx.png" class="icon_sx"></image>
          </view>
        </view>
        <view class="title_format" :class="{ x: isRequired }"
          >病情描述（详细描述您的病情，症状，治疗经过）{{
            !isRequired ? '（非必填）' : ''
          }}</view
        >
        <view class="content">
          <textarea
            placeholder="详细描述您的病情，症状，治疗经过"
            maxlength="200"
            :value="info.diseaseDescription"
            v-model="info.diseaseDescription"
            class="other-content"
          />
          <view class="char-count" style="bottom: -22px"
            >{{ info.diseaseDescription.length }}/200</view
          >
        </view>
        <view class="title_format" :class="{ x: isRequired }"
          >期望获得的帮助{{ !isRequired ? '（非必填）' : '' }}</view
        >
        <view class="content">
          <textarea
            placeholder="详细描述您的期望帮助"
            maxlength="200"
            :value="info.expectHelp"
            v-model="info.expectHelp"
            class="other-content"
          />
          <view class="char-count" style="bottom: 10px"
            >{{ info.expectHelp.length }}/200</view
          >
          <view class="shortcut">
            <view style="color: #999; font-size: 24rpx; line-height: 34rpx"
              >快捷输入</view
            >
            <view class="shortcut_box">
              <template v-for="item in shortcutList">
                <view
                  class="shortcut_little"
                  @click="getShortcut(item.quickInputName)"
                  >{{ item.quickInputName }}</view
                >
              </template>
            </view>
          </view>
        </view>
      </view>

      <view>
        <view class="quickContinuation-item">
          <view>处方图片（9）（非必填）</view>
        </view>
        <!--        <view style="margin-bottom: 8px;font-size: 12px;color: #737373">-->
        <!--          请上传患者历史就诊病例图片，最多9张-->
        <!--        </view>-->
        <view class="img_list">
          <template v-show="fileList.length">
            <view
              class="img_item"
              v-for="(item, index) in fileList"
              :key="index"
            >
              <image
                :src="item.baseUrl || item.url"
                @click="preview(item)"
                mode="aspectFit"
                class="img2"
              ></image>
              <!-- 删除按钮 -->
              <view class="del" @click="delFile(index)">
                <uni-icons type="close" color="#fff" size="24"></uni-icons>
              </view>
            </view>
          </template>
          <!-- 上传 -->
          <view class="img_item" @click="cloosImg" v-if="fileList.length < 9">
            <image class="img" src="/static/doc/u-1.png"></image>
            <!--            <text>+</text>-->
            <!--            <text>上传图片</text>-->
          </view>
        </view>
      </view>
    </u-form>
    <!-- 选择患病时长单位 -->
    <propBottom
      v-if="unitShow"
      :actions="columns"
      @propConfirm="propConfirm"
      @propCancel="propCancel"
    ></propBottom>
    <FooterButton @click="submit">{{
      this.source === 'shop' ? '提交' : '支付并提交'
    }}</FooterButton>
    <!-- 药弹框 -->
    <DrugPopUp v-model="isAddMedicine" :cfIndex="0" :drugIndex="0" />
    <!-- 设置用法用量 -->
    <!--    <DrugSetPopUp v-model:popup-visable="setDrugPopVisable" :patent="patent" :editOrAddDrug="editOrAddDrug"-->
    <!--                  :cfIndex="cfIndex" :drugIndex="drugIndex" />-->
  </view>
</template>

<script>
import { Toast } from '@/common/js/pay.js'
import FooterButton from '@/components/footer_button/button.vue'
import propBottom from '@/components/propBottom/propBottom.vue'
import {
  getPatientReceiveStatus,
  getPatientList,
  saveQuickPrescriptionRegister,
  createPatientAllocateDoc,
} from '@/api/user.js'
import _ from 'lodash'
import myJsTools from '@/common/js/myJsTools.js'
import { uploadImg, downPdf } from '@/api/oss.js'
import ZdCom from './zdCom.vue'
import DrugPopUp from './drugPopup.vue'
import { mapState } from 'vuex'
import { getPatientPrescriptionBusinessOrderInfo } from '@/api/cf'
import Pay from '@/modules/pay'
import {
  batchGetDrugLimitNum,
  dicSzHospitalList,
  getRegisterPayInfo,
  queryRegisterPayStatus,
  checkMaxQuanPatient,
} from '../../api/base'
import { batchGetDrugAdaptation } from '@/api/shop'
import { basicgetPatientChatList } from '@/api/chat'
import { getPatientChat2 } from '../../api/order'
import { showModal } from '@/common/js/pay.js'
import drugRulesMixin from '@/mixins/drugRules.js'
let PAY
let num = 3
export default {
  components: { FooterButton, ZdCom, DrugPopUp, propBottom },
  mixins: [drugRulesMixin],
  data() {
    return {
      form: {
        patientName: '',
        intro: '',
        sex: '',
        previousPrescriptionTime: '',
        prescriptionImg: [],
        historyPrescriptionId: '',
      },
      columns: [
        {
          name: '天',
          value: '0',
        },
        {
          name: '周',
          value: '1',
        },
        {
          name: '月',
          value: '2',
        },
        {
          name: '年',
          value: '3',
        },
      ],
      btnFlag: true,
      params: {
        year: true,
        month: true,
        day: true,
        hour: false,
        minute: false,
        second: false,
      },
      show: false,
      isPeopleToSee: false,
      isAddMedicine: false,
      keyword: '',
      patientList: [
        {
          patientNameStr: '添加就诊人',
          patientId: 'add',
        },
      ],
      checkpatientInfo: {},
      fileList: [],
      checkCf: {},
      getAllocation: {},
      ghId: '',
      regId: '',
      isHistory: false,
      // 基本案列数据
      radiolist1: [
        {
          label: '否',
          name: '0',
          disabled: false,
        },
        {
          label: '是',
          name: '1',
          disabled: false,
        },
      ],
      // u-radio-group的v-model绑定的值如果设置为某个radio的name，就会被默认选中
      isPlatformKf: '1',
      isRequired: true,
      isFirstVisitHos: false,
      checkHos: [],
      hosList: [],
      allList: [],
      checkHosInfo: {},
      info: {
        recordsTitle: '', // 标题
        firstVisitHos: '', // 首诊医院
        sickTime: '', // 患病时长
        timeUnit: '天', // 患病时长单位
        diseaseDescription: '', // 病情描述
        expectHelp: '', // 期望获得的帮助
      },
      indicatorStyle: `height: 50px;`,
      unitShow: false,
      shortcutList: [], // 快捷输入
      source: null,
      selectedHospitalIndex: null,
      isLoadingMore: false,
      currentPage: 1,
      totalPages: 1,
      pageSize: 20,
      drugstoreId: '',
      drugAdaptationList: [], // 药品适应症列表
    }
  },
  computed: {
    ...mapState({
      drugList: (state) => state.drugList,
    }),
  },
  watch: {
    drugList: {
      async handler(newVal) {
        console.log('newVal', newVal)
        if (newVal.length >= 0) {
          this.drugAdaptationList = []
          let drugIds = newVal.map((v) => v.drugId)
          let res = await batchGetDrugAdaptation({
            drugIds,
          })
          console.log('res', res)
          if (res.data && res.data.length > 0) {
            const lists = res.data
            lists.forEach((item) => {
              item.indications.forEach((indication) => {
                let listSome = this.drugAdaptationList.some(
                  (v) => v.diagId == indication.diagId
                )
                if (!listSome) {
                  this.drugAdaptationList.push(indication)
                }
              })
            })
          }
          console.log('this.drugAdaptationList', this.drugAdaptationList)
        }
      },
    },
  },
  async onLoad(query) {
    await this.initDrugRules()
    this.source = query.source
    this.isPlatformKf = this.source === 'shop' ? '0' : '1'
    uni.setNavigationBarTitle({
      title: this.source === 'shop' ? '补充就诊人信息' : '快速续方',
    })
    this.drugstoreId = this.drugList[0]?.drugstoreId || ''
    console.log('drugList', this.drugList, this.drugstoreId)
    // 如果是特定药店的购物车提交，不需要必填字段
    if (
      this.drugstoreId === '574a40ea01424f7a8b62762b37ff58e2' &&
      this.source === 'shop'
    ) {
      this.isRequired = false
    }

    // 商城处方购药，购物车提交，需要获取药品适应症
    if (this.drugList.length > 0 && this.source === 'shop') {
      this.drugAdaptationList = []
      let drugIds = this.drugList.map((v) => v.drugId)
      let res = await batchGetDrugAdaptation({
        drugIds,
      })
      console.log('res', res)
      if (res.data && res.data.length > 0) {
        const lists = res.data
        lists.forEach((item) => {
          item.indications.forEach((indication) => {
            let listSome = this.drugAdaptationList.some(
              (v) => v.diagId == indication.diagId
            )
            if (!listSome) {
              this.drugAdaptationList.push(indication)
            }
          })
        })
      }
      console.log('this.drugAdaptationList', this.drugAdaptationList)
    }
    this.getPayList()
    this.getHosList()
  },
  onShow() {
    this.getPatientList()
    console.log('checkCf', uni.getStorageSync('checkCf'))
    this.checkCf = uni.getStorageSync('checkCf')
    this.isHistory = false
    if (this.checkCf && this.checkCf.businessId) {
      this.isHistory = true
      this.form.patientName = this.checkCf.patientName
      this.form.patientId = this.checkCf.patientId
      this.form.previousPrescriptionTime = this.checkCf.addTime.split(' ')[0]
      console.log(this.drugList, this.checkCf.diagNames)
      this.$store.commit('setDiagList', [{ diagName: this.checkCf.diagNames }])
      this.getPatientPrescriptionBusinessOrderInfo()
    }
    console.log(this.drugList)
  },
  methods: {
    // 选择适应症
    selectAdaptation(item) {
      console.log('item', item)
      let list = this.$store.state.diagList
      let listSome = list.some((v) => v.diagId == item.diagId)
      if (listSome) {
        return uni.showToast({
          title: '请勿重复添加',
          icon: 'none',
        })
      }
      list.push({
        ...item,
      })
      this.$store.commit('setDiagList', list)
    },
    // 接收选择患病时长组件的返回值
    propConfirm(evt) {
      this.unitShow = false
      this.info.timeUnit = evt.name
    },
    // 取消选择患病时长单位
    propCancel() {
      this.unitShow = false
    },
    groupChange(n) {
      console.log('groupChange', n)
    },
    // 操作快捷输入
    getShortcut(evt) {
      this.info.expectHelp = this.info.expectHelp + evt
    },
    // 选择患病时长,默认"天"
    selectUnit() {
      this.unitShow = true
    },
    getHosList() {
      const data = {
        page: this.currentPage,
        limit: this.pageSize,
      }
      if (this.keyword) {
        data.name = this.keyword
      }

      this.isLoadingMore = true
      dicSzHospitalList(data)
        .then((res) => {
          if (this.currentPage > 1) {
            this.hosList = [...this.hosList, ...(res.data.rows || [])]
            this.allList = [...this.allList, ...(res.data.rows || [])]
          } else {
            this.hosList = res.data.rows || []
            this.allList = res.data.rows || []
          }
          this.totalPages = Math.ceil(res.data.total / this.pageSize)
          this.isLoadingMore = false
        })
        .catch(() => {
          this.isLoadingMore = false
        })
    },
    getSearch(value) {
      this.keyword = value
      this.currentPage = 1
      this.hosList = []
      this.isLoadingMore = true

      const data = {
        page: 1,
        limit: this.pageSize,
        name: value,
      }

      dicSzHospitalList(data)
        .then((res) => {
          this.hosList = res.data.rows || []
          this.totalPages = Math.ceil(res.data.total / this.pageSize)
          this.isLoadingMore = false
        })
        .catch(() => {
          this.isLoadingMore = false
        })
    },
    sureEvent() {
      this.isFirstVisitHos = false
      if (this.selectedHospitalIndex === null) {
        this.info.firstVisitHos = this.hosList[0]?.name
        return
      }
      this.info.firstVisitHos = this.hosList[this.selectedHospitalIndex]?.name
    },
    selectHospital(index) {
      this.selectedHospitalIndex = index
      this.info.firstVisitHos = this.hosList[index]?.name
    },
    deleteDrug(item, index) {
      console.log(this.drugList)
      this.drugList.splice(index, 1)
      // this.$store.commit('setDrugList',[])
      console.log('this.$store.state.drugList', this.$store.state.drugList)
    },
    historyCF() {
      uni.navigateTo({
        url:
          '/pages/personalCenter/myPrescription/index?' +
          `flag=check` +
          `&selectedPatientId=${this.form.patientId || ''}`,
      })
    },
    getPatientPrescriptionBusinessOrderInfo() {
      const data = {
        businessId: this.checkCf.businessId,
      }
      getPatientPrescriptionBusinessOrderInfo(data).then((res) => {
        const info = res.data[0]
        if (info && info.prescriptions.length) {
          const details = info.prescriptions[0].details
          const proPrescriptionMasterVO =
            info.prescriptions[0].proPrescriptionMasterVO
          const proBusinessInfo = info.proBusinessInfo || {}
          this.form.age = proBusinessInfo.age
          this.form.historyPrescriptionId =
            proPrescriptionMasterVO.prescriptionId
          this.$store.commit(
            'setDrugList',
            details.map((v) => {
              return {
                ...v,
                history: true,
              }
            })
          )
        }
      })
    },
    reduce(item) {
      const reduceQuantity = this.getReduceQuantity(item.drugId, item.quan)
      const newQuantity = item.quan * 1 - reduceQuantity

      if (newQuantity <= 0) {
        // item.quan = 0
      } else {
        item.quan = newQuantity
      }
    },
    // 增加药品数量
    add(item) {
      const addQuantity = this.getAddQuantity(item.drugId, item.quan)
      item.quan = item.quan * 1 + addQuantity
    },
    commitDiagList() {},
    // 选择图片
    cloosImg() {
      const that = this
      uni.chooseImage({
        count: 1,
        success: function (res) {
          // 读取图片
          const file = res.tempFiles[0]
          myJsTools.setImgZip(file, async (dataUrl) => {
            let url = await that.upImg({
              base64: dataUrl.split(',')[1],
              url: dataUrl,
              name: '',
            })
            let fileName =
              Number(
                Math.random().toString().substr(3, 18) + Date.now()
              ).toString(36) + '.png'
            console.log(url)
            that.fileList.push({ url, fileName, baseUrl: dataUrl })
          })
        },
      })
    },
    // 上传图片
    async upImg(obj) {
      let para = {
        folderType: 11,
        imgBody: obj.base64,
        // 患者id
        otherId: this.patientId,
      }
      let res = await uploadImg(para)
      // 图片名称
      return res.data.url
    },
    // 图片预览
    previewShow(item) {
      console.log('11111', item)
      const showImg = item.map((ele) => {
        return ele.url
      })
      uni.previewImage({
        urls: showImg,
      })
    },
    // 图片预览
    preview(item) {
      uni.previewImage({
        urls: [item.baseUrl || item.url],
      })
    },
    // 移除图片
    delFile(n) {
      this.fileList.splice(n, 1)
    },
    // 获取就诊人列表
    async getPatientList() {
      let userId = uni.getStorageSync('userId')
      let { data } = await getPatientList({
        userId,
      })
      if (!data) {
        this.patientList = []
        return
      }
      let disease = []
      data.forEach((element) => {
        disease.push(element.patientId)
      })
      uni.setStorageSync('patientIdList', disease)
      this.patientList = (data || []).map((v) => {
        return {
          ...v,
          patientNameStr: `${v.patientName}(${v.sex}-${v.age})`,
        }
      })
      const addPatientFlag = uni.getStorageSync('addPatientFlag')
      if (addPatientFlag == 1) {
        const value = this.patientList[this.patientList.length - 1] || {}
        this.form.patientName = value.patientName
        this.form.patientId = value.patientId
        this.form = {
          ...this.form,
          ...value,
        }
      }
      this.patientList.push({
        patientNameStr: '添加就诊人',
        patientId: 'add',
      })
    },
    confirmPeople(value) {
      if (value[0].value === 'add') {
        // 添加就诊人
        uni.navigateTo({
          url:
            '/pages/personalCenter/patientManage/addPatient/index?' + `kuxf=1`,
        })
        return
      }
      console.log(value)
      const item =
        this.patientList.find((v) => v.patientId === value[0].value) || {}
      this.form.patientName = item.patientName
      this.form.patientId = value[0].value
      this.form.age = item.age
      this.form = {
        ...this.form,
        ...item,
      }
      // 特定药店ID检查
      this.checkSpecialStore(item)
    },
    // 特定药店ID就诊人检查
    async checkSpecialStore(item) {
      const isSpecialStore =
        this.drugstoreId === '574a40ea01424f7a8b62762b37ff58e2' &&
        this.source === 'shop'
      if (isSpecialStore) {
        try {
          // 检查药品ID, 查找drugId为72d263bd0ebd48e49e9063320efa0ff7的商品
          const specialDrug = this.drugList.find(
            (drug) => drug.drugId === '72d263bd0ebd48e49e9063320efa0ff7'
          )
          console.log('specialDrug', specialDrug, this.drugList)

          if (specialDrug) {
            const checkResult = await checkMaxQuanPatient({
              patientId: this.form.patientId,
              drugId: specialDrug.drugId,
              quan: specialDrug.quan || 0,
            })
            console.log(checkResult, 'checkResult')

            if (checkResult && checkResult.data === false) {
              showModal(
                '',
                '同一就诊人限购 30 盒，若需购买30盒以上，请切换/添加新就诊人再次下单。',
                '我已知晓，关闭',
                '取消',
                false
              )
              // 清空就诊人，防止重复提交
              this.form.patientName = ''
              this.form.patientId = ''
              this.form.age = ''
              this.form = {
                ...this.form,
              }
            }
          }
        } catch (error) {
          console.error('检查患者购买限制失败', error)
        }
      }
    },
    // 获取支付方式
    getPayList() {
      let hosId = uni.getStorageSync('userId')
      PAY = new Pay(hosId)
    },
    confirmDate(value) {
      console.log(value)
      const day = value.year + '-' + value.month + '-' + value.day
      this.form.previousPrescriptionTime = day
    },
    // 药品限制数量
    async vaDrugLimitNum(drugs) {
      const drugIds = drugs.map((v) => v.drugId)
      const res = await batchGetDrugLimitNum({ drugIds })
      if (!res.data) {
        return true
      }
      const drugLimitNumList = res.data.filter((v) => v.limitNum != 0)
      if (drugLimitNumList.length === 0) {
        return true
      }
      const fails = drugs.filter((v) => {
        const find = drugLimitNumList.find((item) => item.drugId === v.drugId)
        return find && find.limitNum < v.quan
      })
      if (fails.length) {
        const find = drugLimitNumList.find(
          (item) => item.drugId === fails[0].drugId
        )
        if (find) {
          uni.showToast({
            title: `${fails[0].drugName}单次开具数量限制${find.limitNum}，请核实！`,
            icon: 'none',
          })
          return false
        }
      }
      return true
    },
    submit: _.debounce(async function () {
      if (!this.btnFlag) {
        return
      }

      // 特定药店不需要严格验证
      const isSpecialStore =
        this.drugstoreId === '574a40ea01424f7a8b62762b37ff58e2' &&
        this.source === 'shop'

      if (!this.form.patientName) {
        uni.showToast({
          title: '请选择就诊人',
          icon: 'none',
        })
        return
      }

      // 临床诊断始终是必填项
      if (!this.$store.getters.diagList.length) {
        uni.showToast({
          title: '请输入诊断',
          icon: 'none',
        })
        return
      }

      // 如果不是特定药店，才需要验证药品和其他内容
      if (!isSpecialStore) {
        if (!this.$store.getters.drugList.length) {
          uni.showToast({
            title: '请选择药品',
            icon: 'none',
          })
          return
        }
        if (!(await this.vaDrugLimitNum(this.drugList))) {
          return
        }
      }
      if (isSpecialStore) {
        if (this.drugList.length > 0) {
          const specialDrug = this.drugList.find(
            (drug) => drug.drugId === '72d263bd0ebd48e49e9063320efa0ff7'
          )
          if (specialDrug && specialDrug.quan == 0) {
            uni.showToast({
              title: '药品数量为0，请重新选择药品',
              icon: 'none',
              duration: 3000,
            })
            return
          }
        }
      }
      let obj = this.info
      let recordsTitle = this.info.recordsTitle.trim()
      let diseaseDescription = this.info.diseaseDescription.trim()

      // 是否全必填 - 特定药店不需要这些字段
      if (this.isRequired && this.isPlatformKf === '0' && !isSpecialStore) {
        if (!recordsTitle) {
          Toast('请填写标题')
          return
        }

        if (!obj.firstVisitHos) {
          Toast('请输入首诊医院')
          return
        }

        let reg = /^[0-9]{1,}$/
        if (!reg.test(obj.sickTime)) {
          Toast('患病时长请填写正数')
          return
        }

        if (!diseaseDescription) {
          Toast('请输入病情描述')
          return
        }

        if (!obj.expectHelp) {
          Toast('请输入期望帮助')
          return
        }
      }
      this.btnFlag = false
      const saveData = {
        ...this.form,
        age: this.form.age,
        previousPrescriptionTime: this.form.previousPrescriptionTime
          ? this.form.previousPrescriptionTime + ' 00:00:00'
          : '',
        prescriptionImg: JSON.stringify(this.fileList.map((v) => v.url)),
        quickPrescriptionDiags: this.$store.getters.diagList,
        quickPrescriptionDetailVOS: this.$store.getters.drugList,
        info: {
          ...obj,
          patientName: this.form.patientName,
          patientId: this.form.patientId,
          diagnosisDisease: this.$store.getters.diagList
            .map((item) => item.diagName)
            .join(','),
        },
        isPlatformKf: this.isPlatformKf,
        source: this.source,
        yfkcIdList: this.drugList
          .map((item) => item.yfkcId)
          .filter((item) => item),
        openid: uni.getStorageSync('wxInfo').openId,
      }
      console.log(saveData)
      try {
        const req = await saveQuickPrescriptionRegister(saveData)
        let money = req.data.reg.totalPay || 0

        let { index, item } = await PAY.selePay(money)

        uni.showLoading({
          mask: true,
        })
        this.ghId = req.data.reg.ghId
        let para = {
          callId: item.appid,
          ghId: this.ghId,
          openid: uni.getStorageSync('wxInfo').openId,
          payType: index,
        }
        let res = await getRegisterPayInfo(para)
        uni.hideLoading()
        this.regId = req.data.reg.regId
        const data = {
          patientName: this.form.patientName,
          patientId: this.form.patientId,
          regId: req.data.reg.regId,
          orderNo: res.data.orderNo,
          ghId: this.ghId,
          userId: uni.getStorageSync('userId'),
        }
        const getAllocation = await createPatientAllocateDoc(data)
        this.getAllocation = getAllocation.data || {}

        // 重新查询购物车。
        this.$store.dispatch('shop/getCartList')
        // 无需支付，返回成功，直接跳转
        if (res.data && res.data.success == '1') {
          this.btnFlag = true
          let docId = this.getAllocation.docId
          let docName = this.getAllocation.docName
          let patientId = this.form.patientId

          this.ksxfMessage(docId, patientId, docName)
          return
        }
        let info = res.data
        if (index == 1) {
          // 微信支付
          this.wxPay(info)
          return
        } else {
          uni.showToast({
            title: '只支持微信支付',
            icon: 'none',
          })
          return
          this.btnFlag = true
          uni.navigateTo({
            url:
              '/pages/pay/pay?price=' +
              money +
              '&ghId=' +
              this.ghId +
              '&url=' +
              btoa(info.url),
          })
        }
      } catch (error) {
        console.log(error)
        uni.hideLoading()
        this.btnFlag = true
        this.$store.dispatch('shop/getCartList')
      }
    }, 1000),
    async ksxfMessage(docId, patientId, docName) {
      const dataVal = await getPatientChat2({ regId: this.regId })
      let _this = this
      let param = {
        docId: docId,
      }
      const chatItem = {
        ...dataVal.data,
      }
      uni.setStorageSync('chatItem', chatItem)
      let url = '/pages/chatList/scanChatDetail?param=' + JSON.stringify(param)
      uni.navigateTo({
        url: url,
      })
    },
    // 微信支付
    async wxPay(info) {
      try {
        await PAY.wxPay(info)
        this.getState()
        this.btnFlag = true
      } catch (error) {
        PAY.Toast('取消支付')
        this.btnFlag = true
      }
    },
    // 查询支付状态
    async getState() {
      // if (num <= 0) {
      //   await cancelRegister({
      //     ghId: this.ghId,
      //   });
      //   uni.reLaunch({
      //     url: "/pages/register/thatDayRegister/payState/index?flag=" + "0",
      //   });
      //   return;
      // }
      uni.showLoading({
        mask: true,
      })
      let res = await queryRegisterPayStatus({
        ghId: this.ghId,
      })
      num--
      uni.hideLoading()
      if (!res.data) return
      if (res.data.regStatus == '2') {
        this.btnFlag = true
        let docId = this.getAllocation.docId
        let docName = this.getAllocation.docName
        let patientId = this.form.patientId

        this.ksxfMessage(docId, patientId, docName)
        // if (this.isShowDisease) {
        //   uni.navigateTo({
        //     url:
        //         "/pages/register/thatDayRegister/payState/index?isShowDisease=1&flag=1" +
        //         "&type=" +
        //         this.infoDetail.visitTypeCode,
        //   });
        // } else {
        //   uni.navigateTo({
        //     url:
        //         "/pages/register/thatDayRegister/payState/index?flag=1" +
        //         "&type=" +
        //         this.infoDetail.visitTypeCode,
        //   });
        // }
        return
      } else {
        // 三秒后台调用
        setTimeout(this.getState, 3000)
      }
    },
    loadMoreHospitals() {
      if (this.isLoadingMore || this.currentPage >= this.totalPages) return
      this.currentPage++
      this.getHosList()
    },
  },
}
</script>

<style lang="scss" scoped>
.x {
  &::before {
    content: '*';
    color: red;
    margin-right: 5px;
  }
}
.border-1 {
  border-top: 0.5px solid #f3f3f3;
}
::v-deep .u-form-item--left__content__label {
  font-size: 14px !important;
  color: rgba(51, 51, 51, 1);
  white-space: nowrap;
}
::v-deep .uni-input-wrapper {
  text-align: right;
}
::v-deep .uicon-arrow-down-fill {
  &::before {
    content: '' !important;
  }
}
::v-deep .uni-textarea-placeholder {
  font-size: 14px;
  color: rgba(153, 153, 153, 1);
  white-space: nowrap;
  display: flex;
  align-items: center;
}
.shortcut_little {
  color: #75777a;
  background-color: #eeeeee;
  border-radius: 19rpx;
  font-size: 24rpx;
  text-align: center;
  padding: 4rpx 32rpx;
  margin-right: 28rpx;
  margin-top: 20rpx;
}
.picker-view {
  width: 750rpx;
  height: 400rpx;
  margin-top: 20rpx;
}
.item {
  line-height: 100rpx;
  text-align: center;
}
.popup-top {
  display: flex;
  align-items: center;
  line-height: 50px;
  justify-content: space-between;
  padding: 0 20px;
  .success {
    color: #15a0e6;
  }
}
.other-content {
  width: 100%;
  box-sizing: border-box;
  border-radius: 8rpx;
  border: 0.5px solid rgba(131, 106, 255, 1);
  font-size: 28rpx;
  line-height: 44rpx;
  padding: 30rpx 16rpx;
  height: 240rpx;
  align-items: normal !important;
  ::v-deep .uni-textarea-placeholder {
    align-items: normal !important;
  }
}
.select-box {
  display: flex;
  align-items: center;
  text-align: right;
  flex: 1;
}
.shortcut {
  padding: 20rpx 0;
}
.title_format {
  font-size: 28rpx;
  font-weight: 400;
  color: #333333;
  line-height: 22px;
  margin: 38rpx 0 20rpx 0;
}
.input-box {
  width: 100%;
  display: flex;
  height: 100rpx;
  padding: 16rpx 0;
  align-items: center;
  box-sizing: border-box;
  border-bottom: 1rpx solid #f3f3f3 !important;
  font-size: 28rpx;
  font-weight: 400;
}

.input-box text {
  display: inline-block;
  width: 180rpx;
  line-height: 88rpx;
  flex: none;
  color: rgba(51, 51, 51, 1);
  white-space: nowrap;
}

.input-box input {
  flex: 1;
  /* height: 88rpx; */
  /* line-height: 88rpx; */
  padding-left: 28rpx;
  color: rgba(102, 102, 102, 1);
}

.input-box textarea {
  flex: 1;
  height: 88rpx;
  /* line-height: 44rpx; */
  padding-left: 28rpx;
  color: rgba(102, 102, 102, 1);
}

.input-box .select-day {
  width: 102rpx;
  @include flex(center);
  border: 1px solid $k-hr-color;
  border-radius: 4upx;
  box-sizing: border-box;
  height: 60upx;

  .select_time {
    font-size: 24upx;
    color: $k-title;
    font-weight: 500;
    padding-right: 10upx;
  }

  .icon_sx {
    width: 30upx;
    height: 30upx;
  }
}
.quickContinuation {
  height: calc(100vh - 100rpx);
  overflow: auto;
  background: white;
}
.quickContinuation-form {
  background: white;
  padding: 5px 20px;
}
.quickContinuation-item {
  display: flex;
  justify-content: space-between;
  margin-top: 15px;
  margin-bottom: 15px;
  align-items: center;
}
.history-icon {
  font-size: 14px;
  color: rgba(153, 153, 153, 1);
}
.add-medicine {
  text-align: center;
  color: rgba(131, 106, 255, 1);
  margin: 10px 0;
  font-size: 12px !important;
}
.AddMedicine {
  padding: 30px 20px;
}
.medicine-name {
  line-height: 30px;
}
.img_list {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: 20rpx;
  padding-bottom: 20rpx;

  .img_item {
    width: 200rpx;
    height: 200rpx;
    border-radius: 8rpx;
    @include flex;
    flex-direction: column;
    background: rgba(255, 255, 255, 1);
    border: 0.5px dashed rgba(131, 106, 255, 1);
    position: relative;
    text {
      color: #999;
    }
    .img2 {
      width: 80px !important;
      height: 80px !important;
    }
    .img {
      width: 20px !important;
      height: 20px !important;
    }
    .del {
      background-color: rgba($color: #000000, $alpha: 0.3);
      position: absolute;
      border-radius: 50%;
      top: 0;
      right: 0;
    }
  }
}
.drug_card {
  width: 100%;
  padding: 24rpx;
  background-color: #fafafa;
  border-radius: 16rpx;
  margin-bottom: 20rpx;

  .card {
    @include flex;

    .card_img {
      width: 128rpx;
      height: 128rpx;
      border-radius: 8rpx;
      object-fit: cover;
      flex: none;
    }

    .card_text {
      flex: 1;
      min-height: 128rpx;
      padding-left: 24rpx;
      @include flex(lr);
      flex-direction: column;
      align-items: stretch;

      .text_title {
        font-size: 28rpx;
        font-weight: bold;
      }

      .text_info {
        font-size: 24rpx;
        color: #999;
      }

      .active {
        color: red;
        font-size: 24rpx;
      }

      .text_price {
        font-size: 28rpx;
        color: #ff3b30;
        font-weight: bold;

        text {
          font-size: 24rpx;
          font-weight: normal;
          color: #999;
        }
      }
    }
  }

  .drug_action {
    margin-top: 10rpx;
    @include flex(lr);
    height: 40rpx;

    .count {
      min-width: 128rpx;
      text-align: center;
      font-size: 20rpx;
      color: #999;
    }

    .action {
      @include flex;

      .icon {
        width: 32rpx;
        height: 32rpx;
      }

      .num {
        width: 60rpx;
        height: 36rpx;
        @include flex;
        font-size: 28rpx;
        background-color: #fff;
        margin: 0 16rpx;
        border-radius: 4rpx;
      }
    }
  }
}
.cfCover {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  text-align: center;
  font-size: 10px;
  background: rgba(255, 255, 255, 0.7) !important;
  display: flex;
  align-items: center;
  flex: 1;
  z-index: 2;
}
.gx {
  filter: blur(2px);
}
.drug-coverUp {
  width: 128rpx;
  height: 128rpx;
  background: rgba(255, 255, 255, 0.5);
  position: absolute;
  top: 0;
  left: 0;
}
::v-deep .footer_button {
  background: white;
  font-size: 14px;
  .com {
    height: 36px;
    font-size: 14px;
  }
}
.isPlatformKf {
  display: flex;
  justify-content: right;
  flex: 1;
}
.hospital-list {
  height: 400rpx;
  overflow: auto;
}
.hospital-item {
  height: 84rpx;
  line-height: 84rpx;
  padding: 0 20rpx;
  border-bottom: 1px solid #eee;
  font-size: 28rpx;
  color: #333;
  text-align: center;
  &.hospital-item-selected {
    color: #836aff;
    background-color: #f5f5f5;
  }
}
.loading-more {
  padding: 10rpx;
  text-align: center;
  color: #999;
}

.adaptation-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  .adaptation-item {
    padding: 10rpx 20rpx;
    font-size: 24rpx;
    background-color: #836aff;
    color: #fff;
    border-radius: 10rpx;
  }
}
</style>
