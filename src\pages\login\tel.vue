<template>
  <div>
    <div class="warp">
      <div class="item">
        <input
          type="tel"
          placeholder-class="pls"
          placeholder="请输入手机号"
          v-model="telNo"
          maxlength="11"
        />
      </div>

      <div class="item">
        <input
          type="text"
          placeholder-class="pls"
          placeholder="请输入验证码"
          v-model="code"
          maxlength="6"
        />
        <span :class="num < 60 ? 'act' : ''" @click="sendPhoneCode">{{
          msg
        }}</span>
      </div>
    </div>

    <!-- 提示文案 -->
    <div class="tip">
      <p>系统检测到您的手机已被其他用户使用</p>
      <p>请重新绑定</p>
    </div>

    <FOOTER @click="send">保存</FOOTER>
  </div>
</template>

<script>
import { sendCaptcha, patientCompletionTelNo } from '@/api/user.js';
import FOOTER from '@/components/footer_button/button.vue';

import { Toast } from '@/common/js/pay.js';

let timer;

export default {
  name: 'Tel',
  components: {
    FOOTER,
  },
  data() {
    return {
      telNo: '',
      code: '',
      msg: '获取验证码',
      num: 60,
    };
  },
  onLoad(v) {},
  methods: {
    // 单独发送验证码
    async sendPhoneCode() {
      let telNo = this.telNo;
      if (this.num < 60) return;
      if (!telNo.length) {
        Toast('请输入手机号');
        return;
      }
      if (telNo.length != 11) {
        Toast('手机号位数错误');
        return;
      }
      try {
        await sendCaptcha({ telNo });
        timer = setInterval(() => {
          this.num--;
          if (this.num <= 0) {
            clearInterval(timer);
            this.msg = '获取验证码';
            this.num = 60;
          } else {
            this.msg = this.num + 's后重新获取';
          }
        }, 1000);
      } catch (error) {}
    },
    // 发送
    async send() {
      if (!this.telNo) {
        Toast('请填写手机号');
        return;
      }
      if (!this.code) {
        Toast('请填写验证码');
        return;
      }

      let obj = {
        appid: uni.getStorageSync('appId'),
        captcha: this.code,
        openid: uni.getStorageSync('wxInfo').openId,
        telNo: this.telNo,
        userId: uni.getStorageSync('userId'),
      };

      let res = await patientCompletionTelNo(obj);

      if (res.code == 20000) {
        this.suc();
      }
    },
    // 成功操作
    suc() {
      uni.setStorageSync('tel', this.telNo);
      uni.navigateBack();
    },
  },
};
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}
page {
  background-color: #fff;
}

.pls {
  font-size: 28rpx;
}

.warp {
  padding: 0 32rpx;

  .item {
    height: 90rpx;
    border-bottom: 1px solid #f5f5f5;
    font-size: 28rpx;
    @include flex(lr);
    margin-bottom: 20rpx;

    input {
      flex: 1;
    }

    span {
      @include font_theme;

      &.act {
        color: #999;
      }
    }
  }
}

.tip {
  width: 100%;
  text-align: center;
  font-size: 26rpx;
  color: red;
  line-height: 44rpx;
  position: absolute;
  bottom: 120rpx;
}
</style>
