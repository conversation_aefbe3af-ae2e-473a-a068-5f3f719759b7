import http from '../common/request/request.js';

// 我的挂号列表
export function selectRegisterListPage(param) {
  return http({
    url: 'basic/patientRegister/selectRegisterListPage',
    param,
    method: 'post',
  });
}

// 挂号详情
export function selectRegisterDetail(regId) {
  return http({
    url: 'basic/patientRegister/selectRegisterDetail',
    param: {
      regId,
    },
    method: 'post',
  });
}

/**
 * 根据患者id和医生id判断是否在黑名单
 * @param {string} docId 医生id
 * @param {string} patientId 患者id
 * */
export function getDocPatientBlackList(param) {
  return http({
    url: 'basic/propatient/getDocPatientBlackList',
    param,
    method: 'post',
  });
}
export function queryQuickPrescription(param) {
  return http({
    url: 'business/quickPrescription/queryQuickPrescription',
    param,
    method: 'post',
  });
}
export function queryAllocateTime(param) {
  return http({
    url: 'business/quickPrescription/queryAllocateTime',
    param,
    method: 'post',
  });
}