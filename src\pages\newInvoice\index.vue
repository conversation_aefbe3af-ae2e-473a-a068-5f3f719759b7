<template>
  <view class="invoice-page">
    <!-- 顶部状态栏 -->
    <view class="tab-bar">
      <view
        v-for="(tab, index) in tabs"
        :key="index"
        :class="['tab-item', currentTab === index ? 'active' : '']"
        @click="switchTab(index)"
      >
        {{ tab.name }}
      </view>
    </view>

    <!-- 就诊人选择 -->
    <view class="filter-bar" v-if="currentTab === 0">
      <view class="patient-select">
        <text>就诊人：</text>
        <picker
          @change="onPatientChange"
          :value="selectedPatientIndex"
          :range="patientList"
          range-key="patientName"
        >
          <view class="picker-value">
            {{ selectedPatient ? selectedPatient.patientName : '请选择' }}
            <uni-icons type="bottom" size="14" color="#666"/>
          </view>
        </picker>
      </view>

      <!-- 全选按钮 -->
      <view class="select-all" @click="toggleSelectAll">
        <checkbox :checked="isAllSelected" color="#6174ff"/>
        <text>页面全选</text>
      </view>
    </view>
    <template v-if="orderList.length">
      <scroll-view
          scroll-y
          class="order-list"
          @scrolltolower="loadMore"
          v-if="currentTab === 0"
      >
        <view
            v-for="(order, index) in orderList"
            :key="index"
            class="order-item"
        >
          <view class="order-header">
            <checkbox
                :checked="order.selected"
                @click="toggleSelect(index)"
                color="#6174ff"
            />
            <view class="order-info">
              <view style="display: flex;justify-content: space-between">
                <text>{{order.patientName}}</text>
                <text class="order-type" style="color: #15a0e6">{{ order.orderType==1?'问诊订单':'药品订单' }}</text>
              </view>
              <view style="display: flex;justify-content: space-between;align-items: center">
                <view>
                  <text class="order-date" style="line-height: 30px">{{ order.orderDate }}</text>
                  <view class="order-date">{{ formatMedicineNames(order.drugName) }}</view>
                </view>
                <text class="order-amount">¥{{ order.orderMoney }}</text>
              </view>
            </view>
          </view>

          <!-- 药品订单才显示药品名称 -->
          <view class="medicine-list" v-if="order.orderType === '药品订单'">
            <text class="medicine-names">{{ formatMedicineNames(order.medicines) }}</text>
          </view>
        </view>
      </scroll-view>

    </template>
    <!-- 可开票列表 -->

    <!-- 申请中/已开票列表 -->
    <template v-if="invoiceList.length">
      <scroll-view
          scroll-y
          class="invoice-list"
          @scrolltolower="loadMore"
          v-if="currentTab!=0"
      >
        <view
            v-for="(invoice, index) in invoiceList"
            :key="index"
            class="invoice-item"
            @click="goToInvoiceDetail(invoice)"
        >
          <view class="invoice-header">
            <text class="invoice-title">{{invoice.titleType === '2'? invoice.companyName :invoice.invoiceTitle}}</text>
            <text class="invoice-status">{{ getStatusText(invoice.status) }}</text>
          </view>
          <view class="invoice-info">
            <text class="invoice-date">{{ invoice.addTime }}</text>
            <text class="invoice-type">{{ invoice.invoiceType==1?'普通发票':'增值税发票' }}</text>
            <text class="invoice-amount">¥{{ invoice.amount }}</text>
          </view>
        </view>
      </scroll-view>

    </template>

    <!-- 底部按钮 -->
    <view class="bottom-btn" v-if="currentTab === 0 && orderList.length > 0">
      <button
        class="apply-btn"
        :disabled="!orderList.some(order => order.selected)"
        @click="applyInvoice"
      >
        申请开票
      </button>
    </view>

    <!-- 空状态 -->
    <view class="empty" v-if="showEmpty">
      <image src="/static/images/empty.png"/>
      <text>{{ emptyText }}</text>
    </view>
  </view>
</template>

<script>
import {getPatientList} from "../../api/user";
import {getInvoiceList, getPeddingIssueList} from "../../api/base";
import {getStatusText} from "../../utils/validate";

export default {
  data() {
    return {
      tabs: [
        { name: '可开票', status: 0 },
        { name: '申请中', status: 1 },
        { name: '已开票', status: 2 }
      ],
      currentTab: 0,
      patientList: [], // 就诊人列表
      selectedPatientIndex: -1,
      selectedPatient: null,
      orderList: [], // 可开票订单列表
      invoiceList: [], // 申请中/已开票列表
      isAllSelected: false,
      page: 1,
      loading: false,
      hasMore: true
    }
  },

  computed: {
    hasSelectedOrders() {
      return this.orderList.some(order => order.selected)
    },
    showEmpty() {
      return (this.currentTab === 0 && !this.orderList.length) ||
        (this.currentTab !== 0 && !this.invoiceList.length)
    },
    emptyText() {
      const texts = ['暂无可开票订单', '暂无申请中发票', '暂无已开票记录']
      return texts[this.currentTab]
    }
  },
  async onShow() {
   this.init()
    await this.getPatientList()
    if(!this.patientList.length){
      return
    }
    await this.getList()
  },
  async onLoad() {
    console.log('onLoad')
    this.$store.commit('setSelectedOrders', [])
  },

  methods: {
    getStatusText,
    init(){
      this.isAllSelected=false
      this.hasMore=true
      this.currentTab = 0
      this.page = 1
      this.orderList = []
    },
    // 跳转至发票详情
    goToInvoiceDetail(invoice) {
      uni.navigateTo({
        url: `/pages/newInvoice/detail?invoiceId=${invoice.pkId}`
      })
    },
    // 切换tab
    switchTab(index) {
      if(this.currentTab === index) return
      this.currentTab = index
      this.page = 1
      this.hasMore = true
      this.orderList = []
      this.invoiceList = []
      this.getList()
    },

    // 获取就诊人列表
    async getPatientList() {
      try {
        // TODO: 调用获取就诊人列表接口
        const res = await getPatientList({userId: uni.getStorageSync('userId')})
        this.patientList = res.data
        // if(this.patientList.length){
        //   this.selectedPatientIndex=0
        //   this.selectedPatient = this.patientList[0]
        // }
      } catch(e) {
        console.error(e)
      }
    },

    // 选择就诊人
    onPatientChange(e) {
      console.log(e);
      const index = e.detail.value
      this.selectedPatientIndex = index
      this.selectedPatient = this.patientList[index]
      this.page = 1
      this.hasMore=true
      this.orderList = []
      this.getList()
    },

    // 获取列表数据
    async getList() {
      if(this.loading || !this.hasMore) return

      this.loading = true
      try {
        let params = {
          type: this.currentTab==1?1:2,
          page: this.page,
          limit: 10,
          userId: uni.getStorageSync('userId')
        }
        let res=null
        if(this.currentTab=='0'){
          if(this.selectedPatient?.patientId){
            params.patientIds=[this.selectedPatient?.patientId]
          }else {
            params.patientIds=[...this.patientList.map(v => v.patientId)]
          }
          res = await getPeddingIssueList(params)
        }else{
          res = await getInvoiceList(params)
        }
        // 使用新接口
        const { rows, total } = res.data
        if(this.page>1){
          this.isAllSelected=false
        }
        if(this.currentTab === 0) {
          this.orderList = this.page === 1 ? rows : [...this.orderList, ...rows]
        } else {
          this.invoiceList = this.page === 1 ? rows : [...this.invoiceList, ...rows]
        }

        const currentList = this.currentTab === 0 ? this.orderList : this.invoiceList
        this.hasMore = currentList.length < total
        this.page++
      } catch(e) {
        console.error(e)
      } finally {
        this.loading = false
      }
    },

    // 加载更多
    loadMore() {
      this.getList()
    },

    // 全选/取消全选
    toggleSelectAll() {
      this.isAllSelected = !this.isAllSelected
      this.orderList.forEach(order => {
        order.selected = this.isAllSelected
      })
      console.log(this.orderList,this.orderList)
    },

    // 选择/取消选择单个订单
    toggleSelect(index) {
      this.orderList[index].selected = !this.orderList[index].selected
      this.isAllSelected = this.orderList.every(order => order.selected)
      this.$forceUpdate()
    },

    // 格式化药品名称
    formatMedicineNames(medicines) {
      if(!medicines?.length) return ''

      const text = medicines
      return text.length > 15 ? text.slice(0, 15) + '...' : text
    },

    // 申请开票
    applyInvoice() {
      var isNoPatients=false
      var patient=''
      const selectedOrders = this.orderList.filter(order => order.selected).map(v=>{
        const find= this.patientList.find(a=>a.patientName==v.patientName)
        if(!find){
          isNoPatients = true
          patient=v.patientName
        }
        return {
          ...v,
          idCardNo: find && find.idNo,
          phone: find && find.telNo
        }
      })
      if(isNoPatients){
        uni.showToast({
          title: `请先完善就诊人信息：${patient}`,
          icon: 'none'
        })
        return
      }
      this.$store.commit('setSelectedOrders', selectedOrders)
      uni.navigateTo({
        url: `/pages/newInvoice/apply?flag=1`
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.invoice-page {
  height:calc(100vh - 88rpx);
  background: #F5F6FA;
  padding-bottom: 120rpx;
}

.tab-bar {
  display: flex;
  background: #fff;
  padding: 0 32rpx;
  height: 88rpx;
  border-bottom: 1rpx solid #EEEEEE;

  .tab-item {
    position: relative;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28rpx;
    color: #666;

    &.active {
      color: #6174ff;
      font-weight: 500;

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 48rpx;
        height: 4rpx;
        background:linear-gradient(135deg, #516afb 0%, #859bff 99.97%);
        border-radius: 2rpx;
      }
    }
  }
}

.filter-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 32rpx;
  background: #fff;
  margin-bottom: 20rpx;

  .patient-select {
    display: flex;
    align-items: center;
    font-size: 28rpx;
    color: #333;

    .picker-value {
      display: flex;
      align-items: center;
      color: #666;
    }
  }

  .select-all {
    display: flex;
    align-items: center;
    font-size: 28rpx;
    color: #666;

    checkbox {
      margin-right: 8rpx;
    }
  }
}

.order-list {
  height: calc(100vh - 400rpx);
  padding: 0 32rpx;
  box-sizing: border-box;
}

.order-item {
  background: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;

  .order-header {
    display: flex;
    align-items: center;

    checkbox {
      margin-right: 16rpx;
    }

    .order-info {
      flex: 1;
      font-size: 24rpx;
      .order-date {
        font-size: 24rpx;
        color: #333;
        margin-right: 16rpx;
      }

      .order-type {
        font-size: 24rpx;
        color: #666;
      }
    }

    .order-amount {
      font-size: 32rpx;
      color: #333;
      font-weight: 500;
    }
  }

  .medicine-list {
    margin-top: 16rpx;
    font-size: 26rpx;
    color: #666;
  }
}

.invoice-list {
  height: calc(100vh - 188rpx);
  padding: 0 32rpx;
  box-sizing: border-box;
}

.invoice-item {
  background: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;

  .invoice-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16rpx;

    .invoice-title {
      font-size: 28rpx;
      color: #333;
      font-weight: 500;
    }

    .invoice-status {
      font-size: 24rpx;
      color: #6174ff;
    }
  }

  .invoice-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 26rpx;
    color: #666;

    .invoice-amount {
      color: #333;
      font-weight: 500;
    }
  }
}

.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 160rpx;

  image {
    width: 240rpx;
    height: 240rpx;
    margin-bottom: 20rpx;
  }

  text {
    font-size: 28rpx;
    color: #999;
  }
}

.bottom-btn {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 16rpx 32rpx;
  background: #fff;

  .apply-btn {
    width: 100%;
    height: 88rpx;
    line-height: 88rpx;
    background: linear-gradient(135deg, #516afb 0%, #859bff 99.97%);
    color: #fff;
    font-size: 32rpx;
    border-radius: 44rpx;

    &[disabled] {
      background: #ccc;
    }
  }
}
</style>