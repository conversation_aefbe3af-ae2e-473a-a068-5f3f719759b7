export function validateEmail(email) {
    const reg = /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/
    return reg.test(email)
}

export const statusMap = {
    1: '待开票',
    2: '驳回',
    3: '开票中',
    4: '待复核',
    5: '已开票',
    6: '重开票',
    7: '重开票待审核'
}

export const getStatusText = (status) => {
    if(status==4||status==3){
        return '开票中'
    }
    if(status==7){
        return '重开票'
    }
    return statusMap[status] || ''
}