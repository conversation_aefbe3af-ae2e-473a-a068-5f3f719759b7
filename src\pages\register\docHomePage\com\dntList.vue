<template>
  <!-- 号别列表 -->
  <view class="dnt_list">
    <!-- tab选项 -->
    <view class="dnt_tab" :class="{ desc: isDesc }">
      <view
        class="button"
        v-if="isZx"
        :class="{ act: act == 0 }"
        @click="setMenu(0)"
        >{{ isFz ? '健康咨询' : '在线挂号' }}</view
      >
      <view
        class="button"
        v-if="isFz"
        :class="{ act: act == 1 }"
        @click="setMenu(1)"
        >{{ isZx ? '问诊咨询' : '在线挂号' }}</view
      >
    </view>

    <!-- 排班列表 -->
    <view class="list" v-if="list">
      <!-- 图文 -->
      <view
        class="item"
        v-for="(item, index) in list"
        :class="{ end: !item.surplusNnm }"
        :key="index"
        @click="clickItem(item)"
        :style="{
          background: item.visitTypeCode==2
            ? 'linear-gradient(90deg, rgba(204, 255, 249, 1) 0%, rgba(199, 255, 248, 0.4) 100%)'
            : item.visitTypeCode == 4? 'linear-gradient(90deg, rgba(219, 232, 255, 1) 0%, rgba(82, 139, 236, 0.2) 99.94%)' : '',
          border: item.visitTypeCode==2
            ? '0.5px solid rgba(91, 179, 168, 1)'
            : item.visitTypeCode == 4? 'border: 0.5px solid rgba(27, 135, 219, 1)' : '',
        }"
      >
        <!-- 类型 -->
        <view class="icon_cont">
          <!-- 图文 -->
          <block v-if="item.visitTypeCode == 1">
<!--            <image-->
<!--              src="/static/images/doc/text.png"-->
<!--              class="icon"-->
<!--              v-if="item.surplusNnm"-->
<!--            />-->

<!--            <image src="/static/images/doc/te.png" class="icon" v-else />-->
            <div class="lICON">
              <img src="/static/images/tw.png" alt="">
            </div>
          </block>

          <!-- 语音 -->
          <block v-if="item.visitTypeCode == 2">
<!--            <image-->
<!--              src="/static/images/doc/audio.png"-->
<!--              class="icon"-->
<!--              v-if="item.surplusNnm"-->
<!--            />-->
<!--            <image src="/static/images/doc/au.png" class="icon" v-else />-->
            <div class="lICON yy">
              <img src="/static/images/yy.png" alt="">
            </div>
          </block>

          <!-- 视频 -->
          <block v-if="item.visitTypeCode == 4">
<!--            <image-->
<!--              src="/static/images/doc/video.png"-->
<!--              class="icon"-->
<!--              v-if="item.surplusNnm"-->
<!--            />-->
<!--            <image src="/static/images/doc/vi.png" class="icon" v-else />-->
            <div class="lICON sp">
              <img src="/static/images/sp.png" alt="">
            </div>
          </block>
        </view>
        <view class="right">
          <!-- 类别 -->
          <view class="item_title" v-if="isZx && isFz" :style="{
            color:item.visitTypeCode==2
            ? 'rgba(55, 171, 155, 1)'
            : item.visitTypeCode == 4? 'rgba(59, 133, 246, 1)' : ''
          }"
            >{{ item.visitTypeName }}{{ act == 1 ? '复诊' : '咨询' }}</view
          >
          <view class="item_title" v-else>{{ item.visitTypeName }}问诊</view>

          <view class="bottom">
            <view>
              <text>{{ item.dntName || '' }}</text> /
              <!-- 时间 -->
              <text>{{ item.visitDuration }}</text>
            </view>
            <!-- 剩余数量 -->
            <text class="sy" v-if="item.surplusNnm" :style="{
               border: item.visitTypeCode==2
                ? '0.5px solid rgba(91, 179, 168, 1)'
                : item.visitTypeCode == 4? 'border: 0.5px solid rgba(27, 135, 219, 1)' : '',
               color:item.visitTypeCode==2
                ? 'rgba(55, 171, 155, 1)'
                : item.visitTypeCode == 4? 'rgba(59, 133, 246, 1)' : ''
            }">余{{ item.surplusNnm }}</text>
            <!-- 已满 -->
            <text class="item_num" v-if="!item.surplusNnm">已约满</text>
          </view>
        </view>

        <!-- 价位 -->
        <view class="item_price">
          <text>¥ {{ item.visitPrice }}</text>
        </view>
      </view>
    </view>

    <!-- 空白 -->
    <view class="empty" v-if="!list.length">
      <image src="/static/images/doc/empty.png" class="empty_img" />
      <text>暂无出诊信息</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'DntList',
  props: {
    list: {
      type: Array,
      default: () => [],
    },
    act: {
      type: Number,
      default: 0,
    },
    // 是否开启复诊
    isFz: {
      type: Boolean,
      default: false,
    },
    // 是否开启咨询
    isZx: {
      type: Boolean,
      default: false,
    },
    // 是否复诊在前 倒序
    isDesc: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    clickItem(item) {
      this.$emit('click', item);
    },
    setMenu(n) {
      this.$emit('setNum', n);
    },
  },
};
</script>

<style lang="scss" scoped>
.sy{
  margin-left: 22px;
  border-radius: 242px;
  background: rgba(242, 245, 255, 1);
  border: 0.5px solid rgba(131, 106, 255, 1);
  padding: 4rpx 20rpx;
  color: rgba(131, 106, 255, 1)
}
.lICON{
  width: 23px;
  height: 23px;
  border-radius: 50%;
  opacity: 1;
  background: linear-gradient(126.8deg, rgba(124, 103, 230, 1) 0%, rgba(236, 232, 255, 1) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}
.yy{
  width: 23px;
  height: 23px;
  border-radius: 50%;
  opacity: 1;
  background: linear-gradient(270deg, rgba(118, 222, 184, 1) 0%, rgba(99, 207, 194, 1) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}
.sp{
  width: 23px;
  height: 23px;
  border-radius: 50%;
  opacity: 1;
  background: linear-gradient(270deg, rgba(82, 139, 236, 1) 0%, rgba(81, 160, 235, 1) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}
.dnt_list {
  width: 100%;
  background: #fff;
  overflow: hidden;
  font-size: 28rpx;
  border-radius: 16rpx;
  border: 8rpx solid #fff;

  .dnt_tab {
    @include flex;
    background-color: #fff;

    &.desc {
      flex-direction: row-reverse;
      border-bottom: 1px solid #eaeaea;
    }

    .button {
      flex: 1;
      height: 88rpx;
      @include flex;
      color: #666;
      font-size: 32rpx;
      background: none;

      &.act {
        font-weight: bold;
        color: rgba(51, 51, 51, 1);
        background: #fff;
        border-radius: none;
        position: relative;
        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          display: block;
          width: 80rpx;
          height: 6rpx;
          @include bg_theme;
        }
      }
    }
  }

  .list {
    border-radius: 20rpx;
    padding: 12rpx;

    .item {
      @include flex(lr);
      margin-bottom: 12rpx;
      border-radius: 20rpx;
      background: rgba(245, 245, 255, 1);
      border: 0.5px solid rgba(131, 106, 255, 1);
      height: 48px;
      &:last-child {
        margin-bottom: 0;
      }

      &.end {
        border-color: #eaeaea;
        background-color: #d1d1d1;
      }

      .icon_cont {
        width: 104rpx;
        height: 104rpx;
        @include flex;
        flex: none;
        //background-color: #fff;
        border-radius: 16rpx 0 0 16rpx;

        .icon {
          width: 66rpx;
          height: 64rpx;
        }
      }

      .right {
        flex: 1;
        display: flex;
        color: black;
        align-items: center;
        .bottom {
          font-size: 24rpx;
          color: rgba(51, 51, 51, 1);
          display: flex;
          align-items: center;
          margin-left: 5px;
        }
      }

      .item_title {
        font-size: 28rpx;
        font-weight: bold;
        margin-bottom: 4rpx;
        color: rgba(135, 79, 240, 1);
      }

      .item_price {
        font-size: 26rpx;
        color: rgba(255, 87, 51, 1);
        flex: none;
        text-align: left;
        margin-right: 15px;
        text {
          font-weight: bold;
        }
      }
    }
  }

  .empty {
    @include flex(left);
    flex-direction: column;
    font-size: 28rpx;
    color: #999;
    padding: 40rpx 0;

    &_img {
      width: 386rpx;
      height: 324rpx;
      margin-bottom: 30rpx;
    }
  }
}
</style>
