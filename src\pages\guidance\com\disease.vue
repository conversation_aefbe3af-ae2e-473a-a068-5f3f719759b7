<template>
  <!-- 疾病 -->
  <view class="disease">
    <!-- 标题 -->
    <view class="disease_head">
      <view class="title">您可能患的疾病</view>
      <view class="info"
        >根据您的描述，智能助手为您列出如下相关疾病，信息仅供参考，如需治疗请找专业医生确认后进行行</view
      >
    </view>
    <!-- 列表 -->
    <view class="disease_list">
      <!-- 单个 -->
      <view
        class="disease_item"
        v-for="item in list"
        :key="item.diseaseId"
        @click="toDocList(item.diseaseName)"
      >
        <!-- 名称 概率 -->
        <view class="item_top">
          <text class="name">{{ item.diseaseName }}</text>
          <text class="scale">概率{{ item.scale }}%</text>
        </view>
        <!-- 建议 -->
        <view class="item_desc">
          {{ item.proposal }}
        </view>
      </view>

      <!-- 底部 -->
      <view class="list_tip">以上结果仅供参考</view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'DiseaseList',
  props: {
    list: {
      type: Array,
      default: () => [],
    },
  },
  methods: {
    // 去医生列表
    toDocList(name) {
      uni.navigateTo({
        url: '/pages/register/docList/index?name=' + name,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.disease {
  background: #ffffff;
  box-shadow: 0 0 16rpx rgba(0, 0, 0, 0.05);
  border-radius: 16rpx;
  padding: 0 24rpx;
  margin-bottom: 32rpx;

  .disease_head {
    padding: 32rpx 0;

    .title {
      font-size: 28rpx;
      color: #333;
      font-weight: bold;
    }

    .info {
      font-size: 24rpx;
      color: #999;
      line-height: 36rpx;
      margin-top: 28rpx;
    }
  }

  .disease_list {
    padding-bottom: 32rpx;

    .disease_item {
      padding: 0 24rpx 24rpx;
      background-color: #fafafa;
      border-radius: 16rpx;

      .item_top {
        @include flex(lr);
        padding: 24rpx 0;

        .name {
          font-size: 32rpx;
          color: #333;
          font-weight: bold;
        }

        .scale {
          font-size: 24rpx;
          color: #ff5050;
        }
      }

      .item_desc {
        padding: 24rpx 28rpx;
        color: #14a0e6;
        background-color: #e8f6fd;
        font-size: 28rpx;
        border-radius: 16rpx;
      }
    }

    .list_tip {
      text-align: center;
      font-size: 28rpx;
      color: #999;
      padding-top: 24rpx;
    }
  }
}
</style>
