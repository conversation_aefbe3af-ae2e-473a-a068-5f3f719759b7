<template>
  <!-- 时间线 -->
  <view class="time_line">
    <block v-for="(item, index) in list">
      <!-- 时间段 -->
      <view class="line_item" v-if="item.time" :key="index">
        <text class="item">{{ item.time }}</text>
        <text>{{ item.label }}</text>
      </view>
    </block>
  </view>
</template>

<script>
export default {
  props: {
    list: {
      type: Array,
      default: () => [],
    },
  },
};
</script>

<style lang="scss" scoped>
.time_line {
  font-size: 28rpx;
  color: #666666;
  position: relative;
  background: #fff;
  border-radius: 8rpx;
  padding: 32rpx;
  margin-top: 16rpx;

  &::before {
    content: "";
    display: block;
    position: absolute;
    width: 1px;
    top: 55upx;
    left: 38upx;
    height: calc(100% - 110upx);
    @include bg_theme;
  }

  .line_item {
    font-size: 28upx;
    padding-left: 30upx;
    margin-bottom: 36upx;
    position: relative;
    @include flex(lr);

    .item {
      @include font_theme;
    }

    // 原点
    &::before {
      content: "";
      display: block;
      position: absolute;
      left: 0;
      top: 50%;
      margin-top: -8upx;
      width: 16upx;
      height: 16upx;
      border-radius: 50%;
      @include bg_theme;
    }

    &:last-child {
      margin-bottom: 0;
    }

    .time {
      font-size: 24upx;
    }
  }
}
</style>
