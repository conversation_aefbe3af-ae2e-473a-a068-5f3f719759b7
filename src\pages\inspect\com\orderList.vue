<template>
  <!-- 列表 -->
  <view class="order_list">
    <!-- 单个 -->
    <view
      class="order_item"
      v-for="(item, index) in list"
      :key="index"
      @click="click(item)"
    >
      <!-- 编号 -->
      <view class="item_title">
        <text>检{{ isLis ? '验' : '查' }}编号：{{ item.ppiCode }}</text>
        <text
          class="status wait"
          v-if="['1', '2', '3'].includes(item.status)"
          >{{ item.statusName }}</text
        >
        <text
          class="status wait"
          v-if="['7', '8', '9'].includes(item.status)"
          >{{ item.statusName }}</text
        >
        <text class="status act" v-if="['4', '5'].includes(item.status)">{{
          item.statusName
        }}</text>
        <text class="status" v-if="['0', '6'].includes(item.status)">{{
          item.statusName
        }}</text>
      </view>

      <!-- 检验项目 -->
      <view class="item_cont">
        <view class="left_info">
          检{{ isLis ? '验' : '查' }}项目：{{ item.itemName }}
        </view>
        <view class="left_info"> 医院：{{ item.hosName }} </view>
        <view class="left_info">
          就诊人：{{ item.patientName }}
          <!-- 右箭头 -->
          <uni-icons type="arrowright" color="#666" size="16"></uni-icons>
        </view>
        <view class="left_info"> 医生姓名：{{ item.docName }} </view>
        <view class="left_info"> 医生诊断：{{ item.diagName }} </view>
      </view>

      <!-- 时间 -->
      <view class="item_time"> 开单时间：{{ item.addTime }} </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'OrderList',
  props: {
    isLis: {
      type: Boolean,
      default: false,
    },
    list: {
      type: Array,
      default: () => [],
    },
  },
  methods: {
    click(item) {
      this.$emit('click', item);
    },
  },
};
</script>

<style lang="scss" scoped>
.order_list {
  padding: 24rpx 32rpx;

  .order_item {
    padding: 0 32rpx;
    border-radius: 8rpx;
    background: #fff;
    margin-bottom: 24rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .item_title {
      height: 88rpx;
      @include flex(lr);
      font-size: 28rpx;
      color: #333;
      font-weight: bold;
      border-bottom: 1px solid #ebebeb;

      .status {
        font-weight: normal;
        color: #333;

        &.wait {
          color: #ff3b30;
        }

        &.act {
          @include font_theme;
        }
      }
    }

    .item_cont {
      padding: 10rpx 0;
      border-bottom: 1px solid #ebebeb;

      .left_info {
        @include flex(lr);
        font-size: 28rpx;
        color: #666666;
        line-height: 50rpx;

        &:first-child {
          font-weight: bold;
          color: #333;
        }
      }
    }

    .item_time {
      height: 88rpx;
      @include flex(lr);
      font-size: 28rpx;
      color: #333;
    }
  }
}
</style>
