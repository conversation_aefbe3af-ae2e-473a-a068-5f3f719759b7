<template>
  <div class="ewm_card">
    <div class="cont">
      <div class="info" v-if="docInfo">
        <img v-if="docInfo.docImg" class="img" v-img="docInfo.docImg" alt="" />
        <image class="img" v-else src="/static/images/docHead.png" />

        <div class="doc">
          <p class="name">{{ docInfo.docName }} {{ docInfo.docProf }}</p>
          <p class="dept">{{ docInfo.defaultDeptName }}</p>
          <p class="label">
            <span v-for="item in docInfo.docLabelStr" :key="item">{{
              item
            }}</span>
          </p>
        </div>
      </div>
      <div class="code">
        <QRCODE
          class="ewm"
          :val="url"
          :size="400"
          background="#ffffff"
          foreground="#000000"
          pdground="#000000"
          onval
          loadMake
        />
        <p>长按识别二维码</p>
        <p>快捷购药</p>
      </div>
    </div>
  </div>
</template>

<script>
import { getDoctorInfo } from '@/api/base.js';
import QRCODE from '@/components/tki-qrcode/tki-qrcode.vue';

export default {
  name: 'ewmCard',
  components: {
    QRCODE,
  },
  data() {
    return {
      docInfo: null,
      url: '',
    };
  },
  onLoad(v) {
    this.url = v.hbCodeUrl;
    if (v.hosId) {
      uni.setStorageSync('hosId', v.hosId);
    }
    this.getDocInfo(v.docId);
  },
  methods: {
    async getDocInfo(docId) {
      let { data } = await getDoctorInfo({
        docId,
      });
      if (data.docLabelStr) {
        data.docLabelStr = data.docLabelStr.split(',');
      }
      this.docInfo = data;
    },
  },
};
</script>

<style lang="scss" scoped>
.ewm_card {
  padding: 100rpx 56rpx;

  .cont {
    padding: 24rpx;
    background: #fff;
    border-radius: 16rpx;
    box-shadow: 0px 4rpx 20rpx 0px rgba(150, 150, 150, 0.2);

    .info {
      @include flex;
      background: #f5f5f5;
      border-radius: 16rpx;
      padding: 24rpx;

      .img {
        width: 128rpx;
        height: 128rpx;
        object-fit: cover;
        border-radius: 16rpx;
        flex: none;
      }

      .doc {
        padding-left: 18rpx;

        .name {
          font-size: 32rpx;
          font-weight: bold;
        }

        .dept {
          color: #666;
          font-size: 24rpx;
          line-height: 40rpx;
        }

        .label {
          @include flex(left);
          flex-wrap: wrap;

          span {
            padding: 0 20rpx;
            background: #e8f6fd;
            @include font_theme;
            font-size: 24rpx;
            height: 36rpx;
            border-radius: 18rpx;
            margin-right: 16rpx;
            margin-bottom: 16rpx;
          }
        }
      }
    }

    .code {
      @include flex;
      flex-direction: column;
      padding: 32rpx;

      .ewm {
        width: 400rpx;
        height: 400rpx;
        margin-bottom: 20rpx;
      }

      p {
        font-size: 28rpx;
        line-height: 42rpx;
      }
    }
  }
}
</style>
