<template>
  <!--药品列表 -->
  <u-popup v-model="popupVisable1" mode="bottom" close-btn height="80%" @close="popClose"
    @open="openDrugPopUp">
    <view class="drug-content mt-[60rpx]">
      <!-- 药房选择 -->
      <scroll-view scroll-x class="drug-store-scroll">
        <view class="drug-store-list">
          <view
            v-for="(store, index) in drugStoreList"
            :key="index"
            class="drug-store-item"
            :class="{ active: currentDrugStoreId === store.drugstoreId }"
            @click="selectDrugStore(store)"
          >
            {{ store.drugstoreName }}
          </view>
        </view>
      </scroll-view>
      <view class="drug-search">
        <u-icon name="arrow-left" size="45" style=""></u-icon>
        <u-search placeholder="请输入药品名称" v-model="listquery.drugName" shape="round" :clearabled="false" @custom="handleConfirm" @search="handleConfirm"></u-search>
        <view class="drug-search-a">
          <view class="drug-search-b"></view>
          <u-icon name="close-circle-fill" size="30" style="" color="rgba(196, 196, 196, 1)" @click="listquery.drugName=''"></u-icon>
        </view>
      </view>
      <view class="drug-list flex h-[100%]">
        <view class="drug-slider">
          <block v-for='(item, index) in drugTypeList' :key="index">
            <view class="drug-slider-item" :class="currentSliderIndex === index ? 'active' : ''"
              @click="changeDrugType(item, index)">
              <img v-if="currentSliderIndex === index" src="/static/doc/u-4.png" alt="">
              {{ item.ddtName }}
            </view>
          </block>
        </view>
        <view class="drug-slider-content">
          <!-- 二级分类 -->
          <scroll-view scroll-x class="list_nav" v-if="sub_menu.length">
            <text class="span" :class="{ act: act === -1 }" @click="setSubMenu('')">全部</text>
              <text
                  class="span"
                  :class="{ act: ddtId == item.ddtId }"
                  v-for="item in sub_menu"
                  :key="item.ddtId"
                  @click="setSubMenu(item)"
              >{{ item.ddtName }}</text
              >
          </scroll-view>
          <scroll-view scroll-y class="drug-slider-content_wrapper" @scrolltolower="loadMoreData">
            <view class="drugListInfo" v-if="patentList.length > 0">
              <view class="drugCard" v-for="(item, index) in patentList" :key="index">
                <view class="drugInfo">
                  <view style="position: relative">
                    <image class="card_img" :src="item.docImgUrl ||`/static/drug1.png`" ></image>
                    <view v-if="item.drugType=='025.9'" class="drug-coverUp cfCover">
                      处方药 <br>
                      依规定不展示包装
                    </view>
                  </view>
                  <view class="info">
                    <view class="name ">{{ item.drugName }}</view>
                    <view class="specification">规格：{{ item.gg }}</view>
                    <view class="specification">
                      厂商：{{ item.scqy }}
                    </view>
                    <view style="display: flex;align-items: center;justify-content: space-between">
                      <view class="price">¥ {{ item.price }}</view>
                      <view class="btn-style btn-add" @click="selectPatent(item)">
                        添 加
                      </view>
                    </view>
                  </view>
                </view>
<!--                <view class="btns">-->
<!--                  <view class="btn-style btn-add" @click="selectPatent(item)">-->
<!--                    <u-button size="mini" type="primary" shape="circle" style="padding: 0 20px" @click="selectPatent(item)">添 加</u-button>-->
<!--                  </view>-->
<!--                </view>-->
              </view>
            </view>
            <view v-else class="none-data">
              <u-empty mode="data" class="pt-[100rpx]" color="#999999">
                <template #icon>
                  <image src="/static/image/cf/noneRrug.png" class="w-[350rpx] h-[350rpx]" />
                </template>
                <template #tips>暂无药品列表</template>
              </u-empty>
            </view>
          </scroll-view>
        </view>
      </view>
    </view>
    <!-- 设置用法用量 -->
    <DrugSetPopUp v-model="setDrugPopVisable" :patent="patent" :editOrAddDrug="editOrAddDrug"
                  @closeDrugPopUp="popClose" :cfIndex="cfIndex" />
    <!-- 药品详情 -->
    <DrugDetailPopUp v-model="showDrugDetailPopup" :durginfo="durginfo" />
  </u-popup>
</template>

<script >
import { findDrugById, doccommondrug, getDicDrugType, findAllDrug, findDrugByDocId,drugClassificationList,webDicDrugListPage } from '@/api/user.js'
import { getDrugStoreInfoPage } from '@/api/base.js'
import DrugDetailPopUp from './drugDetailPopUp.vue'
import DrugSetPopUp from './drugSetPopUp.vue'
import {mapState} from "vuex";
import drugRulesMixin from '@/mixins/drugRules.js'
export  default {
  components:{DrugSetPopUp,DrugDetailPopUp},
  mixins: [drugRulesMixin],
  props:{
    value: false,
    drugIndex: {
      type: [Number, String],
      default: 0
    },
    cfIndex: {
      type: [Number, String],
      default: 0
    },
  },
  data(){
    return {
      currentSliderIndex:0,
      currentDrugStoreId: '',
      drugStoreList: [],
      drugTypeList:[
          { ddtName: "全部", ddtId: '', },
          { ddtName: "常用药", ddtId: "1" },
      ],
      patentList:[],
      listquery:{
        page: 1,
        limit: 50,//每页条数
        docId: uni.getStorageSync("docId"),
        type: "1",//药品类型药品类型 0⻄成药 3草药
        drugName: "",
        otc: 0,
        ddtId: [],//药品分类ID
        regId: "",//挂号ID
        sortField: "drugName",
        sortType: "DESC",
        drugStoreId: ''
      },
      drugTypeFlag:0,
      showDrugDetailPopup:false,
      setDrugPopVisable: false,
      patent: {},
      editOrAddDrug: 'add',
      durginfo: {},
      totalNum:0,
      act:-1,
      ddtId:'',
      sub_menu:[],
      menuId:''
    }
  },
  computed:{
    popupVisable1:{
      get(){
        return this.value
      },
      set(newVal){
        this.$emit('input', newVal)
      }
    },
    ...mapState({
      drugList: (state) => state.drugList,
    }),
  },
  async created() {
    await this.initDrugRules()
  },
  methods:{
    setSubMenu(item){
      this.ddtId = item ? item.ddtId : "";
      this.act = item ? item.ddtId : -1;
      this.listquery.page = 1;
      this.listquery.limit = 50;
      this.listquery.ddtId = item ? [item.parentCode, item.ddtId] : [this.menuId];
      this.getDrugDetail();
    },
    handleConfirm(){
      this.listquery.page = 1
      this.listquery.limit = 50
      this.getDrugDetail()
    },
    changeDrugType(item,index){
      if (item.ddtName == "常用药") {
        //常用药
        this.drugTypeFlag = 1;
      } else {
        this.drugTypeFlag = 0;
      }
      this.menuId = item.ddtId;
      this.listquery.page = 1;
      this.listquery.limit = 50;
      this.listquery.drugName = "";
      this.currentSliderIndex = index;
      this.ddtId = item.ddtId;
      this.act = -1;
      this.sub_menu = this.drugTypeList[index].children;
      this.listquery.ddtId = [item.ddtId];
      this.getDrugDetail();
    },
    loadMoreData(){
      if (this.patentList.length >= this.totalNum) return;
      this.listquery.page += 1;
      this.getDrugDetail();
    },
    openDrugPopUp(){
      this.getdrugTypeList()
      this.getDrugStoreList()
      this.getDrugDetail()
    },
    getDrugStoreList(){
      getDrugStoreInfoPage({}).then(res => {
        if (res.data && res.data.length > 0) {
          this.drugStoreList = res.data;
          let source= uni.getStorageSync('sourceCode')||0
          if(source!='618') {
            this.drugStoreList=res.data.filter(item=>item.drugstoreId!='574a40ea01424f7a8b62762b37ff58e2')
          }
          // 默认选中第一个药房
          if (this.drugStoreList.length > 0 && !this.currentDrugStoreId) {
            this.currentDrugStoreId = this.drugStoreList[0].drugstoreId;
            this.listquery.drugStoreId = this.drugStoreList[0].drugstoreId;
            this.selectDrugStore(this.drugStoreList[0])
          }
        }
      });
    },
    getDrugDetail(){
      webDicDrugListPage({
        ...this.listquery,
        flag: this.drugTypeFlag === 1 ? "1" : null,
      }).then((res) => {
        let total = res.data.total;
        this.totalNum = total;
        if (this.listquery.page > 1) {
          this.patentList = [...this.patentList, ...res.data.rows];
        } else {
          this.patentList = res.data.rows;
        }
      });
      // if (this.drugTypeFlag == 0) {//全部或者其他的
      //   findAllDrug(this.listquery).then((res) => {
      //     let total = res.data.total
      //     this.totalNum = total
      //     if (this.listquery.page > 1) {
      //       this.patentList = [...this.patentList, ...res.data.rows];
      //     } else {
      //       this.patentList = res.data.rows;
      //     }
      //   })
      // } else {//常用
      //   findDrugByDocId(this.listquery).then((res) => {
      //     let total = res.data.total
      //     this.totalNum = total
      //     if (this.listquery.page > 1) {
      //       this.patentList = [...this.patentList, ...res.data.rows];
      //     } else {
      //       this.patentList = res.data.rows;
      //     }
      //   })
      // }
    },
    getdrugTypeList(){
      drugClassificationList({}).then((res) => {
        console.log(res);
        let menu = res.data
            .filter((item) => item.level === 1)
            .map((item) => {
              return {
                ...item,
                ddtName: item.name,
                ddtId: item.code,
                children: [],
              };
            });
        res.data.forEach((item) => {
          if (item.level === 2) {
            menu
                .find((item1) => item1.code === item.parentCode)
                ?.children.push({ ...item, ddtName: item.name, ddtId: item.code, children: [] });
          }
        });

        this.drugTypeList = [{ ddtName: "全部", ddtId: "", children: [] }, ...menu];
      });
      // getDicDrugType().then((res) => {
      //   this.drugTypeList=[{ ddtName: "全部", ddtId: '', }]
      //   this.drugTypeList =this.drugTypeList.concat(res.data)
      // })
    },
    popClose(){
      this.$emit("input", false);
    },
    goDurgInstruction(id, flag){
      findDrugById({ drugId: id }).then((res) => {
        this.durginfo = { flag: flag, ...res.data }
        this.showDrugDetailPopup = true
      })
    },
    colleCttDrug(item){
      let userInfo = uni.getStorageSync("userInfo")
      let data = {
        drugId: item.drugId,
        docId: userInfo.docId,
        docName: userInfo.docName
      }
      doccommondrug(data).then((res) => {
        uni.showToast({
          title: '收藏成功',
          icon: 'success',
        })
      })
    },
    selectPatent(item){
      const isNoDrugStore =this.drugList.some(
        (item) => item.drugstoreId !== this.currentDrugStoreId
      );
      if(isNoDrugStore){
        // this.$store.commit('setDrugList',[])
        uni.showModal({
          title: '提示',
          content: '同一处方内不可存在多个药房的药品，如更改则清空当前处方',
          success:  (res)=> {
            if (res.confirm) {
              // 用户点击确认，清空药品列表并添加新药
              this.$store.commit('setDrugList', [])
              if (this.drugList.some((v) => v.drugId == item.drugId)) {
                uni.showToast({
                  title: '不可重复添加药品',
                  icon: 'none'
                })
                return
              }
              if (this.drugList.length >= 5) {
                uni.showToast({
                  title: '最多添加5种药',
                  icon: 'none'
                })
                return
              }
              const initialQuantity = this.getInitialQuantity(item.drugId)
              this.$store.commit('addDrugList', {
                ...item,
                quan: initialQuantity,
                itemName: item.drugName,
                itemId: item.drugId,
                drugstoreId: this.currentDrugStoreId
              })
              this.popupVisable1 = false
            } else {
              // 用户点击取消，关闭弹框
              this.popupVisable1 = false
            }
          }
        })
        return
      }
      if(this.drugList.some(v=>v.drugId==item.drugId)){
        uni.showToast({
          title: '不可重复添加药品',
          icon: 'none',
        })
        return
      }
      if(this.drugList.length>=5){
        uni.showToast({
          title: '最多添加5种药',
          icon: 'none',
        })
        return
      }
      const initialQuantity = this.getInitialQuantity(item.drugId)
      this.$store.commit('addDrugList',{...item,quan:initialQuantity,itemName:item.drugName, itemId:item.drugId,drugstoreId:this.currentDrugStoreId})
      this.popupVisable1=false
    },
    selectDrugStore(store){
      this.currentDrugStoreId = store.drugstoreId;
      this.listquery.drugStoreId = store.drugstoreId;
      // 重置列表和分页
      this.patentList = [];
      this.listquery.page = 1;
      this.listquery.limit = 50;
      this.getDrugDetail();
    }
  }
}

</script>

<style scoped lang='scss'>
.drug-num-content {
  padding: 0 54rpx;

  .drug-num-label {
    font-weight: 800;
    font-size: 36rpx;
    color: #333333;
    line-height: 50rpx;
    text-align: center;
    margin: 32rpx 0;
  }

  .drug-name {
    font-weight: 500;
    font-size: 32rpx;
    color: #14A0E6;
    line-height: 32rpx;
    text-align: left;
    font-style: normal;
  }

  .drug-list {
    .drug-item {
      display: flex;
      align-items: center;
      margin: 30rpx 0;

      .drug-item-label {
        font-weight: 800;
        font-size: 32rpx;
        color: #333333;
        padding-right: 32rpx;
      }

      .drug-item-value {
        font-weight: 400;
        font-size: 28rpx;
        color: #333333;
        flex: 1;
        align-items: center;

        .flex-auto {
          display: flex;
        }
      }
    }
  }
}

.drug-content {
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;

  .drug-list {
    display: flex;
    height: calc(100% - 78px);
    flex: 1;

    .drug-slider {
      background: rgba(250, 250, 250, 1);
      overflow: auto;
      height:calc(100% - 10px);
      width: 186rpx;
      margin-top: 10px;
      .drug-slider-item {
        width: 100%;
        height: 88rpx;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 400;
        font-size: 12px;
        color: rgba(102, 102, 102, 1) !important;
        img{
          margin-right: 10px;
          width: 18px;
          height: 18px;
        }
        &.active {
          color: rgb(0, 0, 0) !important;
          position: relative;
          background: #fff;
          border-radius: 16rpx 0rpx 0rpx 16rpx;

          //&:before {
          //  content: '';
          //  position: absolute;
          //  top: 50%;
          //  left: 0;
          //  width: 4px;
          //  height: 16px;
          //  background-color: #1686FF;
          //  -webkit-transform: translateY(-50%);
          //  transform: translateY(-50%);
          //
          //}
        }
      }
    }

    .drug-slider-content {
      padding: 20rpx;
      flex: 1;
      overflow: auto;

      .drug-slider-content_wrapper {
        height: 100%
      }
    }
  }
}

.drugListInfo {
  .drugCard {
    .card_img{
      width: 230rpx;
      height: 163rpx;
    }
    .star {
      position: absolute;
      left: 0;
      width: 27px;
      height: 27px;
      top: 0;
    }
    position: relative;
    padding: 12px;
    background: white;
    border-radius: 8px;
  }

  .drugInfo {
    display: flex;

    .info {
      padding-left: 12px;

      .name {
        font-weight: 600;
        font-size: 28rpx;
        color: rgba(51, 51, 51, 1);
      }

      .specification {
        font-weight: normal !important;
        font-size: 20rpx;
        color: rgba(102, 102, 102, 1);
        margin-top: 2px;
      }

      .price {
        margin-top: 4px;
        font-size: 28rpx;
        color: #FF3B30;
      }
    }
  }

  .btns {
    text-align: right;
    margin-top: 12px;
    display: flex;
    justify-content: space-between;

  }
}
.btn-add{
  color: #ffffff;
  width: 100rpx;
  height: 34rpx;
  opacity: 1;
  border-radius: 10px;
  background: rgba(131, 106, 255, 1) !important;
  font-size: 11px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.drug-search{
  padding: 10px 10px 0 0;
  display: flex;
  align-items: center;
  ::v-deep .uni-input-placeholder{
    text-align: left;
    font-size: 12px;
  }
  ::v-deep .uni-input-input{
    text-align: left;
  }
}
.drug-search-a{
  display: flex;
  align-items: center;
  position: absolute;
  right: 70px;
  .drug-search-b{
    width: 1px;
    height: 30rpx;
    margin-right: 10px;
    background:  rgba(153, 153, 153, 1);
    color: rgba(153, 153, 153, 1);
  }
}
.list_nav {
  height: 56rpx;
  overflow-x: scroll;
  white-space: nowrap;
  overflow-y: hidden;
  flex: none;
  display: -webkit-box;  // 这句是重点，加了它就好使了。
  overflow-x: scroll;
  overflow-y: hidden;
  .span {
    padding: 0 24rpx;
    height: 100%;
    align-items: center;
    display: inline-flex;
    font-size: 24rpx;
    background-color: #fafafa;
    color: #333;
    margin-right: 12rpx;
    border-radius: 6rpx;

    &.act {
      color: rgba(135, 79, 240, 1);
      background-color: #e7f5fc;
    }
  }
}
.cfCover{
  position: absolute;
  top: 0;
  left: 0;
  width: 230rpx;
  height: 163rpx;
  text-align: center;
  font-size: 10px;
  background: rgba(255,255,255,0.7) !important;
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  z-index: 2;
}
.gx{
  filter: blur(2px);
}
.drug-coverUp{
  width: 230rpx;
  height: 163rpx;
  background: rgba(255, 255, 255, 0.5);
  position: absolute;
  top: 0;
  left: 0;
}
.drug-store-scroll {
  width: 100%;
  white-space: nowrap;
  background-color: #fff;
  padding: 20rpx 0;
  border-bottom: 1px solid rgba(245, 245, 245, 1);

  .drug-store-list {
    display: inline-flex;
    padding: 0 20rpx;

    .drug-store-item {
      padding: 0 30rpx;
      height: 60rpx;
      line-height: 60rpx;
      background-color: #f5f5f5;
      color: #666;
      border-radius: 30rpx;
      margin-right: 20rpx;
      font-size: 26rpx;

      &.active {
        background-color: rgba(135, 79, 240, 0.1);
        color: rgba(135, 79, 240, 1);
      }
    }
  }
}
</style>