<template>
  <view>
    <doc-info
      :doc-id="pageParam.docId"
      @docInfo="docInfo"
      pageType="visit"
    ></doc-info>
    <view class="tab-box">
      <view class="tab-item" v-if="curt == 1 || visiStatus == 3">
        <text class="text" @click="tabFun(1)" :class="{ active: curt == 1 }">
          线上
        </text>
      </view>
      <view class="tab-item" v-if="curt == 2 || visiStatus == 3">
        <text class="text" @click="tabFun(2)" :class="{ active: curt == 2 }">
          线下
        </text>
      </view>
    </view>
    <lineVisit v-if="curt == 1" :pageParam="pageParam" />
    <Offline v-if="curt == 2" :pageParam="pageParam" />
  </view>
</template>

<script>
import DocInfo from '@/components/docInfo/docInfo';
import lineVisit from './components/lineVisit.vue';
import Offline from './components/Offline.vue';
export default {
  components: {
    DocInfo,
    lineVisit,
    Offline,
  },
  data() {
    return {
      pageParam: {},
      aboutData: ['线上', '线下'],
      curt: 0,
      visiStatus: 0,
    };
  },
  onLoad(option) {
    this.pageParam = JSON.parse(option.param);
    this.visiStatus = option.visiStatus;
    // 如果不存在 则默认
    if (!this.visiStatus) this.visiStatus = 3;
    if (this.visiStatus == 3) {
      this.curt = 1;
    } else {
      this.curt = this.visiStatus;
    }
  },
  methods: {
    //线上线下切换
    tabFun(index) {
      this.curt = index;
    },
    docInfo(e) {
      let infoDetail = {
        dntName: e.dntName,
        docId: e.docId,
        docName: e.docName,
        docProf: e.docProf,
        deptName: e.deptName,
        deptId: e.deptId,
        docImg: e.docImg,
        docTel: e.telNo,
        docLable: e.docLable,
        dntId: e.dntId,
        hosId: e.hosId,
      };
      uni.setStorageSync('nowDocInfo', infoDetail);
    },
  },
};
</script>

<style scoped lang="scss">
.tab-box {
  display: flex;

  .tab-item {
    flex: 1;
    text-align: center;
    font-size: 36upx;
    color: #666;
    line-height: 86upx;

    .text {
      position: relative;
      display: inline-block;

      &.active {
        @include font_theme;

        &::after {
          position: absolute;
          bottom: 0;
          left: 0;
          content: '';
          display: block;
          width: 100%;
          height: 6upx;
          @include bg_theme;
        }
      }
    }
  }
}
</style>
