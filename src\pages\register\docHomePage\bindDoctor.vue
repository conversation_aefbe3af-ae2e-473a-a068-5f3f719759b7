<template>
  <view class="home">
    <!-- 浮动图标 -->
    <GOHOME />

    <!--    顶部绑定提示-->
    <view
      class="topTit"
      v-if="
        patientList.length > 0 &&
        patientList[patientIndex].isBound === 1 &&
        patientList[patientIndex].docId === docInfo.docId
      "
      >已绑定</view
    >

    <view
      class="topTit"
      v-if="
        patientList.length > 0 &&
        patientList[patientIndex].isBound === 1 &&
        patientList[patientIndex].changeDocStatus === 1
      "
      >审核中</view
    >

    <view
      class="topTit"
      v-if="
        patientList.length > 0 &&
        patientList[patientIndex].isBound === 1 &&
        patientList[patientIndex].changeDocStatus === 3 &&
        patientList[patientIndex].applyDocId === docInfo.docId
      "
      >审核不通过</view
    >

    <!--    医生信息-->
    <HEAD
      :info="docDetail"
      :collectFlag="collectFlag"
      @collect="collectOperation"
      @share="toShare"
    />

    <!--    顶部绑定提示-->
    <view class="topTit" style="padding: 20upx; margin-top: 20upx">
      <view style="margin-bottom: 20upx">绑定说明</view>
      <view style="margin-bottom: 20upx; text-align: left">
        1）首次完成绑定医生后，您可在我的医生内查看绑定医生信息，每个就诊人只能绑定一位医生</view
      >

      <view style="margin-bottom: 20upx; text-align: left"
        >2）如您需修改我的医生，扫描新的医生二维码后，补充修改原因，提交审核即可</view
      >
      <view style="margin-bottom: 20upx; text-align: left"
        >3）通过核实修改原因后，审核通过后您的绑定医生即可修改完成</view
      >
    </view>

    <!--    当前就诊人区域-->
    <view class="topTit" style="display: flex; padding: 20upx">
      <view>就诊人</view>
      <view style="width: 80%">
        <picker
          @change="bindPickerChange"
          :range="patientList"
          :value="patientIndex"
          range-key="patientName"
        >
          <view class="uni-input">{{
            patientList[patientIndex].patientName
          }}</view>
        </picker>
      </view>
    </view>

    <view
      class="topTit"
      style="display: flex; padding: 20upx; flex-wrap: wrap"
      v-if="
        patientList.length > 0 &&
        patientList[patientIndex].isBound == 1 &&
        patientList[patientIndex].applyDocId === docInfo.docId &&
        patientList[patientIndex].changeDocStatus === 3
      "
    >
      <view>不通过原因</view>
      <view style="width: 100%">
        <u-input
          style="width: 100%; padding: 20upx; margin-top: 20upx"
          v-model="patientList[patientIndex].rejectReason"
          type="textarea"
          maxlength="100"
          disabled
        ></u-input>
      </view>
    </view>

    <view
      class="topTit"
      style="display: flex; padding: 20upx; flex-wrap: wrap"
      v-if="btnTips"
    >
      <view>更换原因</view>
      <view style="width: 100%">
        <u-input
          style="width: 100%; padding: 20upx; margin-top: 20upx"
          v-model="changeReason"
          type="textarea"
          placeholder="请输入更换原因"
          maxlength="100"
          :disabled="patientList[patientIndex].changeDocStatus === 1"
          @input=""
        ></u-input>
        <view style="float: right">{{ changeReason.length }} / 100 </view>
      </view>
    </view>

    <!-- 分享 -->
    <view class="share-container" v-if="isShareShow" @click="cancelShare">
      <image
        src="/static/images/index/share-toast.png"
        mode=""
        class="share-bg"
      ></image>
    </view>

    <!--    底部按钮部分-->
    <!--    确认绑定医生-->
    <view
      class="pay_buts"
      v-if="
        patientList.length > 0 &&
        (patientList[patientIndex].isBound == 0 ||
          !patientList[patientIndex].isBound)
      "
    >
      <text @click.stop="applyRefund">确认绑定</text>
    </view>
    <!--    申请更换绑定医生-->
    <view class="pay_buts" v-if="btnChange">
      <text @click.stop="changeCollectOperation">申请更换</text>
    </view>
  </view>
</template>

<script>
import { Toast } from "@/common/js/pay.js";
import HEAD from "./com/head.vue";
import DNTLIST from "./com/dntList.vue";
import OFFLINE from "./com/offline.vue";
import GOHOME from "@/components/home/<USER>";
import uniIcons from "@/components/uni-icons/uni-icons.vue";
import {
  findDoctorByID,
  getIsExist,
  cancelKeepDoc,
  addCollectDoctor,
  findDocDept,
  getConfigInfo,
  getSysPlatformConfigByKeyList,
  saveUserRelation,
  changeApply,
} from "@/api/base.js";
import { getPatientList } from "@/api/user.js";

import myJsTools from "@/common/js/myJsTools.js";
import { getJSSDKSign } from "@/api/share.js";
var jweixin = require("jweixin-module");

import urlConfig from "@/common/request/config.js";
import Login from "@/mixins/login.js";

export default {
  mixins: [Login],
  components: {
    uniIcons,
    GOHOME,
    DNTLIST,
    OFFLINE,
    HEAD,
  },
  data() {
    return {
      docInfo: {},
      showFlag: true,
      deptList: [],
      docDetail: {
        specialties: "",
        docSynopsis: "",
      },
      collectFlag: false,
      initialFlag: false,
      num: 0,
      inquiryWay: [],
      subsequent: [],
      visitInfo: [],
      listPr: [],
      prTotal: "",
      evaluateQuery: {
        docId: "",
        limit: 10,
        page: 1,
      },
      // 评价的标签
      evaluate: [
        {
          title: "接诊神速",
          cont: 5,
        },
        {
          title: "接诊神速",
          cont: 7,
        },
        {
          title: "接诊神速",
          cont: 3,
        },
      ],
      evaluateFlag: false, // 是否显示查看全部评价按钮
      loadingFlag: false,
      isShareShow: false, // 分享引导图
      // 是否开启咨询
      isZx: false,
      // 是否开启复诊
      isFz: false,
      // 是否倒序 默认 咨询在前
      isDesc: false,
      // 是否显示排班
      showTab: false,
      // 线下排班
      offlineList: [],
      patientList: [], // 就诊人列表
      patientIndex: 0,
      changeReason: "", // 更换理由
      rejectReason: "", // 申请不通过原因
    };
  },
  computed: {
    // 控制申请更换按钮显示
    btnChange() {
      if (this.patientList.length > 0) {
        if (this.patientList[this.patientIndex].isBound === 1) {
          if (this.patientList[this.patientIndex].changeDocStatus === 0) {
            if (
              this.patientList[this.patientIndex].docId !== this.docInfo.docId
            ) {
              return true;
            }
          }

          if (this.patientList[this.patientIndex].changeDocStatus === 1) {
            if (
              this.patientList[this.patientIndex].applyDocId !==
              this.docInfo.docId
            ) {
              return true;
            }
            return false;
          }

          if (this.patientList[this.patientIndex].changeDocStatus === 3) {
            return true;
          }

          if (this.patientList[this.patientIndex].changeDocStatus === 2) {
            if (
              this.patientList[this.patientIndex].docId !== this.docInfo.docId
            ) {
              return true;
            }
            return false;
          }
        }
      }
    },
    // 控制更换原因控件显示
    btnTips() {
      if (this.patientList.length > 0) {
        if (this.patientList[this.patientIndex].isBound === 1) {
          if (this.patientList[this.patientIndex].changeDocStatus === 0) {
            if (
              this.patientList[this.patientIndex].docId !== this.docInfo.docId
            ) {
              return true;
            }
          }

          if (this.patientList[this.patientIndex].changeDocStatus === 1) {
            return true;
          }

          if (this.patientList[this.patientIndex].changeDocStatus === 2) {
            if (
              this.patientList[this.patientIndex].docId !== this.docInfo.docId
            ) {
              return true;
            }
            return false;
          }

          if (this.patientList[this.patientIndex].changeDocStatus === 3) {
            return true;
          }
        }
      }
    },
  },
  onLoad(option) {
    // return;
    this.getConfig();
    let docId = option.docId;
    this.docInfo.docId = docId;
    this.getDoc();
    this.getJSSDKSignFun();
  },
  methods: {
    // 确认绑定按钮
    applyRefund() {
      console.log(this.patientList[this.patientIndex]);
      this.collectOperation();
    },
    // 就诊人回调
    bindPickerChange: async function (e) {
      console.log("picker发送选择改变，携带值为", e.detail.value);
      this.getPaList();
      this.patientIndex = e.detail.value;
      if (this.patientList[this.patientIndex].changeDocStatus === 3) {
        this.changeReason = this.patientList[this.patientIndex].changeReason;
      }
      if (this.patientList[this.patientIndex].changeDocStatus === 1) {
        this.changeReason = this.patientList[this.patientIndex].changeReason;
        let res = await findDocDept({
          docId: this.patientList[this.patientIndex].applyDocId,
        });
        if (res.data.length <= 1) {
          let docInfo = {
            docId: this.patientList[this.patientIndex].applyDocId,
            deptId: res.data[0].deptId,
          };
          this.docInfo = docInfo;
          this.getDocDetail();
        }
      }
    },
    async getDoc() {
      let docId = this.docInfo.docId;
      let res = await findDocDept({
        docId,
      });
      if (res.data.length <= 1) {
        let docInfo = {
          docId,
          deptId: res.data[0].deptId,
        };
        this.docInfo = docInfo;
        this.initData();
      } else {
        let deptList = [];
        for (let i = 0; i < res.data.length; i++) {
          deptList.push({
            name: res.data[i].deptName,
            deptId: res.data[i].deptId,
          });
        }
        this.deptList = deptList;
        this.deptFlag = true;
      }
    },
    // 获取当前用户下所有就诊人
    async getPaList() {
      let userId = uni.getStorageSync("userId");
      console.log("查看当前用户id", userId);
      try {
        let { data } = await getPatientList({
          userId,
        });

        if (data) {
          console.log(data);
          this.patientList = data;
          if (this.patientList[this.patientIndex].changeDocStatus === 3) {
            this.changeReason =
              this.patientList[this.patientIndex].changeReason;
          }
          if (this.patientList[this.patientIndex].changeDocStatus === 1) {
            this.changeReason =
              this.patientList[this.patientIndex].changeReason;
            let res = await findDocDept({
              docId: this.patientList[this.patientIndex].applyDocId,
            });
            if (res.data.length <= 1) {
              let docInfo = {
                docId: this.patientList[this.patientIndex].applyDocId,
                deptId: res.data[0].deptId,
              };
              this.docInfo = docInfo;
              this.getDocDetail();
            }
          }
        }
        setTimeout(uni.hideLoading, 300);
      } catch (err) {
        throw err;
      }
    },
    // 查询配置
    async getConfig() {
      let { data } = await getSysPlatformConfigByKeyList([
        "display_consultative",
        "display_fllow",
        "consultation_follow_up_order",
      ]);
      data.forEach((v) => {
        // 开启咨询
        if (v.configKey == "display_consultative") {
          this.isZx = v.configValue == 0 ? false : true;
        }
        // 开启复诊
        if (v.configKey == "display_fllow") {
          this.isFz = v.configValue == 0 ? false : true;
        }
        // 1 咨询前 2 复诊前
        if (v.configKey == "consultation_follow_up_order") {
          let str = v.configValue.substring(0, 1);
          if (str == 1 || str == 2) {
            this.isDesc = str == 1 ? false : true;
          }
        }
      });
    },
    // 调起分享sdk
    async getJSSDKSignFun() {
      let para = {
        appid: uni.getStorageSync("appId"),
        url: window.location.href.split("#")[0],
      };
      let res = await getJSSDKSign(para);
      var info = res.data;
      jweixin.config({
        debug: false,
        appId: uni.getStorageSync("appId"), // 必填，公众号的唯一标识
        timestamp: info.timestamp, // 必填，生成签名的时间戳
        nonceStr: info.nonceStr, // 必填，生成签名的随机串
        signature: info.signature, // 必填，签名
        jsApiList: ["updateAppMessageShareData", "updateTimelineShareData"], // 必填，需要使用的JS接口列表
      });
    },
    // 分享
    toShare() {
      let evt = this.docDetail;
      let docInfo = {
        docId: evt.docId,
        docImg: evt.docImg,
        docName: evt.docName,
        docProf: evt.docProf,
        deptName: evt.deptName,
        hosName: evt.hosName,
        docLable: evt.docLable,
        hosId: evt.hosId,
      };

      const shareLogoUrl = require("@/common/request/config.js").shareLogoUrl;

      let userId = uni.getStorageSync("userId") || "";
      let openid = uni.getStorageSync("wxInfo").openId;
      let url = `${urlConfig.rootUrl}#/pages/shareDocCard/index?docId=${docInfo.docId}&hosId=${docInfo.hosId}&userId=${userId}&openid=${openid}&doCodeType=D_C_2`;

      // alert("调用分享微信好友");
      jweixin.updateAppMessageShareData({
        title: `医生-${evt.docName}`, // 分享标题
        desc: evt.docName + "医生医术高明、服务贴心、点击去问诊沟通>>>", // 分享描述
        link: url,
        imgUrl: shareLogoUrl,
      });
      // alert("调用分享朋友圈");
      jweixin.updateTimelineShareData({
        title: evt.docName + "医生名片，点击沟通交流>>>", // 分享标题
        desc: evt.docName + "医生医术高明、服务贴心，点击去问诊沟通>>>",
        link: url, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
        imgUrl: shareLogoUrl, // 分享图标
      });

      this.isShareShow = true;
    },
    // 取消分享
    cancelShare() {
      this.isShareShow = false;
    },
    // 获取全局参数
    async getWholeArg() {
      // 获取全局参数
      let res = await getConfigInfo();
      this.$store.commit("setWholeArg", res.data);
    },

    // 初始化页面
    async initData() {
      uni.showLoading({
        mask: true,
      });
      this.evaluateQuery.docId = this.docInfo.docId;

      // 获取就诊人列表
      this.getPaList();

      // 医生详情
      await this.getDocDetail();

      this.getIsExistFun();
      uni.hideLoading();
    },
    // 获取医生信息
    async getDocDetail() {
      let res = await findDoctorByID({
        docId: this.docInfo.docId,
        deptId: this.docInfo.deptId,
      });
      this.docDetail = res.data;
      this.docDetail.docImgCopy = res.data.docImg;
      if (res.data.docImg) {
        myJsTools.downAndSaveImg(res.data.docImg, (url) => {
          this.docDetail.docImg = url;
        });
      }
      uni.setStorageSync("hosId", res.data.hosId);
    },
    // 接收选择医生科室的结果
    propConfirm(evt) {
      this.deptFlag = false;
      this.docInfo.deptId = evt.deptId;
      this.initData();
    },
    // 取消选择医生
    propCancel() {
      this.deptFlag = false;
      // 获取当前页面栈数量
      let length = getCurrentPages().length;
      // 可以后退
      if (length > 1) {
        uni.navigateBack({
          delta: 1,
        });
      } else {
        // 刷新页面导致页面栈丢失
        uni.reLaunch({
          url: "/pages/index/index",
        });
      }
    },
    // 查询该医生是否被关注
    async getIsExistFun() {
      if (uni.getStorageSync("userId")) {
        let res = await getIsExist({
          docId: this.docInfo.docId,
          openid: uni.getStorageSync("wxInfo").openId,
        });
        this.collectFlag = res.data;
      }
    },
    // 绑定医生
    async collectOperation() {
      let obj = {
        docId: this.docInfo.docId,
        patientId: this.patientList[this.patientIndex].patientId,
        relationType: "1",
        isInquiry: "0",
      };
      await saveUserRelation(obj);
      Toast("绑定成功");
      this.getPaList();
    },

    // 更换绑定医生
    async changeCollectOperation() {
      let obj = {
        docId: this.docInfo.docId,
        patientId: this.patientList[this.patientIndex].patientId,
        applyReason: this.changeReason,
      };
      await changeApply(obj);
      Toast("您的更换申请已提交，请等待");
      this.getPaList();
    },
  },
  filters: {
    time(val) {
      if (!val) return "**";
      let str = val.split(" ")[0];
      str = str.replace(/-/g, ".");
      return str;
    },
    userName(val) {
      if (!val) return "";
      let str = val[0];
      str += "**";
      return str;
    },
  },
};
</script>

<style lang="scss" scoped>
.home {
  position: relative;
  padding: 20rpx 32rpx;
  min-height: 100vh;
  background: linear-gradient(225deg, #eaf8ff 0%, #f3f4f6 100%);
}

.pay_buts {
  @include flex(right);

  text {
    @include flex;
    width: 100%;
    height: 86rpx;
    border-radius: 100rpx;
    @include bg_theme;
    color: #fff;
    font-size: 34rpx;
  }
}

.topTit {
  width: 100%;
  text-align: center;
  padding: 20upx 0;
  color: #666666;
  background: #fff;
  border-radius: 20upx;
  margin-bottom: 20upx;
}

* {
  box-sizing: border-box;
}

.offline {
  margin-top: 24rpx;
}

.dnt_list {
  margin-top: 24rpx;
}

/* 空排班 */
.empty {
  height: 488rpx;
  border-radius: 16rpx;
  background-color: #fff;
  margin-top: 24rpx;
  box-shadow: 0px 4rpx 40rpx 0px rgba(0, 0, 0, 0.1);
  @include flex(left);
  flex-direction: column;

  &_img {
    width: 386rpx;
    height: 324rpx;
    margin-bottom: 30rpx;
  }

  text {
    font-size: 28rpx;
    color: #999;
  }
}

/* 评价 */
.evaluate {
  padding: 24rpx;
  margin-top: 24rpx;
  background-color: #fff;
  border-radius: 16rpx;
}

.evaluate_top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 22rpx;
  color: #999999;
}

.evaluate_top .font_col_333 {
  font-size: 28rpx;
  color: #333333;
  font-weight: bold;
  padding-right: 20rpx;
}

.evaluate_top .font_col_666 {
  color: #666666;
  margin-right: 10rpx;
  font-weight: bold;
}

.evaluate_top .font_col_blue {
  @include font_theme;
  font-weight: bold;
}

.evaluate_list {
  font-size: 26rpx;
  padding: 20rpx 0;
}

/* 更多文案 */
.more-text {
  text-align: center;
  font-size: 24rpx;
  color: #999;
  padding: 30rpx 0 0;
  border-top: 1px solid #eee;
  margin-top: 30rpx;
}

.rate-content {
  display: flex;
  align-items: center;

  /deep/ .uni-rate {
    position: relative;
    top: 6upx;
  }
}

/deep/.rate-media .rate-media-body {
  margin-top: 0;
}

.evaluate_list .nickname {
  color: #666666;
  margin-right: 20rpx;
}

.evaluate_list .eval-content {
  color: #333333;
  font-size: 26rpx;
  padding: 10rpx 0;
}

.evaluate_list .diagnose {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #999999;
  font-size: 26rpx;
}

.evaluate_btn {
  width: 100%;
  height: 92rpx;
  border-radius: 46rpx;
  @include border_theme;
  line-height: 92rpx;
  text-align: center;
  font-size: 36rpx;
  @include font_theme;
  box-sizing: border-box;
}

/* 分享 */
.share-container {
  position: fixed;
  width: 100%;
  height: 100vh;
  left: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  z-index: 999;
}

.share-bg {
  width: 100%;
  height: 100%;
}

.content {
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  background: #ffffff;
  border-radius: 8px 8px 0px 0px;
  padding: 42rpx 32rpx 32rpx;
  box-sizing: border-box;
}

.share-style {
  display: flex;
  align-items: center;
  justify-content: space-around;
  font-size: 22rpx;
  font-weight: 400;
  color: #999999;
  line-height: 32rpx;
}

.share-style .list {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.share-style .list image {
  display: block;
  width: 100rpx;
  height: 100rpx;
  margin-bottom: 6rpx;
}

.cancel {
  width: 100%;
  height: 98rpx;
  margin-top: 62rpx;
}

.cancel .btn {
  width: 100%;
  height: 100%;
  @include bg_theme;
  border-radius: 48rpx;
  text-align: center;
  line-height: 98rpx;
  font-size: 36rpx;
  font-weight: 400;
  color: #ffffff;
}

// 评价内容
.eval-content {
  .eval-cont-imgs {
    @include flex(left);
    flex-wrap: wrap;
    padding: 20rpx 0;

    .eval-cont-imgs-item {
      width: 200rpx;
      height: 200rpx;
      margin-right: 20rpx;
      margin-bottom: 15rpx;
      border: 1px solid #efefef;
      box-sizing: border-box;
      border-radius: 8rpx;

      &:nth-child(3n) {
        margin-right: 0;
      }
    }
  }
}

// 没有评价文案
.no_eval {
  line-height: 180upx;
  text-align: center;
  font-size: 28upx;
  color: $k-info-title;
}

// 评价标签
.pr_label {
  @include flex(left);
  flex-wrap: wrap;
  padding-top: 26upx;

  .pr_label_item {
    height: 36upx;
    background-color: #e8f6fd;
    padding: 0 12upx;
    margin-right: 16upx;
    margin-bottom: 16upx;
    font-size: 22upx;
    @include font_theme;
    @include flex;
    border-radius: 18upx;
  }
}
</style>
