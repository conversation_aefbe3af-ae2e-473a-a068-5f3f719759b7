<template>
  <!-- 卡片组件 -->
  <view class="user_info">
    <!-- 患者信息 -->
    <view class="user">
      <text>{{ info.patientName }}</text>
      <text>{{ info.sex }}</text>
      <text>{{ info.age }}{{ info.ageUnit }}</text>
    </view>
    <!-- 开单医生 -->
    <view class="info_item">
      <text>开单医生：{{ info.docName }}</text>
    </view>
    <!-- 医生科室 -->
    <view class="info_item">
      <text>医生科室：{{ info.deptName }}</text>
    </view>
    <!-- 临床诊断 -->
    <view class="info_item">
      <text>临床诊断：{{ info.ppiDiag }}</text>
    </view>
    <!-- 开单时间 -->
    <view class="info_item">
      <text>开单时间：{{ info.addTime }}</text>
    </view>
    <!-- 检查单号 -->
    <view class="info_item">
      <text
        >检{{ isLis ? '验' : '查' }}单号：{{
          isLis ? info.pliCode : info.ppiCode
        }}</text
      >
    </view>
  </view>
</template>

<script>
export default {
  name: 'Info',
  props: {
    // 是否检验
    isLis: {
      type: Boolean,
      default: false,
    },
    info: {
      type: Object,
      default: () => {},
    },
  },
};
</script>

<style lang="scss" scoped>
page {
  background: #f5f5f5;
}
* {
  box-sizing: border-box;
}

.user_info {
  width: 100%;
  padding: 24rpx 32rpx 32rpx;
  background: #ffffff;
  border-radius: 8rpx;
  margin-top: 16rpx;

  .user {
    @include flex(lr);

    text {
      font-size: 30rpx;
      color: #333;
      font-weight: bold;
    }
  }

  .info_item {
    font-size: 28rpx;
    color: #666;
    margin-top: 16rpx;
    line-height: 40rpx;
  }
}
</style>
