<template>
  <!-- 修改手机号 -->
  <view class="page">
    <view class="page-container">
      <view class="input-box">
        <text>新手机号</text>
        <input
          type="number"
          v-model="newTelNo"
          placeholder="请输入新手机号"
          maxlength="11"
        />
      </view>
      <view class="input-box">
        <text>验证码</text>
        <input
          type="number"
          v-model="captcha"
          placeholder="请输入验证码"
          maxlength="6"
        />
        <view class="sendBtn" @click="sendCode">
          {{ codeinfo.btnText }}
        </view>
      </view>
    </view>

    <view class="editor-btn">
      <button @click="confirmEditor">确认更换</button>
    </view>
  </view>
</template>

<script>
import { Toast } from "@/common/js/pay.js";
import { sendCaptcha, updatePatientPersonalCenterInfo } from "@/api/user.js";
import regex from "@/common/js/regex.js";
import {
  getPatientPersonalCenterInfo,
} from "@/api/user.js";
export default {
  data() {
    return {
      newTelNo: "",
      captcha: "",
      codeinfo: {
        sendAuthCode: true,
        auth_time: 0,
        btnText: "获取验证码",
      },
      patientInfo: {},
    };
  },
  onShow() {
    this.getInfo();
  },
  methods: {
    async getInfo() {
      let { data } = await getPatientPersonalCenterInfo({
        userId: uni.getStorageSync("userId"),
        appid: uni.getStorageSync("appId"),
      });
      this.patientInfo = data;
      this.cardIdBlur(this.patientInfo.idNo)
    },
    cardIdBlur(e) {
      let cardId =e;
      let form = this.patientInfo;
      if (regex.idNoBlur(cardId)) {
        if (regex.disCriCard(cardId, "sex") == 2) {
          form.sex = "女";
        } else {
          form.sex = "男";
        }
        form.age = regex.disCriCard(cardId, "age");
        form.sexCode = regex.disCriCard(cardId, "sex");
      }
    },
    async sendCode() {
      if (!regex.telBlur(this.newTelNo)) {
        Toast("手机号格式错误");
        return;
      }
      if (this.codeinfo.auth_time > 0) return;

      await sendCaptcha({ telNo: this.newTelNo });
      this.codeinfo.auth_time = 60;
      const timer = setInterval(() => {
        this.codeinfo.auth_time--;
        this.codeinfo.btnText = `${this.codeinfo.auth_time}s后重新发送`;
        if (this.codeinfo.auth_time <= 0) {
          this.codeinfo.btnText = "重新发送";
          clearInterval(timer);
        }
      }, 1000);
    },
    async confirmEditor() {
      if (!regex.telBlur(this.newTelNo) || !regex.codeBlur(this.captcha)) {
        Toast("请填写完整信息");
        return;
      }

      await updatePatientPersonalCenterInfo({
        ...this.patientInfo,
        userId: uni.getStorageSync("userId"),
        telNo: this.newTelNo,
        captcha: this.captcha
      });

      Toast("手机号修改成功");
      setTimeout(() => {
        uni.navigateBack();
      }, 1000);
    }
  }
};
</script>

<style scoped lang="scss">
.page-container {
  padding: 32rpx;
  background: #fff;
}

.input-box {
  display: flex;
  align-items: center;
  height: 92rpx;
  border-bottom: 1rpx solid #ebebeb;
  font-size: 28rpx;

  text {
    width: 140rpx;
  }

  input {
    flex: 1;
    padding-left: 40rpx;
  }
}

.sendBtn {
  width: 188rpx;
  text-align: right;
  color: var(--theme-color);
}

.editor-btn {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 32rpx;

  button {
    background: #836aff;
    color: #fff;
    height: 84rpx;
    border-radius: 42rpx;
    font-size: 32rpx;
  }
}
</style>
