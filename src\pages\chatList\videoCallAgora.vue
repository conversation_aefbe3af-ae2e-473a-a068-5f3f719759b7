<template>
  <!-- 视频通话 -->
  <view class="video_call">
    <!-- 医生头像 -->
    <view class="doc_info" v-if="!isVideo && !showTip">
      <img v-img="docImg" v-if="docImg" :data-src="errImg" class="doc_head" />
      <view class="doc_name">{{ docName }}</view>
    </view>

    <!-- 对方视频 -->
    <div
        id="docter"
        v-show="isVideo"
        :class="isMeMax ? 'me_video' : 'docter_video'"
        @click="onSwitch"
    ></div>

    <!-- 自己视频 -->
    <div
        id="me"
        v-show="isVideo"
        :class="isMeMax ? 'docter_video' : 'me_video'"
        @click="onSwitch"
    ></div>

    <!-- 控制按钮 -->
    <view class="footer" v-if="!showTip">
      <!-- 倒计时 -->
      <view class="footer_time" v-if="isPrepool==0">{{ timeStr }}</view>
      <!-- 静音 -->
      <view class="menu_item" v-if="false" >
        <image src="/static/images/video/mute.png" class="item_icon" />
        <button @click="onMuted">静音</button>
      </view>

      <!-- 挂断 -->
      <view class="menu_item" @click="hangUp">
        <image src="/static/images/video/hangUp.png" class="item_icon" />
        <button>挂断</button>
      </view>

      <!-- 切换 -->
      <view class="menu_item" @click="setCamera" v-if="false">
        <image src="/static/images/video/switch.png" class="item_icon" />
        <button>切换</button>
      </view>
    </view>

    <!-- <TIP v-if="showTip" /> -->

    <button class="call" @click="start" v-if="showTip">
      <image src="/static/images/video/call.png" />
      <text>点击接听</text>
    </button>

    <!-- 遮罩 -->
    <view class="zhe" v-if="showZhe">
      <view class="title">
        <image src="/static/images/video/waing.png" class="tip_icon"></image>
        提示
      </view>
      <view class="content">
        <text>抱歉！当前通话已失效</text>
        <text>如需继续沟通</text>
        <text>请回到微信公众号联系该医生</text>
      </view>
    </view>
  </view>
</template>

<script>
import {Toast} from '@/common/js/pay.js';

import {findDoctorByID} from '@/api/base';

import {
  // 获取token
  getAgoraToken,
  // 挂断
  AgoraUpVideo,
  // 接听
  videoPhone,
} from '@/api/pv.js';

let timer;
import AgoraRTC from '../../utils/AgoraRTC_N-4.18.3'

function getTime(n) {
  let h = parseInt(n / 60 / 60);
  let m = parseInt((n / 60) % 60);
  let s = parseInt(n % 60);
  if (h < 10) h = '0' + h;
  if (m < 10) m = '0' + m;
  if (s < 10) s = '0' + s;
  return h + ':' + m + ':' + s;
}


import CONFIG from '@//common/request/config';

import TIP from '@/components/tips/tips.vue';

var client, localTracks,remoteUsers = [];
export default {
  name: 'VideoCall',
  components: {
    TIP,
  },
  data() {
    return {
      // 声网相关
      channel: '',
      uid: null,
      appid: '',
      agoraClient: null, //实例
      localTracks: {
        //信道
        videoTrack: null,
        audioTrack: null,
      },
      // 是否显示遮罩
      showZhe: false,
      // 是否微信内
      isIos: false,
      // 是否自己最大化
      isMeMax: false,
      // 服务id
      regId: '',
      // 用户 id
      userId: '',
      // token
      token: '00645bc5769f3c74fa28346a7561657d179IAArK5cxgjkmxRfyTcVjtA35P5FeQ7vYG5Rk1JtvuldiAbXRsGgAAAAAIgC71fC85S4qZQQAAQB06yhlAgB06yhlAwB06yhlBAB06yhl',
      // 是否挂断
      isHangUp: false,
      // 倒计时
      time: 10,
      // 循环
      timer: '',
      // 显示
      timeStr: '00:00:00',
      // 显示提示
      showTip: true,
      // 医生信息
      docImg: '',
      // 医生id
      docId: '',
      // 科室id
      deptId: '',
      // 医生姓名
      docName: '',
      // 是否视频
      isVideo: false,
      // 默认头像
      errImg: require('../../static/images/docHead.png'),
      //是否流转处方
      isPrepool: '',
    };
  },
  onLoad(obj) {
    uni.setNavigationBarTitle({
      title: obj.isVideo == 0 ? "语音通话" : "视频通话",
    });
    // 是否ios
    let isIos = false;
    let {system} = uni.getSystemInfoSync();
    if (system.indexOf('iOS') != -1) {
      isIos = true;
    }
    this.isIos = isIos;

    // 赋值
    this.regId = obj.regId;
    // 房间号用挂号id
    this.channel = obj.regId;
    this.appid = CONFIG.appid;
    this.userId = obj.userId;
    this.token = obj.token;
    this.docId = obj.docId;
    this.deptId = obj.deptId;
    this.isVideo = obj.isVideo == 0 ? false : true;
    this.isPrepool = obj.isPrepool;
    let hosId = obj.hosId;
    // 如果存在hosId
    if (hosId) {
      uni.setStorageSync('hosId', hosId);
    }

    // 初始化
    // this.init();

    // 获取头像
    this.getDocInfo();

    // this.showTip = false;

    // this.joinRoom();
  },
  async created() {
    // create Agora client
    client = AgoraRTC.createClient({mode: "live", codec: "h264",role:"host"});

    localTracks = {
      videoTrack: null,
      audioTrack: null
    };
    // await this.join();
  },
  methods: {
    onMuted(){
      console.log("调用静音")
      //调用静音
      localTracks.audioTrack.setMuted(true);
    },
    async join() {
      const {data: agoraToken} = await getAgoraToken({
        channelName: this.channel,
        userId: this.userId,
      });
      this.showTip = false;
      // add event listener to play remote tracks when remote user publishs.
      client.on("user-published", this.handleUserPublished);
      client.on("user-unpublished", this.handleUserUnpublished);
      client.on('user-left', this.hangUp);
      // await client.setClientRole('host')
      if (this.isVideo == 1) {
        // join a channel and create local tracks, we can use Promise.all to run them concurrently
        [this.uid, localTracks.audioTrack, localTracks.videoTrack] = await Promise.all([
          // join the channel
          client.join(this.appid, this.channel, agoraToken || null),
          // create local tracks, using microphone and camera
          AgoraRTC.createMicrophoneAudioTrack(),
          AgoraRTC.createCameraVideoTrack({
            facingMode:"user",
            encoderConfig: {
              width: 2560,
              // 支持指定一个范围和参考值，具体配置参考相关 API 文档
              height: {ideal: 1440, min: 400, max: 2000},
              frameRate: 15,
              bitrate: 4780,
              bitrateMin: 3000, bitrateMax: 6000,
            },
          })
        ]);

        // play local video track
        localTracks.videoTrack.play("me",{
          mirror:false,
        });
        // publish local tracks to channel
        await client.publish(Object.values(localTracks));
        console.log("publish success");
      }else{
        [this.uid, localTracks.audioTrack] = await Promise.all([
          // join the channel
          client.join(this.appid, this.channel, agoraToken || null),
          // create local tracks, using microphone and camera
          AgoraRTC.createMicrophoneAudioTrack()
        ]);
        await client.publish(localTracks.audioTrack);
        // localTracks.audioTrack.play();
      }
      this.joinRoom();
    },

    async subscribe(user, mediaType) {
      console.log(user,mediaType)
      const uid = user.uid;
      await client.subscribe(user, mediaType);
      if (mediaType === 'video') {
        user.videoTrack.play('docter',{
          mirror:false,
        });
      }
      if (mediaType === 'audio') {
        user.audioTrack.play();
      }
    },

    handleUserPublished(user, mediaType) {
      const id = user.uid;
      remoteUsers[id] = user;
      this.subscribe(user, mediaType);
    },

    handleUserUnpublished(user,type) {
      console.log(type,'用户音视频订阅')
      // const id = user.uid;
      // delete remoteUsers[id];
      // this.leave();
    },

    start() {
      this.showTip = false;
      this.join();
    },
    // 加入会议
    async joinRoom() {
      try {
        // await this.pushLive();
        // 调用接视频接口
        console.log('看看状态', this.isPrepool)
        let data = {//isShoppingRegId是否为商城挂号业务信息(0.否 1.是)
          regId: this.regId,
          isShoppingRegId: this.isPrepool
        }
        let res = await videoPhone(data);

        this.time = res.data.surplusDuration;
        if (this.isIos) {
          uni.showModal({
            title: '提示',
            content: '当前通话已接通',
            showCancel: false,
            success() {
            },
          });
        }

        // 倒计时
        //this.setTime();
        if (this.isPrepool == 0) {
          this.setTime();
        }

        if (this.isIos) {
          uni.showModal({
            title: '提示',
            content: '当前通话已接通',
            showCancel: false,
            success() {
            },
          });
        }
      } catch (e) {
        console.log('加入会议失败', e);

        Toast('当前通话已失效');
        setTimeout(() => {
          this.hangUp();
        }, 1500);
      }
    },
    // 开始推流
    async pushLive() {
      try {
        const {data: agoraToken} = await getAgoraToken({
          channelName: this.channel,
          userId: this.userId,
        });

        if (this.isVideo == 1) {
          // 加入频道
          [
            this.uid,
            localTracks.audioTrack,
            localTracks.videoTrack,
          ] = await Promise.all([
            // join the channel
            client.join(this.appid, this.channel, agoraToken || null),
            // 使用麦克风和摄像头
            // AgoraRTC.createMicrophoneAudioTrack(),
            // AgoraRTC.createCameraVideoTrack(),
            AgoraRTC.createCameraVideoTrack({
              optimizationMode: "motion",
              facingMode: "user"
              // encoderConfig: {
              //   width: 2560,
              //   // 支持指定一个范围和参考值，具体配置参考相关 API 文档
              //   height: {ideal: 1440, min: 400, max: 2000},
              //   frameRate: 15,
              //   bitrate: 4780,
              //   bitrateMin: 3000, bitrateMax: 6000,
              // },
            }),
            AgoraRTC.createMicrophoneAudioTrack({
              AEC: true,
              ANS: true,
              AGC: true,
            }),

          ]);

          console.log(this.channel, this.uid, '=================')
        } else {
          [this.uid, localTracks.audioTrack] = await Promise.all([
            // join the channel
            client.join(this.appid, this.channel, agoraToken || null),
            // 使用麦克风
            AgoraRTC.createMicrophoneAudioTrack(),
          ]);
        }

        let arr = [];

        let list = Object.values(localTracks);

        list.forEach((v) => {
          if (v) {
            arr.push(v);
          }
        });

        // 将本地曲目发布到频道
        await client.publish(arr);
        // 播放本地视频曲目
        localTracks.videoTrack?.play('me');
        localTracks.audioTrack?.play();
      } catch (e) {
        console.log('发布推流失败', e);
        Toast(JSON.stringify(e));
        return Promise.reject(e);
      }
    },
    // 关闭
    async leave() {
      const {videoTrack, audioTrack} = localTracks;

      videoTrack && videoTrack.stop();
      videoTrack && videoTrack.close();

      audioTrack && audioTrack.stop();
      audioTrack && audioTrack.close();

      // 清除监听
      client.off('user-published', this.handleUserPublished);
      client.off('user-left', this.hangUp);
      client.off('user-unpublished', this.handleUserUnpublished);

      await client.leave();
      this.hangUp();
      client = null;
    },
    // 挂断
    hangUp() {
      console.log('用户离开');
      // 要挂断
      this.isHangUp = true;
      this.leave();
      clearTimeout(timer);
      timer = setTimeout(() => {
        this.sendServe();
        let length = getCurrentPages().length;
        // 如果有上一页 返回上一页
        if (length > 1) {
          uni.navigateBack();
        } else {
          // 如果不是微信环境
          if (!this.isWx) {
            this.showZhe = true;
            return;
          }
          uni.switchTab({
            url: '/pages/index/index',
          });
        }
      }, 500);
    },
    // 切换视频(自己跟医生谁大)
    onSwitch() {
      this.isMeMax = !this.isMeMax;
    },
    // 告诉后台挂断
    sendServe() {
      let obj = {
        confrId: this.channel,
        regId: this.regId,
      };
      AgoraUpVideo(obj);
    },
    // 切换摄像头
    async setCamera() {
    },
    // 倒计时
    setTime() {
      this.timer = setInterval(() => {
        this.time--;
        this.timeStr = getTime(this.time);
        if (this.time <= 0) {
          clearInterval(this.timer);
          this.hangUp();
        }
      }, 1000);
    },
    // 环信登录
    login() {
      return new Promise((resolve, reject) => {
      });
    },
    // 获取医生头像信息
    async getDocInfo(id) {
      let {
        data: {docName, docImg},
      } = await findDoctorByID({
        docId: this.docId,
        deptId: this.deptId,
      });
      this.docName = docName;
      this.docImg = docImg;
    },
  },
  // 监听页面卸载
  onUnload() {
    // 已调用挂断 则不执行
    if (this.isHangUp) return;
    this.hangUp();
  },
};
</script>

<style lang="scss" scoped>
.video_call {
  height: 100vh;
  overflow: hidden;
  position: relative;
  background: #000000;
}

.call {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 99;
  @include flex;
  flex-direction: column;
  font-size: 28rpx;
  color: #fff;

  image {
    width: 128rpx;
    height: 128rpx;
  }
}

.zhe {
  width: 100%;
  height: 100vh;
  background: #f5f5f5;
  padding: 200upx 30upx 30upx;
  text-align: center;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 99;
  box-sizing: border-box;

  .title {
    margin-top: 60upx;
    font-size: 32upx;
    font-weight: bold;
    @include flex;

    .tip_icon {
      width: 40upx;
      height: 40upx;
      margin-right: 10upx;
    }
  }

  .content {
    margin-top: 50upx;
    padding: 0 30upx;
    font-size: 28upx;
    color: #666;
    text {
      display: block;
      padding-bottom: 20upx;
    }
  }
}

// 医生信息
.doc_info {
  width: 100%;
  height: 70vh;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;

  .doc_head {
    width: 200upx;
    height: 200upx;
    border-radius: 8upx;
  }

  .doc_name {
    color: #fff;
    font-size: 72upx;
    margin-top: 20upx;
  }
}

// 医生视频
.docter_video {
  height: 100vh;
  width: 100vw;
  object-fit: contain;
  z-index: 0;
  //transform: rotateY(180deg);
  background-color: #000;
}

// 患者视频
.me_video {
  width: 172upx;
  height: 305upx;
  object-fit: cover;
  border-radius: 8upx;
  overflow: hidden;
  position: absolute;
  top: 30upx;
  right: 30upx;
  z-index: 2;
  background-color: #000;
  //transform: rotateY(180deg);
}

.footer {
  width: 100%;
  height: 350upx;
  padding: 44upx 56upx;
  @include flex(center);
  flex-wrap: wrap;
  box-sizing: border-box;
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 2;

  .footer_time {
    width: 100%;
    color: #fff;
    padding-bottom: 30upx;
    text-align: center;
    font-size: 36upx;
  }

  .menu_item {
    width: 160upx;

    .item_icon {
      width: 128upx;
      height: 128upx;
      display: block;
      margin: 0 auto;
    }

    button {
      color: #fff;
      font-size: 32upx;
    }
  }
}
</style>
