<template>
  <!-- 完善就诊人健康信息 -->
  <view>
    <view class="page-container">
      <view class="patient-info">
        <view class="input-box">
          <text>身高</text>
          <input
            type="number"
            maxlength="3"
            :value="patientInfo.height"
            v-model="patientInfo.height"
            placeholder="请输入就诊人身高"
          />
          <view class="unit"> CM </view>
        </view>
        <view class="input-box">
          <text>体重</text>
          <input
            type="number"
            maxlength="3"
            :value="patientInfo.weight"
            v-model="patientInfo.weight"
            placeholder="请输入就诊人体重"
          />
          <view class="unit"> KG </view>
        </view>

        <view>
          <view class="title_format">药物过敏史</view>
          <view class="content">
            <textarea
              placeholder="请输入就诊人药物过敏史"
              maxlength="200"
              :value="patientInfo.drugAllergyHistory"
              v-model="patientInfo.drugAllergyHistory"
              class="other-content"
            />
          </view>
          <view class="title_format">既往病史</view>
          <view class="content">
            <textarea
              placeholder="请输入就诊人既往病史"
              maxlength="200"
              value="patientInfo.previousHistory"
              v-model="patientInfo.previousHistory"
              class="other-content"
            />
          </view>
          <view class="title_format">遗传病史</view>
          <view class="content">
            <textarea
              placeholder="请输入就诊人遗传病史"
              maxlength="200"
              :value="patientInfo.geneticHistory"
              v-model="patientInfo.geneticHistory"
              class="other-content"
            />
          </view>
          <view class="title_format">家族病史</view>
          <view class="content">
            <textarea
              placeholder="请输入就诊人家族病史"
              maxlength="200"
              :value="patientInfo.familyMedicalHistory"
              v-model="patientInfo.familyMedicalHistory"
              class="other-content"
            />
          </view>
        </view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="footer_but">
      <!--      <button class="left" @click="jumpPatient">跳过</button>-->
      <button class="right" @click="editorPatient">保存</button>
    </view>
  </view>
</template>

<script>
import FooterButton from "@/components/footer_button/button.vue";
import { editorPatient } from "@/api/user.js";
export default {
  components: {
    FooterButton,
  },
  data() {
    return {
      patientInfo: {},
      patientId: "",
    };
  },
  onLoad(option) {
    this.patientInfo = JSON.parse(option.patientInfo);
    console.log("查看当前传过来的患者信息", this.patientInfo);
    this.patientId = option.patientId;
    console.log("查看当前传过来的患者ID", this.patientId);
  },
  methods: {
    // 跳过
    jumpPatient() {
      uni.navigateBack({
        delta: 1,
      });
    },
    // 保存
    async editorPatient() {
      let para = {
        ...this.patientInfo,
        isDefault: 0,
        patientId: this.patientId,
        userId: uni.getStorageSync("userId"),
      };
      delete para.patientImg;
      // 提交
      await editorPatient(para);

      uni.navigateBack({
        delta: 1,
      });
    },
  },
};
</script>

<style scoped lang="scss">
page {
  background-color: #f5f5f5;
}
.page-container {
  padding: 24rpx 32rpx 140rpx;
  box-sizing: border-box;
  overflow-y: auto;
}

.patient-info {
  border-radius: 8rpx;
  background: #ffffff;
  padding-left: 32upx;
  padding-bottom: 24rpx;
}

.input-box {
  width: 100%;
  display: flex;
  height: 92rpx;
  padding-right: 32upx;
  border-radius: 4rpx;
  box-sizing: border-box;
  line-height: 92rpx;
  border-bottom: 1rpx solid #ebebeb;
}

.input-box text {
  display: inline-block;
  width: 150rpx;
  flex: none;
  font-size: 28rpx;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
}

.input-box .icon {
  width: 40rpx;
}

.input-box .unit {
  font-size: 28rpx;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
}

.input-box input {
  flex: 1;
  height: 100%;
  line-height: 100%;
  padding-left: 40rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: rgba(102, 102, 102, 1);
}

.title_format {
  font-size: 32rpx;
  color: rgba(51, 51, 51, 1);
  line-height: 92rpx;
}

.content {
  padding-right: 32rpx;
}

.other-content {
  box-sizing: border-box;
  width: 100%;
  border-radius: 8rpx;
  border: 1px solid rgba(229, 229, 229, 1);
  font-size: 28rpx;
  line-height: 40rpx;
  padding: 24rpx 16rpx;
  height: 240rpx;
}

.footer_but {
  width: 100%;
  height: 108upx;
  @include flex(lr);
  position: fixed;
  bottom: 0;
  left: 0;
  background-color: #fff;
  box-shadow: 0px 0px 2upx 0px rgba(0, 0, 0, 0.12);
  border-radius: 16upx 16upx 0 0;
  box-sizing: border-box;

  button {
    width: 334upx;
    height: 84upx;
    border-radius: 42upx;
    @include flex;
    font-size: 32upx;
    box-sizing: border-box;

    &.left {
      @include font_theme;
      @include border_theme;
    }

    &.right {
      color: #fff;
      @include bg_theme;
    }
  }
}
</style>
