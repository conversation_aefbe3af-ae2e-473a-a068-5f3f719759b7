<template>
  <view>
    <view class="page-container">
      <template v-if="listData.length > 0">
        <scroll-view
          class="content-container"
          scroll-y="true"
          @scrolltolower="getMore"
        >
          <template v-for="item in listData">
            <view class="list">
              <view class="title">
                <text class="line"></text>
                <text>挂号编号：{{ item.remark.regCode }}</text>
              </view>
              <view class="content">
                <view @click="toDetail(item)">
                  <view class="items">
                    姓名：{{ item.remark.patientName }}
                  </view>
                  <view class="items"> 医生：{{ item.remark.docName }} </view>
                  <view class="items"> 科室：{{ item.remark.deptName }} </view>
                  <!-- 后台无法返回 -->
                  <!-- <view class="items">
										医院：{{item.hosName}}
									</view> -->
                  <view class="items"> 时间：{{ item.addTime }} </view>
                </view>
                <view class="btns">
                  <view class="btn" @click="toEvaluation(item)">评价</view>
                </view>
              </view>
            </view>
          </template>
          <!-- 加载更多 -->
          <view v-if="isShowMore">
            <uni-load-more :status="status"></uni-load-more>
          </view>
        </scroll-view>
      </template>
      <view class="empty_list" v-else>
        <image src="/static/images/question/empty_toast.png" />
        <view> 暂无订单 </view>
      </view>
    </view>
  </view>
</template>

<script>
import { toEvaluateOrderListPage } from '@/api/order.js';
export default {
  data() {
    return {
      listData: [],
      listQuery: {
        page: 1,
        limit: 10,
        patients: [],
      },
      total: '',
      isShowMore: false,
      status: 'loading',
    };
  },
  onLoad() {
    this.listQuery.patients = uni.getStorageSync('patientIdList');
    this.getListQuery();
  },
  onPullDownRefresh() {
    this.listData = [];
    this.listQuery.page = 1;
    this.getListQuery();
  },
  onShow() {
    let action = uni.getStorageSync('evaluaAction') || '';
    // 评价成功返回列表时刷新列表
    if (action == 'evaluaSuccess') {
      uni.removeStorageSync('evaluaAction'); // 移除标识
      this.listData = [];
      this.listQuery.page = 1;
      this.getListQuery();
    }
  },
  methods: {
    // 查询全部订单列表
    async getListQuery() {
      uni.stopPullDownRefresh();
      let { data } = await toEvaluateOrderListPage(this.listQuery);
      if (!data) return;
      let { rows, total } = data;
      rows.forEach((element) => {
        element.remark = JSON.parse(element.remark);
      });
      this.listData = [...this.listData, ...rows];
      this.total = total;
    },
    // 进入订单详情
    toDetail(item) {
      const { docId, hosId, regId, orderNo } = item;
      uni.setStorageSync('hosId', hosId);
      uni.navigateTo({
        url:
          '/pages/personalCenter/myOrder/evaluation/evaluationDetail?regId=' +
          regId +
          '&docId=' +
          docId +
          '&orderNo=' +
          orderNo,
      });
    },
    // 去评价
    toEvaluation(item) {
      const { docId, hosId, regId, orderNo } = item;
      uni.setStorageSync('hosId', hosId);
      uni.navigateTo({
        url:
          '/pages/personalCenter/myOrder/evaluation/evaluation?regId=' +
          regId +
          '&docId=' +
          docId +
          '&orderNo=' +
          orderNo,
      });
    },

    // 查看更多
    getMore() {
      this.isShowMore = true;
      // 共几页
      let num = Math.ceil(this.total / this.listQuery.limit);
      if (this.listQuery.page < num) {
        this.listQuery.page += 1;
        this.getListQuery();
        this.isShowMore = false;
      } else {
        this.status = 'noMore';
      }
    },
  },
};
</script>

<style scoped lang="scss">
.page-container {
  box-sizing: border-box;
}

.content-container {
  height: 100vh;
  padding: 0 32rpx;
  box-sizing: border-box;
}

.list .title {
  height: 88rpx;
  display: flex;
  align-items: center;
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.line {
  display: inline-block;
  width: 6rpx;
  height: 28rpx;
  @include bg_theme;
  border-radius: 2rpx;
  margin-right: 16rpx;
}

.content {
  width: 100%;
  background: #ffffff;
  border-radius: 8rpx;
  padding: 30rpx 24rpx;
  box-sizing: border-box;
}

.items {
  font-size: 28rpx;
  font-weight: 400;
  color: #6c6c6c;
  line-height: 28rpx;
}

.items:not(:first-child) {
  margin-top: 30rpx;
}

.btns {
  display: flex;
  flex-direction: row-reverse;
  align-items: center;
  margin-top: 30rpx;
}

.btns .btn {
  width: 158rpx;
  height: 56rpx;
  @include bg_theme;
  border-radius: 28rpx;
  font-size: 26rpx;
  font-weight: 500;
  color: #ffffff;
  @include flex;
}

/* 列表为空提示 */
.empty_list {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  font-weight: 400;
  color: rgba(158, 163, 173, 1);
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.empty_list image {
  margin-bottom: 40rpx;
  width: 386rpx;
  height: 324rpx;
}
</style>
