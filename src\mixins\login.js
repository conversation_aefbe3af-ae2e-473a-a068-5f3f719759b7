/**
 * 混入 判断没有userId 跳转登录页
 */
export default {
  methods: {
    // 跳转登录
    toLogin() {
      uni.navigateTo({
        url: '/pages/login/login',
      });
    },
    // 补全手机号
    toTel() {
      uni.navigateTo({
        url: '/pages/login/tel',
      });
    },
    // 逻辑判断
    hasInfo() {
      // 没有用户信息 去登录
      if (!uni.getStorageSync('userId')) {
        this.toLogin();
        return false;
      }
      // 没有手机号 去补全
      if (!uni.getStorageSync('tel')) {
        this.toTel();
        return false;
      }

      return true;
    },
  },
};
