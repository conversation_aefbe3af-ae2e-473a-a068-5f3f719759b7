<!--
 * @Descripttion: 
 * @version: 
 * @Author: zhengyangyang
 * @Date: 2025-05-06 14:04:40
 * @LastEditors: zhengyangyang
 * @LastEditTime: 2025-05-06 16:47:09
-->
<template>
  <view>
    <rich-text :nodes="info" @itemclick="handleRichTextClick" class="texttitle"></rich-text>
  </view>
</template>
<script>
import myJsTools from "../../../common/js/myJsTools";

export default {
  data() {
    return {
      carouselId: "",
      info: "",
      imageUrls: [],
      title:''
    }
  },
  onLoad(option) {
    this.info = JSON.parse(option.obj)
    this.extractImageUrls();
    this.title = option.title
    uni.setNavigationBarTitle({
      title: this.title||'轮播详情'
    })
  },
  methods: {
    extractImageUrls() {
      let data = this.info;
      var imgLst = data.match(/<img\s*src=\"([^\"]*?)\"[^>]*>/gi);
      this.imageUrls = [];
      
      if (imgLst) {
        for (let i = 0; i < imgLst.length; i++) {
          imgLst[i].replace(/<img [^>]*src=['"]([^'"]+)[^>]*>/gi, (match, capture) => {
            this.imageUrls.push(capture);
          });
        }
      }
    },
    handleRichTextClick(e) {
      // 判断是否点击的是图片
      if (e.detail.node && e.detail.node.name === 'img') {
        // 获取图片src
        const imgSrc = e.detail.node.attrs.src;
        // 预览图片
        uni.previewImage({
          urls: this.imageUrls,
          current: imgSrc,
          longPressActions: {
            itemList: ['保存图片'],
            success: function(data) {
              if(data.tapIndex === 0) {
                // 保存图片到相册
                uni.saveImageToPhotosAlbum({
                  filePath: imgSrc,
                  success: function () {
                    uni.showToast({
                      title: '保存成功',
                      duration: 2000
                    });
                  },
                  fail: function() {
                    uni.showToast({
                      title: '保存失败',
                      icon: 'none'
                    });
                  }
                });
              }
            }
          }
        });
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.home-box {
  text-align: center;
  padding: 10rpx;
}

.texttitle {
  padding: 40rpx;
}
::v-deep img{
  width: 100%;
  height: auto;
}
</style>
