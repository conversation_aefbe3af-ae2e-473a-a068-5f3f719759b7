<template>
  <!-- 全部订单 -->
  <view class="order_index">
    <!-- 订单列表 -->
    <view class="order_list">
      <block v-for="(item, index) in list">
        <!-- 挂号 -->
        <REGISTER
          v-if="item.orderType == 1"
          :item="item"
          :remark="item.remark"
          :key="index"
        />
        <!-- 服务 -->
        <SERVICE v-if="item.orderType == 4" :item="item" :key="index" />
        <!-- 检验单 -->
        <INSPECT
          isLis
          v-if="item.orderType == 5"
          :detail="item.pacsLisOrderListNewQueryVO"
          :item="item"
          :key="index"
        />
        <!-- 检查单 -->
        <INSPECT
          v-if="item.orderType == 6"
          :detail="item.pacsLisOrderListNewQueryVO"
          :item="item"
          :key="index"
        />
        <!-- 购药 -->
        <SHOP
          v-if="item.orderType == 3 && item.source == 2"
          @confirmGood="confirmGood"
          :item="item"
          :key="index"
        />

        <!-- 处方 -->
        <PRESCRIPTION
          v-if="item.orderType == 3 && item.source != 2"
          @confirmGood="confirmGood"
          :item="item"
          :isScan="true"
          :key="index"
        />

        <!-- 扫码购药处方 -->
        <PRESCRIPTION
          v-if="item.orderType == 8 && item.source == 3"
          @confirmGood="confirmGood"
          :item="item"
          :isScan="true"
          :key="index"
        />
      </block>

      <view class="more" v-if="total <= list.length && list.length > 0"
        >到底了</view
      >
    </view>

    <!-- 空白 -->
    <EMPTY v-if="!list.length" />
  </view>
</template>

<script>
import { Toast } from '@/common/js/pay.js'
import { orderListPageNew, confirmGoods } from '@/api/order.js'
// 挂号
import REGISTER from './components/register.vue'
import SERVICE from './components/service.vue'
import INSPECT from './components/inspect.vue'
import SHOP from './components/shop.vue'
import PRESCRIPTION from './components/prescription.vue'
import EMPTY from './components/empty.vue'
export default {
  name: 'Order',
  components: {
    REGISTER,
    SERVICE,
    SHOP,
    INSPECT,
    PRESCRIPTION,
    EMPTY,
  },
  data() {
    return {
      listQuery: {
        page: 1,
        limit: 10,
      },
      list: [],
      total: 0,
    }
  },
  onLoad() {
    this.getList()
  },
  onReachBottom() {
    if (this.list.length >= this.total) return
    this.listQuery.page++
    this.getList()
  },
  onPullDownRefresh() {
    this.listQuery.page = 1
    this.getList()
  },
  methods: {
    // 获取列表
    async getList() {
      try {
        let res = await orderListPageNew(this.listQuery)
        this.total = res.data.total
        const list = res.data.rows
        list.forEach((v) => {
          if (v.remark) v.remark = JSON.parse(v.remark)
        })
        if (this.listQuery.page > 1) {
          this.list = [...this.list, ...list]
        } else {
          this.list = list
        }
        this.listQuery.page = res.data.page
      } catch (e) {}
      uni.stopPullDownRefresh()
    },
    // 确认收货
    async confirmGood(item) {
      delete item.businessId
      await confirmGoods(item)
      Toast('收货成功')
      this.getList()
    },
  },
}
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}
.order_index {
  padding: 24rpx;
  min-height: 100vh;
  background-color: rgba(240, 242, 252, 1);

  .more {
    text-align: center;
    font-size: 24rpx;
    color: #999;
    line-height: 50rpx;
  }
}
.order-text {
  position: relative;
  img {
    position: absolute;
    left: 0px;
    top: 50%;
    transform: translateY(-50%);
  }
}
::v-deep .status {
  width: 54px;
  height: 22px;
  opacity: 1;
  border-radius: 10px;
  background: rgba(232, 250, 244, 1);
  font-size: 11px;
  font-weight: bold !important;
  color: rgba(44, 199, 147, 1);
  display: flex;
  align-items: center;
  justify-content: center;
}
::v-deep .left_info {
  font-size: 12px;
  color: rgba(153, 153, 153, 1);
  font-weight: 400;
}
</style>
