<template>
  <div class="sele">
    <div class="list" v-if="list.length">
      <!-- 就诊人 -->
      <PATIENT :list="list" @click="sele" />
    </div>

    <view class="empty_list" v-else>
      <image src="/static/images/index/office_empty.png" />
      <view> 暂无就诊人 </view>
    </view>

    <FOOTER @click="add">添加就诊人</FOOTER>
  </div>
</template>

<script>
import { createFastPrescription } from '@/api/base';

import { updateOrderPatient } from '@/api/order';

import { getPatientList } from '@/api/user';

// 就诊人组件
import PATIENT from '@/pages/personalCenter/components/patients.vue';
import FOOTER from '@/components/footer_button/button.vue';
export default {
  name: 'Sele',
  components: {
    PATIENT,
    FOOTER,
  },
  data() {
    return {
      list: [],
      orderNo: '',
      dpmpId: '',
    };
  },
  onLoad(v) {
    this.orderNo = v.orderNo;
    this.dpmpId = v.dpmpId;
    this.getList();
  },
  methods: {
    // 获取就诊人列表
    async getList() {
      let { data } = await getPatientList({
        userId: uni.getStorageSync('userId'),
      });
      if (!data) return;
      let arr = [];
      data.forEach((v) => {
        arr.push(v.patientId);
      });
      uni.setStorageSync('patientIdList', arr);
      this.list = data;
    },
    // 点击就诊人
    async sele(item) {
      let {
        age,
        ageUnit,
        birthDate,
        idNo,
        patientId,
        patientName,
        sex,
        sexCode,
        telNo,
      } = item;

      let obj = {
        age,
        ageUnit,
        birthDate,
        idNo,
        patientId,
        patientName,
        sex,
        sexCode,
        telNo,
        dpmpId: this.dpmpId,
        orderNo: this.orderNo,
      };
      try {
        uni.showLoading({
          mask: true,
        });

        // 直接调用接口 之后到成功页
        await createFastPrescription(obj);

        // 更改订单中就诊人id
        await updateOrderPatient({ orderNo: this.orderNo, patientId });

        uni.hideLoading();

        uni.redirectTo({
          url: '/pages/quick/result?orderNo=' + this.orderNo,
        });
      } catch (error) {
        uni.hideLoading();
        console.log(error);
      }
    },
    // 添加就诊人
    add() {
      // 添加就诊人
      uni.navigateTo({
        url: '/pages/personalCenter/patientManage/addPatient/index',
      });
    },
  },
};
</script>

<style lang="scss" scoped>
page {
  background-color: #f5f5f5;
}
.sele {
  padding-bottom: 120rpx;
  position: relative;
  min-height: 80vh;

  .list {
    padding: 24rpx 32rpx;
  }

  /* 列表为空提示 */
  .empty_list {
    @include flex;
    flex-direction: column;
    font-size: 26rpx;
    font-weight: 400;
    color: rgba(158, 163, 173, 1);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);

    image {
      margin-bottom: 40rpx;
      width: 386rpx;
      height: 324rpx;
    }
  }
}
</style>
