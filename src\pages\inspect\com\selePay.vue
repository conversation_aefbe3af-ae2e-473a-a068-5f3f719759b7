<template>
  <!-- 支付选择 -->
  <view class="sele_pay">
    <!-- 支付列表 -->
    <view
      class="pay_item"
      v-for="(item, index) in list"
      :key="index"
      @click="click(item.id)"
    >
      <!-- 支付方式 -->
      <text class="label">{{ item.name }}</text>
      <!-- 价位信息 -->
      <view class="pay_info">
        <!-- 价位 -->
        <text class="price" v-if="false">¥{{ cost }}</text>
        <!-- 单选图标 -->
        <image
          v-if="currAct == item.id"
          class="icon"
          src="/static/inspection/radio_act.png"
        />
        <!-- 选中 -->
        <image v-else class="icon" src="/static/inspection/radio.png" />
      </view>
    </view>
  </view>
</template>

<script>
export default {
  naem: 'SelePay',
  props: {
    list: {
      type: Array,
      default: () => [],
    },
    isSupport: {
      type: Boolean,
      default: false,
    },
    cost: {
      type: Number,
      default: 0,
    },
    currAct: {
      type: Number,
      default: 0,
    },
  },
  methods: {
    click(n) {
      this.$emit('change', n);
    },
  },
};
</script>

<style lang="scss" scoped>
.sele_pay {
  border-radius: 8rpx;
  background: #fff;
  margin-top: 16rpx;
  font-size: 28rpx;

  .pay_item {
    height: 88rpx;
    @include flex(lr);
    padding: 0 32rpx;
    border-bottom: 1px solid #eee;

    &:last-child {
      border-bottom: none;
    }

    .label {
      color: #333;
    }

    .pay_info {
      @include flex;

      .price {
        color: #ff0000;
        margin-right: 28rpx;
      }

      .icon {
        width: 32rpx;
        height: 32rpx;
      }
    }
  }
}
</style>
