<template>
  <!-- 完善就诊人信息 -->
  <view>
    <view class="page-container">
      <view class="patient-info" style="padding-bottom: 24rpx">
        <view class="input-box">
          <text>身高</text>
          <input
            type="number"
            maxlength="3"
            :value="patientInfo.height"
            v-model="patientInfo.height"
            placeholder="请输入就诊人身高"
          />
          <view class="unit"> CM </view>
        </view>
        <view class="input-box">
          <text>体重</text>
          <input
            type="number"
            maxlength="3"
            :value="patientInfo.weight"
            v-model="patientInfo.weight"
            placeholder="请输入就诊人体重"
          />
          <view class="unit"> KG </view>
        </view>
        <view class="title_format">药物过敏史</view>
        <view class="content">
          <textarea
            placeholder="请输入就诊人药物过敏史"
            maxlength="200"
            :value="patientInfo.drugAllergyHistory"
            v-model="patientInfo.drugAllergyHistory"
            class="other-content"
          />
        </view>
        <view class="title_format">既往病史</view>
        <view class="content">
          <textarea
            placeholder="请输入就诊人既往病史"
            maxlength="200"
            value="patientInfo.previousHistory"
            v-model="patientInfo.previousHistory"
            class="other-content"
          />
        </view>
        <view class="title_format">遗传病史</view>
        <view class="content">
          <textarea
            placeholder="请输入就诊人遗传病史"
            maxlength="200"
            :value="patientInfo.geneticHistory"
            v-model="patientInfo.geneticHistory"
            class="other-content"
          />
        </view>
        <view class="title_format">家族病史</view>
        <view class="content">
          <textarea
            placeholder="请输入就诊人家族病史"
            maxlength="200"
            :value="patientInfo.familyMedicalHistory"
            v-model="patientInfo.familyMedicalHistory"
            class="other-content"
          />
        </view>
      </view>
    </view>
    <FooterButton @click="editorPatient"> 保存 </FooterButton>
  </view>
</template>

<script>
import FooterButton from '@/components/footer_button/button.vue';
import { getVisitingPersonInfo } from '@/api/patient';
import { updatePatientBasicState } from '@/api/user';
export default {
  components: { FooterButton },
  data() {
    return {
      patientInfo: {},
      patientId: '',
    };
  },
  onLoad(option) {
    this.patientInfo = JSON.parse(option.param);
    this.patientId = this.patientInfo.patientId;
    this.getPatientDetail();
  },
  methods: {
    async getPatientDetail() {
      let patientId = this.patientId;
      let res = await getVisitingPersonInfo({ patientId: patientId });
      let patientInfo = res.data;
      this.patientInfo = patientInfo;
    },
    // 保存
    async editorPatient() {
      let {
        height,
        weight,
        drugAllergyHistory,
        previousHistory,
        geneticHistory,
        familyMedicalHistory,
      } = this.patientInfo;
      let para = {
        patientId: this.patientId,
        height,
        weight,
        drugAllergyHistory,
        previousHistory,
        geneticHistory,
        familyMedicalHistory,
      };

      await updatePatientBasicState(para);
      uni.navigateBack({
        delta: 1,
      });
    },
  },
};
</script>

<style scoped lang="scss">
.page-container {
  padding: 24rpx 32rpx 120rpx;
  box-sizing: border-box;
  overflow-y: auto;
}

.patient-info {
  border-radius: 8rpx;
  background: #ffffff;
  padding: 0 32rpx;
  border-radius: 8rpx;
}

.input-box {
  width: 100%;
  display: flex;
  height: 92rpx;
  border-radius: 4rpx;
  box-sizing: border-box;
  line-height: 92rpx;
  border-bottom: 1rpx solid #ebebeb;
}

.input-box text {
  display: inline-block;
  width: 112rpx;
  flex: none;
  font-size: 28rpx;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
}

.input-box .icon {
  width: 40rpx;
}

.input-box .unit {
  font-size: 28rpx;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
}

.input-box input {
  flex: 1;
  height: 100%;
  line-height: 100%;
  padding-left: 40rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: rgba(102, 102, 102, 1);
}

.title_format {
  font-size: 28rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: rgba(51, 51, 51, 1);
  line-height: 92rpx;
}

.other-content {
  width: 100%;
  box-sizing: border-box;
  border-radius: 8rpx;
  border: 1px solid rgba(229, 229, 229, 1);
  font-size: 28rpx;
  line-height: 40rpx;
  padding: 30rpx 16rpx;
  height: 240rpx;
}

.jump-patient {
  @include border_theme;
  background: #fff;
  @include font_theme;
}
</style>
