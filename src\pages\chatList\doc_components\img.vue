<template>
  <!-- 图片消息 -->
  <view class="chat_img">
    <!-- 头像 -->
    <image
      @click="head"
      :src="imgUrl || '/static/images/docHead.png'"
      mode="aspectFill"
      class="chat_user_img"
    />
    <view>
      <view class="chatName">{{chatName}}</view>
      <!-- 气泡 -->
      <view class="img_content" @click="click">
        <image class="content_img" :src="imgSrc" mode="widthFix" />
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    // 头像
    imgUrl: {
      type: String,
      default: '',
    },
    // 地址
    imgSrc: {
      type: String,
      default: '',
    },
    chatName:{}
  },
  methods: {
    head() {
      this.$emit('head');
    },
    // 预览图片
    click() {
      uni.previewImage({
        urls: [this.imgSrc],
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.chat_img {
  max-width: 600upx;
  box-sizing: border-box;
  @include flex(left);
  padding-bottom: 30upx;
  align-items: flex-start;

  // 头像
  .chat_user_img {
    width: 64upx;
    height: 64upx;
    border-radius: 4upx;
    margin-right: 16upx;
    flex: none;
  }

  // 内容
  .img_content {
    width: 280upx;

    // 图片
    .content_img {
      width: 100%;
      height: auto;
      border-radius: 16upx;
    }
  }
}
</style>
