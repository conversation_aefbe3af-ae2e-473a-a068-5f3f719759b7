<template>
  <div class="look">
    <div class="cont">
      <p class="title">配送详情</p>
      <p class="info">正在配送您的订单，请注意来电提醒</p>
      <p class="time">
        预计配送时间：<span>{{ time }}</span>
      </p>
    </div>
  </div>
</template>

<script>
import { queryClerkDelivery } from '@/api/shop';

export default {
  name: 'Logistic',
  data() {
    return {
      time: '',
    };
  },
  onLoad(v) {
    this.orderNo = v.orderNo;
    this.getList();
  },
  methods: {
    // 获取列表
    async getList() {
      let { data } = await queryClerkDelivery(this.orderNo);
      let { scheduledDeliveryDate, scheduledDeliveryApw } = data[0];
      this.time = scheduledDeliveryDate + ' ' + (scheduledDeliveryApw || '');
    },
  },
};
</script>

<style lang="scss" scoped>
page {
  background-color: #f5f5f5;
}

.look {
  padding: 24rpx 32rpx;

  .cont {
    padding: 24rpx;
    background-color: #fff;
    border-radius: 16rpx;

    .title {
      font-size: 32rpx;
      font-weight: bold;
    }

    .info {
      font-size: 28rpx;
      color: #333;
      line-height: 100rpx;
    }

    .time {
      font-size: 28rpx;

      span {
        @include font_theme;
      }
    }
  }
}
</style>
