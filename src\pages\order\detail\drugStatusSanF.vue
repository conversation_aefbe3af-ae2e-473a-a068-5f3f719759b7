<template>
  <!-- 药品状态详情 -->
  <view class="detail">
    <!-- 浮动图标 -->
    <GOHOME />

    <!-- 顶部状态 -->
    <STATUS
      :status="detail.payStatus"
      :deliveryType="detail.deliveryType"
      :time="time"
    />

    <!-- 收货地址 -->
    <view class="address" v-if="detail.deliveryType == 1">
      <view class="title">收货地址</view>

      <view class="address_detail">{{ address.deliveryAddressDetail }}</view>

      <view class="address_info">
        <text>{{ address.deliveryName }}</text
        >{{ address.deliveryTelNo }}
      </view>
    </view>

    <view class="pre" :class="{ mt: detail.deliveryType == 2 }">
      <!-- 处方 -->
      <PRESCRIPTIONSANF
        :list="list"
        :payStatus="detail.payStatus"
        :deliveryType="detail.deliveryType"
        @click="toPreDetail"
        :otc="detail.otcCount"
        :beforePayVisible="detail.beforePayVisible"
        @confirm="confirmGood"
        v-if="param.source != 2 && list.length"
        :showIcon="detail.prescriptionBusinessId || detail.businessId"
        :detail="detail"
        :item="detail"
        pageType="detail"
      />
      <!-- 购药 -->
      <SHOPSANF
        :list="list"
        :deliveryType="detail.deliveryType"
        :otc="detail.otcCount"
        :time="time"
        @click="toPreDetail"
        @lockLogist="lockLogist"
        @confirmGood="confirmGood"
        :payStatus="detail.payStatus"
        v-if="param.source == 2"
        :showIcon="detail.prescriptionBusinessId || detail.businessId"
        :detail="detail"
        :item="detail"
        pageType="detail"
      />
    </view>

    <view class="content">
      <!-- 支付明细 -->
      <view class="pay_list">
        <view class="pay_info">
          药品总价
          <text
            ><text style="margin-right: 3px">¥</text
            >{{ detail.drugMoney | toFixed }}</text
          >
        </view>
        <view class="pay_info" v-if="detail.djCost > 0">
          代煎费
          <text
            ><text style="margin-right: 3px">¥</text
            >{{ detail.djCost | toFixed }}</text
          >
        </view>
        <view class="pay_info" v-if="detail.dispensingFee > 0">
          处方调剂费用
          <text
            ><text style="margin-right: 3px">¥</text
            >{{ detail.dispensingFee | toFixed }}</text
          >
        </view>

        <view class="pay_info" v-if="detail.deliveryType == 1">
          快递费
          <text
            ><text style="margin-right: 3px">¥</text
            >{{ detail.logisticsCost | toFixed }}</text
          >
        </view>
        <view class="pay_info">
          合计金额
          <text
            ><text style="margin-right: 3px">¥</text
            >{{ detail.orderShouldMoney | toFixed }}</text
          >
        </view>
        <view class="pay_info red">
          优惠
          <text
            ><text style="margin-right: 3px">-¥</text
            >{{ detail.orderDisMoney | toFixed }}</text
          >
        </view>
        <view class="pay_info red" v-show="detail.paymentType !== 1">
          首付款
          <text>¥{{ detail.downPayment | toFixed }}</text>
        </view>
        <view class="pay_info red" v-show="detail.paymentType !== 1">
          尾款
          <text>¥{{ detail.balancePayment | toFixed }}</text>
        </view>
        <view class="pay_price">
          实付款
          <!-- <text>¥{{ detail.paymentType==1? detail.orderMoney : detail.downPayment}}</text> -->
          <text
            ><text style="margin-right: 5px">¥</text
            >{{ detail.orderMoney }}</text
          >
        </view>
      </view>

      <!-- 配送方式 -->
      <view
        class="logist"
        v-if="false"
        @click="show = !show"
        :class="{ act: show }"
      >
        <!-- 默认折叠 -->
        <view class="label">
          <text class="name">配送方式</text>
          <view class="type">
            <text class="text" v-if="detail.deliveryType == 1">统一配送</text>
            <text class="text" v-if="detail.deliveryType == 2">自提</text>
            <uni-icons type="arrowright" color="#333" size="20"></uni-icons>
          </view>
        </view>
        <!-- 内容 -->
        <view class="logist_cont">
          <!-- 快递 -->
          <view class="zt" v-if="detail.deliveryType == 1">
            <view class="address">{{ detail.deliveryAddressDetail }}</view>
            <view class="user">
              {{ detail.deliveryName }} {{ phone(detail.deliveryTelNo) }}
            </view>
          </view>

          <!-- 自提 -->
          <PHARMACY :detail="pharmacy" v-if="detail.deliveryType == 2">
            <!-- 存在处方 -->
            <block v-if="detail.otcCount">
              <!-- 审核通过 -->
              <text
                class="qym"
                v-if="detail.payStatus == 4"
                @click.stop="getCode"
                >取药码</text
              >
            </block>

            <!-- 不存在处方 -->
            <block v-if="!detail.otcCount">
              <text
                class="qym"
                v-if="detail.payStatus == 2 || detail.payStatus == 4"
                @click.stop="getCode"
                >取药码</text
              >
            </block>
          </PHARMACY>
        </view>
      </view>

      <!-- 订单信息 -->
      <view class="order_info">
        <view class="info_title">订单信息 </view>

        <view class="info_item">
          <text>订单编号</text> {{ detail.orderNo }}
        </view>

        <view class="info_item">
          <text>创建时间</text> {{ detail.addTime }}
        </view>

        <view class="info_item" v-if="detail.payTime">
          <text>支付时间</text> {{ detail.payTime }}
        </view>

        <view
          class="info_item"
          v-if="detail.receiveTime && detail.payStatus != 9"
        >
          <text>成交时间</text> {{ detail.receiveTime }}
        </view>

        <view class="info_item" v-if="detail.returnTime">
          <text>退费时间</text> {{ detail.returnTime }}
        </view>

        <view class="info_item" v-if="detail.payStatus == 9">
          <text>关闭时间</text> {{ detail.updateTime }}
        </view>
      </view>

      <!-- 非商城 -->
      <block v-if="param.source != 2">
        <!-- 支付前不可见 -->
        <FOOTER
          v-if="detail.payStatus == 1"
          @click="goPay"
          @leftClick="cancelPay"
          showLeft
        >
          <template #left>取消支付</template>
          去支付
        </FOOTER>
        <!--        <FOOTER-->
        <!--          v-if="detail.payStatus == 1"-->
        <!--          @click="goPay"-->
        <!--          @leftClick="payment"-->
        <!--          showLeft-->
        <!--        >-->
        <!--          <template #left>好友付</template>-->
        <!--          去支付-->
        <!--        </FOOTER>-->
        <FOOTER
          v-if="detail.payStatus == 3 && detail.deliveryType == 1"
          @click="applyRefund"
          >申请退费</FOOTER
        >
      </block>

      <!-- 商城 -->
      <block v-if="param.source == 2">
        <FOOTER
          v-if="detail.payStatus == 1"
          @click="goPay"
          @leftClick="cancelPay"
          showLeft
        >
          <template #left>取消支付</template>
          去支付
        </FOOTER>
        <!--        <FOOTER-->
        <!--          v-if="detail.payStatus == 1"-->
        <!--          @click="goPay"-->
        <!--          @leftClick="payment"-->
        <!--          showLeft-->
        <!--        >-->
        <!--          <template #left>好友付</template>-->
        <!--          去支付-->
        <!--        </FOOTER>-->
        <FOOTER
          v-if="detail.payStatus == 3 && detail.deliveryType == 1"
          @click="applyRefund"
          >申请退费</FOOTER
        >
      </block>

      <!-- 非商城处方 -->
      <block v-if="param.source != 2 && false">
        <!-- 支付前可见 -->
        <block v-if="detail.beforePayVisible == 1">
          <FOOTER
            v-if="detail.payStatus != 1 && detail.payStatus != 9"
            @click="toPreDetail"
            >查看处方详情</FOOTER
          >
        </block>

        <!-- 支付前不可见 -->
        <block v-else>
          <FOOTER
            v-if="detail.payStatus == 3 && detail.deliveryType == 1"
            @click="applyRefund"
            >申请退费</FOOTER
          >
          <FOOTER
            v-if="detail.payStatus == 4 || detail.payStatus == 10"
            @click="toPreDetail"
            >查看处方详情</FOOTER
          >
        </block>
      </block>

      <!-- 商城购药 -->
      <block v-if="param.source == 2 && false">
        <FOOTER
          v-if="
            detail.payStatus == 2 ||
            detail.payStatus == 4 ||
            detail.payStatus == 10
          "
          @click="toPreDetail"
          >查看处方详情</FOOTER
        >
      </block>
    </view>

    <!-- 取药码 -->
    <view class="qrcode-container" v-if="isShowEwm" @click="isShowEwm = false">
      <view class="qrcode-content">
        <view class="title"> 取药码 </view>
        <view class="qrcode">
          <view class="qrimg">
            <tkiQrcode
              ref="qrcode"
              :val="codeUrl"
              :size="300"
              background="#ffffff"
              foreground="#000000"
              pdground="#000000"
              icon="/static/images/logo-img.png"
              iconSize="45"
              :onval="true"
              :loadMake="true"
            ></tkiQrcode>
          </view>
          <view class="qrcode_text" v-if="false">
            <text class="qrcode-toast">{{ code_text }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 引导 -->
    <Tips v-show="showTip" @click="showTip = false" />
  </view>
</template>

<script>
import STATUS from "./components/status.vue";
import PHARMACY from "@/pages/shop/components/pharmacy.vue";
import FOOTER from "@/components/footer_button/button.vue";
import SHOPSANF from "../components/shopSanF.vue";
import PRESCRIPTIONSANF from "../components/prescriptionSanF.vue";
import GOHOME from "@/components/home/<USER>";
import { findDrugStoreDetail } from "@/api/base";
import {
  orderDetailNew,
  findPayOrderInfo,
  confirmGoods,
  getPresPayStatue,
  queryGetMedicine,
  orderDetailByMall,
} from "@/api/order.js";

import {
  queryMallOrderStatus,
  refundAndAddYf,
  cancelOrderzf,
} from "@/api/shop.js";

import myJsTools from "@/common/js/myJsTools.js";

import { wxPay } from "@/common/js/pay";

import tkiQrcode from "@/components/tki-qrcode/tki-qrcode.vue";

import { Toast } from "@/common/js/pay.js";

import payment from "@/mixins/wx";
import Pay from "@/modules/pay";
import { getSysPlatformConfigByKeyList } from "@/api/base";
import { getHelpPayCreateResult } from "@/api/order";
import { getYxOrderStatus } from "../../../api/base";
let timer = null;
let inter = null;
let PAY;
export default {
  name: "Detail",
  mixins: [payment],
  components: {
    STATUS,
    PHARMACY,
    FOOTER,
    PRESCRIPTIONSANF,
    SHOPSANF,
    GOHOME,
    tkiQrcode,
  },
  data() {
    return {
      // 展开地址
      show: false,
      // 参数
      param: {
        // 订单号
        orderNo: "",
        // 来源
        source: "",
      },
      detail: "",
      // 处方列表
      list: [],
      // 药店信息
      pharmacy: {},
      // 计数
      num: 3,
      // 计时器
      time: 0,
      // 挂号id
      ghId: "",
      // 展示取药码
      isShowEwm: false,
      // 取药码
      codeUrl: "",
      // 文案
      code_text: "",
      // 收货地址
      address: {},
    };
  },
  onLoad(opt) {
    let { orderNo, source } = opt;
    this.param = {
      orderNo,
      source,
    };
    if (opt?.payStatus) {
      let { payStatus, deliveryType, orderNo, source } = opt;
      this.param = {
        payStatus,
        deliveryType,
        orderNo,
        source,
      };
    }
  },
  onShow() {
    this.getDetail();
  },
  onUnload() {
    if (timer) clearInterval(timer);
    if (inter) clearInterval(inter);
  },
  methods: {
    // 业务逻辑的取消支付
    cancelPay() {
      const { orderNo, source } = this.detail;
      cancelOrderzf({ orderNo: orderNo }).then((ret) => {
        Toast("取消成功！");
        this.getDetail();
      });
    },
    // 好友付
    async payment() {
      const { orderMoney } = this.detail;
      let ghId = this.detail.ghId;
      const { orderNo, source } = this.param;
      if (source != 1) {
        ghId = "";
      }
      await this.setShare(orderMoney, orderNo, source, ghId);
      this.showTip = true;
    },
    // 获取取药码
    async getCode() {
      let { businessId } = this.detail;
      let res = await queryGetMedicine(businessId);
      // 如果不为空
      if (res.data.drugCode) {
        this.codeUrl = res.data.drugCode;
        this.isShowEwm = true;
        return;
      }
      Toast("未能获取取药码");
    },
    // 去处方详情
    toPreDetail() {
      const { prescriptionBusinessId, businessId, hosId, payStatus } =
        this.detail;
      if (!prescriptionBusinessId || !businessId) {
        Toast("未能查询到处方详情");
        return;
      }
      uni.setStorageSync("hosId", hosId);
      let url;
      if (payStatus == 1) {
        url =
          "/pages/prescription/prescriptionDetail?businessId=" +
          prescriptionBusinessId +
          "&type=2";
      } else {
        url =
          "/pages/prescription/preDetail?businessId=" + prescriptionBusinessId;
      }
      uni.navigateTo({
        url,
      });
    },
    // 查看物流
    lockLogist(code, name) {
      console.log("this.detail", this.detail);
      const { deliveryTelNo, businessId, orderNo } = this.detail;
      let tel = deliveryTelNo.slice(-4);
      uni.navigateTo({
        url:
          "/pages/personalCenter/myOrder/allOrder/prescriptionOrder/logistics?code=" +
          code +
          "&tel=" +
          tel +
          "&name=" +
          name +
          "&businessId=" +
          businessId +
          "&orderNo=" +
          orderNo,
      });
    },
    // 查询详情
    async getDetail() {
      let res = await orderDetailByMall(this.param);

      console.log(res);
      this.detail = res.data;
      //  需要接第三方逻辑
      const { logisticsListMallVOS: list, source } = res.data;
      if (res.data.payStatus == 1) {
        this.time = res.data.forRestOf;
        // 正常处方 只会存在一个药店
        if (source == 1 || source == 3) {
          const { drugstoreId } = list[0];
          PAY = new Pay(drugstoreId);
        } else {
          PAY = new Pay();
          this.getPriceList();
        }
        this.setTimes();
      }
      if (res.data.deliveryType == 1) {
        let { deliveryName, deliveryTelNo, deliveryAddressDetail } = res.data;
        this.address = {
          deliveryName,
          deliveryTelNo,
          deliveryAddressDetail,
        };
      }
      this.list = res.data.logisticsListMallVOS;
    },
    // 待支付倒计时
    setTimes() {
      if (this.time <= 0) {
        this.time = 0;
        return;
      }
      inter = setInterval(() => {
        this.time--;
        if (this.time <= 0) {
          this.getDetail();
          clearInterval(inter);
        }
      }, 1000);
    },
    // 获取药店详情
    async getDurgDetail(drugstoreId) {
      let res = await findDrugStoreDetail({
        drugstoreId,
      });
      this.pharmacy = res.data;
    },
    // 手机号显示处理
    phone(str) {
      return myJsTools.phone(str);
    },
    // 获取平台支付方式 商城用
    async getPriceList() {
      let res = await getSysPlatformConfigByKeyList(["onlineMallPayId"]);
      let callId = res.data[0].configValue;
      // 收款方
      let list = callId.split(",") || [];
      let arr = list.map((v) => {
        let obj = {
          appid: v,
        };
        if (v.indexOf("wx") > -1) {
          obj.receiptType = 1;
        } else {
          obj.receiptType = 2;
        }
        return obj;
      });
      PAY.payList = arr;
    },
    // 点击支付
    async goPay() {
      const { index, item } = await PAY.selePay(this.detail.orderRealMoney);
      console.log(index, item);
      let res = await getHelpPayCreateResult({
        callId: item.appid,
        payType: index,
        orderNo: this.param.orderNo,
        openid: uni.getStorageSync("wxInfo").openId,
      });
      let info = res.data;
      // 用于处方
      // if(res.data.orderStatus == 2){
      //   Toast("该订单已支付成功，请勿重新支付");
      //   this.getDetail()
      //   return;
      // }if(res.data.orderStatus == 3){
      //   Toast("该订单已退款，请重新进入订单页面");
      //   this.getDetail()
      //   return;
      // }else if(res.data.orderStatus == 1 || res.data.orderStatus == null){
      if (index == 1) {
        this.wxPay(info);
      } else if (index == 2) {
        uni.navigateTo({
          url:
            "/pages/pay/pay?price=" +
            res.data.orderMoney +
            "&orderNo=" +
            info.orderNo +
            "&url=" +
            btoa(info.url),
        });
      }
      // }
    },
    async wxPay(info) {
      // 调用微信支付
      let source = this.param.source;
      try {
        await wxPay(info);
        if (source == 1) {
          this.preStatus();
          timer = setInterval(() => {
            this.preStatus();
          }, 2000);
          return;
        }
        this.getPayStatus();
        timer = setInterval(() => {
          this.getPayStatus();
        }, 2000);
      } catch (error) {
        Toast("取消支付");
      }
    },
    // 商城支付状态
    async getPayStatus() {
      const { orderNo } = this.param;
      // 根据订单号查询
      let res = await queryMallOrderStatus(orderNo);
      this.getStatus(res);
    },
    // 处方查询状态
    async preStatus() {
      let res = await getPresPayStatue({
        ghId: this.detail.ghId,
      });
      this.getStatus(res);
    },
    // 轮询查状态
    getStatus(res) {
      this.num--;

      if (res.data.orderStatus == 2 || res.data.regStatus == 2) {
        clearInterval(timer);
        timer = null;
        this.num = 3;

        // 支付成功 重新获取状态
        this.getDetail();
      }

      // 查询过三次
      if (this.num <= 0) {
        clearInterval(timer);
        // 应该跳转到失败
        if (res.data.orderStatus == 1 || res.data.regStatus == 1) {
          Toast("支付状态异常");
        }
      }
    },
    // 确认收货
    async confirmGood(merchantsOrderNo) {
      let { businessId, orderNo } = this.detail;
      let item = {
        merchantsOrderNo,
        businessId,
        orderNo,
      };
      let res = await confirmGoods(item);
      uni.showToast({
        title: "收货成功",
        icon: "none",
      });
      this.getDetail();
    },
    // 申请退费
    async applyRefund() {
      console.log("111111", this.detail);
      if (!this.detail.orderNo) {
        return;
      }
      const res = await getYxOrderStatus({ orderNo: this.detail.orderNo });
      if (res.data && res.data.shipping_status * 1 > 2) {
        uni.showToast({
          title: "订单已发货，不可退费",
          icon: "none",
        });
        return;
      }
      // uni.navigateTo({
      //   url: `/pages/prescription/refundOfFees?orderNo=${this.detail.orderNo}&totalMoney=${this.detail.totalMoney}`,
      // });
      uni.navigateTo({
        url: `/pages/prescription/refundOfFees?orderNo=${this.detail.orderNo}&totalMoney=${this.detail.orderMoney}`,
      });
      // refundAndAddYf({ orderNo: this.item.orderNo }).then((res) => {
      //   uni.showToast({
      //     title: "申请退款成功！",
      //     icon: "none",
      //   });
      // });
    },
  },
};
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}

.detail {
  min-height: 100vh;
  background-color: #f0f2fc;
  padding-bottom: 120rpx;

  .address {
    margin-top: -32rpx;
    background-color: #fff;
    border-radius: 32rpx;
    padding: 0 32rpx;
    width: 90%;
    margin: auto;
    margin-top: -32rpx;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0px 1px 4px rgba(121, 72, 234, 0.15);
    .title {
      height: 88rpx;
      @include flex(left);
      font-size: 28rpx;
      font-weight: bold;
      border-bottom: 1px solid #f5f5f5;
    }

    .address_detail {
      font-size: 28rpx;
      padding: 24rpx 0;
      color: #333;
      line-height: 40rpx;
    }

    .address_info {
      font-size: 28rpx;
      color: #666;
      padding-bottom: 24rpx;

      text {
        margin-right: 12rpx;
      }
    }
  }

  .pre {
    width: 90%;
    margin: auto;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0px 1px 4px rgba(121, 72, 234, 0.15);
    margin-top: 24rpx;
    border-radius: 32rpx;
  }

  .mt {
    margin-top: -32rpx;
  }

  .pay_list {
    padding: 0rpx 32rpx;
    background-color: #fff;

    .pay_info {
      @include flex(lr);
      color: #999;
      font-size: 28rpx;
      height: 68rpx;

      &.red {
        text {
          color: red;
        }
      }

      text {
        color: #333;
      }
    }

    .pay_price {
      @include flex(lr);
      font-size: 28rpx;
      color: #333;
      height: 68rpx;

      text {
        color: #ff3b30;
        font-weight: bold;
      }
    }
  }

  .logist {
    background-color: #fff;
    padding: 0 32rpx;
    margin-top: 24rpx;

    &.act {
      .label {
        .type {
          .uni-icons {
            transform: rotate(90deg);
          }
        }
      }

      .logist_cont {
        border-top: 1px solid #ebebeb;
        max-height: 500rpx;
      }
    }

    .label {
      height: 88rpx;
      @include flex(lr);
      font-size: 28rpx;

      .name {
        font-weight: bold;
      }

      .type {
        @include flex;

        .text {
          margin-right: 20rpx;
        }

        .uni-icons {
          transition: all 0.3s;
        }
      }
    }

    .logist_cont {
      max-height: 0;
      transition: all 0.3s;
      overflow: hidden;
      border-top: 1px solid transparent;

      .zt {
        padding: 24rpx 0;
        font-size: 28rpx;
        line-height: 40rpx;

        .address {
          color: #333;
        }

        .user {
          color: #666;
          margin-top: 10rpx;

          text {
            margin-left: 20rpx;
          }
        }
      }
    }
  }

  .pharmacy_detail {
    padding: 24rpx 0;

    .qym {
      width: 160rpx;
      height: 60rpx;
      border-radius: 30rpx;
      @include flex;
      font-size: 28rpx;
      color: #fff;
      @include bg_theme;
    }
  }

  .order_info {
    margin-top: 24rpx;
    background-color: #fff;
    padding: 10rpx 32rpx;
    overflow: hidden;
    border-radius: 20rpx;
    .info_title {
      font-size: 28rpx;
      font-weight: bold;
      line-height: 68rpx;
    }

    .info_item {
      font-size: 28rpx;
      line-height: 68rpx;

      text {
        display: inline-block;
        width: 140rpx;
      }
    }
  }
}

/* 取药号二维码  */
.qrcode-container {
  position: fixed;
  width: 100%;
  height: 100vh;
  left: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;

  .qrcode-content {
    width: 544rpx;
    height: 588rpx;
    background: #ffffff;
    border-radius: 16rpx;
    overflow: hidden;
    text-align: center;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);

    .title {
      width: 100%;
      height: 116rpx;
      @include bg_theme;
      line-height: 116rpx;
      font-size: 36rpx;
      font-weight: 500;
      color: #ffffff;
      display: flex;
      justify-content: center;
    }
  }
}
.content {
  width: 90%;
  margin: auto;
  box-shadow: 0px 1px 4px rgba(121, 72, 234, 0.15);
  overflow: auto;
}
.qrcode {
  margin-top: 70rpx;
}

// 底部
.footer {
  width: 100%;
  height: 108rpx;
  background: #ffffff;
  box-shadow: 0 0 2rpx rgba(0, 0, 0, 0.2);
  border-radius: 16rpx 16rpx 0px 0px;
  position: fixed;
  left: 0;
  bottom: 0;
  z-index: 2;
  @include flex(lr);
  padding: 0 32rpx;

  text {
    width: 334rpx;
    height: 84rpx;
    border-radius: 42rpx;
    @include border_theme;
    font-size: 32rpx;
    @include flex;

    &.a {
      @include font_theme;
    }

    &.b {
      @include bg_theme;
      color: #fff;
    }
  }
}
</style>
