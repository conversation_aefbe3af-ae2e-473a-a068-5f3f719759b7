<template>
  <view class="menu">
    <view
      class="menu_item"
      v-for="(item, index) in list"
      @click="click(item)"
      :key="index"
      v-show="!item.display"
    >
      <view class="left">
        <image :src="item.icon" class="item_icon"></image>
        <text>{{ item.title }}</text>
      </view>
      <uni-icons type="arrowright" color="#999" size="20"></uni-icons>
    </view>
  </view>
</template>

<script>
export default {
  naem: "Menu",
  props: {
    list: {
      type: Array,
      default: () => [],
    },
  },
  methods: {
    click(item) {
      this.$emit("click", item);
    },
  },
};
</script>

<style lang="scss" scoped>
.menu {
  background: #fff;
  border-radius: 16rpx;
  padding: 0 32rpx;

  .menu_item {
    @include flex(lr);
    height: 88rpx;
    font-size: 30rpx;
    color: #333;
    border-bottom: 1px solid #ebebeb;

    &:last-child {
      border: none;
    }

    .left {
      @include flex;

      .item_icon {
        width: 44rpx;
        height: 44rpx;
        margin-right: 16rpx;
      }

      text {
        font-weight: normal;
      }
    }
  }
}
</style>
