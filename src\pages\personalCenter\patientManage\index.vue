<template>
  <!-- 我的就诊人 -->
  <view class="page">
    <view class="page-container">
      <scroll-view scroll-y v-if="patientList.length != 0">
        <!-- 就诊人 -->
        <PATIENT :list="patientList" @click="toPatientDetail" />
      </scroll-view>

      <view class="empty_list" v-else>
        <image src="/static/images/index/office_empty.png" />
        <view> 暂无就诊人 </view>
      </view>

      <view class="add_patient" @click="addPatient">
        <img src="../../../static/路径 <EMAIL>" alt="" />
        <span>添加就诊人</span>
      </view>
    </view>

    <!-- 底部按钮 -->
    <!--    <FooterButton @click="addPatient">添加就诊人</FooterButton>-->
  </view>
</template>

<script>
import { Toast } from "@/common/js/pay.js";
// 就诊人组件
import PATIENT from "../components/patients.vue";
import FooterButton from "@/components/footer_button/button.vue";
import { getPatientReceiveStatus, getPatientList } from "@/api/user.js";
import { judgmentBeforeRegistration } from "@/api/appoint.js";

function showModel(str) {
  return new Promise((suc, rej) => {
    uni.showModal({
      title: "提示",
      content: str,
      showCancel: false,
      success() {
        rej();
      },
    });
  });
}

export default {
  components: {
    PATIENT,
    FooterButton,
  },
  data() {
    return {
      patientList: [],
      action: "", // 标识从哪个页面进入
    };
  },
  onLoad(options) {
    if (options.action) {
      this.action = options.action;
    }
  },
  onShow() {
    this.getPatientList();
  },
  onPullDownRefresh() {
    this.getPatientList();
  },
  methods: {
    // 获取就诊人列表
    async getPatientList() {
      let userId = uni.getStorageSync("userId");
      let { data } = await getPatientList({
        userId,
      });
      uni.stopPullDownRefresh();

      if (!data) {
        this.patientList = [];
        return;
      }
      let disease = [];
      data.forEach((element) => {
        disease.push(element.patientId);
      });
      uni.setStorageSync("patientIdList", disease);
      this.patientList = data;
    },
    addPatient() {
      // 添加就诊人
      uni.navigateTo({
        url: "/pages/personalCenter/patientManage/addPatient/index",
      });
    },
    async toPatientDetail(patientInfo) {
      let patientId = patientInfo.patientId;
      // / 存储患者信息，患者id
      uni.setStorageSync("patientId", patientId);
      uni.setStorageSync("patientInfo", patientInfo);
      let action = this.action;
      if (action == "shop") {
        uni.setStorageSync("shop_patient", patientInfo);
        uni.navigateBack();
        return;
      }
      if (action == "selectPatient") {
        let infoDetail = uni.getStorageSync("infoDetail");

        // 0 咨询 1 复诊
        const type = infoDetail.type;

        let res = await getPatientReceiveStatus({
          docId: infoDetail.docId,
          patientId: patientId,
          // 问诊类型
          visitTypeCode: infoDetail.visitTypeCode,
        });
        if (res.data) {
          if (res.data.isAllowInquiry != "1") {
            Toast("您已经挂过医生的号了，请勿重复挂号");
          } else if (res.data && res.data.isPayOrder != "1") {
            Toast("存在待支付的订单，请勿重复预约");
          } else if (type == 1) {
            await this.isNext({
              docId: infoDetail.docId,
              patientId,
            });
            uni.navigateTo({
              url: "/pages/register/thatDayRegister/rulesPages/index",
            });
          } else {
            uni.navigateTo({
              url: "/pages/register/thatDayRegister/rulesPages/index",
            });
          }
        }
        // 复诊 或预约
      } else if (action == "appointSelectPatient") {
        let info = uni.getStorageSync("appointReserveInfo");
        let doc = uni.getStorageSync("appointInfoDetail");
        let obj = {
          docId: doc.docId,
          patientId,
          // 预约类型
          visitTypeCode: info.visitTypeCode,
          // 日期
          appointDate: info.visitDate,
          // 开始时间
          appointStartTime: info.startTime,
          // 结束时间
          appointEndTime: info.endTime,
        };

        // 0 咨询 1 复诊
        const consultationCode = info.consultationCode;

        let res = await getPatientReceiveStatus(obj);
        // 不可预约
        if (res.data && res.data.isAllowInquiry != "1") {
          Toast("您已预约过医生的号了，请勿重复预约");
          // 复诊
        } else if (res.data && res.data.isPayOrder != "1") {
          Toast("存在待支付的订单，请勿重复预约");
        } else if (consultationCode == 1) {
          await this.isNext({
            docId: info.docId,
            patientId,
          });
          uni.navigateTo({
            url: "/pages/register/appointRegister/rulesPage/index",
          });
        } else {
          uni.navigateTo({
            url: "/pages/register/appointRegister/rulesPage/index",
          });
        }

        // 线下预约
      } else if (action == "offline") {
        // 获取医生id
        let { docId, dntId } = uni.getStorageSync("offlineAppoint");

        uni.navigateTo({
          url:
            "/pages/chatCardDetail/offineVisit?docId=" +
            docId +
            "&patientId=" +
            patientId +
            "&dntId=" +
            dntId,
        });
      } else if (action == "uncleic") {
        uni.redirectTo({
          url: `/pages/inspect/list/nucleicOrderList?patientInfo=${JSON.stringify(
            patientInfo
          )}`,
        });
      } else {
        uni.navigateTo({
          url: "/pages/personalCenter/patientManage/editorPatient/index",
        });
      }
    },
    // 是否可复诊
    async isNext(obj) {
      try {
        let res = await judgmentBeforeRegistration(obj);
        let { canRegister, errorMsg } = res.data;
        if (canRegister == 1) {
          return Promise.resolve("next");
        } else {
          await showModel(errorMsg);
          return Promise.reject("noNext");
        }
      } catch (e) {
        return Promise.reject("noNext");
      }
    },
  },
};
</script>

<style scoped lang="scss">
.page {
  background-color: #f0f2fc !important;
}

.add_patient {
  display: flex;
  align-items: center;
  border: 1px solid #836aff;
  height: 72rpx;
  justify-content: center;
  background-color: #ffffff;
  border-radius: 100rpx;
  margin-top: 48rpx;
  img {
    width: 28rpx;
    height: 28rpx;
    margin-right: 12rpx;
  }
  span {
    font-size: 28rpx;
    color: #836aff;
  }
}
/* 就诊人列表模块 */
.page-container {
  box-sizing: border-box;
  padding: 0 34rpx;
  overflow-y: auto;
}

/* 列表为空提示 */
.empty_list {
  @include flex;
  flex-direction: column;
  font-size: 26rpx;
  font-weight: 400;
  color: rgba(158, 163, 173, 1);
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.empty_list image {
  margin-bottom: 40rpx;
  width: 386rpx;
  height: 324rpx;
}
</style>
