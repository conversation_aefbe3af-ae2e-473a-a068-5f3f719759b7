<template>
  <div class="detail">
    <!-- 状态 -->
    <STATUS
      :payType="detail.payType"
      :deliverytype="detail.deliveryType"
      :auditStatus="detail.auditStatus"
      :orderStatus="detail.orderStatus"
      :refundAuditStatus="detail.refundAuditStatus"
      :time="timeStr"
    />

    <!-- 容器 -->
    <div class="mt_warp">
      <!-- 订单相关 -->
      <REASONS v-if="detail.auditStatus > 3" :list="reasons" />

      <!-- 快递信息 -->
      <LOGISTICS
        v-if="detail.deliveryType != 2"
        :name="detail.deliveryName"
        :address="detail.deliveryAddressdeTail"
        :telNo="detail.deliveryTelNo"
      />

      <!-- 药店信息 -->
      <div class="drug_store" v-if="detail.deliveryType == 2">
        <p class="title">药店信息</p>
        <DRUGSTORE :detail="drugStore" />
      </div>

      <ROW
        :label="detail.deliveryType == 2 ? '自提时间' : '期望配送时间'"
        :value="detail.getgoodsTime"
      />

      <!-- 药品信息 -->
      <div class="drug">
        <p class="title">药品信息</p>
        <DRUGLIST
          :list="drugList"
          :showTelNo="detail.deliveryType == 1 ? true : false"
          :telNo="drugStore.telNo"
          :name="detail.subjectName"
        />
      </div>

      <ROW
        v-if="invoice.invoiceType"
        label="发票信息"
        :value="
          invoice.invoiceType == 1
            ? `个人（${invoice.invoiceTitle}）`
            : invoice.invoiceTitle
        "
        showIcon
        @click="showTip"
      />

      <!-- 价格相关 -->
      <div class="price">
        <p>
          <span>药品总价</span>
          <span class="n">￥{{ detail.orderMoney | toFixed }}</span>
        </p>
        <p v-if="detail.deliveryType != 2">
          <span>快递费</span>
          <span class="n">￥{{ detail.logisticsCost | toFixed }}</span>
        </p>
        <p>
          <span>处方调剂费</span>
          <span class="n">￥{{ detail.dispensingfee | toFixed }}</span>
        </p>
        <p>
          <span>商品优惠</span>
          <span class="n red"
            >-￥{{ detail.merchantsOrderMoneyDis || 0 | toFixed }}</span
          >
        </p>
        <!-- 实际 -->
        <p class="last">
          实付款：<span>￥{{ detail.totalMoneyReal | toFixed }}</span>
        </p>
      </div>

      <!-- 订单信息 -->
      <div class="order">
        <p class="title">订单信息</p>
        <p>
          订单编号<span>{{ orderNo }}</span>
        </p>
        <p>
          创建时间<span>{{ detail.addTime }}</span>
        </p>
        <p v-if="detail.payTime">
          支付时间<span>{{ detail.payTime }}</span>
        </p>
        <p v-if="detail.auditTime">
          审核时间<span>{{ detail.auditTime }}</span>
        </p>
        <p v-if="detail.deliveryTime">
          发货时间<span>{{ detail.deliveryTime }}</span>
        </p>
        <p v-if="detail.receiveTime">
          收货时间<span>{{ detail.receiveTime }}</span>
        </p>
        <p v-if="detail.refundApplicationTime">
          退费时间<span>{{ detail.refundApplicationTime }}</span>
        </p>
        <p v-if="detail.transactionClosingTime">
          关闭时间<span>{{ detail.transactionClosingTime }}</span>
        </p>
      </div>
    </div>

    <!-- 弹框 -->
    <uni-popup ref="popup" type="bottom">
      <div class="pop">
        <div class="pop_title">
          <text>发票信息</text>
          <uni-icons type="closeempty" @click="hideTip"></uni-icons>
        </div>
        <div class="pop_text">
          <p>抬头类型: {{ invoice.invoiceType == 1 ? '个人' : '单位' }}</p>
          <p v-if="invoice.invoiceType == 2">
            公司税号: {{ invoice.invoiceTaxNo }}
          </p>
          <p>备注: {{ invoice.invoiceMemo || '暂无备注' }}</p>
        </div>
      </div>
    </uni-popup>

    <!-- 底部 -->
    <div class="footer">
      <span class="but" v-if="showInvoice(detail)" @click="updateOrderInvoice"
        >补开发票</span
      >
      <span class="but" v-if="showEdit(detail)" @click="toEdit">修改订单</span>

      <span
        class="but act"
        v-if="showLogistics(detail) && detail.deliverytype == 1"
        @click="lockLogistics"
      >
        查看物流
      </span>

      <span
        class="but act"
        v-if="detail.deliverytype == 3 && showLogistics(detail)"
        @click="lock"
      >
        配送查看
      </span>

      <span
        class="but act"
        v-if="
          detail.deliverytype == 1 &&
            detail.logisticsStatus == 1 &&
            detail.orderStatus < 6
        "
        @click="confirmGoods"
      >
        确认收货
      </span>

      <span class="but act" v-if="showRefund(detail)" @click="toRefund">
        <block v-if="refundAuditList.length">取消详情</block>
        <block v-else>取消订单</block>
      </span>

      <span class="but act" v-if="showRepeat(detail)" @click="toRepeat"
        >再次购买</span
      >

      <span class="but act" v-if="showDrugCode(detail)" @click="showCode"
        >取药码</span
      >

      <span class="but act" v-if="showPay(detail)" @click="selePay"
        >去支付</span
      >
    </div>

    <!-- 弹框 -->
    <uni-popup ref="drugCode" type="center">
      <EWM v-if="code" :code="code" />
    </uni-popup>
  </div>
</template>

<script>
import { findDrugStoreDetail } from '@/api/base';
import { getReceiptWay } from '@/api/order';
import {
  orderFastMallNewList,
  confirmGoods,
  secondAddShoppingCart,
  // 再次发起支付
  secondSaveFastMallOrder,
  queryFastMallOrderStatus,
  queryFastMallOrderStatusIschecking,
  tradeRefundFastMallOrder,
} from '@/api/shop';
import STATUS from './com/status.vue';
import REASONS from './com/reasons.vue';
import DRUGLIST from './com/drugList.vue';
import ROW from './com/row.vue';
import LOGISTICS from './com/logistics.vue';
import DRUGSTORE from './com/drugStore.vue';
import { Toast, wxPay } from '@/common/js/pay';
import EWM from './com/ewm.vue';
import {
  isShowInvoice,
  isShowRepeat,
  isShowRefund,
  isShowDrugCode,
  isShowEdit,
  isShowLogistics,
  isShowPay,
} from './com/status';

let timer;

let payTimer;

function setTimeStr(val) {
  if (!val) return 0;
  val = val.replace(/-/g, '/');
  let start = new Date(val).getTime();
  let end = new Date().getTime();
  let c = 1800 - (end - start) / 1000;
  if (c <= 0) return 0;
  let m = parseInt((c / 60) % 60);
  let s = parseInt(c % 60);
  if (m < 10) m = '0' + m;
  if (s < 10) s = '0' + s;
  return m + '分' + s + '秒';
}

export default {
  components: {
    ROW,
    EWM,
    REASONS,
    STATUS,
    DRUGLIST,
    LOGISTICS,
    DRUGSTORE,
  },
  data() {
    return {
      // 订单号
      orderNo: '',
      // 药品列表
      drugList: [],
      // 详情
      detail: {},
      // 药店详情
      drugStore: {},
      // 发票信息
      invoice: {},
      // 不通过相关
      reasons: {},
      // 审核状态列表
      refundAuditList: [],
      // 取药码
      code: '',
      // 支付方式
      payList: [],
      // 计数
      num: 3,
      // 倒计时
      timeStr: '',
    };
  },
  onLoad(v) {
    this.orderNo = v.orderNo;
    // this.getDetail();
  },
  onUnload() {
    if (timer) clearInterval(timer);
    if (payTimer) clearInterval(payTimer);
  },
  onShow() {
    this.getDetail();
  },
  methods: {
    // 获取支付配置
    async getSystem(subjectId) {
      let { data } = await getReceiptWay({
        subjectId,
      });
      this.payList = data;
    },
    // 获取详情
    async getDetail() {
      let { data } = await orderFastMallNewList(this.orderNo);
      if (!data.orderNo) {
        if (payTimer) clearInterval(payTimer);
        uni.showModal({
          title: '提示',
          content: '未查询到该订单，或订单已失效',
          showCancel: false,
          success() {
            uni.navigateBack();
          },
        });
        return;
      }
      let item = data;
      item.invoicetitle = item.invoiceTitle;
      item.deliverytype = item.deliveryType;
      item.logisticsstatus = item.logisticsStatus;
      this.drugList = item.drugList;

      this.getSystem(item.subjectId);

      let {
        drugCode,
        orderAuditList,
        invoiceMemo,
        invoiceTaxNo,
        invoiceTitle,
        invoiceType,
        payType,
        orderStatus,
        refundAuditList,
      } = item;

      this.refundAuditList = refundAuditList;

      // 取药码
      this.code = drugCode;
      // 发票信息
      this.invoice = {
        invoiceMemo,
        invoiceTaxNo,
        invoiceTitle,
        invoiceType,
      };
      delete item.invoiceMemo;
      delete item.invoiceTaxNo;
      delete item.invoiceType;
      delete item.drugList;
      delete item.drugCode;

      // 不通过相关
      this.reasons = orderAuditList;

      if (orderAuditList.length) {
        item.auditTime = orderAuditList[0].auditTime;
      }

      // 赋值
      this.detail = item;
      // 获取药店信息 快递时需获取联系方式
      this.getDrugStroe();

      // 如果线上支付 未支付
      if (payType == 1 || payType == 2) {
        // 如果订单状态未待支付
        if (orderStatus == 1) {
          if (setTimeStr(item.addTime) == 0) {
            this.timeStr = '00分00秒';
            return;
          }
          payTimer = setInterval(() => {
            this.timeStr = setTimeStr(item.addTime);
            if (this.timeStr <= 0) {
              clearInterval(payTimer);
              setTimeout(this.getDetail, 2000);
            }
          }, 1000);
        }
      }
    },
    // 获取药店信息
    async getDrugStroe() {
      const drugstoreId = this.detail.subjectId;
      let { data } = await findDrugStoreDetail({ drugstoreId });
      this.drugStore = data;
    },
    // 修改订单
    async toEdit() {
      const { orderNo } = this;
      const {
        data: { status },
      } = await queryFastMallOrderStatusIschecking(orderNo);
      if (status > 0) {
        Toast('订单正在审核中，请稍后再试！');
        return;
      }
      await secondAddShoppingCart(orderNo);
      let url = '/pages/shop/submit/edit?orderNo=' + orderNo;
      uni.navigateTo({ url });
    },
    // 再次购买
    async toRepeat() {
      await secondAddShoppingCart(this.orderNo);
      let url = '/pages/shop/submit/repeat?orderNo=' + this.orderNo;
      uni.navigateTo({ url });
    },
    // 跳转取消申请
    async toRefund() {
      const { orderNo } = this;
      if (this.refundAuditList.length) {
        uni.navigateTo({
          url: '/pages/shopOrder/cancel?orderNo=' + orderNo,
        });
        return;
      }
      const {
        data: { status },
      } = await queryFastMallOrderStatusIschecking(orderNo);
      // 已申请退款
      if (status == 5) {
        Toast('您已提交取消申请，请耐心等待');
        return;
      }
      // 订单关闭
      if (status == 6) {
        Toast('订单已关闭');
        return;
      }
      // 可直接退费
      if (status == 0) {
        await tradeRefundFastMallOrder(orderNo);
        Toast('成功取消');
        this.getDetail();
        return;
      }
      // 需要提交申请
      let url = '/pages/shopOrder/refund?orderNo=' + orderNo;
      uni.navigateTo({ url });
    },
    // 调用接口
    async toPay({ callId, payType }) {
      const { orderNo } = this;
      let obj = {
        orderNo,
        callId,
        payType,
        appid: uni.getStorageSync('appId'),
        openid: uni.getStorageSync('wxInfo').openId,
      };

      uni.showLoading({
        mask: true,
      });

      try {
        let { data } = await secondSaveFastMallOrder(obj);
        uni.hideLoading();
        if (payType == 1) {
          delete data.orderNo;
          delete data.canPay;
          this.wxPay(data);
        } else {
          uni.navigateTo({
            url:
              '/pages/pay/pay?isNewShop=1&price=' +
              this.detail.totalMoneyReal +
              '&orderNo=' +
              orderNo +
              '&url=' +
              btoa(data.url),
          });
        }
      } catch (error) {
        console.log(error);
        uni.hideLoading();
      }
    },
    // 发起微信支付
    async wxPay(info) {
      try {
        await wxPay(info);
        this.getPayStatus();
        timer = setInterval(this.getPayStatus, 2000);
      } catch (error) {
        Toast('取消支付');
      }
    },
    // 查询支付状态
    async getPayStatus() {
      // 根据订单号查询
      let res = await queryFastMallOrderStatus(this.orderNo);
      this.num--;

      if (res.data.orderStatus == 2) {
        clearInterval(timer);
        timer = null;
        this.num = 3;
        this.getDetail();
      }

      // 查询过三次
      if (this.num <= 0) {
        clearInterval(timer);
      }
    },
    // 确认收货
    async confirmGoods() {
      await confirmGoods(this.orderNo);
      Toast('收货成功');
      this.getDetail();
    },
    // 查看物流
    lockLogistics() {
      let {
        logisticsCode: code,
        deliveryTelNo,
        logisticsName: name,
      } = this.detail;
      if (!code) {
        Toast('未查询到物流单号');
        return;
      }
      let tel = deliveryTelNo.slice(-4);
      uni.navigateTo({
        url:
          '/pages/personalCenter/myOrder/allOrder/prescriptionOrder/logistics?code=' +
          code +
          '&tel=' +
          tel +
          '&name=' +
          name,
      });
    },
    // 配送查看
    lock() {
      const { orderNo } = this;
      let url = '/pages/shopOrder/logistic?orderNo=' + orderNo;
      uni.navigateTo({ url });
    },
    // 发起支付
    selePay() {
      const { payList } = this;

      if (!payList.length) {
        Toast('未配置支付方式');
        return;
      }

      let callId, payType;

      // 只有一个方式
      if (payList.length == 1) {
        let item = payList[0];
        callId = item.appid;

        // 是否微信支付
        if (item.receiptType == 1) {
          payType = 1;
        } else {
          payType = 2;
        }

        this.toPay({ callId, payType });
        return;
      }

      let that = this;
      uni.showActionSheet({
        itemList: ['微信支付', '支付宝支付'],
        success(res) {
          const index = res.tapIndex;
          let item;
          if (index === 0) {
            item = payList.find((v) => v.receiptType == 1);
          } else {
            item = payList.find((v) => v.receiptType == 2);
          }
          callId = item.appid;
          // 是否微信支付
          if (item.receiptType == 1) {
            payType = 1;
          } else {
            payType = 2;
          }
          that.toPay({ callId, payType });
        },
      });
    },
    // 展示取药码
    showCode() {
      this.$refs.drugCode.open();
    },
    // 显示提示
    showTip() {
      this.$refs.popup.open();
    },
    // 隐藏提示
    hideTip() {
      this.$refs.popup.close();
    },
    // 补开发票
    updateOrderInvoice() {
      let url = '/pages/shop/invoice/index?id=' + this.orderNo;
      uni.navigateTo({ url });
    },
    // 控制发票按钮
    showInvoice(item) {
      return isShowInvoice(item);
    },
    // 控制再次购买
    showRepeat(item) {
      return isShowRepeat(item);
    },
    // 控制退费申请
    showRefund(item) {
      return isShowRefund(item);
    },
    // 取药码
    showDrugCode(item) {
      return isShowDrugCode(item);
    },
    // 修改订单按钮
    showEdit(item) {
      return isShowEdit(item);
    },
    // 查看物流
    showLogistics(item) {
      return isShowLogistics(item);
    },
    // 显示支付
    showPay(item) {
      return isShowPay(item);
    },
  },
};
</script>

<style lang="scss" scoped>
page {
  background-color: #f5f5f5;
}

.detail {
  * {
    box-sizing: border-box;
  }

  padding-bottom: 132rpx;

  .mt_warp {
    margin-top: -32rpx;
    border-radius: 32rpx 32rpx 0 0;
    overflow: hidden;

    .drug_store {
      // margin-top: 24rpx;
      background-color: #fff;

      .title {
        font-size: 28rpx;
        line-height: 88rpx;
        padding: 0 32rpx;
        border-bottom: 1px solid #f5f5f5;
      }
    }

    .drug {
      padding: 0 32rpx;
      background-color: #fff;
      margin-top: 24rpx;

      .title {
        height: 88rpx;
        @include flex(left);
        font-size: 28rpx;
        font-weight: bold;
        color: #333;
        border-bottom: 1px solid #f5f5f5;
      }
    }

    .price {
      background-color: #fff;
      margin-top: 24rpx;
      padding: 10rpx 32rpx;
      font-size: 28rpx;

      p {
        @include flex(lr);
        line-height: 60rpx;

        span {
          color: #999;

          &.n {
            color: #333;

            &.red {
              color: red;
            }
          }
        }

        &.last {
          justify-content: flex-end;

          span {
            color: red;
            font-weight: bold;
          }
        }
      }
    }

    .order {
      margin-top: 24rpx;
      background-color: #fff;
      padding: 0 32rpx 12rpx;
      font-size: 28rpx;

      p {
        line-height: 60rpx;
        span {
          margin-left: 24rpx;
        }
      }

      .title {
        font-weight: bold;
        line-height: 70rpx;
      }
    }
  }

  .footer {
    width: 100%;
    height: 108rpx;
    background: #ffffff;
    box-shadow: 0px 0px 2rpx 0px rgba(0, 0, 0, 0.12);
    border-radius: 16rpx 16rpx 0px 0px;
    position: fixed;
    left: 0;
    bottom: 0;
    z-index: 2;
    @include flex(right);
    padding: 0 32rpx;
    box-sizing: border-box;

    .but {
      width: 158rpx;
      height: 56rpx;
      border-radius: 28rpx;
      @include flex;
      font-size: 26rpx;
      @include font_theme;
      margin-left: 24rpx;
      padding: 0;
      @include border_theme;
      margin-right: inherit;

      &.act {
        @include bg_theme;
        color: #fff;
      }
    }
  }
}

.pop {
  padding: 0 32rpx 50rpx;
  background-color: #fff;
  border-radius: 16rpx 16rpx 0px 0px;

  .pop_title {
    height: 88rpx;
    @include flex;
    position: relative;
    font-size: 32rpx;

    .uni-icons {
      position: absolute;
      right: 0;
    }
  }

  .pop_text {
    color: #999;
    font-size: 24rpx;
    line-height: 60rpx;
  }
}
</style>
