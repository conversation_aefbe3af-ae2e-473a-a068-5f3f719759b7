<template>
  <view>
    <view class="content">
      {{ interrogationSummary }}
    </view>
  </view>
</template>

<script>
import { getInterrogationSummary } from '@/api/chatCardDetail';
export default {
  data() {
    return {
      regId: '',
      interrogationSummary: '',
    };
  },
  onLoad(option) {
    this.regId = option.regId;
  },
  created() {
    this.getInterrogationSummaryFun();
  },
  methods: {
    async getInterrogationSummaryFun() {
      let res = await getInterrogationSummary({
        regId: this.regId,
      });
      this.interrogationSummary = res.data.interrogationSummary;
    },
  },
};
</script>

<style scoped lang="scss">
.content {
  background: #ffffff;
  margin: 24rpx 32rpx;
  padding: 24rpx 32rpx;
  color: #333333;
  font-size: 28rpx;
  line-height: 36rpx;
}
</style>
