{"name": "cloud_patient", "version": "0.1.0", "private": true, "scripts": {"serve": "cross-env NODE_ENV=development UNI_PLATFORM=h5 vue-cli-service uni-serve --open", "build": "cross-env NODE_ENV=production UNI_PLATFORM=h5 vue-cli-service uni-build", "build:test": "cross-env NODE_ENV=test UNI_PLATFORM=h5 vue-cli-service uni-build", "upload-dev": "sh ./upload.sh dev", "upload-test": "sh ./upload.sh test"}, "dependencies": {"@dcloudio/uni-app-plus": "^2.0.0-31220210205003", "@dcloudio/uni-h5": "^2.0.0-31220210205003", "@dcloudio/uni-helper-json": "^1.0.13", "@dcloudio/uni-mp-360": "^2.0.0-31220210205003", "@dcloudio/uni-mp-alipay": "^2.0.0-31220210205003", "@dcloudio/uni-mp-baidu": "^2.0.0-31220210205003", "@dcloudio/uni-mp-qq": "^2.0.0-31220210205003", "@dcloudio/uni-mp-toutiao": "^2.0.0-31220210205003", "@dcloudio/uni-mp-vue": "^2.0.0-31220210205003", "@dcloudio/uni-mp-weixin": "^2.0.0-31220210205003", "@dcloudio/uni-quickapp-native": "^2.0.0-31220210205003", "@dcloudio/uni-quickapp-webview": "^2.0.0-31220210205003", "@dcloudio/uni-stat": "^2.0.0-31220210205003", "@dcloudio/uni-ui": "^1.2.10", "@microsoft/fetch-event-source": "^2.0.1", "@vue/shared": "^3.0.5", "axios": "^0.21.1", "benz-amr-recorder": "^1.1.3", "core-js": "^3.9.0", "crypto-js": "^4.2.0", "easemob-emedia": "^3.4.1", "easemob-websdk": "^4.11.0", "eruda": "^3.0.1", "flyio": "^0.6.2", "js-audio-recorder": "^1.0.7", "jweixin-module": "^1.6.0", "lodash": "^4.17.21", "marked": "^4.3.0", "node-sass": "^4.14.1", "pdfh5": "^1.4.2", "recorder-core": "^1.1.21021500", "regenerator-runtime": "^0.12.1", "uview-ui": "^1.8.8", "vue": "^2.6.11", "vue-jsonp": "^2.0.0", "vuex": "^3.6.2"}, "devDependencies": {"@dcloudio/types": "^2.0.22", "@dcloudio/uni-automator": "^2.0.0-31220210205003", "@dcloudio/uni-cli-i18n": "^2.0.1-36420220922003", "@dcloudio/uni-cli-shared": "^2.0.0-31220210205003", "@dcloudio/uni-i18n": "^2.0.1-36420220922003", "@dcloudio/uni-migration": "^2.0.0-31220210205003", "@dcloudio/uni-template-compiler": "^2.0.0-31220210205003", "@dcloudio/vue-cli-plugin-hbuilderx": "^2.0.0-31220210205003", "@dcloudio/vue-cli-plugin-uni": "^2.0.0-31220210205003", "@dcloudio/vue-cli-plugin-uni-optimize": "^2.0.0-31220210205003", "@dcloudio/webpack-uni-mp-loader": "^2.0.0-31220210205003", "@dcloudio/webpack-uni-pages-loader": "^2.0.0-31220210205003", "@vue/cli-plugin-babel": "^4.5.11", "@vue/cli-service": "^4.5.11", "babel-plugin-import": "^1.11.0", "cross-env": "^7.0.2", "jest": "^25.4.0", "mini-types": "*", "miniprogram-api-typings": "^3.2.2", "postcss-comment": "^2.0.0", "vue-template-compiler": "^2.6.11"}, "browserslist": ["Android >= 4", "ios >= 8"], "uni-app": {"scripts": {}}}