<template>
  <view class="address">
    <!-- 地图组件 -->
    <iframe
      id="mapPage"
      width="100%"
      height="100%"
      frameborder="0"
      :src="src"
      v-if="false"
    />

    <Location @seleItem="seleItem" v-if="!false" />
  </view>
</template>

<script>
import { Toast } from '@/common/js/pay.js';
import { getCityByLocation } from '@/pages/shop/map';

import Location from './com/location';
const mapKey = require('@/common/request/config.js').mapKey;
export default {
  components: {
    Location,
  },
  data() {
    return {
      act: '',
      src: `https://apis.map.qq.com/tools/locpicker?search=1&type=1&key=${mapKey}&referer=qywx`,
    };
  },
  onLoad(v) {
    console.log(v)
    if (v.act) this.act = v.act;
    // window.addEventListener('message', this.getLoc, false);
  },
  onUnload() {
    // window.removeEventListener('message', this.getLoc);
  },
  methods: {
    async getLoc(e) {
      let loc = e.data;
      if (loc && loc.module == 'locationPicker') {
        let {
          cityname: city,
          latlng,
          poiaddress: address,
          poiname: name,
        } = loc;
        if (name == '我的位置') {
          Toast('请选择具体位置');
          return;
        }
        let obj = {
          city,
          latitude: latlng,
          address,
          name,
        };
        console.log(obj);
        // 商城购药
        if (this.act == 'shop') {
          let { province } = await getCityByLocation(
            latlng.lat + ',' + latlng.lng
          );
          uni.setStorageSync('shop_city', {
            city: address,
            lat: latlng.lat,
            lng: latlng.lng,
            province,
          });
          uni.navigateBack({
            delta: 2,
          });
        } else {
          uni.setStorageSync('selectAddress', obj);
          uni.navigateBack();
        }
      }
    },
    async seleItem(obj) {
      let { latitude, address ,item} = obj;
      console.log(obj,'8888')
      // item.ad_info = item.ad_info || {}
      // 商城购药
      if (this.act == 'shop') {
        let { province } = await getCityByLocation(
          latitude.lat + ',' + latitude.lng
        );
        uni.setStorageSync('shop_city', {
          city: address,
          lat: latitude.lat,
          lng: latitude.lng,
          province,
          obj
        });
        uni.navigateBack({
          delta: 2,
        });
      } else {
        uni.setStorageSync('selectAddress', obj);
        uni.navigateBack();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.address {
  width: 100vw;
  height: 100vh;
}
</style>
