<template>
  <div class="refund">
    <div class="cont">
      <p class="title">取消原因</p>
      <!-- 选项 -->
      <ul>
        <li v-for="item in options" :key="item.code" @click="setStr(item)">
          <image
            v-show="index != item.code"
            src="/static/images/question/radio.png"
          />
          <image
            v-show="index == item.code"
            src="/static/images/question/radio_active.png"
          />
          <span>{{ item.meaning }}</span>
        </li>
        <!-- 其他 -->
        <li @click="setOther">
          <image v-show="index != 0" src="/static/images/question/radio.png" />
          <image
            v-show="index == 0"
            src="/static/images/question/radio_active.png"
          />
          <span>其他原因</span>
        </li>
      </ul>

      <div class="warp">
        <!-- 自填 -->
        <textarea
          class="area"
          maxlength="100"
          placeholder="请输入申请理由"
          @input="(e) => (applicationDescription = e.detail.value)"
          :value="applicationDescription"
        ></textarea>

        <!-- 图片 -->
        <div class="img_list">
          <view class="img_item" v-for="(item, index) in fileList" :key="index">
            <image
              :src="item.url"
              @click="preview(item)"
              mode="aspectFit"
              class="img"
            ></image>
            <!-- 删除按钮 -->
            <view class="del" @click="delFile(index)">
              <uni-icons type="close" color="#fff" size="24"></uni-icons>
            </view>
          </view>
          <!-- 上传 -->
          <view class="img_item" @click="cloosImg" v-if="fileList.length < 6">
            <image src="/static/images/question/image-upload.png" />
            <text>上传图片</text>
          </view>
        </div>
      </div>
    </div>

    <FOOTER @click="send">提交申请</FOOTER>
  </div>
</template>

<script>
import { fastMallOrderRefundAudit, getSysCodeByType } from '@/api/shop';
import myJsTools from '@/common/js/myJsTools.js';
import { uploadImg } from '@/api/oss.js';
import { Toast } from '@/common/js/pay.js';
import FOOTER from '@/components/footer_button/button.vue';

export default {
  name: 'Refund',
  components: { FOOTER },
  data() {
    return {
      // 选项
      options: [],
      index: 1,
      applicationPictureList: '',
      orderNo: '',
      reasonsForapplication: '',
      // 描述
      applicationDescription: '',
      // 图片列表
      fileList: [],
    };
  },
  onLoad(v) {
    this.orderNo = v.orderNo;
    this.getConfig();
  },
  methods: {
    // 获取配置
    async getConfig() {
      let { data } = await getSysCodeByType('067');
      this.options = data;
      this.setStr(data[0]);
    },
    // 设置文案
    setStr(v) {
      this.index = v.code;
      this.reasonsForapplication = v.meaning;
    },
    // 选择其他
    setOther() {
      this.reasonsForapplication = '';
      this.index = 0;
    },
    // 上传图片
    async upImg(obj) {
      let para = {
        folderType: 35,
        imgBody: obj.base64,
        // 患者id
        otherId: this.orderNo,
      };
      let res = await uploadImg(para);
      // 图片名称
      return res.data.url;
    },
    // 移除图片
    delFile(n) {
      this.fileList.splice(n, 1);
    },
    // 选择图片
    cloosImg() {
      const that = this;
      uni.chooseImage({
        count: 1,
        success: function(res) {
          // 读取图片
          const file = res.tempFiles[0];
          myJsTools.setImgZip(file, (dataUrl) => {
            that.fileList.push({
              base64: dataUrl.split(',')[1],
              url: dataUrl,
              name: '',
            });
          });
        },
      });
    },
    // 图片预览
    preview(item) {
      uni.previewImage({
        urls: [item.url],
      });
    },
    // 提交
    async send() {
      let {
        applicationPictureList,
        orderNo,
        reasonsForapplication,
        applicationDescription,
      } = this;
      if (this.index == 0 && !applicationDescription) {
        Toast('请填写申请理由');
        return;
      }

      uni.showLoading({
        mask: true,
      });

      let images = [];

      // 如果存在图片
      if (this.fileList.length) {
        // 循环上传图片
        for (let i = 0; i < this.fileList.length; i++) {
          try {
            let url = await this.upImg(this.fileList[i]);
            images.push(url);
          } catch (e) {
          }
        }
      }

      if (images.length) {
        applicationPictureList = images.join('|');
      }

      await fastMallOrderRefundAudit({
        applicationPictureList,
        orderNo,
        reasonsForapplication,
        applicationDescription,
      });

      Toast('提交成功,请耐心等待审核');
      setTimeout(() => {
        uni.navigateBack();
      }, 2000);
    },
  },
};
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}
.refund {
  padding: 24rpx 32rpx;

  .cont {
    background-color: #fff;
    border-radius: 16rpx;
    padding: 24rpx;

    .title {
      font-size: 32rpx;
    }

    ul {
      padding: 12rpx 0;

      li {
        list-style: none;
        @include flex(left);
        height: 80rpx;

        image {
          width: 36rpx;
          height: 36rpx;
          margin-right: 24rpx;
        }

        span {
          font-size: 28rpx;
        }
      }
    }

    .warp {
      border: 1px solid #f5f5f5;

      .area {
        width: 100%;
        height: 80rpx;
        border-radius: 16rpx;
        padding: 24rpx;
        font-size: 28rpx;
      }

      .img_list {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        grid-gap: 20rpx;
        padding-bottom: 20rpx;
        border-radius: 16rpx;
        padding: 24rpx;

        .img_item {
          width: 180rpx;
          height: 180rpx;
          border-radius: 8rpx;
          border: 1px dashed #eee;
          position: relative;
          @include flex;
          flex-direction: column;

          image {
            width: 64rpx;
            height: 64rpx;
          }

          text {
            color: #666;
            font-size: 28rpx;
          }

          .img {
            width: 100%;
            height: 100%;
          }

          .del {
            background-color: rgba($color: #000000, $alpha: 0.3);
            position: absolute;
            border-radius: 50%;
            top: 0;
            right: 0;
          }
        }
      }
    }
  }
}
</style>
