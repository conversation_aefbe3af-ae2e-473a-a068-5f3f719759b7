<template>
  <div class="list">
    <!-- 单个 -->
    <u-radio-group
      v-model="check"
      style="width: 100%"
      @change="radioGroupChange"
    >
      <view
        class="card"
        v-for="(item, index) in list"
        :key="index"
        @click="click(item)"
      >
        <!-- 编号 -->
        <view class="item_title">
          <view style="display: flex; align-items: center">
            <img
              style="width: 13px; height: 15px; margin-right: 7px"
              src="../../../../static/cfimCard.png"
              alt=""
            />

            <text>处方编号：{{ item.prescriptionCode }}</text>
          </view>

          <block v-if="item.status < 3">
            <text class="status wait">待支付</text>
          </block>

          <block v-if="item.status == 3">
            <text class="status wait" v-if="item.deliveryType == 1"
              >待发货</text
            >
            <text class="status wait" v-if="item.deliveryType == 2"
              >待取药</text
            >
          </block>

          <block v-if="item.status == 4">
            <text class="status done">待收货</text>
          </block>

          <block v-if="item.status == 5">
            <text class="status" v-if="item.deliveryType == 1">已收货</text>
            <text class="status" v-if="item.deliveryType == 2">已取药</text>
          </block>

          <block v-if="item.status == 7">
            <text class="status">已退费</text>
          </block>

          <block v-if="item.status == 9">
            <text class="status">已失效</text>
          </block>
        </view>

        <!-- 相关信息 -->
        <view class="item_cont">
          <view class="left_info"> 诊断：{{ item.diagNames }} </view>
          <view class="left_info"> 医院：{{ item.hosName }} </view>
          <!--          <view class="left_info">-->
          <!--            就诊人：{{ item.patientName }}-->
          <!--          </view>-->
          <view class="left_info">
            医生姓名：{{ item.platformDocName || item.docName }}
            <!-- 右箭头 -->
            <uni-icons type="arrowright" color="#666" size="16"></uni-icons
          ></view>
          <view class="left_info"> 医生科室：{{ item.deptName }} </view>
          <view
            style="
              border-radius: 1px;
              background: #f7f8fc;
              border: 0.5px solid #ccb0ff;
              padding: 2px 6px;
              color: #666666;
              font-size: 10px;
              display: inline-block;
            "
            >就诊人：{{ item.patientName }}</view
          >
        </view>

        <!-- 时间 -->
        <view class="item_time">
          开方时间：{{ item.addTime }}
          <u-radio
            v-if="flag === 'check'"
            :name="item.businessId"
            @click.native.stop
          >
          </u-radio>
        </view>
      </view>
    </u-radio-group>
  </div>
</template>

<script>
export default {
  name: "List",
  props: {
    list: {
      type: Array,
      default: () => [],
    },
    flag: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      check: "",
    };
  },
  onLoad(options) {
    console.log("options", options);
  },
  methods: {
    radioGroupChange(value) {
      console.log(value);
      this.$emit("checkCF", this.list.find((v) => v.businessId == value) || {});
    },
    click(item) {
      this.$emit("click", item);
    },
  },
};
</script>

<style lang="scss" scoped>
.list {
  padding: 20rpx 34rpx;

  .card {
    padding: 0 32rpx;
    border-radius: 7.62px;
    background: #ffffff;
    box-shadow: 0px 1px 4px rgba(121, 72, 234, 0.15);
    margin-bottom: 24rpx;
    width: 100%;
    &:last-child {
      margin-bottom: 0;
    }

    .item_title {
      height: 88rpx;
      @include flex(lr);
      font-size: 24rpx;
      color: #333;
      font-weight: bold;
      border-bottom: 1px solid #ebebeb;

      .status {
        &.wait {
          color: #fb6262;
        }
        &.done {
          @include font_theme;
        }
      }
    }

    .item_cont {
      padding: 10rpx 0;
      border-bottom: 1px solid #ebebeb;

      .left_info {
        @include flex(lr);
        font-size: 24rpx;
        color: #999999;
        line-height: 50rpx;

        &:first-child {
          font-weight: bold;
          color: #333;
        }
      }
    }

    .item_time {
      height: 88rpx;
      @include flex(lr);
      font-size: 24rpx;
      color: #333;
    }
  }
}
</style>
