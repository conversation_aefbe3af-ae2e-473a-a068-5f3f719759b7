<template>
  <!-- 药品明细 -->
  <div class="drug_detail">
    <p class="title">处方信息 <span v-if="onShowName&&deliveryType==1" class="title-1">{{onShowName}}</span></p>
    <!-- 药品列表 -->
    <div class="list">
      <!-- 循环 -->
      <div class="list_item" v-for="(item, index) in list" :key="index">
        <!-- 成药方 -->
        <div class="label">
          <span>Rp</span>
          <span>{{ item.proPrescriptionMasterVO.prescriptionTypeName }}</span>
        </div>
        <div class="item" v-for="(d, t) in item.details" :key="t">
          <p class="item_name">{{ t + 1 }}.{{ d.drugName }}({{ d.gg }})</p>


          <p class="item_text"
            v-show="item.proPrescriptionMasterVO.prescriptionType == 1 || item.proPrescriptionMasterVO.prescriptionType == 2||item.proPrescriptionMasterVO.prescriptionType == 4">
            用法：{{ d.dduName || "口服" }}，每次{{ d.eachQuan
            }}{{ d.eachUnit }}，{{ d.ddufName }}，用药{{ d.days }}天
          </p>


          <p class="item_text">
            <span>价格：¥{{ d.price }}</span>
            <span>x{{ d.quan }}</span>
          </p>
          <p class="item_text" v-if="d.memo">说明：{{ d.memo }}</p>
        </div>
        <!-- 草药 -->
        <p class="item_num" v-show="item.proPrescriptionMasterVO.prescriptionType ==3">
          用法：{{
                  item.proPrescriptionMasterVO.dduName || "口服"
                }}，每次{{ item.proPrescriptionMasterVO.rj }}剂，每日{{ item.proPrescriptionMasterVO.rc }}次，用药{{
            item.proPrescriptionMasterVO.days
          }}天
        </p>
        <p class="item_num" v-if="item.proPrescriptionMasterVO.herbalNum">
          共{{ item.proPrescriptionMasterVO.herbalNum }}付
        </p>
        <!-- <p class="item_num" v-if="item.proPrescriptionMasterVO.djCost">
          代煎费：¥{{ item.proPrescriptionMasterVO.djCost | toFixed }}
        </p> -->
        <view v-if="item.proPrescriptionMasterVO.prescriptionType==3">
          剂型：{{item.proPrescriptionMasterVO.herbalFormulations}}
        </view>
        <view class="price-class" v-if="item.proPrescriptionMasterVO.prescriptionType==3 && getDjCost(item) !=0">
          单付药品的代煎费为￥{{item.proPrescriptionMasterVO.dfPrice | toFixed}}，共{{item.proPrescriptionMasterVO.herbalNum}}付，代煎费用共￥{{item.proPrescriptionMasterVO.djCost
          | toFixed}}
        </view>
        <p class="item_btm">
          合计：<span>¥{{ item.proPrescriptionMasterVO.totalMoney | toFixed }}</span>
        </p>
      </div>
    </div>

    <!-- 总计 -->
    <div class="bottom">
      <p>
        合计：<span>¥{{ totalMoney | toFixed }}</span>
      </p>
      <p>
        优惠：<span>¥{{ preferentialAmount | toFixed }}</span>
      </p>
      <p>
        实付：<span>¥{{ orderMoney | toFixed }}</span>
      </p>
    </div>
  </div>
</template>

<script>
  import {
    getSysPlatformConfigByKeyList,
  } from "@/api/base";

  export default {
    name: "DrugDetail",
    props: {
      list: {
        type: Array,
        default: () => [],
      },
      dosageCodeList: {
        type: Array,
        default: () => [],
      },
      // 优惠
      preferentialAmount: String | Number,
      // 实际
      orderMoney: String | Number,
      // 合计
      totalMoney: String | Number,
      deliveryType: {
        type: String | Number,
        default: 1,
      },
    },
    data() {
      return {
        dosageCodeMap: new Map(), //剂型map  
        onShowName: ''
      }
    },
    created() {
      console.log(this.list);
      console.log('codeList', this.dosageCodeList)
      this.dosageCodeList.forEach((item) => {
        item.drugstoreDosagePrices.forEach((t) => {
          this.dosageCodeMap.set(t.dosageName, t.pendingFryingFee)
        })
      });
      this.getConfig();
    },
    methods: {
      // 获取配置 是否显示
      async getConfig() {
        let {
          data
        } = await getSysPlatformConfigByKeyList(["isShowPrescriptionListCharacters"]);
        // 显示要展示的文字
        //console.log('000',data[0].configValue);
        if (data.length > 0) {
          this.onShowName = data[0].configValue
        } else {
          this.onShowName = ''
        }

      },
      //单付代煎费(新加)
      getDfPrice(item) {
        return this.dosageCodeMap.get(item.proPrescriptionMasterVO.herbalFormulations) ? this.dosageCodeMap.get(item
          .proPrescriptionMasterVO.herbalFormulations) : 0
      },
      //代煎费用（新加）
      getDjCost(item) {
        return (Number(this.dosageCodeMap.get(item.proPrescriptionMasterVO.herbalFormulations) ? this.dosageCodeMap.get(
          item.proPrescriptionMasterVO.herbalFormulations) : 0) * 100 * item.proPrescriptionMasterVO.herbalNum) / 100
      },
    }
  };
</script>

<style lang="scss" scoped>
  .drug_detail {
    background: #fff;
    border-radius: 8rpx;
    margin-top: 24rpx;

    .title {
      height: 92rpx;
      @include flex(left);
      font-size: 28rpx;
      font-weight: bold;
      padding-left: 32rpx;
    }

    .title-1 {
      color: #b4b4b4;
      padding-left: 10rpx;
    }

    .list {
      padding: 0 32rpx 0;
      font-size: 28rpx;
      color: #333;

      .list_item {
        line-height: 60rpx;
        padding-bottom: 12rpx;
        border-bottom: 1px dashed #f5f5f5;

        .label {
          @include flex(lr);
          font-weight: bold;
        }

        .item {
          .item_name {
            font-weight: bold;
          }

          .item_text {
            @include flex(lr);
          }
        }

        .item_btm {
          @include flex(right);

          span {
            color: red;
          }
        }
      }
    }
  }

  .bottom {
    padding-right: 32rpx;
    @include flex(right);
    font-size: 28rpx;
    height: 80rpx;

    p {
      padding-left: 32rpx;

      span {
        color: #ff5050;
      }
    }
  }

  .price-class {
    color: #ff0000;
  }
</style>