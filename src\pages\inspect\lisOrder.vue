<template>
  <!-- 检查 -->
  <view class="inspect">
    <!-- 详情部分 -->
    <view class="detail">
      <!-- 顶部信息 -->
      <USERINFO isLis :info="detail" />

      <!-- 选择机构 -->
      <SELECT
        isLis
        :showRight="!isHaveDpoId"
        :check="checkObj || {}"
        @click="toPath('./center/lisList?id=' + pliId)"
      />

      <!-- 项目信息 -->
      <PROJECT
        isLis
        v-if="list.length"
        @click="
          toPath('./inspectTime?dpoId=' + checkObj.dloId + '&pliId=' + pliId)
        "
        :list="list"
        :isShowTime="!!checkObj.dloId"
        :time="timeObj.showTime"
      />

      <!-- 选择支付 -->
      <SELEPAY
        v-if="payList.length == 2"
        :list="payList"
        :currAct="payType"
        @change="setPayType"
      />

      <ROW text="支付方式" v-if="payList.length == 1">{{
        payList[0].name
      }}</ROW>

      <!-- 医保类型 -->
      <INSURANCE
        :showRight="isSupportMedical == 1"
        :currAct="isSupportMedical == 1 ? support : 0"
        @change="supportChange"
      />
    </view>

    <!-- 底部 -->
    <FOOTER :length="list.length" :cost="detail.cost" @click="getNext">
      提交订单
    </FOOTER>
  </view>
</template>

<script>
// 顶部信息
import USERINFO from "./com/userInfo.vue";
// 项目信息
import PROJECT from "./com/project.vue";
// 选择机构
import SELECT from "./com/check.vue";
// 底部展示
import FOOTER from "./com/footer.vue";
// 选择支付
import SELEPAY from "./com/selePay.vue";
// 医保类型
import INSURANCE from "./com/insurance.vue";
// 行
import ROW from "./com/row.vue";

import { Toast } from "@/common/js/pay.js";

import {
  getProLisPayInfoByID,
  queryDicLisOrganizationbyId,
  getLisItemPrice,
  getLisAppointPayInfo,
  queryLisRegisterPayStatus,
} from "@/api/inspect";

import { wxPay } from "@/common/js/pay";

import { getReceiptWay } from "@/api/order";
let num = 3;
export default {
  components: {
    USERINFO,
    PROJECT,
    SELECT,
    FOOTER,
    SELEPAY,
    INSURANCE,
    ROW,
  },
  data() {
    return {
      // 检验单id
      pliId: "",
      // 检查机构id
      dloId: "",
      // 步骤
      step: 0,
      // 检查项目
      list: [],
      // 详情
      detail: {},
      // 机构
      mechanism: "",
      // 检查机构
      checkObj: "",
      // 预约时间段
      timeObj: {
        showTime: "",
      },
      // 支付类型 1到院 2在线
      payType: -1,
      // 是否可选支付方式
      isSelePay: true,
      // 支持的支付列表
      payList: [],
      // 支付方appid
      pays: [],
      // 是否支持医保
      isSupportMedical: true,
      // 选择医保
      support: -1,
      // 是否指定机构
      isHaveDpoId: false,
      // 支付方id
      callId: "",
    };
  },
  onLoad(opt) {
    if (!opt.id) return;
    this.pliId = opt.id;
    this.getDetail();
  },
  onShow() {
    if (this.dloId && !this.isHaveDpoId) {
      this.getOrgan();
      this.getPrice();
      this.getPayList();
      // 防止二次加载
      this.dloId = "";
    }
  },
  methods: {
    // 获取指定机构价位
    async getPrice() {
      let arr = [];
      this.list.forEach((v) => {
        arr.push(v.dliId);
      });
      let obj = {
        dloId: this.dloId,
        itemList: arr,
      };
      let { data } = await getLisItemPrice(obj);
      let cost = 0;
      data.forEach((v) => {
        cost += v.price * 100;
        this.detail.lisListDVO.forEach((k) => {
          if (v.dliId == k.dliId) {
            if (v.price) k.cost = v.price;
          }
        });
      });
      if (cost) this.detail.cost = cost / 100;
      this.list = this.detail.lisListDVO;
    },
    // 选择医保
    supportChange(n) {
      this.support = n;
    },
    // 选择支付方式
    setPayType(n) {
      this.payType = Number(n);
      if (n == 2) {
        this.support = 0;
        this.isSupportMedical = false;
      } else if (this.checkObj.isSupportMedical == 1) {
        this.isSupportMedical = true;
      }
    },
    // 获取机构信息
    async getOrgan() {
      this.timeObj = {};
      let { data } = await queryDicLisOrganizationbyId(this.dloId);
      // 是否支持医保 支付类型（逗号分隔）
      let { isSupportMedical, paymentType } = data;
      this.isSupportMedical = isSupportMedical != 1 ? false : true;
      // 如果不支持医保
      console.log(isSupportMedical)
      if (isSupportMedical == "0" || !isSupportMedical) {
        // 默认 0 自费
        this.support = 0;
      }
      // 支持的支付方式
      paymentType = paymentType.split(",");
      // 如果两种支付方式
      if (paymentType.length == 1) {
        this.payType = Number(paymentType[0]);
        this.isSelePay = false;
        if (this.payType == 1) {
          this.payList = [{ name: "到院支付", id: 1 }];
        } else {
          this.payList = [{ name: "在线支付", id: 2 }];
          this.support = 0;
        }
      } else {
        // 默认在线支付
        this.payType = 2;
        this.isSelePay = true;
        this.isSupportMedical = false;
        this.support = 0;
        paymentType.forEach((v, i) => {
          let obj = {
            id: v,
          };
          if (v == 1) {
            obj.name = "到院支付";
          } else {
            obj.name = "在线支付";
          }
          paymentType[i] = obj;
        });
        this.payList = paymentType;
      }

      // 检查机构信息
      this.checkObj = data;
    },
    // 获取支付方callId
    async getPayList() {
      let { data } = await getReceiptWay({
        subjectId: this.dloId,
      });
      this.pays = data;
    },
    // 提示信息
    help() {
      this.$refs.popup.open();
    },
    // 跳转页面
    toPath(path) {
      uni.navigateTo({
        url: path,
      });
    },
    // 获取详情
    async getDetail() {
      let { data } = await getProLisPayInfoByID(this.pliId);
      this.list = data.lisListDVO;
      this.detail = data;
      if (data.isHaveDpoId == 1) {
        this.isHaveDpoId = true;
        if (data.lisType == 2) {
          this.dloId = data.dsoId;
        } else {
          this.dloId = data.dpoId;
        }

        this.getOrgan();
        this.getPrice();
        this.getPayList();
      }
    },
    // 下一步
    async getNext() {
      // 检验机构id
      let { dloId } = this.checkObj;

      if (!dloId) {
        Toast("请选择检验机构");
        return;
      }

      // 预约相关
      let { appointDate, appointEndTime, appointStartTime, viRealId } =
        this.timeObj;

      if (!appointDate) {
        Toast("请选择预约时间");
        return;
      }

      // 其他字段
      let { samplingOrgName, lisType, lisOrgName, dsoId, dpoId } = this.detail;

      // 是否医保
      let isMedicare = this.support;

      if (isMedicare == -1) {
        Toast("请选择医保类型");
        return;
      }

      uni.showLoading({
        mask: true,
      });

      // 检验单id
      let pliId = this.pliId;

      // 用户openid
      let openid = uni.getStorageSync("wxInfo").openId;

      // 支付金额
      let payCost = this.detail.cost;

      // 支付方式 0 无需支付 1 微信 2 支付宝 3 业务平台 4 线下支付
      let payType = 4;

      // 支付类型 1 到院（线下） 2 线上支付
      let paymentType = this.payType;

      // 项目列表
      let projectDTOList = [];

      this.list.forEach((v) => {
        projectDTOList.push({
          cost: v.cost,
          pldId: v.pldId,
        });
      });

      payType = await this.selePay();

      let callId = this.callId;

      // 组合参数
      let param = {
        dpoId,
        samplingOrgName,
        lisType,
        lisOrgName,
        dsoId,
        appointDate,
        appointEndTime,
        appointStartTime,
        viRealId,
        isMedicare,
        callId,
        pliId,
        openid,
        payCost,
        payType,
        paymentType,
        projectDTOList,
      };

      try {
        let { data } = await getLisAppointPayInfo(param);
        uni.hideLoading();

        // 线下支付
        if (payType == 4) {
          this.toSuc();
          return;
        }

        // 无需支付
        if (payType == 0 && data.success == 1) {
          this.toSuc();
          return;
        }

        // 微信支付
        if (payType == 1) {
          this.wxPay(data);
          return;
        }

        // 支付宝支付
        if (payType == 2) {
          uni.navigateTo({
            url:
              "/pages/pay/pay?price=" +
              payCost +
              "&pliId=" +
              this.pliId +
              "&url=" +
              btoa(data.url),
          });
        }
      } catch (error) {
        uni.hideLoading();
      }
    },
    // 微信支付
    async wxPay(info) {
      try {
        await wxPay(info);
        this.getStatus();
      } catch (error) {
        Toast("取消支付");
        uni.redirectTo({
          url: "./pay/lis?id=" + this.pliId,
        });
      }
    },
    // 选择支付方式
    async selePay() {
      let that = this;
      return new Promise((resolve, reject) => {
        // 如果到院支付
        if (this.payType == 1) {
          return resolve(4);
        }
        // 如果无需支付
        if (this.detail.cost == 0) {
          return resolve(0);
        }
        // 如果不可选支付
        if (!this.pays.length) {
          Toast("当前没有配置支付方式");
          return reject();
        }
        // 如果在线支付
        uni.showActionSheet({
          itemList: ["微信支付", "支付宝支付"],
          success(res) {
            let index = res.tapIndex + 1;
            let item;
            item = that.pays.filter((it) => it.receiptType == index)[0];
            if (!item) {
              Toast("暂不支持该支付方式");
              return reject();
            }
            that.callId = item.appid;
            resolve(index);
          },
          fail(err) {
            uni.hideLoading();
            reject(err);
          },
        });
      });
    },
    // 查询支付状态
    async getStatus() {
      let {
        data: { status },
      } = await queryLisRegisterPayStatus(this.pliId);
      // 支付成功
      if (status == 2) {
        this.toSuc();
        return;
      }
      if (status == 1) {
        if (num > 0) {
          setTimeout(this.getStatus(), 2000);
          num--;
        }
      }
    },
    // 跳转成功
    toSuc() {
      uni.redirectTo({
        url: "./result/lisResult?id=" + this.pliId,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.inspect {
  padding-bottom: 108rpx;

  .detail {
    padding: 32rpx;
  }

  .pop {
    background: #fff;
    border-radius: 16rpx 16rpx 0 0;

    &_title {
      font-size: 32rpx;
      color: #333;
      text-align: center;
      line-height: 80rpx;
    }

    &_cont {
      padding: 0 32rpx 32rpx;
      font-size: 24rpx;
      line-height: 34rpx;
      color: #666;
    }
  }
}
</style>
