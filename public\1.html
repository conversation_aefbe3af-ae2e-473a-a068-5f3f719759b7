<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <script src="./config.js?v=<%=Math.random()%>"></script>
    <!-- 腾讯地图 -->


    <!--  <script charset="utf-8" src="https://map.qq.com/api/js?v=2.exp&key=HYRBZ-LHUC7-O3PXV-PNPAN-7VEOS-K6FQ5"></script>-->
    <!--    <script src="https://map.qq.com/api/gljs?v=1.exp&key=HYRBZ-LHUC7-O3PXV-PNPAN-7VEOS-K6FQ5"></script>-->
    <title> </title>
    <link rel="stylesheet" href="<%= BASE_URL %>static/index.<%= VUE_APP_INDEX_CSS_HASH %>.css" />
    <script>
        const mapKey = "HYRBZ-LHUC7-O3PXV-PNPAN-7VEOS-K6FQ5";
        let script = document.createElement('script');
        script.src = 'https://map.qq.com/api/gljs?v=1.exp&key=' + mapKey;
        document.getElementsByTagName('head')[0].appendChild(script)
        // 引入成功
        script.onload = function () {
            alert('js1资源已加载成功了')
        }
        // 引入失败
        script.onerror = function () {
            alert('j1s资源加载失败了')
        }
        let script2 = document.createElement('script');
        script2.src = 'https://map.qq.com/api/js?v=2.exp&key=' + mapKey;
        // document.head.appendChild(script2);
        document.getElementsByTagName('head')[0].appendChild(script2)
        // 引入成功
        script2.onload = function () {
            alert('js资源已加载成功了')
        }
        // 引入失败
        script2.onerror = function () {
            alert('js资源加载失败了')
        }

        document.addEventListener('DOMContentLoaded', function () {
            document.documentElement.style.fontSize =
                document.documentElement.clientWidth / 20 + 'px';
        });
        var coverSupport =
            'CSS' in window &&
            typeof CSS.supports === 'function' &&
            (CSS.supports('top: env(a)') || CSS.supports('top: constant(a)'));
        document.write(
            '<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' +
            (coverSupport ? ', viewport-fit=cover' : '') +
            '" />'
        );
    </script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/eruda"></script>
</head>

<body>
<noscript>
    <strong>Please enable JavaScript to continue.</strong>
</noscript>
<div id="app"></div>
<!-- built files will be auto injected -->
</body>

</html>
