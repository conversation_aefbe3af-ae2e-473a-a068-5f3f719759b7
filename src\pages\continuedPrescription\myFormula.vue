<template>
  <view class="page">
    <view class="page-container">
      <template v-if="prescriptionDatas.length > 0">
        <scroll-view
          class="content-container"
          scroll-y="true"
          @scrolltolower="getMore"
        >
          <template v-for="item in prescriptionDatas">
            <view class="list" @click="toDetails(item)">
              <view class="items title"
                ><text>患者:{{ item.patientName }}</text>
                <!-- <text>{{item.extendInfo.isRealPre==1?'处方':'处方建议'}}</text>
								<text v-if="item.extendInfo.isRealPre == 1">{{item.extendInfo.ywStatus == 3 ?'已支付':'待支付'}}</text> -->
              </view>
              <view class="items">医生：{{ item.docName }}</view>
              <view class="items">科室：{{ item.deptName }}</view>
              <view class="items text-max-length"
                >主诉：{{ item.recordsTitle }}</view
              >
              <view class="items text-max-length"
                >诊断：{{ item.diagNames }}</view
              >
              <!-- <view class="items text-max-length">医院：{{item.hosName}}</view> -->
              <view class="items">开方时间：{{ item.addTime }}</view>
            </view>
          </template>
          <!-- 加载更多 -->
          <view v-if="true">
            <uni-load-more :status="loadMore"></uni-load-more>
          </view>
        </scroll-view>
      </template>
      <view class="empty_list" v-else>
        <image src="/static/images/question/empty_toast.png" />
        <view> 暂无处方记录 </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getDocPatientPreList } from '@/api/base.js';

export default {
  data() {
    return {
      listQuery: {
        page: 1,
        limit: 10,
        docId: '',
        patientId: '',
      },
      total: 0,
      loadMore: 'loading',
      prescriptionDatas: [],
    };
  },
  onLoad() {
    let parmas = uni.getStorageSync('chatItem');
    this.listQuery.docId = parmas.docId;
    this.listQuery.patientId = parmas.patientId;
    this.getDocPatientPreList();
  },
  onPullDownRefresh() {},
  methods: {
    // 患者慢病续方处方列表
    async getDocPatientPreList() {
      this.loadMore = 'loading';
      let res = await getDocPatientPreList(this.listQuery);
      if (!res.data) return;
      if (this.listQuery.page > 1) {
        this.prescriptionDatas = [...this.prescriptionDatas, ...res.data.rows];
      } else {
        this.prescriptionDatas = res.data.rows;
      }
      this.total = res.data.total;
      if (this.prescriptionDatas.length >= this.total) {
        this.loadMore = 'noMore';
      } else {
        this.loadMore = 'more';
      }
    },
    toDetails(item) {
      let { patientId, docId } = this.listQuery;
      var businessId = item.businessId;
      uni.navigateTo({
        url:
          '/pages/continuedPrescription/myFormulaDetail?businessId=' +
          businessId +
          '&patientId=' +
          patientId +
          '&docId=' +
          docId,
      });
    },
    getMore() {
      if (this.prescriptionDatas.length >= this.total) return;
      this.listQuery.page += 1;
      this.getDocPatientPreList();
    },
  },
};
</script>

<style scoped lang="scss">
.page-container {
  box-sizing: border-box;
}

/* 筛选条件 */
.query-container {
  width: 100%;
  height: 84rpx;
  position: fixed;
  top: 44px;
  left: 0;
  z-index: 13;
}

.content-container {
  height: 100vh;
  padding: 32rpx 32rpx 0;
  box-sizing: border-box;
}

.list {
  width: 100%;
  background: #ffffff;
  box-shadow: 0rpx 8rpx 20rpx 0rpx rgba(0, 0, 0, 0.05);
  border-radius: 16rpx;
  padding: 28rpx 24rpx;
  box-sizing: border-box;
}

.list:not(:first-child) {
  margin-top: 24rpx;
}

.items {
  font-size: 28rpx;
  font-weight: 400;
  color: #6c6c6c;
  line-height: 28rpx;
}

.items:not(:first-child) {
  margin-top: 24rpx;
}

.title {
  display: flex;
  justify-content: space-between;
  font-weight: 600;
  color: #333333;
}

/* 待接诊颜色 */
.color1 {
  @include font_theme;
}

/* 接诊中颜色 */
.color1 {
  color: #ff5050;
}

/* 列表为空提示 */
.empty_list {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  font-weight: 400;
  color: rgba(158, 163, 173, 1);
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.empty_list image {
  margin-bottom: 40rpx;
  width: 386rpx;
  height: 324rpx;
}
</style>
