import { Toast } from "@/common/js/pay.js";
// 下载pdf
import { downPdf, downMergePDF } from "../../api/oss.js";

// 获取pdf
import { getPdf, getPdfs } from "../../api/order.js";

const lockPDF = async (id) => {
  try {
    // 获取pdf文件名称
    // let res = await getPdf(id);
    let res = await getPdfs(id);
    console.log("12312312123", res);
    if (!res.data) {
      Toast("没有电子处方");
      return Promise.reject("没有电子处方");
    }
    // oss下载
    // res = await downPdf(res.data);

    res = await downMergePDF(res.data.fileNames);

    // let url = res.data[0].url;
    let url = res.data.url;

    url = url.split("-internal").join("");

    return Promise.resolve(url);
  } catch (e) {
    console.log("下载pdf错误", e);
    return Promise.reject();
  }
};

export default lockPDF;
