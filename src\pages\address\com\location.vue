<template>
  <div class="container">
    <!-- 搜索 -->
    <div id="search">
      <input
        type="text"
        v-model="keyWord"
        placeholder="搜索地点"
        @confirm="confirm"
        confirm-type="search"
      />

      <button @click="confirm">搜索</button>
    </div>
    <!-- 搜索地点 -->
    <div class="search_list" v-if="cityList.length">
      <div
        class="item"
        v-for="item in cityList"
        :key="item.id"
        @click="setCity(item)"
      >
        <p class="label">{{ item.title }}</p>
        <p class="info">{{ item.address }}</p>
      </div>
    </div>

    <!-- 地图容器 -->
    <div id="map"></div>

    <!-- 列表 -->
    <div class="list">
      <div
        class="item"
        v-for="item in list"
        :key="item.id"
        @click="setItem(item)"
      >
        <p class="label">{{ item.title }}</p>
        <p class="info">{{ item.address }}</p>
      </div>
    </div>
  </div>
</template>

<script>
let map;

/**
 * 防抖函数 短时间内多次触发 只会执行一次
 * @param fn 要执行的方法
 * @param time 延迟时间
 * @returns Function
 */
const debounce = (fn, time = 500) => {
  let timer;

  return (...args) => {
    clearTimeout(timer);

    timer = setTimeout(fn.bind(null, ...args), time);
  };
};
import { Toast } from "@/common/js/pay.js";
const mapKey = require("@/common/request/config.js").mapKey;

export default {
  name: "Location",
  data() {
    return {
      list: [],
      cityList: [],
      keyWord: "",
      markerLayer: null,
    };
  },
  created() {
    this.getLocation();
  },
  methods: {
    async getIp() {
      let url = "https://apis.map.qq.com/ws/location/v1/ip";
      try {
        let { result } = await this.$jsonp(url, {
          key: mapKey,
          output: "jsonp",
        });

        let { location } = result;
        return location;
      } catch (error) {
        return {
          lat: 39.908802,
          lng: 116.397502,
        };
      }
    },
    async confirm() {
      if (!this.keyWord) return;
      let url = "https://apis.map.qq.com/ws/place/v1/suggestion/";
      let { data } = await this.$jsonp(url, {
        key: mapKey,
        page_size: 20,
        policy: 1,
        keyword: this.keyWord,
        output: "jsonp",
      });
      console.log(data,88)
      this.cityList = data||[];
    },
    setCity(item) {

      let {
        location: { lat, lng },
      } = item;
      this.cityList = [];
      map.setCenter(new TMap.LatLng(lat, lng));
      this.markerLayer.updateGeometries([
        {
          id: "center",
          position: map.getCenter(),
        },
      ]);
    },
    async getList(lat, lng) {
      var url = "https://apis.map.qq.com/ws/place/v1/explore";
      let boundary = `nearby(${lat},${lng},1000)`;

      let { data } = await this.$jsonp(url, {
        key: mapKey,
        boundary,
        page: 1,
        page_size: 20,
        output: "jsonp",
      });

      this.list = data;
    },
    setItem(item) {
      const { title, address, ad_info, location, city } = item;
      let obj = {
        name: title,
        address,
        latitude: location,
        city: ad_info?.city || city,
        item
      };
      this.$emit("seleItem", obj);
    },
    initMap(latitude, longitude) {
      let center = new TMap.LatLng(latitude, longitude);

      //初始化地图
      map = new TMap.Map("map", {
        zoom: 17, //设置地图缩放级别
        center, //设置地图中心点坐标
      });
      let markerGeo = {
        id: "center",
        position: map.getCenter(),
      };

      // 创建一个位于地图中心点的marker
      this.markerLayer = new TMap.MultiMarker({
        map,
        geometries: [markerGeo],
      });

      // 监听中心点变化事件，更新marker的位置
      map.on(
        "center_changed",
        debounce(() => {
          let pos = map.getCenter();
          markerGeo.position = pos;
          this.markerLayer.updateGeometries([markerGeo]);

          // 调用查询周边接口
          let { lat, lng } = pos;
          this.getList(lat, lng);
        })
      );
      setTimeout(uni.hideLoading, 300);
    },
    getLocation() {
      uni.showLoading();
      let locat = navigator.geolocation;
      console.log(">>>>>>>>",locat)
      locat.getCurrentPosition(
        ({ coords }) => {
          const { latitude, longitude } = coords;
          console.log("lat =>", latitude, "lng =>", longitude);
          this.initMap(latitude, longitude);
          this.getList(latitude, longitude);
        },
        async (err) => {
          // Toast('定位失败,请检查定位权限是否开启' + err.message);
          console.log(err)
          let { lat, lng } = await this.getIp();
          // 如果失败 使用默认定位
          this.initMap(lat, lng);
          this.getList(lat, lng);
        },
        {
          enableHighAccuracy: true,
          timeout: 5000,
          maximumAge: 0,
        }
      );
    },
  },
};
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}
.container {
  width: 100%;
  height: 100vh;
  overflow: hidden;

  #search {
    height: 90rpx;
    padding: 0 32rpx;
    @include flex;
    background: #f5f5f5;

    input {
      flex: 1;
      height: 50rpx;
      background-color: #fff;
      border-radius: 30rpx;
      font-size: 28rpx;
      padding-left: 32rpx;
    }

    button {
      padding-left: 32rpx;
      flex: none;
      font-size: 28rpx;
    }
  }

  .search_list {
    height: calc(100vh - 90rpx);
    overflow-y: scroll;
    padding: 32rpx;

    .item {
      font-size: 28rpx;
      padding: 14rpx 0;
      border-bottom: 1px solid #f5f5f5;

      .label {
        font-weight: bold;
      }

      .info {
        color: #999;
      }
    }
  }

  #map {
    height: 50vh;
  }

  .list {
    height: calc(50vh - 90rpx);
    overflow-y: scroll;
    padding: 32rpx;

    .item {
      font-size: 28rpx;
      padding: 14rpx 0;
      border-bottom: 1px solid #f5f5f5;

      .label {
        font-weight: bold;
      }

      .info {
        color: #999;
      }
    }
  }
}
</style>
