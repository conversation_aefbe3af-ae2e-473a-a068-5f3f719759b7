<script>
export default {
  globalData: {},
  data() {
    return {
      sysNum: "0",
      chatNum: "0"
    };
  },
  methods: {
    showDot() {
      let chatList = this.$store.getters.getChatList;
      let list1 = chatList.chatRecordList;
      this.sysNum = list ? String(list1.length) : "0";
      let list = this.$store.getters.getChatListDoc;
      this.chatNum = list
        ? String(list.filter((v) => v.id != "admin,admin").length)
        : "0";
      // 设置tabbar 红点标识
      if (this.chatNum != "0" || this.sysNum != "0") {
        uni.showTabBarRedDot({
          index: 1,
        });
      } else {
        uni.hideTabBarRedDot({
          index: 1
        });
      }
    },
  },
  onLaunch: function () {},
  onShow: function () {
    this.showDot();
  },
  onLoad(query) {},
  onUnload() {},
  onHide: function () {},
};
</script>

<style lang="scss">
@import "uview-ui/theme.scss";
@import "uview-ui/index.scss";
body {
  font-family: PingFangSC-Regular, PingFang SC;
}
/* 解决头条小程序组件内引入字体不生效的问题 */
/* #ifdef MP-TOUTIAO */
@font-face {
  font-family: uniicons;
  src: url("/static/uni.ttf");
}

uni-modal {  
  z-index: 99999999999999999 !important;
  .uni-modal{
    z-index: 99999999999999999 !important;
  }
}  
/* #endif */
body {
  width: 100%;
  height: 100vh;
  background: #fbfbfb;
}

.page {
  width: 100%;
  height: 100vh;
  // overflow-y: scroll;
  background: #f5f5f5;
  font-family: PingFangSC-Medium, PingFang SC;
}

/* tabbar样式 */
/deep/ .uni-tabbar {
  height: var(--window-bottom);
  z-index: 99999 !important;
}

/deep/.uni-tabbar__icon {
  // 图标
  //width: 50upx !important;
  //height: 50upx !important;
  //margin-top: 0 !important;
}

/deep/ .uni-tabbar__label {
  //margin-top: 0 !important;
}

/deep/.uni-tabbar__icon img {
  // 图标
  //width: 50upx !important;
  //height: 50upx !important;
  object-fit: cover;
}

/deep/.uni-tabbar__label {
  // 文字
  font-size: 24upx !important;
}

/* 医生自定义标签的显示 */
/* 白底的样式 */
.doc_label_fff {
  background: rgba(232, 246, 253, 1);
  border-radius: 18rpx;
  font-size: 22rpx;
  color: rgba(20, 160, 230, 1);
  margin-right: 20rpx;
  padding: 4rpx 10rpx;
}
/* 蓝底的样式 */
.doc_label_blue {
  background: #fff;
  border-radius: 18rpx;
  font-size: 22rpx;
  color: $k-theme-color;
  margin-right: 20rpx;
  padding: 0 10rpx;
}

/*
	 按钮样式初始化
	*/
button {
  outline: none;
  background: none;
  border: none;
}

button::after {
  border: none;
}
input {
  -webkit-appearance: none;
  -moz-appearance: none;
  -o-appearance: none;
  appearance: none;
}
/deep/input[type="search"] {
  -webkit-appearance: none !important;
}
/deep/ .uni-input-input[type="search"] {
  -webkit-appearance: none !important;
}
//touchstart事件 ios兼容问题
// * {
//   -webkit-touch-callout: none;
//   -webkit-user-select: none;
//   -khtml-user-select: none;
//   -moz-user-select: none;
//   -ms-user-select: none;
//   user-select: none;
// }
// input,
// textarea {
//   -webkit-user-select: auto !important;
//   -khtml-user-select: auto !important;
//   -moz-user-select: auto !important;
//   -ms-user-select: auto !important;
//   -o-user-select: auto !important;
//   user-select: auto !important;
// }
.waterMark {
  position: fixed;
  left: 0;
  top: -100px;
  right: -200px;
  bottom: -100px;
  z-index: 9999;
  pointer-events: none;
  background: rgba(245, 245, 245, 0.1);
  overflow: hidden;
  &-text {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    .text {
      font-size: 16px;
      width: auto;
      max-width: 30%;
      padding: 0 10px;
      transform: rotateZ(-40deg);
      color: rgba(222, 222, 222, 0.6);
      margin-bottom: 150px;
      white-space: nowrap;
      overflow: hidden;
    }
  }
}
.uni-tabbar {
  height: auto;
  padding-bottom: env(safe-area-inset-bottom) !important;
}
.chat_user_img{
  border-radius: 50% !important;
}
.user_text{
  .text_cont{
    border-radius: 24px 15px 0px 24px !important;
    background: rgba(255, 255, 255, 1) !important;
    padding: 15rpx 22rpx !important;
  }
}
.m-item{
  .chat_text{
    .text_cont{
      border-radius: 30px 24px 24px 0px !important;
      background: rgba(255, 255, 255, 1) !important;
      padding: 20rpx 40rpx !important;
    }
  }
}
.cus{
  display: flex;
  border-radius: 2px;
  background: rgba(247, 248, 252, 1);
  border: 0.5px solid rgba(204, 176, 255, 1);
  width: auto;
  padding: 0 10rpx;
  font-size: 10px !important;
  line-height: 20px;
}
.order-text{
  position: relative;
  img{
    position: absolute;
    left: -15px;
    top: 50%;
    transform: translateY(-50%);
  }
  .order-text-name{
    position: absolute;
    z-index: 99;
    left: -10px;
    top: 50%;
    transform: translateY(-50%);
    color: white;
    font-size: 10px;
    white-space: nowrap;
  }
}
.order-red{
  font-size: 18px !important;
  font-weight: 600;
  color: rgba(255, 87, 51, 1);
}
.o-2{
  position: absolute;
  right: 50px;
  top: 25px;
}
.sub-menu-class{
  background: white !important;
}
.content{
  position: relative;
  .char-count {
    position: absolute;
    right: 20rpx;
    bottom: 30rpx;
    font-size: 24rpx;
    color: #999;
    background: rgba(255, 255, 255, 0.9);
    padding: 0 10rpx;
    border-radius: 4rpx;
  }
}
</style>
