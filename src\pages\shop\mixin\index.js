/*
  Author: 王可 (<EMAIL>)
  index.js (c) 2021
  Desc: 多个提交需求页面 公用部分
  Created:  2021/11/12上午10:24:37
  Modified: 2021/11/12上午10:42:50
*/
import { getReceiptWay } from '@/api/order';
import {
  queryPrescriptionAdjustMoney,
  querySelfExtractionTips,
  queryToPayTips,
  queryLogisticsTips,
} from '@/api/shop.js';
import { getCityByLocation } from '../map';
import myJsTools from '@/common/js/myJsTools.js';

export default {
  data() {
    return {
      // 处方调剂费提示信息
      adjustMoneyTip: '',
      // 支付提示
      payTip: '',
      // 物流提示
      logisticsTip: '',
      // 自提提示
      ztTip: '',
      // 支付方式
      payList: [],
      // 图片列表
      fileList: [],
    };
  },
  created() {
    this.getLogisTip();
    this.getPayTip();
    this.getZtTip();
    this.getAdjustMoney();
  },
  methods: {
    // 药品拦截点击确定 把当前定位修改为已选择的收货地址
    async intercept() {
      const {
        latitude,
        longitude,
        addressArea,
        addressDetail,
        deliveryAddressDetail,
      } = this.address;
      let { province } = await getCityByLocation(latitude + ',' + longitude);
      let obj = {
        lat: latitude,
        lng: longitude,
        city: addressArea
          ? addressArea + '' + addressDetail
          : deliveryAddressDetail,
        province,
      };
      uni.setStorageSync('shop_city', obj);
      uni.switchTab({
        url: '/pages/shop/index',
      });
    },
    // 获取系统配置
    async getSystem(subjectId) {
      let { data } = await getReceiptWay({
        subjectId,
      });
      this.payList = data;
    },
    // 处方调剂费提示
    async getAdjustMoney() {
      let res = await queryPrescriptionAdjustMoney();
      this.adjustMoneyTip = res.data;
    },
    // 物流提示
    async getLogisTip() {
      let res = await queryLogisticsTips();
      this.logisticsTip = res.data;
    },
    // 自提提示
    async getZtTip() {
      let res = await querySelfExtractionTips();
      this.ztTip = res.data;
    },
    // 获取支付提示
    async getPayTip() {
      let res = await queryToPayTips();
      this.payTip = res.data;
    },
    // 去选择收货地址
    toAddress() {
      // 选择快递地址
      uni.navigateTo({
        url: '/pages/address/index?action=shop',
      });
    },
    // 跳转发票信息
    toInvoice() {
      // 抬头 类型
      const { invoiceTitle, invoiceType } = this.invoice;
      uni.navigateTo({
        url:
          '/pages/shop/invoice/index?invoiceTitle=' +
          invoiceTitle +
          '&invoiceType=' +
          invoiceType,
      });
    },
    // 图片预览
    preview(item) {
      uni.previewImage({
        urls: [item.url],
      });
    },
    // 选择患者
    toPatient() {
      uni.navigateTo({
        url: '/pages/personalCenter/patientManage/index?action=shop',
      });
    },
    // 移除图片
    delFile(n) {
      this.fileList.splice(n, 1);
    },
    // 选择图片
    cloosImg() {
      const that = this;
      uni.chooseImage({
        count: 1,
        success: function(res) {
          // 读取图片
          const file = res.tempFiles[0];
          myJsTools.setImgZip(file, (dataUrl) => {
            that.fileList.push({
              base64: dataUrl.split(',')[1],
              url: dataUrl,
              name: '',
            });
          });
        },
      });
    },
  },
};
