<template>
  <!-- 首页 -->
  <view>
    <img
      v-show="imgflag"
      class="home_img"
      ref="homeimg"
      :src="homeimg"
      alt=""
      @error="imageLoad"
    />

    <view class="home" :class="{ home_position: imgflag }">
      <!-- 搜索 -->
      <view class="search" @click="toPath('/pages/search/search')">
        <image src="/static/images/search/search.png" />
        <text>请输入您想要搜索的内容</text>
      </view>

      <!-- 菜单 -->
      <MENU :isAll="isAll" />

      <!-- 卡片 -->
      <CARD v-if="fristList.length" :list="fristList" :unit="unit" />

      <!-- 卡片更多 -->
      <view class="card_more" v-if="numList.length > 3" @click="showMore = true"
        >查看更多<uni-icons type="arrowdown" color="#999" size="14"></uni-icons
      ></view>

      <!-- 轮播 -->
      <SWIPER v-if="recList.length" :list="recList" />

      <!-- 我的医生 -->
      <DOCLIST :list="myList" v-if="myList.length" />

      <!-- 弹窗 -->
      <view class="zhe" v-show="showMore">
        <view class="pop">
          <view class="fiexd" @click="showMore = false">
            <uni-icons type="arrowdown" color="#666" size="26"></uni-icons>
          </view>
          <view class="warp">
            <!-- 卡片 -->
            <CARD v-if="numList.length" :list="numList" :unit="unit" />
          </view>
        </view>
      </view>
    </view>
  </view>
</template>
<script>
import MENU from "./com/menu.vue";
import CARD from "./com/card.vue";
import SWIPER from "./com/swiper.vue";
import DOCLIST from "./com/docList.vue";

import {
  randomDoctor,
  findDoctorByUserID,
  selectElectronicArticleListPage,
  getSysPlatformConfigByKeyList,
} from "@/api/base.js";

export default {
  name: "Home",
  components: {
    MENU,
    CARD,
    SWIPER,
    DOCLIST,
  },
  data() {
    return {
      homeimg: "",
      imgflag: true,
      // 我的医生
      myList: [],
      // 推荐医生
      recList: [],
      // 号条
      numList: [],
      // 前三条
      fristList: [],
      // 是否开启智能导诊
      isAll: false,
      // 处方有效期
      unit: 7,
      // 显示更多
      showMore: false,
    };
  },
  onLoad() {
    this.getMyDocList();
    this.getRecDoc();
    this.getConfig();
  },
  onShow() {
    // this.$store.commit("SET_CHATLIST", []);
    if (uni.getStorageSync("userId")) this.getNumList();
    this.getimgUrl();
  },
  methods: {
    imageLoad() {
      this.imgflag = false;
    },
    getimgUrl() {
      if (process.env.NODE_ENV === "development") return;
      this.$nextTick((_) => {
        let href = window.location.host;
        let index = href.indexOf("index.html");
        // let img = href.substring(0, index) + "home.png";
        let img = CONFIG_ENV.VUE_APP_FIRMID + "/patientHome.png";
        this.homeimg = img;
      });
    },
    // 查询配置
    async getConfig() {
      let { data } = await getSysPlatformConfigByKeyList([
        "display_intelligent_diagnosis",
        "prescription_indate",
        "PatientShowCoronavirusTest",
      ]);
      // console.log(data);
      data.forEach((v) => {
        if (v.configKey == "prescription_indate") {
          this.unit = v.configValue;
        }
        if (v.configKey == "display_intelligent_diagnosis") {
          this.isAll = v.configValue == 0 ? false : true;
        }
      });
    },
    // 跳转
    toPath(url) {
      uni.navigateTo({
        url,
      });
    },
    // 查询关注过的医生
    async getMyDocList() {
      let { data } = await findDoctorByUserID({
        openid: uni.getStorageSync("wxInfo").openId,
        userId: uni.getStorageSync("userId") || "",
      });
      this.myList = data;
    },
    // 获取推荐医生
    async getRecDoc() {
      let { data } = await randomDoctor({
        openid: uni.getStorageSync("wxInfo").openId,
        userId: uni.getStorageSync("userId") || "",
      });
      this.recList = data;
    },
    // 查询电子号条
    async getNumList() {
      let list = uni.getStorageSync("patientIdList") || [];
      if (!list.length) return;
      let { data } = await selectElectronicArticleListPage({
        patientIds: list,
      });
      this.numList = data;
      if (data.length > 3) {
        this.fristList = data.slice(0, 3);
      } else {
        this.fristList = data;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
page {
  background: #f5f5f5;
}
.home_img {
  width: 100%;
  height: 130rpx;
  vertical-align: top;
  // float: left;
}
.home {
  padding: 32rpx;
  background: #f5f5f5;
  border-radius: 32rpx 32rpx 0 0;

  .search {
    @include flex(left);
    height: 88upx;
    border-radius: 44upx;
    background-color: #fff;
    padding: 0 36upx;
    font-size: 32upx;
    color: $k-info-title;
    border: 2rpx solid #f5f5f5;

    image {
      width: 44upx;
      height: 44upx;
      margin-right: 18upx;
    }
  }

  .card {
    margin-top: 24rpx;
  }

  .card_more {
    height: 80rpx;
    @include flex;
    font-size: 28rpx;
    color: #999;
  }

  .swiper {
    margin-top: 24rpx;
  }

  .doc_list {
    margin-top: 24rpx;
  }

  .zhe {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.5);
    @include flex(right);
    flex-direction: column;

    .pop {
      width: 100vw;
      background-color: #f5f5f5;
      max-height: 70vh;
      overflow-y: scroll;
      padding: 0 0 120rpx;
      border-radius: 32rpx 32rpx 0 0;
      position: relative;
      @include flex;
      flex-direction: column;

      .warp {
        padding: 0 32rpx;
        width: 100%;
        box-sizing: border-box;
        flex: 1;
        overflow-y: scroll;

        .card {
          margin-top: 0rpx;
        }
      }

      .fiexd {
        height: 60rpx;
        @include flex;
        background-color: #f5f5f5;
        z-index: 1;
        flex: none;
      }
    }
  }
}
.home_position {
  position: absolute;
  top: 90rpx;
}
</style>
