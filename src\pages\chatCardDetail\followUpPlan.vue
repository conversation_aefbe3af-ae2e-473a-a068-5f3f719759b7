<template>
  <view class="fsSend">
    <view class="page-container">
      <view class="title">{{ plsInfo.planName }}</view>
      <view class="text-content">
        <text>{{ plsInfo.sendContent }}</text>
      </view>
      <view class="img-container" @click="previewImg(plsInfo.sendImg)">
        <image :src="plsInfo.sendImg" mode="widthFix"></image>
      </view>
      <view class="other">
        <view
          v-if="plsInfo.didOnlyId && plsInfo.didOnlyId != ''"
          class="list"
          @click="toLBlist"
        >
          <view>
            <text class="line"></text>
            <text class="list-title">量表</text>
          </view>
          <view class="list-btn">查看</view>
        </view>
        <view
          @click="openLineVisit"
          v-if="plsInfo.visitRealType && plsInfo.visitRealType != ''"
          class="list"
        >
          <view>
            <text class="line"></text>
            <text class="list-title">出诊表</text>
          </view>
          <view class="list-btn">查看</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { followupQueryPlanDetails } from '@/api/chatCardDetail';
import * as myJsTools from '@/common/js/myJsTools';
export default {
  data() {
    return {
      pageParam: {},
      plsInfo: {},
    };
  },
  onLoad(option) {
    let param = JSON.parse(option.param);
    this.pageParam = param;
  },
  onShow() {
    this.getPlanDetails();
  },
  methods: {
    // 预览图片
    previewImg(item) {
      myJsTools.previewImg(item);
    },
    // 跳转量表详情
    toLBlist() {
      let param = {
        sendId: this.plsInfo.sendId,
        patientId: this.pageParam.patientId,
        docId: this.pageParam.docId,
      };
      if (this.plsInfo.didAnswerStatus == 1) {
        // 已填写
        uni.navigateTo({
          url:
            '/pages/chatCardDetail/questionnaireRead?param=' +
            JSON.stringify(param),
        });
        return;
      }
      // 未填写
      uni.navigateTo({
        url:
          '/pages/chatCardDetail/questionnaire?action=' +
          'chatRoom' +
          '&param=' +
          JSON.stringify(param),
      });
    },
    async getPlanDetails() {
      let plsId = this.pageParam.plsId;
      let res = await followupQueryPlanDetails({
        id: plsId,
      });
      let plsInfo = res.data;
      if (plsInfo.sendImg) {
        myJsTools.downAndSaveImg(plsInfo.sendImg, (url) => {
          plsInfo.sendImg = url;
        });
      }
      this.plsInfo = plsInfo;
    },
    //打开出诊表
    openLineVisit() {
      if (this.plsInfo.visitRealType == '1') {
        let param = {
          docId: this.pageParam.docId,
        };
        uni.navigateTo({
          url: '/pages/chatCardDetail/lineVisit?param=' + JSON.stringify(param),
        });
      } else {
        uni.navigateTo({
          url:
            '/pages/chatCardDetail/offineVisit?docId=' +
            this.pageParam.docId +
            '&patientId=' +
            this.pageParam.patientId,
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.page-container {
  padding: 32rpx;
  background: #fff;
}

.page-container .title {
  text-align: center;
  font-size: 36rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: rgba(51, 51, 51, 1);
  line-height: 50rpx;
}

.text-content {
  margin-top: 24rpx;
  font-size: 28rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  line-height: 40rpx;
}

.img-container {
  margin-top: 24rpx;
  width: 100%;
}

image {
  width: 100%;
  height: auto;
}

.other {
  width: 100%;
  height: 98rpx;
  line-height: 98rpx;
}

.list {
  display: flex;
  justify-content: space-between;
  font-size: 32rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
}

.line {
  display: inline-block;
  width: 6rpx;
  height: 28rpx;
  border-radius: 2rpx;
  @include bg_theme;
}

.list-title {
  margin-left: 24rpx;
}

.list-btn {
  @include font_theme;
}
</style>
