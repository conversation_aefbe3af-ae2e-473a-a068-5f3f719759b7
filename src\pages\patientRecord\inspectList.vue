<template>
  <view class="pacs_list">
    <!-- 医生 -->
    <DOCINFO :info="detail" />

    <!-- 就诊 -->
    <view class="cont">
      <view class="cont_item">
        <text class="label">就诊人</text>
        <text>{{ detail.patientName }}</text>
      </view>

      <view class="cont_item">
        <text class="label">主诉</text>
        <text>{{ detail.recordsTitle }}</text>
      </view>

      <view class="cont_item">
        <text class="label">问诊时间</text>
        <text>{{ detail.signTime }}</text>
      </view>
    </view>

    <!-- 列表 -->
    <view class="list">
      <!-- 单个 -->
      <view
        class="list_item"
        v-for="item in list"
        :key="item.ppiId"
        @click="toDetail(item)"
      >
        <!-- 状态 -->
        <view class="item_title">
          <text>检查编号：{{ item.ppiCode }}</text>
          <text class="status">{{ item.statusName }}</text>
        </view>
        <!-- 诊断 -->
        <view class="item_diag"> 诊断：{{ item.ppiDiag }} </view>
        <!-- 检查项 -->
        <view class="item_project">
          <view class="project_name">检查项</view>
          <!-- 列表 -->
          <view
            class="project_item"
            v-for="(obj, index) in item.pacsListDVO"
            :key="index"
          >
            <text>1. {{ obj.positionName }} - {{ obj.itemName }}</text>
            <text>￥{{ obj.cost.toFixed(2) }}</text>
          </view>
        </view>
        <!-- 合计 -->
        <view class="project_price">
          <view
            >合计：<text>￥{{ item.cost.toFixed(2) }}</text></view
          >
          <view v-if="item.status > 1"
            >实际支付：<text>￥{{ item.payCost.toFixed(2) }}</text></view
          >
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getProPacsList } from '../../api/base.js';

import DOCINFO from './com/doc.vue';

export default {
  name: 'PacsList',
  components: {
    DOCINFO,
  },
  data() {
    return {
      regId: '',
      docInfo: {},
      detail: {},
      list: [],
    };
  },
  onLoad(option) {
    this.regId = option.regId;
  },
  onShow() {
    this.getInit();
  },
  methods: {
    async getInit() {
      let { data } = await getProPacsList({
        regId: this.regId,
      });
      this.list = data.pacsListMVO;
      this.detail = data;
    },
    // 点击卡片
    toDetail(item) {
      let url;
      let { paymentType, status, ppiId } = item;
      // 不存在支付方式
      if (!paymentType && status == 1) {
        url = '/pages/inspect/pacsOrder?id=' + ppiId;
        // 待支付 且 线上支付
      } else if (status == 1 && paymentType == 2) {
        url = '/pages/inspect/pay/pacs?id=' + ppiId;
      } else {
        url = '/pages/inspect/pacsDetails?id=' + ppiId;
      }
      uni.navigateTo({ url });
    },
  },
};
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}
page {
  background-color: #f5f5f5;
}
.pacs_list {
  padding: 24rpx 32rpx;

  .cont {
    margin-top: 24rpx;
    background-color: #fff;
    border-radius: 8rpx;
    padding: 10rpx 24rpx;

    &_item {
      @include flex(left);
      height: 60rpx;

      text {
        color: #333;
        font-size: 28rpx;
      }

      .label {
        flex: none;
        width: 180rpx;
      }
    }
  }

  .list {
    margin-top: 24rpx;

    .list_item {
      background-color: #fff;
      border-radius: 8rpx;
      margin-bottom: 24rpx;
      padding: 0 32rpx;
      font-size: 28rpx;
      color: #333;

      .item_title {
        height: 88rpx;
        @include flex(lr);
        font-weight: bold;

        .status {
          color: #ff3b30;
          font-weight: normal;
        }
      }

      .item_diag {
        height: 88rpx;
        @include flex(left);
        border-bottom: 1px solid #ebebeb;
        border-top: 1px solid #ebebeb;
      }

      .item_project {
        padding-top: 10rpx;

        .project_name {
          font-weight: bold;
          line-height: 50rpx;
        }

        .project_item {
          height: 60rpx;
          @include flex(lr);
        }
      }

      .project_price {
        line-height: 80rpx;
        @include flex(right);

        view {
          margin-left: 24rpx;
        }

        text {
          color: red;
        }
      }
    }
  }
}
</style>
