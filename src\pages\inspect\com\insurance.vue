<template>
  <!-- 医保类型 -->
  <view class="insurance" @click="click">
    <text> 医保类型 </text>

    <!-- 右侧选择 -->
    <view class="insurance_sele">
      <text class="text" v-show="currAct == -1">请选择</text>

      <text class="text" v-show="currAct == 0">自费</text>
      <text class="text" v-show="currAct == 1">医保</text>
      <uni-icons v-if="showRight" type="arrowright" color="#666" />
    </view>
  </view>
</template>

<script>
export default {
  name: 'Insurance',
  props: {
    currAct: {
      type: Number,
      default: -1,
    },
    showRight: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    click() {
      // 如果不可选
      if (!this.showRight) return;
      let that = this;
      uni.showActionSheet({
        itemList: ['自费', '医保'],
        success(res) {
          that.$emit('change', res.tapIndex);
        },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.insurance {
  background: #fff;
  border-radius: 8rpx;
  margin-top: 16rpx;
  padding: 0 32rpx;
  height: 88rpx;
  @include flex(lr);
  font-size: 28rpx;
  color: #333;

  &_sele {
    @include flex;

    .text {
      margin-right: 10rpx;
    }
  }
}
</style>
