<template>
  <view class="loading-container" v-if="show">
    <view
      :class="['loading-wrapper', { 'loading-fullscreen': fullscreen }]"
      :style="containerStyle"
    >
      <!-- 加载动画类型1：圆形旋转 -->
      <view v-if="type === 'circle'" class="loading-circle" :style="itemStyle">
        <view
          class="loading-circle-item"
          v-for="(item, index) in 12"
          :key="index"
          :style="{ 'animation-delay': index * 0.1 + 's' }"
        ></view>
      </view>

      <!-- 加载动画类型2：点跳动 -->
      <view v-if="type === 'dots'" class="loading-dots" :style="itemStyle">
        <view
          class="loading-dots-item"
          v-for="(item, index) in 3"
          :key="index"
          :style="{ 'animation-delay': index * 0.2 + 's' }"
        ></view>
      </view>

      <!-- 加载动画类型3：旋转 -->
      <view
        v-if="type === 'spinner'"
        class="loading-spinner"
        :style="itemStyle"
      >
        <view class="loading-spinner-inner"></view>
      </view>

      <!-- 加载文字 -->
      <!-- <text
        v-if="text"
        class="loading-text"
        :style="{ color: textColor, fontSize: textSize + 'px' }"
        >{{ text }}</text
      > -->
    </view>
  </view>
</template>

<script>
/**
 * Loading 加载动画
 * @description 用于展示页面加载中的状态，提供多种动画样式
 * @property {Boolean} show = [true|false] 是否显示加载动画
 * @property {String} type = [circle|dots|spinner] 加载动画类型
 *  @value circle 圆形旋转加载
 *  @value dots 点跳动加载
 *  @value spinner 旋转加载
 * @property {Boolean} fullscreen = [true|false] 是否全屏显示
 * @property {String} backgroundColor 加载背景颜色
 * @property {String} color 加载动画颜色
 * @property {Number} size 加载动画大小
 * @property {String} text 加载文字内容
 * @property {String} textColor 加载文字颜色
 * @property {Number} textSize 加载文字大小
 */
export default {
  name: 'Loading',
  props: {
    // 是否显示加载动画
    show: {
      type: Boolean,
      default: true,
    },
    // 加载动画类型：circle-圆形旋转, dots-点跳动, spinner-旋转
    type: {
      type: String,
      default: 'circle',
    },
    // 是否全屏显示
    fullscreen: {
      type: Boolean,
      default: false,
    },
    // 背景颜色
    backgroundColor: {
      type: String,
      default: 'rgba(0, 0, 0, 0.6)',
    },
    // 加载动画颜色
    color: {
      type: String,
      default: '#ffffff',
    },
    // 加载动画大小
    size: {
      type: Number,
      default: 36,
    },
    // 加载文字内容
    text: {
      type: String,
      default: '加载中...',
    },
    // 加载文字颜色
    textColor: {
      type: String,
      default: '#ffffff',
    },
    // 加载文字大小
    textSize: {
      type: Number,
      default: 14,
    },
  },
  computed: {
    containerStyle() {
      return {
        // backgroundColor: this.backgroundColor,
      }
    },
    itemStyle() {
      return {
        width: this.size + 'px',
        height: this.size + 'px',
        borderColor: this.color,
        color: this.color,
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.loading-container {
  position: relative;
  z-index: 9999;

  .loading-wrapper {
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 20px;
    border-radius: 8px;

    &.loading-fullscreen {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 10000;
      border-radius: 0;
    }
  }

  .loading-text {
    margin-top: 10px;
  }

  // 圆形旋转加载
  .loading-circle {
    position: relative;

    .loading-circle-item {
      position: absolute;
      width: 100%;
      height: 100%;
      left: 0;
      top: 0;

      &::before {
        content: '';
        display: block;
        width: 15%;
        height: 15%;
        background-color: currentColor;
        border-radius: 50%;
        position: absolute;
        top: 0;
        left: 50%;
        transform: translateX(-50%);
        animation: loading-circle 1.2s infinite ease-in-out both;
      }

      @for $i from 1 through 12 {
        &:nth-child(#{$i}) {
          transform: rotate(#{($i - 1) * 30}deg);
        }
      }
    }
  }

  // 点跳动加载
  .loading-dots {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;

    .loading-dots-item {
      width: 25%;
      height: 25%;
      background-color: currentColor;
      border-radius: 50%;
      margin: 0 5%;
      animation: loading-dots 1.4s infinite ease-in-out both;
    }
  }

  // 旋转加载
  .loading-spinner {
    position: relative;

    .loading-spinner-inner {
      width: 100%;
      height: 100%;
      border: 3px solid transparent;
      border-top-color: currentColor;
      border-radius: 50%;
      animation: loading-spinner 0.8s linear infinite;
    }
  }
}

@keyframes loading-circle {
  0%,
  80%,
  100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

@keyframes loading-dots {
  0%,
  80%,
  100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

@keyframes loading-spinner {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
