<template>
  <!-- 并且描述 -->
  <view class="desc">
    <view class="title">
      快速续方
    </view>
    <!-- 内容 -->
    <view class="desc_item">
      <text class="label">是否已在平台开方</text>
      <text>{{ item.isPlatformKf==1?"是":"否" }}</text>
    </view>

    <view class="desc_item">
      <text class="label">临床诊断</text>
    </view>
    <view class="diagNames">
      <text class="diagName" v-for="(diag, index) in item.quickPrescriptionDiags||[]" :key="index">{{diag.diagName}}</text>
    </view>
    <view class="desc_item">
      <text class="label">上次开方时间</text>
      <text>{{ getPreviousPrescriptionTime(item.previousPrescriptionTime) }}</text>
    </view>

    <view class="desc_item">
      <text class="label">药品</text>
      <text></text>
    </view>
    <view v-for="(drug,index) in item.quickPrescriptionDetailVOS||[]" :key="index">
      <view class="desc_item">
        <text class="label" style="color: black">{{ drug.drugName }}</text>
        <text>x{{ drug.quan }}</text>
      </view>
      <view class="desc_item">
        <text class="label">规格：{{drug.gg}}</text>
        <text>单价：{{drug.price}}</text>
      </view>
    </view>
    <!-- 图片 -->
    <view class="desc_img">
      <view class="label">
        处方图片
      </view>
      <!-- 图片容器 -->
      <view class="img_list" v-if="item.prescriptionImgUrls.length">
        <img
          class="img"
          v-for="(img, index) in item.prescriptionImgUrls"
          :src="img"
          :key="index"
          @click="_previewImage(img)"
        />
      </view>
      <!-- 空图片 -->
      <view class="img_empty" v-else> 暂无处方照片 </view>
    </view>
  </view>
</template>

<script>
import TITLE from "@/pages/inspect/com/itemTitle.vue";

export default {
  components: {
    TITLE,
  },
  props: {
    item: {
      type: Object,
      default: () => {},
    },
  },
  methods:{
    getPreviousPrescriptionTime(val){
      if(val){
        return val.split(" ")[0]
      }
    },
    _previewImage(image) {
      uni.previewImage({
        urls: [image],
      });
    },
  }
};
</script>

<style lang="scss" scoped>
.title{
  font-size: 15px !important;
  font-weight: bold;
  line-height: 40px;
  border-bottom: 0.5px solid rgba(217, 217, 217, 1);
}
.desc {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 10rpx 32rpx;
  box-shadow: 0px 1px 4px  rgba(121, 72, 234, 0.15);
  .desc_item {
    min-height: 70rpx;
    padding: 12rpx 0;
    @include flex(left);
    font-size: 12px;
    color: #333;
    box-sizing: border-box;
    color: rgba(102, 102, 102, 1) !important;
    justify-content: space-between;
    .label {
      width: 160rpx;
      flex: none;
      white-space: nowrap;
    }
  }

  .desc_img {
    padding-bottom: 10rpx;
    border-bottom: 1px solid #ebebeb;

    &:last-child {
      border-bottom: none;
    }

    .label {
      height: 84rpx;
      @include flex(left);
      font-size: 12px;
      color: rgba(102, 102, 102, 1);
      white-space: nowrap;
      text {
        color: #999999;
      }
    }

    .img_list {
      @include flex(left);
      flex-wrap: wrap;

      .img {
        width: 200rpx;
        height: 200rpx;
        border-radius: 8rpx;
        margin-right: 10rpx;
        margin-bottom: 10rpx;

        &:nth-child(3n) {
          margin-right: 0;
        }
      }
    }

    .img_empty {
      text-align: center;
      line-height: 100rpx;
      font-size: 28rpx;
      color: #666;
    }
  }
}
.diagName{
  font-size: 12px;
  display: inline-block;
  text-align: center;
  background: #f5f5f5;
  padding: 4px 10px;
  margin-right: 10px;
  margin-bottom: 10px;
}
.diagNames{
  display: flex;
  flex-wrap: wrap;
}
</style>
