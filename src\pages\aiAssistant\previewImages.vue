<template>
  <view class="preview-container">
    <!-- Full-page swiper for banner images -->
    <swiper
      class="banner-swiper"
      :indicator-dots="true"
      :autoplay="true"
      :interval="3000"
      :duration="500"
      indicator-color="rgba(255, 255, 255, 0.6)"
      indicator-active-color="#fff"
    >
      <swiper-item v-for="(item, index) in bannerList" :key="index">
        <image
          :src="item.imageUrl"
          class="banner-image"
          mode="aspectFill"
          @click="previewImage(index)"
        />
      </swiper-item>
    </swiper>

    <!-- Back button -->
    <view class="back-button" @click="goBack">
      <text class="back-icon">×</text>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      flag: 1,
      bannerList: [
        // {
        //   imageUrl: '/static/images/banner/AI医生团队概况.png',
        //   id: 2
        // },
        // {
        //   imageUrl: '/static/images/banner/医生介绍-AI分诊护士.png',
        //   id: 3
        // },
        // {
        //   imageUrl: '/static/images/banner/医生介绍.png',
        //   id: 4
        // },
        // {
        //   imageUrl: '/static/images/banner/医生介绍-AI 免疫医生.png',
        //   id: 5
        // },
        // {
        //   imageUrl: '/static/images/banner/医生介绍-AI 营养师.png',
        //   id: 6
        // },
      ]
    }
  },
  onLoad(options) {
    this.flag = options.flag || 1
    this.title = options.title || ''
    uni.setNavigationBarTitle({
      title: this.title
    })
    if(this.flag == 2){
      this.bannerList = [ 
        {
          imageUrl: '/static/images/banner/AI_consult_banner1.png',
          id: 1
        },
        {
          imageUrl: '/static/images/banner/AI_consult_banner2.png',
          id: 2
        },
        {
          imageUrl: '/static/images/banner/AI_consult_banner3.png',
          id: 3
        },
        {
          imageUrl: '/static/images/banner/AI_consult_banner4.png',
          id: 4
        },
        {
          imageUrl: '/static/images/banner/AI_consult_banner5.png',
          id: 5
        },
        {
          imageUrl: '/static/images/banner/AI_consult_banner6.png',
          id: 6
        },
        {
          imageUrl: '/static/images/banner/AI_consult_banner7.png',
          id: 7
        }
      ]
    }else{
      this.bannerList = [
        {
          imageUrl: '/static/images/banner/AI_banner1.png',
          id: 1
        },
        {
          imageUrl: '/static/images/banner/AI_banner2.png',
          id: 2
        },
        {
          imageUrl: '/static/images/banner/AI_banner3.png',
          id: 3
        },
        {
          imageUrl: '/static/images/banner/AI_banner4.png',
          id: 4
        },
        {
          imageUrl: '/static/images/banner/AI_banner5.png',
          id: 5
        },
        {
          imageUrl: '/static/images/banner/AI_banner6.png',
          id: 6
        },
        {
          imageUrl: '/static/images/banner/AI_banner7.png',
          id: 7
        }
      ]
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    previewImage(index) {
      // Enable full-screen preview of images when clicked
      // const urls = this.bannerList.map(item => item.imageUrl);
      // uni.previewImage({
      //   urls: urls,
      //   current: index
      // });
    }
  }
}
</script>

<style lang="scss" scoped>
.preview-container {
  width: 100vw;
  height: 100vh;
  position: relative;
  background-color: #000;
}

.banner-swiper {
  width: 100%;
  height: 100%;
}

.banner-image {
  width: 100%;
  height: 100%;
}

.back-button {
  position: absolute;
  top: 40rpx;
  left: 40rpx;
  width: 80rpx;
  height: 80rpx;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.back-icon {
  font-size: 50rpx;
  color: #fff;
}
</style>