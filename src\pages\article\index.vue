<template>
  <view class="body-view">
    <view class="fixed">
      <view class="search-container">
        <uni-search-bar
          placeholder="请输入您想要搜索的内容"
          cancelButton="none"
          @confirm="search"
          @input="changeInput"
        ></uni-search-bar>
      </view>
      <view class="scroll-list">
        <scroll-view
          class="top-menu-view"
          scroll-x="true"
          :scroll-left="scrollLeft"
          scroll-with-animation
        >
          <view
            class="menu-topic-view"
            v-for="(item, index) in labelList"
            :id="item.pkId"
            :key="index"
            :data-current="index"
            @click="swichMenu(index, item.pkId)"
          >
            <view
              :class="currentTab == index ? 'menu-topic-act' : 'menu-topic'"
            >
              <text class="menu-topic-text">{{ item.name }}</text>
            </view>
          </view>
        </scroll-view>
        <view class="moreData" @click="togglePage()">
          <u-icon name="list" color="#2979ff" size="50"></u-icon>
        </view>
      </view>
    </view>
    <!-- 内容 -->
    <swiper
      class="swiper-box-list"
      :current="currentTab"
      @change="swiperChange"
    >
      <swiper-item
        class="swiper-topic-list"
        v-for="(itm, index) in labelList"
        :key="itm.pkId"
      >
        <view class="swiper-item">
          <template v-if="artList.length">
            <scroll-view
              scroll-y="true"
              class="doc_list_box"
              @scrolltolower="getMore"
            >
              <view class="list_warp">
                <template v-for="(item, index) in artList">
                  <view
                    class="doc_box"
                    @click="goDocHomePage(item)"
                    :key="index"
                  >
                    <view class="con_box">
                      <view class="con_h1">{{ item.title }}</view>
                      <view>
                        <text style="font-size: 14px">{{
                          item.publisher
                        }}</text>
                        <text>{{ (item.publicTime||'').split(" ")[0] }}</text>
                      </view>
                    </view>
                    <image
                      :src="
                        item.headImgUrl
                          ? item.headImgUrl
                          : 'https://szhy.dchealthcloud.com/image/default.png'
                      "
                      mode="aspectFill"
                      class="header_img"
                    ></image>
                  </view>
                </template>
                <view v-if="isShowMore">
                  <uni-load-more :status="status"></uni-load-more>
                </view>
              </view>
            </scroll-view>
          </template>
          <view class="empty_list" v-else>
            <image src="https://szhy.dchealthcloud.com/image/box_empty.png" />
            <view> 暂无文章 </view>
          </view>
        </view>
      </swiper-item>
    </swiper>

    <!-- 遮罩 -->
    <view class="mask" v-show="isShowMask" @click="togglePage()"></view>
    <view class="sub-menu-class" v-show="isShowMask">
      <block v-for="(item, index) in labelList" :key="item.pkId">
        <view class="deptItem" @click="swichMenu(index, item.pkId)">
          {{ item.name }}
        </view>
      </block>
    </view>
  </view>
</template>

<script>
import { labelList, arcTagList, arcTagInfo } from "@/api/visit.js";
export default {
  data() {
    return {
      scrollInto: "",
      isShowWorkHosName: uni.getStorageSync("isShowWorkHosName") || false,
      labelListQuery: {
        name: "",
        page: 1,
        limit: 999,
      },
      // 文章列表
      listQuery: {
        publicStatus: 1,
        keywords: "",
        publicPort: 1,
        page: 1,
        labelId: null,
        limit: 999,
      },
      total: "",
      isShowMore: false,
      status: "loading",
      isShowMask: false,
      scrollLeft: 0,
      artList: [],
      currentTab: "",
      labelList: [],
    };
  },
  watch: {
    currentTab() {
      this.arcTagLists();
    },
  },
  onLoad() {
    this.getLabelList();
    this.arcTagLists();
  },
  onShow() {},
  onReady() {},
  // 下拉刷新
  onPullDownRefresh() {
    this.artList = [];
    this.labelListQuery.page = 1;
  },
  methods: {
    goDocHomePage(e) {
      let { articleId, docId } = e;
      uni.navigateTo({
        url: "/pages/article/detail?articleId=" + articleId,
      });
    },
    async getLabelList() {
      uni.stopPullDownRefresh();
      let {
        data: { rows, total },
      } = await labelList(this.labelListQuery);
      this.total = total;
      this.labelList = [{ pkId: null, name: "全部" }].concat(rows);
    },
    // 模糊搜索
    search(e) {
      this.artList = [];
      this.listQuery.page = 1;
      this.listQuery.keywords = e.value;
      this.arcTagLists();
    },
    changeInput(e) {
      this.search(e);
    },
    // 获取文章列表
    async arcTagLists() {
      let { data } = await arcTagList(this.listQuery);
      console.log(data);
      this.artList = data.rows;
      // let index = this.getIndex(data.rows, this.listQuery.labelId);
      // this.$nextTick(() => {
      //   this.swichMenu(index, this.listQuery.labelId);
      // });
    },
    getIndex(data, subjectCode) {
      if (subjectCode) {
        return data.findIndex((item) => item.pkId == subjectCode);
      } else {
        //点击查看更多
        this.currentTab = 0;
        this.labelListQuery.labelId = data?.length && data[0].labelId;
        return 0;
      }
    },
    // 加载更多
    getMore() {
      // this.isShowMore = true;
      // // 共几页
      // let num = Math.ceil(this.total / this.listQuery.limit);
      // if (this.listQuery.page < num) {
      //   this.listQuery.page += 1;
      //   this.arcTagLists();
      //   this.isShowMore = false;
      // } else {
      //   this.status = "noMore";
      // }
    },
    togglePage() {
      this.isShowMask = !this.isShowMask;
    },
    setScrollLeft() {
      const query = uni.createSelectorQuery();
      query.selectAll(".menu-topic-view").boundingClientRect();
      console.log("query", query);
      query.exec((res) => {
        console.log("res", res);
        let num = 0;
        for (let i = 0; i < this.currentTab; i++) {
          num += res[0][i].width;
        }
        this.scrollLeft = Math.ceil(num) - 50;
      });
    },
    swichMenu(index, code) {
      if (this.currentTab == index) {
        return;
      } else {
        this.artList = [];
        this.listQuery.labelId = code;
        this.listQuery.page = 1;
        this.isShowMask = false;
        this.currentTab = index;
        console.log("1111111111", index);
        this.setScrollLeft();
      }
    },
    swiperChange(e) {
      console.log(e);
      this.artList = [];
      this.currentTab = e.detail.current;
      let subjectCode = this.labelList.find(
        (item, index) => index == this.currentTab
      ).pkId;
      this.listQuery.labelId = subjectCode;
      this.listQuery.page = 1;
      this.isShowMask = false;
      this.setScrollLeft();
    },
  },
};
</script>

<style scoped lang="scss">
.empty_list {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  font-weight: 400;
  color: rgba(158, 163, 173, 1);
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.list_warp {
  padding: 32rpx;
}

.doc_list_box {
  // padding: 32rpx;
  box-sizing: border-box;
  overflow-y: auto;
  background: #fff;
  flex: 1;
  height: calc(100% - 20px);

  .doc_box {
    border-bottom: 1px solid #ebebeb;
    display: flex;
    justify-content: space-between;
    padding: 24rpx 20rpx;
    position: relative;
    border-radius: 10px;
    box-shadow: 0px 0px 5px #d3d3d3;
    margin-bottom: 15px;
    .header_img {
      width: 160rpx;
      height: 120rpx;
      border-radius: 8upx;
      margin-left: 30rpx;
    }
  }
  .con_box {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    flex: 1;
    view {
      display: flex;
      justify-content: space-between;
    }
    .con_h1 {
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 20px;
    }
  }
}

.scroll-list {
  position: relative;
  display: flex;
  height: 88rpx;
  font-size: 28rpx;
  border-bottom: 1px solid #ebebeb;
  background: #fbfbfb;

  .top-menu-view {
    display: flex;
    // position: fixed;
    z-index: 100;
    top: 88rpx;
    left: 0;
    white-space: nowrap;
    height: 88rpx;
    line-height: 88rpx;
    width: 90%;
    .menu-topic-view {
      display: inline-block;
      white-space: nowrap;
      height: 86rpx;
      position: relative;

      .menu-topic-text {
        font-size: 28rpx;
        color: #303133;
        padding: 30rpx;
        font-weight: 500;
      }

      .menu-topic-act .menu-topic-text {
        color: #14a0e6;
      }
    }
  }

  .moreData {
    right: 0;
    height: 88rpx;
    line-height: 88rpx;
    font-size: 28rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80rpx;
  }
}

.mask {
  z-index: 1;
  position: fixed;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0);
  transition: background-color 0.15s linear;
  background-color: rgba(0, 0, 0, 0.5);
}

.sub-menu-class {
  position: absolute;
  left: 0;
  right: 0;
  top: 100rpx;
  max-height: 550rpx;
  min-height: 500rpx;
  height: 500rpx;
  overflow-y: auto;
  background-color: #ffffff;
  padding: 0 20rpx;
  z-index: 11;
  box-shadow: 0 5px 5px rgba(0, 0, 0, 0.1);
  transition: transform 0.15s linear;
  transform: translate3d(0, calc(44px + 1rpx), 0);
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;

  .deptItem {
    width: 30%;
    height: 90rpx;
    line-height: 90rpx;
    text-align: center;
    margin-right: 20rpx;
    margin-bottom: 20rpx;
    display: block;
    background: #e7eaef;
    font-size: 28rpx;
    float: left;
    border-radius: 8rpx;
  }
}

/* 搜索框样式 */
.search-container {
  width: 100%;
  position: relative;
  z-index: 14;
}

/deep/.uni-searchbar {
  padding: 0 20upx;
  height: 88rpx;
  border-bottom: 2rpx solid #ebebeb;
}

/deep/.uni-searchbar__box {
  height: 88rpx;
  background: #ffffff !important;
  border-radius: 0px !important;
  border: none;
}

.search-container {
  position: relative;
  z-index: 15;
}

.body-view {
  width: 100%;
  position: relative;
  overflow: hidden;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.swiper-box-list {
  flex: 1;
  background-color: #ffffff;
  // height: calc(100vh - 251rpx);
  // margin-top: 176rpx;
  // margin-bottom: 30rpx;

  .swiper-topic-list {
    width: 100%;

    .swiper-item {
      height: 100%;

      .doc_list_box {
        height: calc(100% - 20px);
        overflow-y: auto;
      }
    }
  }
}

/* 隐藏滚动条，但依旧具备可以滚动的功能 */
/deep/.uni-scroll-view::-webkit-scrollbar {
  display: none;
}
</style>
<style scoped lang="scss">
.page {
  display: flex;
  flex-direction: column;
}

/* 搜索框样式 */
.search-container {
  width: 100%;
  position: relative;
  z-index: 14;
}

/deep/.uni-searchbar {
  padding: 0 20upx;
  height: 88rpx;
  border-bottom: 2rpx solid #ebebeb;
}

/deep/.uni-searchbar__box {
  height: 88rpx;
  background: #ffffff !important;
  border-radius: 0px !important;
  border: none;
}

.fixed {
  width: 100%;
  position: sticky;
  // position: fixed;
  height: 170rpx;
  flex: none;
  top: 0;
  z-index: 99999;
}

.search-container {
  position: relative;
  z-index: 15;
}

/* 筛选条件 */
.query-container {
  width: 100%;
  height: 84rpx;
}

/*标签*/
.little_label {
  width: 100%;

  text {
    display: inline-block;
    background: #e8f6fd;
    border-radius: 18rpx;
    font-size: 22rpx;
    color: #14a0e6;
    margin-right: 16upx;
    padding: 0 12rpx;
    height: 36rpx;
    line-height: 36upx;
    font-weight: 400;
  }
}

.doc_box .doc_box_right {
  flex: 1;
}

.doc_box .hint_box {
  display: flex;
}

.doc_box .hint_box view {
  width: 88rpx;
  height: 36rpx;
  border-radius: 8rpx;
  color: #ffffff;
  font-size: 24rpx;
  text-align: center;
  line-height: 36rpx;
}

.doc_box .hint_box view {
  margin-left: 10rpx;
}

.doc_box .hint_box view:first-child {
  margin-left: 0;
}

.doc_box .hint_box .one {
  background-color: #ffb541;
}

.doc_box .hint_box .two {
  background-color: #23b067;
}

.doc_box .hint_box .three {
  background-color: #14a0e6;
}

.doc_box .doc_box_right_top {
  display: flex;
  justify-content: space-between;
}

.doc_box .doc_box_right_name {
  font-size: 32rpx;
  font-weight: 600;
  color: rgba(68, 68, 68, 1);
}

.doc_box .doc_box_right_name view:last-child {
  font-weight: 500;
  font-size: 28rpx;
  color: #444444;
  padding-top: 10rpx;
}

// 描述
.doc_box_right_content {
  justify-content: flex-end;
  font-size: 24rpx;
  color: #a6aab2;
  padding: 6upx 0;
}

/* 转诊标志 */
.zz-logo {
  width: 88rpx;
  height: 36rpx;
  border-radius: 8rpx;
  color: #ffffff;
  background: #159f5c;
  font-size: 24rpx;
  text-align: center;
  line-height: 36rpx;
  position: absolute;
  top: 70rpx;
  right: 0;
}

/* 列表为空提示 */
.empty_list {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  font-weight: 400;
  color: rgba(158, 163, 173, 1);
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.dep {
  margin-right: 8rpx;
}

.empty_list image {
  margin-bottom: 40rpx;
  width: 386rpx;
  height: 324rpx;
}
</style>
