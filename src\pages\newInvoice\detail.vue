<template>
  <view class="detail-page">
    <!-- 已开票状态的操作按钮 -->
    <view class="action-btns" v-if="detail.status === '5'">
      <button class="view-btn" @click="previewInvoice">查看</button>
      <button class="download-btn" @click="showDownloadDialog">下载</button>
    </view>

    <!-- 驳回原因 -->
    <view class="reject-reason" v-if="detail.status === '2'">
      <text class="label">驳回理由：</text>
      <text class="content">{{detail.rejectReason}}</text>
    </view>

    <!-- 发票信息 -->
    <view class="invoice-card">
      <view class="card-title">{{detail.invoiceType==1?'普通发票':'增值税发票'}}</view>
      <view class="info-item">
        <text class="label">发票抬头</text>
        <text class="value">{{detail.titleType === '2'? detail.companyName :detail.invoiceTitle}}</text>
      </view>
      <view class="info-item">
        <text class="label">发票金额</text>
        <text class="value">¥{{detail.amount}}</text>
      </view>
      <view class="info-item">
        <text class="label">发票内容</text>
        <text class="value">{{detail.invoiceContentType==1?'明细':'大类'}}</text>
      </view>
      <view class="info-item">
        <text class="label">申请日期</text>
        <text class="value">{{detail.addTime}}</text>
      </view>
      <view class="info-item">
        <text class="label">订单编号</text>
        <text class="value">{{detail.orderNo}}</text>
      </view>
      <view class="info-item">
        <text class="label">状态</text>
        <text class="value">{{getStatusText(detail.status)}}</text>
      </view>
    </view>

    <!-- 状态时间线 -->
    <view class="timeline">
      <view
        class="timeline-item"
        v-for="(item, index) in detail.szInvoiceApproveRecordLogVos"
        :key="index"
      >
        <view class="time">{{item.operationTime}}</view>
        <view class="status">{{item.operationTypeName}}</view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="bottom-btns">
      <!-- 待开票/驳回状态按钮 -->
      <template v-if="showButtons">
        <button
          class="cancel-btn"
          @click="showCancelConfirm"
        >
          撤销申请
        </button>
        <button
          class="modify-btn"
          @click="handleModify"
        >
          修改申请
        </button>
      </template>

      <!-- 已开票状态按钮 -->
      <button
        v-if="detail.status === '5' && detail.reissueCount!=0 && isWithinSameMonth"
        class="reapply-btn"
        @click="showReapplyConfirm"
      >
        申请重开
      </button>
    </view>

    <!-- 撤销确认弹窗 -->
    <uni-popup ref="cancelPopup" type="dialog">
      <uni-popup-dialog
        type="info"
        title="提示"
        content="是否撤销开票申请"
        :before-close="true"
        @confirm="cancelApply"
        @close="hideCancelConfirm"
      />
    </uni-popup>

    <!-- 修改确认弹窗 -->
    <uni-popup ref="modifyPopup" type="dialog">
      <uni-popup-dialog
        type="info"
        title="提示"
        content="您的开票申请已提交，请确认是否修改"
        :before-close="true"
        @confirm="confirmModify"
        @close="hideModifyConfirm"
      />
    </uni-popup>

    <!-- 下载链接弹窗 -->
    <uni-popup ref="downloadPopup" type="dialog">
      <uni-popup-dialog
        type="info"
        title="下载链接"
        confirmText="复制"
        :before-close="true"
        @confirm="copyDownloadUrl"
        @close="hideDownloadDialog"
      >
        <text class="copyDownloadUrl" @click="copyDownloadUrl">
          {{detail.fileUrl}}
        </text>
      </uni-popup-dialog>
    </uni-popup>

    <!-- 重开确认弹窗 -->
    <uni-popup ref="reapplyPopup" type="dialog">
      <uni-popup-dialog
        type="info"
        title="提示"
        content="是否重开发票？"
        :before-close="true"
        @confirm="confirmReapply"
        @close="hideReapplyConfirm"
      />
    </uni-popup>
  </view>
</template>

<script>
import {szFinancialInvoiceDisable, szFinancialInvoiceInfo} from "../../api/base";
import uniPopupDialog from "../../components/uni-popup/uni-popup-dialog.vue";
import {getStatusText} from "../../utils/validate";


export default {
  components: {
    uniPopupDialog
  },
  data() {
    return {
      detail: {
        pkId: '',
        status: '', // pending-待开票 rejected-驳回 processing-开票中 completed-已开票
        rejectReason: '',
        invoiceType: '',
        title: '',
        amount: '',
        content: '',
        applyDate: '',
        orderNo: '',
        statusList: [],
        modifyCount: 0, // 修改次数
        fileUrl: '', // 下载链接
        hasReapplied: false, // 是否已申请过重开
      },
      currentDate: new Date(), // 添加当前日期
    }
  },

  computed: {
    // 是否显示底部按钮
    showButtons() {
      // 仅在【待开票、驳回】状态显示
      return ['1', '2'].includes(this.detail.status)
    },
    // 判断当前时间是否在开票日期的自然月内
    isWithinSameMonth() {
      if (!this.detail.issueDate) return false;
      const invoiceDate = new Date(this.detail.issueDate);
      return (
        this.currentDate.getFullYear() === invoiceDate.getFullYear() &&
        this.currentDate.getMonth() === invoiceDate.getMonth()
      );
    }
  },
  onShow(options) {
    this.currentDate = new Date();
    this.getDetail()
  },
  onLoad(options) {
    if(options.invoiceId) {
      this.detail.pkId = options.invoiceId
    }
  },

  methods: {
    getStatusText,
    // 获取详情
    async getDetail() {
      try {
        const res = await szFinancialInvoiceInfo({
          pkId: this.detail.pkId
        })
        this.detail = res.data
        this.detail.szInvoiceApproveRecordLogVos=this.detail.szInvoiceApproveRecordLogVos?.filter(v=>v.operationTypeName!='重开票待审核'&&v.operationTypeName!='待复核')
        this.$forceUpdate()
      } catch(e) {
        console.error(e)
      }
    },

    // 显示撤销确认弹窗
    showCancelConfirm() {
      this.$refs.cancelPopup.open()
    },

    // 隐藏撤销确认弹窗
    hideCancelConfirm() {
      this.$refs.cancelPopup.close()
    },

    // 撤销申请
    async cancelApply() {
      try {
        await szFinancialInvoiceDisable({
          pkId: this.detail.pkId
        })
        uni.showToast({
          title: '撤销成功',
          icon: 'success'
        })
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      } catch(e) {
        console.error(e)
      }
    },

    // 处理修改申请
    handleModify() {
      // 驳回状态直接跳转
      if(this.detail.status === '2') {
        this.goToModify()
        return
      }
      if(this.detail.modifyCount === 0){
        uni.showToast({
          title: '该申请已修改过,不可再次修改',
          icon: 'none'
        })
        return;
      }
      // 待开票状态且未修改过,显示确认弹窗
      if(this.detail.status === '1' ) {
        this.$refs.modifyPopup.open()
        return
      }
      // 其他情况提示
      uni.showToast({
        title: '该申请已修改过,不可再次修改',
        icon: 'none'
      })
    },

    // 隐藏修改确认弹窗
    hideModifyConfirm() {
      this.$refs.modifyPopup.close()
    },

    // 确认修改
    confirmModify() {
      this.hideModifyConfirm()
      this.goToModify()
    },

    // 跳转到修改页面
    goToModify() {
      this.$store.commit('setSelectedOrders', [])
      uni.navigateTo({
        url: `/pages/newInvoice/apply?id=${this.detail.pkId}&type=modify`
      })
    },

    // 预览发票
    previewInvoice() {
      uni.navigateTo({
        url: `/pages/newInvoice/preview?url=${encodeURIComponent(this.detail.fileUrl)}`
      })
    },

    // 显示下载弹窗
    showDownloadDialog() {
      this.$refs.downloadPopup.open()
    },

    // 隐藏下载弹窗
    hideDownloadDialog() {
      this.$refs.downloadPopup.close()
    },

    // 复制下载链接
    copyDownloadUrl() {
      uni.setClipboardData({
        data: this.detail.fileUrl,
        success: () => {
          uni.showToast({
            title: '复制成功',
            icon: 'success'
          })
        }
      })
      this.hideDownloadDialog()
    },

    // 显示重开确认弹窗
    showReapplyConfirm() {
      if (this.detail.hasReapplied) {
        uni.showToast({
          title: '该发票已申请过重开',
          icon: 'none'
        })
        return
      }
      if (this.detail.status === '5' && this.isWithinSameMonth) {
        this.$refs.reapplyPopup.open()
      } else {
        uni.showToast({
          title: '不在开票日期的自然月内，无法申请重开',
          icon: 'none'
        })
      }
    },

    // 隐藏重开确认弹窗
    hideReapplyConfirm() {
      this.$refs.reapplyPopup.close()
    },

    // 确认重开
    confirmReapply() {
      this.hideReapplyConfirm()
      uni.navigateTo({
        url: `/pages/newInvoice/apply?id=${this.detail.pkId}&type=reapply`
      })
    },
  }
}
</script>

<style lang="scss" scoped>
.detail-page {
  min-height: 100vh;
  background: #F5F6FA;
  padding: 20rpx 32rpx 120rpx;
}

.action-btns {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 0;
  margin-bottom: 20rpx;

  button {
    width: 160rpx;
    height: 64rpx;
    line-height: 64rpx;
    font-size: 28rpx;
    border-radius: 32rpx;
    display: inline-block;
    margin-left: 0px;
    margin-right: 0px;
  }

  .view-btn {
    background: #fff;
    border: 1px solid #6174ff;
    color: #6174ff;
  }

  .download-btn {
    background:linear-gradient(135deg, #516afb 0%, #859bff 99.97%);
    color: #fff;
  }
}

.reject-reason {
  background: #FFF2F2;
  padding: 24rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;

  .label {
    font-size: 28rpx;
    color: #FF4D4F;
  }

  .content {
    font-size: 26rpx;
    color: #FF4D4F;
  }
}

.invoice-card {
  background: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;

  .card-title {
    font-size: 32rpx;
    color: #333;
    font-weight: 500;
    margin-bottom: 24rpx;
    text-align: center;
  }

  .info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16rpx;

    .label {
      font-size: 28rpx;
      color: #666;
    }

    .value {
      font-size: 28rpx;
      color: #333;
    }
  }
}

.timeline {
  background: #fff;
  border-radius: 12rpx;
  padding: 24rpx;

  .timeline-item {
    position: relative;
    padding-left: 30rpx;
    margin-bottom: 32rpx;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 12rpx;
      height: 12rpx;
      background: linear-gradient(135deg, #516afb 0%, #859bff 99.97%);
      border-radius: 50%;
    }

    &:last-child {
      margin-bottom: 0;
    }

    .time {
      font-size: 24rpx;
      color: #999;
      margin-bottom: 8rpx;
    }

    .status {
      font-size: 28rpx;
      color: #333;
    }
  }
}

.bottom-btns {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 16rpx 32rpx;
  background: #fff;
  display: flex;
  justify-content: space-between;

  button {
    width: 48%;
    height: 88rpx;
    line-height: 88rpx;
    font-size: 32rpx;
    border-radius: 44rpx;
  }

  .cancel-btn {
    background: #fff;
    border: 1px solid #6174ff;
    color: #6174ff;
  }

  .modify-btn {
    background:linear-gradient(135deg, #516afb 0%, #859bff 99.97%);
    color: #fff;
  }

  .reapply-btn {
    width: 100%;
    height: 88rpx;
    line-height: 88rpx;
    background:linear-gradient(135deg, #516afb 0%, #859bff 99.97%);
    color: #fff;
    font-size: 32rpx;
    border-radius: 44rpx;
  }
}
.copyDownloadUrl{
  // 自动换行
  display: inline-block;
  color: #15a0e6;
  // 3行超出省略号
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp:5;
  -webkit-box-orient: vertical;
  word-break: break-all;
  padding: 20rpx;
}
</style>