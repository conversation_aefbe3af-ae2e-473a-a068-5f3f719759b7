<template>
  <!-- 检查 -->
  <view class="inspect">
    <!-- 顶部状态 -->
    <STATUS
      :queueNumber="detail.queueNumber"
      :num="num"
      :status="status"
      :time="signTime"
    />

    <!-- 详情部分 -->
    <view class="detail">
      <!-- 摘要 -->
      <INFO :detail="detail" />

      <!-- 时间线 -->
      <TIMELINE
        :addTime="detail.addTime"
        :signTime="detail.signTime"
        :payTime="detail.payTime"
        :verificationTime="detail.verificationTime"
        :uploadReportTime="detail.uploadReportTime"
        :invalidTime="detail.invalidTime"
        :returnTime="detail.returnTime"
      />

      <!-- 项目信息 -->
      <PROJECT
        :showBtn="detail.status == 5"
        :list="list"
        :price="detail.payCost"
      />

      <!-- 检查机构 -->
      <INPRCTION v-if="detail.dpoId" :dloId="detail.dloId" :pliId="ppiId" />

      <!-- 挂号信息 -->
      <ROW text="挂号信息" @click="toRegister" />
    </view>
  </view>
</template>

<script>
// 状态
import STATUS from './com/status.vue';
// 摘要
import INFO from './com/info.vue';
// 项目信息
import PROJECT from './com/project.vue';
// 检查中心
import INPRCTION from './com/inspection.vue';
// 行
import ROW from './com/row.vue';
// 时间线
import TIMELINE from './com/timeLine.vue';

import { getProPacsAllInfoByID, myPacsQueueNum } from '@/api/inspect';

export default {
  components: {
    STATUS,
    ROW,
    INFO,
    PROJECT,
    INPRCTION,
    TIMELINE,
  },
  data() {
    return {
      ppiId: '',
      status: -1,
      // 预约时间
      time: '',
      // 项目
      list: [],
      // 详情
      detail: {},
      // 签到时间
      signTime: '',
      // 排队号
      num: '',
    };
  },
  onLoad(opt) {
    this.ppiId = opt.id;
    this.getDetail();
  },
  methods: {
    // 获取详情
    async getDetail() {
      let { data } = await getProPacsAllInfoByID(this.ppiId);
      let {
        pacsListDVO,
        appointDate,
        appointEndTime,
        appointStartTime,
        signStartTime,
        signEndTime,
        status,
      } = data;
      // 预约时间
      data.time = appointDate
        ? appointDate +
          ' ' +
          appointStartTime.slice(0, 5) +
          '-' +
          appointEndTime.slice(0, 5)
        : '';
      // 签到时间
      this.signTime = signStartTime
        ? signStartTime.slice(0, 16) + '-' + signEndTime.slice(11, 16)
        : '';

      // 处理项目报告
      // pacsListDVO.forEach((v) => {
      //   if (v.reportOssUrl) v.reportOssUrl = JSON.parse(v.reportOssUrl);
      // });

      // 项目
      this.list = pacsListDVO;
      // 详情
      this.detail = data;
      // 状态
      this.status = status;
      if (status == 3) this.getNum();
    },
    // 查询排号
    async getNum() {
      let { data } = await myPacsQueueNum(this.ppiId);
      if (data) this.num = data;
    },
    // 挂号详情
    toRegister() {
      let { hosId, regId } = this.detail;
      uni.setStorageSync('hosId', hosId);
      let url = '/pages/personalCenter/diagnosisRecord/detail?id=' + regId;
      uni.redirectTo({ url });
    },
  },
};
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}
.inspect {
  .detail {
    padding: 32rpx;
    margin-top: -240rpx;
  }
}
</style>
