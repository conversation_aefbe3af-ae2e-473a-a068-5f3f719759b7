<template>
  <!-- 门诊加号 -->
  <view class="chat_diagnosis">
    <!-- 头像 -->
    <image
      @click="head"
      :src="imgUrl || '/static/images/docHead.png'"
      mode="aspectFill"
      class="chat_user_img"
    />
    <!-- 卡片 -->
    <view class="diagnosis_content" @click="click">
      <!-- 标题 -->
      <view class="content_title">门诊加号</view>
      <!-- 内容 -->
      <view class="content_cont">
        <!-- 左侧图标 -->
        <image src="/static/images/chat/czb.png" class="cont_icon" />
        <!-- 右侧文案 -->
        <view class="cont_right">
          <!-- 诊断 -->
          <view class="cont_right_title">医师：{{ docName }}</view>
          <!-- 描述 -->
          <view class="cont_right_info">
            <text>选择您要问诊的时间进行预约</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    // 头像
    imgUrl: {
      type: String,
      default: '',
    },
    // 医师
    docName: {
      type: String,
      default: '',
    },
  },
  methods: {
    click() {
      this.$emit('click');
    },
    head() {
      this.$emit('head');
    },
  },
};
</script>

<style scoped lang="scss">
.chat_diagnosis {
  max-width: 600upx;
  box-sizing: border-box;
  @include flex(left);
  padding-bottom: 30upx;
  align-items: flex-start;

  // 头像
  .chat_user_img {
    width: 64upx;
    height: 64upx;
    border-radius: 4upx;
    margin-right: 16upx;
    flex: none;
  }

  // 内容
  .diagnosis_content {
    width: 516upx;
    padding: 24upx;
    background-color: #fff;
    color: #333;
    border: 1px solid #ececec;
    border-radius: 8upx 32upx 32upx 32upx;

    // 标题
    .content_title {
      font-size: 28upx;
      color: $k-title;
      font-weight: bold;
    }

    // 底部内容
    .content_cont {
      @include flex(left);
      align-items: flex-start;
      margin-top: 20upx;

      // 左侧图标
      .cont_icon {
        width: 80upx;
        height: 80upx;
        border-radius: 8upx;
        margin-right: 24upx;
        flex: none;
      }

      // 右侧文案
      .cont_right {
        flex: 1;
        min-height: 80upx;
        @include flex(lr);
        flex-direction: column;
        align-items: stretch;

        // 标题
        .cont_right_title {
          font-size: 28upx;
          color: $k-title;
          font-weight: bold;
        }

        // 描述
        .cont_right_info {
          font-size: 24upx;
          color: $k-info-title;
        }
      }
    }
  }
}
</style>
