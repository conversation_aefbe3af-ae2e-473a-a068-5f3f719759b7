<template>
  <view class="address">
    <view class="empty" v-if="addressList.length == 0">
      <image src="../../static/images/index/address_emity.png" mode=""></image>
      <view>您还没有收货地址，赶快添加一个吧</view>
    </view>
    <view class="addressList">
      <uni-swipe-action>
        <uni-swipe-action-item
          :right-options="options"
          v-for="(item, index) in addressList"
          :key="index"
          @click="bindClick($event, item)"
          @change="swipeChange($event, index)"
        >
          <view class="addressInfo" @click.stop="seletAddress(item)">
            <image
              src="../../static/images/index/default.png"
              mode=""
              class="defaultImg"
              v-if="item.isDefault == '1'"
            ></image>
            <view class="leftView">
              <view class="addressInfoDetail">
                {{ item.addressArea }}
              </view>
              <view
                class="addressInfoDetail"
                style="font-size: 12px; color: #333333; font-weight: bolder"
              >
                {{ item.addressDetail }}
              </view>
              <view class="userInfo">
                <text>{{ item.deliveryName }}</text>
                <text class="telNo">{{ item.telNo }}</text>
                <text v-if="item.isDefault == '1'" class="label">默认</text>
                <!--                <text v-if="item.lableName" class="label">{{-->
                <!--                  item.lableName-->
                <!--                }}</text>-->
              </view>
            </view>
            <view class="edit" @click.stop="editorAddress(item)">
              <image
                src="../../static/images/index/画板 <EMAIL>"
                class="editAddress"
              ></image>
            </view>
          </view>
        </uni-swipe-action-item>
      </uni-swipe-action>
    </view>
    <view class="addBtn" @click="newAddress">
      <button>新建地址</button>
    </view>
  </view>
</template>

<script>
import { Toast } from "@/common/js/pay.js";
import {
  dicdeliveryaddressDelete,
  findAddressByUserId,
  updataIsDefault,
} from "@/api/address";
import { updateOrderDelivery } from "@/api/order";

import { uniSwipeAction, uniSwipeActionItem } from "@dcloudio/uni-ui";
export default {
  components: {
    uniSwipeAction,
    uniSwipeActionItem,
  },
  data() {
    return {
      addressList: [],
      options: [
        {
          text: "设为默认",
          style: {
            backgroundColor: "#EBEBEB",
            color: "#999999",
          },
        },
        {
          text: "删除",
          style: {
            backgroundColor: "#FF5050",
          },
        },
      ],
      jumpStatus: "",
      businessId: "",
      action: "", // 记录从哪个页面跳入，没有值为从我的-设置-收货地址进入
      // 快捷开方 订单号
      orderNo: "",
    };
  },
  onLoad(option) {
    this.jumpStatus = option.jumpStatus;
    this.businessId = option.businessId;
    if (option.action) {
      this.orderNo = option.orderNo;
      // switchAddress  处方详情-选择药店页面，切换收货地址进入
      this.action = option.action;
    }
  },
  onShow() {
    this.getAddressList();
  },
  onBackPress(e) {
    if (e.from == "backbutton") {
      if (this.action == "switchAddress" || this.action == "emptyAddress") {
        uni.setStorageSync("action", "switchAddress");
      }
      return true; //阻止默认返回行为
    }
  },

  methods: {
    // 点击收货地址
    async seletAddress(item) {
      // 没有action
      if (!this.action) {
        this.editorAddress(item);
        return;
      }

      // 快捷开方使用
      if (this.action == "quick") {
        let obj = {
          orderNo: this.orderNo,
          deliveryTelNo: item.telNo,
          deliveryName: item.deliveryName,
          deliveryAddressDetail: item.addressArea + "" + item.addressDetail,
        };
        await updateOrderDelivery(obj);

        // 跳转业务详情
        uni.redirectTo({
          url: "/pages/prescription/preDetail?businessId=" + this.businessId,
        });
        return;
      }

      // 购物车使用 businessId
      if (this.action == "shop") {
        uni.setStorageSync("shop_address", item);
        uni.navigateBack();
        return;
      }

      if (this.action == "switchAddress") {
        // 赋值地址  nowAddress.address= 当前点击的收货地址
        let nowAddress = uni.getStorageSync("nowAddress");
        if (nowAddress) {
          nowAddress.address = item;
          uni.setStorageSync("nowAddress", nowAddress);
          uni.setStorageSync("action", "switchAddress");
          uni.navigateBack({
            delta: 1, //想要返回的层级
          });
        } else {
          let nowrObj = {};
          nowrObj.address = item;
          uni.setStorageSync("nowAddress", nowrObj);
          uni.setStorageSync("action", "switchAddress");
          uni.navigateBack({
            delta: 1, //想要返回的层级
          });
        }
        return;
      }

      if (this.action == "invoice") {
        uni.setStorageSync("invoice_address", item);
        uni.navigateBack();
        return;
      }

      if (this.action == "emptyAddress") {
        // 赋值地址  nowAddress.address= 当前点击的收货地址
        let nowAddress = uni.getStorageSync("nowAddress");
        if (nowAddress) {
          nowAddress.address = item;
          uni.setStorageSync("nowAddress", nowAddress);
          uni.redirectTo({
            url: "/pages/address/hospitalAddress?businessId=" + this.businessId,
          });
        } else {
          let nowrObj = {};
          nowrObj.address = item;
          uni.setStorageSync("nowAddress", nowrObj);
          uni.redirectTo({
            url: "/pages/address/hospitalAddress?businessId=" + this.businessId,
          });
        }
        return;
      }
    },
    async bindClick(e, item) {
      let _this = this;
      if (e.index == 0) {
        // 设为默认
        await updataIsDefault({
          ddaId: item.ddaId,
          isDefault: "1",
          userId: uni.getStorageSync("userId"),
        });
        this.getAddressList();
      } else if (e.index == 1) {
        //    删除
        uni.showModal({
          title: "提示",
          content: "确定删除吗？",
          success(res) {
            if (res.confirm) {
              dicdeliveryaddressDelete({
                ddaId: item.ddaId,
              }).then((res) => {
                if (res.code == 20000) {
                  Toast("删除成功");
                  _this.getAddressList();
                }
              });
            }
          },
        });
      }
    },
    swipeChange(e, index) {},
    //新建收货地址
    newAddress() {
      if (this.action == "switchAddress") {
        // 处方详情-购药切换地址进入
        uni.navigateTo({
          url:
            "/pages/address/newAddress?action=" +
            "switchAddress&businessId=" +
            this.businessId,
        });
      } else if (this.action == "shop") {
        uni.navigateTo({
          url: "/pages/address/newAddress?action=shop",
        });
      } else {
        uni.navigateTo({
          url: "/pages/address/newAddress",
        });
      }
    },

    // 编辑收货地址
    editorAddress(evt) {
      uni.navigateTo({
        url: "/pages/address/editorAddress?info=" + JSON.stringify(evt),
      });
    },

    //获取收货地址列表
    async getAddressList() {
      let res = await findAddressByUserId({
        userId: uni.getStorageSync("userId"),
        isDefaul: "",
      });
      this.addressList = res.data;
    },
  },
};
</script>

<style lang="scss" scoped>
.empty {
  text-align: center;
  margin-top: 100px;

  image {
    width: 386rpx;
    height: 324;
    margin-bottom: 40rpx;
    margin: 0 auto;
  }

  view {
    text-align: center;
    color: #333333;
    font-size: 28rpx;
  }
}
.address {
  padding-top: 24rpx;
  padding-bottom: 98rpx;
}
.addBtn {
  width: 100%;
  height: 108upx;
  padding: 0 32upx;
  position: fixed;
  bottom: 0;
  left: 0;
  background-color: #fff;
  border-radius: 16upx 16upx 0 0;
  @include flex;
  box-sizing: border-box;

  button {
    width: 100%;
    height: 84upx;
    border-radius: 406px;
    background: linear-gradient(135deg, #516afb 0%, #859bff 99.97%);
    @include flex;
    color: #fff;
    font-size: 32upx;
  }
}

// 地址列表
.addressList {
  background: #ffffff;

  .addressInfo {
    border-bottom: 1rpx solid #ebebeb;
    padding: 32rpx;
    width: 100%;
    display: flex;
    align-items: center;
    position: relative;
    align-items: center;

    .telNo {
      margin-left: 32rpx;
    }

    .label {
      margin-left: 32rpx;
      display: inline-block;
      padding: 0 8rpx;
      height: 32rpx;
      background: #e3deff;
      border-radius: 2rpx;
      font-size: 18rpx;
      color: #836aff;
      line-height: 32rpx;
      text-align: center;
    }

    .addressInfoDetail {
      color: #999999;
      font-size: 22rpx;
      margin-top: 10rpx;
    }

    .leftView {
      flex: 1;
    }

    .edit {
      padding: 10upx 0 10upx 30upx;
      flex: none;
    }
  }

  .userInfo {
    color: #333333;
    font-size: 24rpx;
    margin-top: 4px;
  }

  .editAddress {
    width: 32rpx;
    height: 32rpx;
  }

  .defaultImg {
    position: absolute;
    right: 0;
    top: 0;
    width: 48rpx;
    height: 48rpx;
  }
}
</style>
