<template>
  <!-- 修改手机号 -->
  <view class="page">
    <view class="page-container">
      <view class="patient-info">
        <view class="input-box">
          <text>手机号</text>
          <input
            type="number"
            :value="telNo"
            v-model="telNo"
            placeholder="请输入手机号"
          />
        </view>
        <view class="input-box">
          <text>验证码</text>
          <input
            type="number"
            :value="telCode"
            v-model="telCode"
            placeholder="请输入验证码"
            maxlength="6"
          />
          <view class="sendBtn" @click="sendCode">
            {{ codeinfo.btnText }}
          </view>
        </view>
      </view>
    </view>
    <view class="add-patient">
      <button @click="confirmEditor">确认修改</button>
    </view>
  </view>
</template>

<script>
import regex from '@/common/js/regex.js';
import { sendCaptcha, cationCode } from '@/api/user.js';
export default {
  data() {
    return {
      telNo: '',
      telCode: '',
      codeinfo: {
        sendAuthCode: true,
        auth_time: '',
        btnText: '发送验证码',
      },
    };
  },
  methods: {
    // 获取验证码
    async sendCode() {
      let telNo = this.telNo;
      if (regex.telBlur(telNo)) {
        await sendCaptcha({
          telNo: telNo,
        });
        // 发送验证码,倒数读秒
        this.codeinfo.auth_time = 60;
        var auth_timetimer = setInterval(() => {
          this.codeinfo.auth_time--;
          this.codeinfo.btnText = this.codeinfo.auth_time + 's后重新发送';
          if (this.codeinfo.auth_time <= 0) {
            this.codeinfo.sendAuthCode = true;
            this.codeinfo.btnText = '重新发送';
            clearInterval(auth_timetimer);
          }
        }, 1000);
      }
    },
    // 确认修改，验证验证码
    confirmEditor() {
      if (regex.telBlur(this.telNo) && regex.codeBlur(this.telCode)) {
        this.cationTelCode();
      }
    },
    // 校验手机验证码
    async cationTelCode() {
      let telNo = this.telNo;
      var para = {
        captcha: this.telCode,
        telNo: telNo,
      };
      await cationCode(para);
      let pages = getCurrentPages();
      console.log('pages',pages,pages[pages.length - 2])
      uni.setStorageSync('newTelNo', telNo)
      let prevPage = pages[pages.length - 2];
      prevPage.$vm._data.patientInfo.telNo = telNo;
      setTimeout(() => {
        uni.navigateBack({
          delta: 1, //想要返回的层级
        });
      }, 1000);
    },
  },
};
</script>

<style scoped lang="scss">
.page-container {
  padding: 24rpx 32rpx 0;
  box-sizing: border-box;
  overflow-y: auto;
}

.patient-info {
  background: #ffffff;
  padding: 0 32rpx;
  border-radius: 8rpx;
}

.input-box {
  width: 100%;
  display: flex;
  height: 92rpx;
  border-radius: 4rpx;
  box-sizing: border-box;
  line-height: 92rpx;
  border-bottom: 1rpx solid #ebebeb;
}

.input-box text {
  display: inline-block;
  width: 112rpx;
  flex: none;
  font-size: 28rpx;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
}

.input-box .icon {
  width: 40rpx;
}

.input-box input {
  flex: 1;
  height: 100%;
  line-height: 100%;
  padding-left: 40rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: rgba(102, 102, 102, 1);
}

.sendBtn {
  flex: none;
  width: 188rpx;
  height: 92rpx;
  line-height: 92rpx;
  font-size: 28rpx;
  font-weight: 400;
  @include font_theme;
  padding: 0;
  text-align: right;
}

/* 添加就诊人按钮 */
.add-patient {
  width: 100%;
  box-sizing: border-box;
  position: fixed;
  left: 0;
  bottom: 32rpx;
  padding: 0 32rpx;
}

.add-patient button {
  display: block;
  width: 100%;
  height: 92rpx;
  @include bg_theme;
  border-radius: 46rpx;
  color: #fff;
  font-size: 32rpx;
  line-height: 92rpx;
}
</style>
