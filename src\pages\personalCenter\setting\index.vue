<template>
  <view class="page">
    <view class="page-container">
      <view class="list-box">
        <template v-for="(item, index) in list1">
          <view class="list" @click="openPageDetail(item)" :key="index">
            <view style="align-items: center; display: flex; font-size: 14px"
              >{{ item.title }}
              <img
                v-if="index == 0"
                style="width: 16px; height: 16px; margin-left: 5px"
                src="../../../static/地址管理@2x.png"
                alt=""
              />
            </view>
            <uni-icons type="arrowright" color="#999" size="20"></uni-icons>
          </view>
        </template>
        <template v-for="(item, index) in list2">
          <view class="list" @click="openPageDetail(item)" :key="index">
            <text>{{ item.title }}</text>
            <uni-icons type="arrowright" color="#999" size="20"></uni-icons>
          </view>
        </template>
      </view>
      <!--      <view class="list-box">-->
      <!--        <template v-for="(item, index) in list2">-->
      <!--          <view class="list" @click="openPageDetail(item)" :key="index">-->
      <!--            <text>{{ item.title }}</text>-->
      <!--            <uni-icons type="arrowright" color="#999" size="20"></uni-icons>-->
      <!--          </view>-->
      <!--        </template>-->
      <!--      </view>-->
    </view>

    <!-- 打印入口 -->
    <button class="but_hide" @click="openCode">开发者模式</button>
  </view>
</template>

<script>
import { Toast } from "@/common/js/pay.js";
import { findVisitAgreement } from "@/api/user.js";
export default {
  data() {
    return {
      code: 0,
      list1: [
        {
          title: "我的收货地址",
          navigateToUrl: "/pages/address/index",
        },
        // {
        //   title: "健康档案",
        //   navigateToUrl: "",
        // },
      ],
      list2: [
        {
          title: "关于我们",
          navigateToUrl: "/pages/setting/about",
        },
        // {
        //   title: "使用帮助",
        //   navigateToUrl: "",
        // },
        {
          title: "隐私协议",
          noLogin: true,
          navigateToUrl: "/pages/protocol/index?type=2",
        },
        {
          title: "平台用户注册及服务协议",
          noLogin: true,
          navigateToUrl: "/pages/protocol/index?type=10",
        },
      ],
    };
  },
  onShow() {
    this.code = 0;
    this.findVisitAgreement();
  },
  methods: {
    async findVisitAgreement() {
      const res = await findVisitAgreement({ agreementType: "11" });
      if (res.data) {
        this.agreementContent = res.data.agreementContent || "";
        if (this.list2.find((v) => v.agreementType == "11")) {
          return;
        }
        this.list2.push({
          title: res.data.agreementName,
          agreementContent: res.data.agreementContent,
          agreementType: res.data.agreementType,
          noLogin: true,
          navigateToUrl: "/pages/protocol/index?type=11",
        });
      }
    },
    openPageDetail(item) {
      if (!item.navigateToUrl) {
        Toast("功能未开通");
        return;
      }
      if (item.noLogin) {
        uni.navigateTo({
          url: item.navigateToUrl,
        });
        return;
      }

      if (!uni.getStorageSync("userId")) {
        uni.navigateTo({
          url: "/pages/login/login",
        });
      }

      uni.navigateTo({
        url: item.navigateToUrl,
      });
    },
    // 开启开发者模式
    openCode() {
      this.code++;
      if (this.code == 3) {
        // 开启打印模式
        eruda.init();
        this.code = 0;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.page-container {
  box-sizing: border-box;
  overflow-y: auto;
}

.list-box {
  width: 100%;
  background: #ffffff;
  margin-bottom: 24rpx;
  padding: 0 32rpx;
  box-sizing: border-box;
}

.list {
  height: 92rpx;
  border-bottom: 2rpx solid #ebebeb;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.list:last-child {
  border-bottom: none;
}

.list text {
  font-size: 26rpx;
  color: #333333;
}

// 开发者入口
.but_hide {
  font-size: 50rpx;
  margin-top: 100rpx;
  height: 100rpx;
  opacity: 0;
}
</style>
