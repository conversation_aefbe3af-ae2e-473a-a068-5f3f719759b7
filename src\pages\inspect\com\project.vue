<template>
  <!-- 项目列表 -->
  <view class="project">
    <!-- 标题 -->
    <TITLE
      :title="isLis ? '检验项目' : '检查项目'"
      :showIcon="!isLis"
      @click="help"
    />
    <!-- 单项 -->
    <view class="project_item" v-for="(item, index) in list" :key="index">
      <block v-if="!showBtn">
        <!-- 检验 -->
        <text v-if="isLis"
          >{{ index + 1 }}. {{ item.lisItemName }}-{{ item.sampleTypeName }}-{{
            item.sampleBodyName
          }}</text
        >
        <!-- 检查 -->
        <text v-if="!isLis"
          >{{ index + 1 }}. {{ item.itemName }}-{{ item.positionName }}</text
        >
        <text class="item_price">￥{{ item.cost }}</text>
      </block>

      <block v-if="showBtn">
        <!-- 存在结果 -->
        <view class="left">
          <!-- 检验 -->
          <text v-if="isLis"
            >{{ index + 1 }}. {{ item.lisItemName }}-{{
              item.sampleTypeName
            }}-{{ item.sampleBodyName }}</text
          >
          <!-- 检查 -->
          <text v-if="!isLis"
            >{{ index + 1 }}. {{ item.itemName }}-{{ item.positionName }}</text
          >
          <text class="item_price">￥{{ item.cost }}</text>
        </view>

        <!-- 按钮 -->
        <button @click="lock(item)" v-if="item.reportOssUrl">查看结果</button>
        <text v-else>等待结果</text>
      </block>
    </view>

    <!-- 预约时间 -->
    <view class="appointment" v-if="isShowTime">
      <TITLE title="预约时间" />

      <view class="time_sele" @click="click">
        <text v-if="!time"
          >点击预约您进行{{ isLis ? '检验' : '检查' }}的时间</text
        >
        <!-- 时间段 -->
        <text v-else class="time">{{ time }}</text>
        <uni-icons type="arrowright" v-if="showRight" color="#666" />
      </view>
    </view>

    <!-- 合计金额 -->
    <view class="project_price" v-if="price">
      合计：<text>￥{{ price || 0.0 }}</text>
    </view>

    <!-- 弹框 -->
    <uni-popup ref="popup" type="bottom">
      <view class="pop">
        <!-- 标题 -->
        <view class="pop_title">注意事项</view>
        <!-- 内容 -->
        <view class="pop_cont">
          {{ tipStr }}
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { downMergePDF } from '@/api/oss';
import { Toast } from '@/common/js/pay.js';
import TITLE from './itemTitle.vue';
export default {
  props: {
    list: {
      type: Array,
      default: () => [],
    },
    // 是否显示预约时间
    isShowTime: {
      type: Boolean,
      default: false,
    },
    // 显示箭头
    showRight: {
      type: Boolean,
      default: true,
    },
    // 显示按钮
    showBtn: {
      type: Boolean,
      default: false,
    },
    // 预约的时间
    time: {
      type: String,
      default: '',
    },
    // 是否检验
    isLis: {
      type: Boolean,
      default: false,
    },
    // 合计金额
    price: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      tipStr: '',
    };
  },
  components: {
    TITLE,
  },
  methods: {
    click() {
      this.$emit('click');
    },
    // 提示信息
    help() {
      if (!this.list.length) return;
      let str = '';
      this.list.forEach((v) => {
        if (v.noteContent) str += v.noteContent;
      });
      this.tipStr = str;
      this.$refs.popup.open();
    },
    // 报告
    async lock(item) {
      console.log(item);
      try {
        let {
          data: { url },
        } = await downMergePDF([item.reportOssUrl]);
        url = url.split('-internal').join('');
        window.location.href = url;
      } catch (error) {
        Toast('下载失败');
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.project {
  margin-top: 16rpx;
  background: #fff;
  padding: 24rpx 32rpx 32rpx;
  border-radius: 8rpx;
  font-size: 28rpx;

  .project_item {
    margin-top: 16rpx;
    line-height: 40rpx;
    @include flex(lr);
    color: #333;

    .item_price {
      color: #ff5050;
      display: block;
    }

    .left {
      flex: 1;
    }

    button {
      flex: none;
      margin-left: 30rpx;
      width: 160rpx;
      height: 60rpx;
      background: #eaf8ff;
      border-radius: 30rpx;
      font-size: 28rpx;
      color: #14a0e6;
      border: 1px solid #14a0e6;
      background: #eaf8ff;
      @include flex;
      padding: 0;
    }
  }

  .appointment {
    margin-top: 18rpx;
    padding-top: 18rpx;
    position: relative;

    &::before {
      content: '';
      display: block;
      width: 684rpx;
      height: 1px;
      position: absolute;
      top: 0;
      left: -32rpx;
      background: #eee;
    }

    .time_sele {
      @include flex(lr);
      font-size: 28rpx;
      height: 60rpx;

      text {
        color: #ffb541;

        &.time {
          @include font_theme;
        }
      }
    }
  }

  .project_price {
    margin-top: 24rpx;
    text-align: right;
    color: #333;

    text {
      color: #ff5050;
    }
  }

  .pop {
    background: #fff;
    border-radius: 16rpx 16rpx 0 0;

    &_title {
      font-size: 32rpx;
      color: #333;
      text-align: center;
      line-height: 80rpx;
    }

    &_cont {
      padding: 0 32rpx 32rpx;
      font-size: 24rpx;
      line-height: 34rpx;
      color: #666;
    }
  }
}
</style>
