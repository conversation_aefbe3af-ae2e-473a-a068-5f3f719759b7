<template>
  <!-- 检查中心 -->
  <view class="inspection">
    <TITLE title="医院信息" />
    <!-- 详细 -->
    <view class="inspection_cont">
      <!-- 左侧 -->
      <view class="left">
        <view class="left_title">{{ detail.offlineHosName }}</view>
        <view class="info"
          >{{ detail.provinceName }} {{ detail.cityName }}
        </view>
        <view class="info" v-show="detail.hosAddress">{{
          detail.hosAddress
        }}</view>
      </view>
      <!-- 右侧 -->
      <view class="right">
        <view class="item" @click="call">
          <image class="icon" src="/static/inspection/call.png" />
          <text>打电话</text>
        </view>
        <view class="item" @click="toAddress">
          <image class="icon" src="/static/inspection/lx.png" />
          <text>去这里</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { findAllHospital } from '@/api/base';
import TITLE from '@/pages/inspect/com/itemTitle.vue';
export default {
  props: {
    hosId: String,
  },
  components: {
    TITLE,
  },
  data() {
    return {
      detail: {},
    };
  },
  created() {
    this.getDetail();
  },
  methods: {
    call() {
      uni.makePhoneCall({
        phoneNumber: this.detail.hosTel,
      });
    },
    toAddress() {
      // const { latitude, longitude } = this.detail;
      // uni.openLocation({
      //   latitude: Number(latitude),
      //   longitude: Number(longitude),
      //   success: function() {},
      //   fail(err) {},
      // });
      uni.navigateTo({
        url: `../address/goAddress?address=${JSON.stringify(this.detail)}`,
      });
    },
    async getDetail() {
      let { data } = await findAllHospital({
        hosId: this.hosId,
      });
      this.detail = data;
    },
  },
};
</script>

<style lang="scss" scoped>
.inspection {
  padding: 24rpx 32rpx 32rpx;
  border-radius: 8rpx;
  background: #fff;
  margin-top: 16rpx;
  font-size: 28rpx;

  &_cont {
    @include flex(lr);
    padding-top: 10rpx;

    .left {
      flex: 1;
      padding-right: 30rpx;

      .left_title {
        color: #333;
        line-height: 54rpx;
      }

      .info {
        font-size: 26rpx;
        color: #666;
        line-height: 40rpx;
      }
    }

    .right {
      flex: none;
      @include flex(lr);
      position: relative;
      padding-left: 30rpx;

      &::after {
        content: '';
        display: block;
        position: absolute;
        width: 4rpx;
        height: 100%;
        background: #d8d8d8;
        left: 0;
        top: 0;
        border-radius: 2rpx;
      }

      .item {
        @include flex;
        flex-direction: column;

        &:last-child {
          margin-left: 16rpx;
        }

        .icon {
          width: 44rpx;
          height: 44rpx;
        }

        text {
          @include font_theme;
          padding-top: 10rpx;
          font-size: 24rpx;
        }
      }
    }
  }
}
</style>
