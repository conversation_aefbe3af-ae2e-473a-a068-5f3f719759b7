<template>
  <!-- 单个 -->
  <view class="inspect" @click="toPath">
    <!-- 编号 -->
    <view class="item_title">
      <text>检{{ isLis ? '验' : '查' }}单</text>
      <text
        class="status wait"
        v-if="item.pacsLisOrderListNewQueryVO.busStatus == 1"
        >待支付</text
      >
      <text
        class="status wait"
        v-if="item.pacsLisOrderListNewQueryVO.busStatus == 2"
        >待签到</text
      >
      <text
        class="status wait"
        v-if="item.pacsLisOrderListNewQueryVO.busStatus == 3"
        >待检查</text
      >
      <text
        class="status act"
        v-if="item.pacsLisOrderListNewQueryVO.busStatus == 4"
        >等待上传报告</text
      >
      <text
        class="status act"
        v-if="item.pacsLisOrderListNewQueryVO.busStatus == 5"
        >交易完成</text
      >
      <text class="status" v-if="item.pacsLisOrderListNewQueryVO.busStatus == 6"
        >已退费</text
      >
      <text
        class="status wait"
        v-if="item.pacsLisOrderListNewQueryVO.busStatus == 7"
        >待取样</text
      >
      <text
        class="status wait"
        v-if="item.pacsLisOrderListNewQueryVO.busStatus == 8"
        >待配送</text
      >
      <text
        class="status wait"
        v-if="item.pacsLisOrderListNewQueryVO.busStatus == 9"
        >待签收</text
      >
      <text class="status" v-if="item.pacsLisOrderListNewQueryVO.busStatus == 0"
        >交易关闭</text
      >
    </view>

    <!-- 检验项目 -->
    <view class="item_cont">
      <view class="left_info">
        检{{ isLis ? '验' : '查' }}项目：{{ detail.itemName }}
      </view>
      <view class="left_info"> 医院：{{ detail.hosName }} </view>
      <view class="left_info">
        就诊人：{{ item.remark.patientName }}
        <!-- 右箭头 -->
        <uni-icons type="arrowright" color="#666" size="16"></uni-icons>
      </view>
      <view class="left_info"> 医生姓名：{{ detail.platformDocName || detail.docName }} </view>
      <view class="left_info"> 医生诊断：{{ detail.diagName }} </view>
    </view>

    <!-- 时间 -->
    <view class="item_footer">
      <view class="left">
        总价:<text>￥{{ item.totalPay }}</text> 实付款:<text class="bold"
          >￥{{ item.orderMoney }}</text
        >
      </view>
      <view class="button" v-if="item.payStatus == 1">去支付</view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'Inspect',
  props: ['item', 'detail', 'isLis'],
  methods: {
    toPath() {
      const { orderNo } = this.item;
      uni.setStorageSync('hosId', this.item.hosId);
      let url;
      if (this.isLis) {
        url = '/pages/order/detail/lis?orderNo=' + orderNo;
      } else {
        url = '/pages/order/detail/pacs?orderNo=' + orderNo;
      }
      uni.navigateTo({
        url,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.inspect {
  padding: 0 32rpx;
  border-radius: 8rpx;
  background: #fff;
  margin-bottom: 24rpx;
  box-shadow: 0 0 20rpx #ddd;

  &:last-child {
    margin-bottom: 0;
  }

  .item_title {
    height: 88rpx;
    @include flex(lr);
    font-size: 28rpx;
    color: #333;
    font-weight: bold;
    border-bottom: 1px solid #ebebeb;

    .status {
      font-weight: normal;
      color: #333;

      &.wait {
        color: #ff3b30;
      }

      &.act {
        @include font_theme;
      }
    }
  }

  .item_cont {
    padding: 10rpx 0;
    border-bottom: 1px solid #ebebeb;

    .left_info {
      @include flex(lr);
      font-size: 28rpx;
      color: #666666;
      line-height: 50rpx;

      &:first-child {
        font-weight: bold;
        color: #333;
      }
    }
  }

  .item_footer {
    height: 88rpx;
    @include flex(lr);
    font-size: 28rpx;
    color: #333;

    .left {
      text {
        color: red;
        margin-right: 16rpx;
      }
    }

    .button {
      width: 160rpx;
      height: 60rpx;
      border-radius: 30rpx;
      @include flex;
      @include bg_theme;
      color: #fff;
    }
  }
}
</style>
