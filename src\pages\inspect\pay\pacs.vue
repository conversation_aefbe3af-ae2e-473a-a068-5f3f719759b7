<template>
  <!-- 检验单 待支付 回显页 -->
  <view class="lis">
    <view class="detail">
      <!-- 顶部信息 -->
      <USERINFO :info="detail" />

      <!-- 检查机构 -->
      <INPRCTION v-if="dpoId" :dloId="dpoId" :pliId="ppiId" />

      <!-- 项目信息 -->
      <PROJECT
        v-if="list.length"
        @help="help"
        :list="list"
        :isShowTime="!!time"
        :time="time"
        :showRight="false"
      />

      <!-- 支付方式 -->
      <ROW text="支付方式" v-if="paymentType">
        <block v-if="paymentType == 1"> 到院支付 </block>

        <block v-else> 在线支付 </block>
      </ROW>

      <!-- 医保类型 -->
      <ROW text="医保类型" v-if="isMedicare">
        <block v-if="isMedicare == 1"> 医保 </block>

        <block v-else> 自费 </block>
      </ROW>
    </view>

    <FOOTER :length="list.length" :cost="detail.cost" @click="getNext"
      >提交订单</FOOTER
    >
  </view>
</template>

<script>
// 顶部信息
import USERINFO from '../com/userInfo.vue';
// 项目信息
import PROJECT from '../com/project.vue';
// 选择机构
import SELECT from '../com/check.vue';
// 底部展示
import FOOTER from '../com/footer.vue';
// 检查中心
import INPRCTION from '../com/inspection.vue';
// 单行内容
import ROW from '../com/row.vue';

import {
  getProPacsAllInfoByID,
  getAppointPayInfo,
  queryPacsRegisterPayStatus,
} from '@/api/inspect';

import { getReceiptWay } from '@/api/order';
import { wxPay } from '@/common/js/pay';
let num = 3;

import { Toast } from '@/common/js/pay.js';

export default {
  components: {
    USERINFO,
    PROJECT,
    SELECT,
    FOOTER,
    INPRCTION,
    ROW,
  },
  data() {
    return {
      dpoId: '',
      ppiId: '',
      time: '',
      detail: {},
      list: [],
      paymentType: '',
      isMedicare: '',
      pays: [],
      callId: '',
    };
  },
  onLoad(opt) {
    this.getDetail(opt.id);
  },
  methods: {
    async getDetail(id) {
      let { data } = await getProPacsAllInfoByID(id);
      let {
        isMedicare,
        paymentType,
        appointDate,
        appointEndTime,
        appointStartTime,
        dpoId,
        ppiId,
        pacsListDVO,
      } = data;
      this.detail = data;
      this.time =
        appointDate +
        ' ' +
        appointStartTime.slice(0, 5) +
        '-' +
        appointEndTime.slice(0, 5);
      this.dpoId = dpoId;
      this.ppiId = ppiId;
      this.list = pacsListDVO;
      this.isMedicare = isMedicare;
      this.paymentType = paymentType;
      this.getPayList();
    },
    // 获取支付方callId
    async getPayList() {
      let { data } = await getReceiptWay({
        subjectId: this.dpoId,
      });
      this.pays = data;
    },
    async getNext() {
      let { ppiId, payCost } = this.detail;
      let openid = uni.getStorageSync('wxInfo').openId;
      let isReady = 1;
      let payType = 1;
      payType = await this.selePay();
      let callId = this.callId;

      let param = {
        ppiId,
        openid,
        isReady,
        payType,
        callId,
      };

      uni.showLoading({
        mask: true,
      });

      try {
        let { data } = await getAppointPayInfo(param);
        uni.hideLoading();

        // 微信支付
        if (payType == 1) {
          this.wxPay(data);
          return;
        }

        // 支付宝支付
        if (payType == 2) {
          uni.navigateTo({
            url:
              '/pages/pay/pay?price=' +
              payCost +
              '&ppiId=' +
              ppiId +
              '&url=' +
              btoa(data.url),
          });
        }
      } catch (error) {
        uni.hideLoading();
      }
    },
    // 微信支付
    async wxPay(info) {
      try {
        await wxPay(info);
        this.getStatus();
      } catch (error) {
        Toast('取消支付');
      }
    },
    // 选择支付方式
    async selePay() {
      let that = this;
      return new Promise((resolve, reject) => {
        // 如果不可选支付
        if (!this.pays.length) {
          Toast('当前没有配置支付方式');
          return reject();
        }
        // 如果在线支付
        uni.showActionSheet({
          itemList: ['微信支付', '支付宝支付'],
          success(res) {
            let index = res.tapIndex + 1;
            let item;
            item = that.pays.filter((it) => it.receiptType == index)[0];
            if (!item) {
              Toast('暂不支持该支付方式');
              return reject();
            }
            that.callId = item.appid;
            resolve(index);
          },
          fail(err) {
            reject(err);
          },
        });
      });
    },
    // 查询支付状态
    async getStatus() {
      let {
        data: { status },
      } = await queryPacsRegisterPayStatus(this.detail.ppiId);
      // 支付成功
      if (status == 2) {
        this.toSuc();
        return;
      }
      if (status == 1) {
        if (num > 0) {
          setTimeout(this.getStatus(), 2000);
          num--;
        }
      }
    },
    // 跳转成功
    toSuc() {
      uni.redirectTo({
        url: '../result/pacsResult?id=' + this.detail.ppiId,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.lis {
  padding-bottom: 108rpx;

  .detail {
    padding: 32rpx;
  }

  .pop {
    background: #fff;
    border-radius: 16rpx 16rpx 0 0;

    &_title {
      font-size: 32rpx;
      color: #333;
      text-align: center;
      line-height: 80rpx;
    }

    &_cont {
      padding: 0 32rpx 32rpx;
      font-size: 24rpx;
      line-height: 34rpx;
      color: #666;
    }
  }
}
</style>
