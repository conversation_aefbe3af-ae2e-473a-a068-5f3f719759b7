<template>
  <!-- 支付结果 -->
  <view>
    <view class="page-container">
      <!-- 医生头部 -->
      <Docter :infoDetail="appointInfoDetail" />

      <view class="bg_wh">
        <view class="person_info">
          <text>就诊人</text>
          <text>{{ patientInfo.patientName }}</text>
        </view>
        <view class="person_info">
          <text>问诊类型</text>
          <text>{{ appointInfoDetail.visitTypeName }}</text>
        </view>
        <view class="person_info">
          <text>服务类型</text>
          <text>{{
            appointInfoDetail.consultationCode == 0 ? '咨询' : '复诊'
          }}</text>
        </view>
        <view class="person_info">
          <text>预约时间</text>
          <text
            >{{ appointReserveInfo.visitDate }}
            {{ appointReserveInfo.startTimeShow }}-{{
              appointReserveInfo.endTimeShow
            }}</text
          >
        </view>
      </view>

      <view class="bg_wh particulars">
        <view class="title_fw">费用明细（{自费）</view>
        <template v-for="item in appointInfoDetail.details">
          <view class="left_right">
            <text>{{ item.priceDetailName }}</text>
            <text>{{ item.totalPay }}</text>
          </view>
        </template>
        <view style="text-align: right; padding-top: 40rpx">
          合计：{{ appointInfoDetail.cost }}<text style="color: red"></text>
        </view>
<!--        <view class="title_fw">医保担负明细</view>-->
<!--        <view class="left_right">-->
<!--          <text>医保统筹支付</text>-->
<!--          <text>0</text>-->
<!--        </view>-->
<!--        <view class="left_right">-->
<!--          <text>医保账户支付</text>-->
<!--          <text>0</text>-->
<!--        </view>-->
        <view class="title_fw">优惠券抵扣明细</view>
        <view class="left_right">
          <text>新用户立减</text>
          <text>0</text>
        </view>
      </view>
    </view>

    <!-- 按钮 -->
    <FooterButton @click="goHome">返回主页</FooterButton>
  </view>
</template>

<script>
import FooterButton from '@/components/footer_button/button.vue';
import Docter from '@/components/doctor_header/doctor_header.vue';
export default {
  components: {
    FooterButton,
    Docter,
  },
  data() {
    return {
      patientInfo: {},
      appointInfoDetail: {},
      appointReserveInfo: {},
    };
  },
  onLoad() {
    this.patientInfo = uni.getStorageSync('patientInfo');
    this.appointInfoDetail = uni.getStorageSync('appointInfoDetail');
    this.appointReserveInfo = uni.getStorageSync('appointReserveInfo');
  },
  methods: {
    // 返回首页
    goHome() {
      uni.switchTab({
        url: '/pages/index/index',
      });
    },
  },
};
</script>

<style scoped lang="scss">
.page-container {
  padding: 24rpx 32rpx 120rpx;
  box-sizing: border-box;
  overflow-y: auto;
  background-color: #f5f5f5;

  .doctor_box_top {
    padding: 28upx 24upx;
    border-radius: 16upx;
  }
}

/* 主体内容 */
.bg_wh {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 0 24rpx;
  margin-top: 24rpx;
  font-size: 28rpx;
}

.bg_wh:first-child {
  margin-top: 0;
}

/* 问诊信息 */
.patient_box {
  margin-top: 40rpx;
  background-color: #ffffff;
  border-radius: 8rpx;
  padding: 0 32rpx;
}

.person_info {
  height: 92rpx;
  border-bottom: 1px solid #ebebeb;
  box-sizing: border-box;
  color: #333333;
  font-size: 28rpx;
  display: flex;
  align-items: center;
}

.person_info text:first-child {
  width: 180rpx;
}

.person_info:last-child {
  border-bottom: none;
}

/* 费用明细 */
.particulars {
  /* padding-top: 26rpx; */
  padding-bottom: 26rpx;
}

.title_fw {
  color: #333333;
  font-weight: 600;
  padding-top: 26rpx;
}

.left_right {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #999999;
  padding-top: 40rpx;
}

.left_right text:last-child {
  color: #333333;
}
</style>
