<template>
  <div>
    <!--    <view style="padding: 40upx">-->
    <!--      <button-->
    <!--        style="background-color: #874ff0; border-radius: 100px; color: #ffffff"-->
    <!--        @click="copyImgUrl"-->
    <!--      >-->
    <!--        点击复制图片地址前往浏览器自行下载-->
    <!--      </button>-->
    <!--    </view>-->

    <img
      style="width: 100%"
      :src="imgItem"
      v-for="(imgItem, index) in imgUrl"
      :key="index"
    />
  </div>
</template>
<script>
import { getPatientPrescriptionBusinessOrderInfo } from "@/api/cf";
export default {
  data() {
    return {
      imgUrl: "", // 处方图片链接地址
    };
  },
  onLoad(option) {
    this.businessId = option.businessId; // 处方ID
    this.getDetail();
  },
  onShow() {},
  created() {},
  methods: {
    async getDetail() {
      let prescriptionQueryVO = {
        businessId: this.businessId,
      };

      let res = await getPatientPrescriptionBusinessOrderInfo(
        prescriptionQueryVO
      );
      if (res.code != 20000 || !res.data.length) return;
      console.log("查看处方详情", res);
      this.imgUrl = res.data[0].proBusinessInfo.httpPngUrl.split(",");
    },
    // 点击复制图片地址
    copyImgUrl() {
      // 检测是否iOS端
      function iosAgent() {
        return navigator.userAgent.match(/(iPhone|iPod|iPad);?/i);
      }

      // 复制文本函数，微信端，需要在用户触发 Click 事件里面才能执行成功
      function copy(message) {
        if (iosAgent()) {
          console.log("input 复制方式 " + message);
          let inputObj = document.createElement("input");
          inputObj.value = message;
          document.body.appendChild(inputObj);
          inputObj.select();
          inputObj.setSelectionRange(0, inputObj.value.length);
          _execCommand("Copy");
          document.body.removeChild(inputObj);
        } else {
          console.log("document 复制方式 " + message);
          let domObj = document.createElement("span");
          domObj.innerHTML = message;
          document.body.appendChild(domObj);
          let selection = window.getSelection();
          let range = document.createRange();
          range.selectNodeContents(domObj);
          selection.removeAllRanges();
          selection.addRange(range);
          _execCommand("Copy");
          document.body.removeChild(domObj);
        }
      }

      // 执行浏览器命令 Copy 顺便输出一下日志，如果在移动端推荐写个方法展示日志或者用alert(msg)也行。
      function _execCommand(action) {
        let is = document.execCommand(action);
        if (is) {
          console.log("复制成功");
        } else {
          console.log("复制失败");
        }
      }

      // 复制逻辑
      copy(this.imgUrl);
    },
  },
};
</script>
