<template>
  <!-- 时间线 -->
  <view class="time_line">
    <block v-for="(item, index) in list">
      <!-- 时间段 -->
      <view class="line_item" :key="index" v-if="item.time">
        <text class="item">{{ item.time }}</text>
        <text>{{ item.name }}</text>
      </view>
    </block>
  </view>
</template>

<script>
export default {
  props: {
    list: {
      type: Array,
      default: () => [],
    },
  },
};
</script>

<style lang="scss" scoped>
.time_line {
  font-size: 28rpx;
  color: #333;
  position: relative;
  background: #fff;
  border-radius: 8rpx;
  padding: 32rpx;
  margin-top: 16rpx;
  box-shadow: 0px 1px 4px  rgba(121, 72, 234, 0.15);
  &::before {
    content: '';
    display: block;
    position: absolute;
    width: 1px;
    top: 60upx;
    left: 38upx;
    height: calc(100% - 110upx);
    border-left: 1px dashed rgba(131, 106, 255, 1);
  }

  .line_item {
    font-size: 24upx;
    padding-left: 30upx;
    margin-bottom: 36upx;
    position: relative;
    @include flex(lr);

    .item {
      @include font_theme;
    }

    // 原点
    &::before {
      content: '';
      display: block;
      position: absolute;
      left: 0;
      top: 50%;
      margin-top: -8upx;
      width: 16upx;
      height: 16upx;
      border-radius: 50%;
      @include bg_theme;
    }

    &:last-child {
      margin-bottom: 0;
    }

    .time {
      font-size: 24upx;
    }
  }
}
</style>
