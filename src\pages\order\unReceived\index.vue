<template>
  <!-- 待收货列表 -->
  <view class="wait_pay">
    <view class="wait_list" v-for="(item, index) in list" :key="index">
      <!-- 挂号 -->
      <REGISTER v-if="item.orderType == 1" :item="item" :key="index" />
      <!-- 服务 -->
      <SERVICE v-if="item.orderType == 4" :item="item" :key="index" />
      <!-- 检验单 -->
      <INSPECTION v-if="item.orderType == 5" :item="item" :key="index" />
      <!-- 检查单 -->
      <INSPECT v-if="item.orderType == 6" :item="item" :key="index" />
      <!-- 购药 -->
      <SHOP
        v-if="item.orderType == 3 && item.source == 2"
        @confirmGood="confirmGood"
        :item="item"
        pageType="list"
        :key="index"
      />
      <!-- 处方 -->
      <PRESCRIPTION
        v-if="item.orderType == 3 && item.source != 2"
        @confirmGood="confirmGood"
        :item="item"
        :isScan="true"
        :key="index"
      />
      <!-- 扫码购药处方 -->
      <PRESCRIPTION
        v-if="item.orderType == 8 && item.source == 3"
        @confirmGood="confirmGood"
        :item="item"
        :isScan="true"
        :key="index"
      />
    </view>

    <!-- 到底 -->
    <view v-if="total == list.length && list.length > 0" class="list_footer"
      >已经到底了</view
    >

    <EMPTY v-if="!list.length" />
  </view>
</template>

<script>
import { Toast } from '@/common/js/pay.js'
import { orderListPageNew, confirmGoods } from '@/api/order.js'
// 挂号
import REGISTER from '../components/register.vue'
import SERVICE from '../components/service.vue'
import INSPECT from '../components/inspect.vue'
import SHOP from '../components/shop.vue'
import PRESCRIPTION from '../components/prescription.vue'
import EMPTY from '../components/empty.vue'
export default {
  name: 'WaitPay',
  components: {
    REGISTER,
    SERVICE,
    SHOP,
    INSPECT,
    PRESCRIPTION,
    EMPTY,
  },
  data() {
    return {
      listQuery: {
        page: 1,
        limit: 10,
        orderStatus: '2',
      },
      list: [],
      total: 0,
    }
  },
  onLoad() {
    this.getList()
  },
  onReachBottom() {
    if (this.list.length >= this.total) return
    this.listQuery.page++
    this.getList()
  },
  onPullDownRefresh() {
    this.listQuery.page = 1
    this.getList()
  },
  methods: {
    // 获取列表
    async getList() {
      try {
        let res = await orderListPageNew(this.listQuery)
        this.total = res.data.total
        const list = res.data.rows
        list.forEach((v) => {
          if (v.remark) v.remark = JSON.parse(v.remark)
        })
        if (this.listQuery.page > 1) {
          this.list = [...this.list, ...list]
        } else {
          this.list = list
        }
        this.listQuery.page = res.data.page
      } catch (e) {}
      uni.stopPullDownRefresh()
    },
    // 确认收货
    async confirmGood(item) {
      delete item.businessId
      await confirmGoods(item)
      Toast('收货成功')
      this.getList()
    },
  },
}
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}

.wait_pay {
  padding: 24rpx;
  min-height: 100vh;
  background-color: #f5f5f5;

  .list_footer {
    font-size: 24rpx;
    color-profile: #999;
    text-align: center;
    line-height: 60rpx;
  }
}
</style>
