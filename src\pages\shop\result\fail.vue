<template>
  <!-- 支付成功 -->
  <view class="pay_success">
    <image src="/static/shop/error.png" mode="widthFix"></image>

    <view class="text">
      抱歉，支付失败，请检查网络是否正常，或者您的余额是否不足
    </view>

    <!-- 按钮组 -->
    <view class="buts">
      <button class="one" @click="back">再次支付</button>
      <button class="two" @click="toShop">返回商城</button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      orderNo: '',
      isNewShop: '',
    };
  },
  onLoad(opt) {
    this.orderNo = opt.orderNo;
    this.isNewShop = opt.isNewShop || 1;
  },
  methods: {
    // 回商城首页
    toShop() {
      uni.reLaunch({
        url: '../index',
      });
    },
    //
    back() {
      // 新商城
      if (this.isNewShop == 1) {
        uni.redirectTo({
          url: '/pages/shopOrder/detail?orderNo=' + this.orderNo,
        });
      } else {
        uni.redirectTo({
          url:
            '/pages/order/detail/drugStatus?source=2&orderNo=' + this.orderNo,
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}
.pay_success {
  height: 100vh;
  padding-top: 80rpx;
  background-color: #f5f5f5;
  @include flex(left);
  flex-direction: column;

  image {
    width: 386rpx;
  }

  .text {
    width: 360rpx;
    text-align: center;
    font-size: 28rpx;
    line-height: 40rpx;
    margin-top: 40rpx;
  }

  .buts {
    width: 100%;
    padding: 0 30rpx;
    @include flex(lr);

    button {
      margin-top: 94rpx;
      width: 320rpx;
      height: 84rpx;
      border-radius: 42rpx;
      font-size: 32rpx;
      @include flex;

      &.one {
        @include border_theme;
        @include font_theme;
      }

      &.two {
        background-color: #ff5050;
        color: #fff;
      }
    }
  }
}
</style>
