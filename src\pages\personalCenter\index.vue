<template>
  <view class="page-container">
    <view class="page-top" @click="toPersonalInfo">
      <view class="top-left">
        <!-- 头像 -->
        <image
          :src="info.headimgurl || '/static/images/docHead.png'"
          mode="aspectFill"
        ></image>
        <!-- 昵称 -->
        <view v-if="info.nickname">
          <view
            style="font-weight: 600; font-size: 28rpx; margin-bottom: 10rpx"
            >{{ info.nickname }}</view
          >
          <!-- <text>{{ info.sex == "2" ? "女" : "男" }}</text> -->
          <text>{{ sex || '' }}</text>
        </view>
        <!-- 空 -->
        <view v-else>您还未登录</view>
      </view>
      <uni-icons type="arrowright" size="20" color="#fff"></uni-icons>
    </view>

    <!-- 订单相关 -->
    <view class="box-container" style="padding: 0 32rpx">
      <view class="order">
        <div v-for="(item, index) in allOrder" :key="index">
          <view class="list" @click="toPage(item)">
            <text style="font-weight: 700">{{ item.title }}</text>
            <view style="color: #999">
              <text style="font-size: 10px">查看全部订单</text>
              <!--              <uni-icons type="arrowright" color="#999" size="20"></uni-icons>-->
            </view>
          </view>
        </div>

        <view class="order-list">
          <block v-for="(item, index) in myList" :key="index">
            <view class="myList" @click="toPage(item)">
              <view>
                <uni-badge
                  style="width: 45rpx"
                  class="badge"
                  type="error"
                  :text="item.quantity"
                  size="normal"
                ></uni-badge>
                <image :src="item.icon"></image>
              </view>
              <text>{{ item.title }}</text>
            </view>
          </block>
        </view>
      </view>
    </view>

    <!-- 菜单1 -->
    <!--    <MENU :list="menu_one" @click="toPage" />-->
    <view class="menu1" style="padding: 0 32rpx">
      <view
        class="menu_item"
        v-for="(item, index) in menu_one"
        @click="toPage(item)"
        :key="index"
        v-show="!item.display"
      >
        <image :src="item.icon" class="item_icon"></image>
        <view class="menu_item-title">
          <view class="title-1">{{ item.title }}</view>
          <view class="title-2">{{ item.title }}</view>
        </view>
      </view>
    </view>
    <view style="padding: 0 32rpx">
      <view class="menu2">
        <view style="font-weight: 700; margin-bottom: 12px">常用服务</view>
        <view class="menu2-box">
          <view
            class="menu_item"
            v-for="(item, index) in [...menu_two, ...menu_three]"
            @click="toPage(item)"
            :key="index"
            v-show="!item.display"
          >
            <image :src="item.icon" class="item_icon"></image>
            <view class="menu_item-title">
              <view class="title-1">{{ item.title }}</view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!--    &lt;!&ndash; 菜单2 &ndash;&gt;-->
    <!--    <MENU :list="menu_two" @click="toPage" />-->

    <!--    &lt;!&ndash; 设置 &ndash;&gt;-->
    <!--    <MENU :list="menu_three" @click="toPage" />-->

    <view class="model-container" v-if="showTips">
      <view class="wrapper">
        <view class="block">
          <view class="bigTitle"> 投诉建议 </view>
          <view class="title">
            {{ telNo }}
          </view>
          <view class="btn_footer">
            <template>
              <view class="cancel" @click="showTips = false">取消</view>
            </template>
            <template>
              <view @click="callTel"> 拨打</view>
            </template>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
// 菜单列表
import MENU from './components/menuList.vue'
import Login from '@/mixins/login.js'
import {
  orderCount,
  AllOrderCount,
  getSysPlatformConfigByKeyList,
} from '@/api/order.js'
import { getPatientPersonalCenterInfo } from '@/api/user.js'
import { getSysPlatformConfigBykey } from '../../api/base'
import Disease from '../disease/index.vue'
export default {
  mixins: [Login],
  components: {
    Disease,
    MENU,
  },
  data() {
    return {
      // 默认头像
      showDefaultImg: false,
      telNo: '',
      showTips: false,
      info: {},
      sex: '',
      allOrder: [
        {
          title: '我的订单',
          navigateToUrl: '/pages/order/index',
        },
      ],
      // 订单相关
      myList: [
        {
          icon: '/static/doc/order-1.png',
          title: '待支付',
          navigateToUrl: '/pages/order/waitPay/index',
          quantity: '',
        },
        {
          icon: '/static/doc/order-2.png',
          title: '待发货',
          navigateToUrl: '/pages/order/unDelivered/index',
          quantity: '',
        },
        {
          icon: '/static/doc/order-3.png',
          title: '待收货',
          navigateToUrl: '/pages/order/unReceived/index',
          quantity: '',
        },
        {
          icon: '/static/doc/画板 <EMAIL>',
          title: '已完成',
          navigateToUrl: '/pages/order/endOrder/index',
          quantity: '',
        },
        {
          icon: '/static/doc/order-4.png',
          title: '待评价',
          navigateToUrl: '/pages/personalCenter/myOrder/evaluation/index',
          quantity: '',
        },
      ],

      // 菜单1
      menu_one: [
        // {
        //   icon: "/static/images/index_my/shop.png",
        //   title: "商城订单",
        //   navigateToUrl: "/pages/shopOrder/index",
        // },
        {
          icon: '/static/doc/order-5.png',
          title: '我的问诊',
          navigateToUrl: '/pages/personalCenter/diagnosisRecord/index',
        },
        {
          icon: '/static/doc/order-6.png',
          title: '我的处方',
          navigateToUrl: '/pages/personalCenter/myPrescription/index',
        },
        // {
        //   icon: "/static/images/index_my/recipe_3.png",
        //   title: "我的检查",
        //   navigateToUrl: "/pages/inspect/list/pacsOrderList",
        // },
        // {
        //   icon: "/static/images/index_my/recipe_2.png",
        //   title: "我的检验",
        //   navigateToUrl: "/pages/inspect/list/lisOrderList",
        // },
        // {
        //   icon: "/static/images/index_my/recipe_4.png",
        //   title: "核酸检测结果查询",
        //   navigateToUrl: "/pages/inspect/list/nucleicOrderList",
        //   configKey: "PatientShowCoronavirusTest",
        //   display: false,
        // },
      ],
      // 菜单2
      menu_two: [
        {
          icon: '/static/images/index_my/分组 13.png',
          title: '就诊人管理',
          navigateToUrl: '/pages/personalCenter/patientManage/index',
        },
        // {
        //   icon: "/static/images/index_my/分组 15.png",
        //   title: "关注医生",
        //   navigateToUrl: "/pages/personalCenter/myDocter/patients/patients",
        // },
        {
          icon: '/static/images/index_my/分组 15.png',
          title: '我的医生',
          navigateToUrl:
            '/pages/personalCenter/myDocter/patients/patients?pageType=myDocter',
        },
        {
          icon: '/static/images/index_my/分组 14.png',
          title: '量表记录',
          navigateToUrl: '/pages/personalCenter/questionCoupon/index',
        },
        {
          icon: '/static/images/index_my/分组 16.png',
          title: '我的预约',
          navigateToUrl: '/pages/personalCenter/myAppoint/index',
        },
      ],
      // 菜单3
      menu_three: [
        {
          icon: '/static/images/index_my/分组 17.png',
          title: '设置',
          noLogin: true,
          navigateToUrl: '/pages/personalCenter/setting/index',
        },
        // {
        //   icon: "/static/images/index_my/分组 18.png",
        //   title: "信息公示",
        //   noLogin: true,
        //   navigateToUrl: "",
        //   isNotPage: true,
        // },
        {
          icon: '/static/images/index_my/分组 20.png',
          title: '投诉建议',
          noLogin: true,
          navigateToUrl: '/pages/personalCenter/casuggestions/index',
        },
        // {
        //   icon: '/static/images/扫码购药.png',
        //   title: '扫码购药订单',
        //   noLogin: true,
        //   navigateToUrl: '/pages/order/unDelivered/scanOrder',
        // },
        {
          icon: '/static/images/发票管理.png',
          title: '发票管理',
          noLogin: true,
          navigateToUrl: '/pages/newInvoice/index',
        },
      ],
    }
  },
  onLoad() {
    this.info = uni.getStorageSync('wxInfo')
    this.getuserSex()
  },
  onShow() {
    console.log('展示')
    this.getConfig()
    this.getAllOrder()
    this.getuserSex()
    this.getAllOrderCount()
    this.getSysPlatformConfigByKeyList()
  },
  methods: {
    getConfig() {
      // getSysPlatformConfigBykey({
      //   configKey: "patientInfoPublicity",
      // }).then((res) => {
      //   this.menu_three[1].navigateToUrl = res.data.configValue;
      // });

      getSysPlatformConfigBykey({
        configKey: 'patient_show_invoice',
      }).then((res) => {
        if (res.data.configValue == 1) {
          let index = this.menu_three.findIndex(
            (item) => item.title == '发票服务'
          )
          if (index == -1) {
            this.menu_three.unshift({
              icon: '/static/images/index_my/invoice.png',
              title: '发票服务',
              navigateToUrl: '/pages/invoice/index',
            })
          }
        }
      })

      getSysPlatformConfigBykey({
        configKey: 'patientComplaint',
      }).then((res) => {
        this.telNo = res.data.configValue
      })
    },

    callTel() {
      window.location.href = 'tel://' + this.telNo
    },
    //由于微信获取用户性别始终未0，因此重新获取用户性别
    async getuserSex() {
      if (!uni.getStorageSync('proPfInfo')) return
      let userId = uni.getStorageSync('userId')
      let appid = uni.getStorageSync('appId')
      let { data } = await getPatientPersonalCenterInfo({
        userId,
        appid,
      })
      let { sex } = data
      this.sex = sex
    },
    // 查询待评价
    async getAllOrderCount() {
      if (!uni.getStorageSync('proPfInfo')) return
      let res = await AllOrderCount()
      this.myList.forEach((item) => {
        if (item.title == '待评价') {
          item.quantity = res.data.allOrderCount
        }
      })
    },
    // 查询全部订单状态(待收货等)
    async getAllOrder() {
      if (!uni.getStorageSync('proPfInfo').patientIdList.length) return

      let res = await orderCount()
      this.myList.forEach((item) => {
        if (item.title == '待支付') {
          item.quantity = res.data.pendingApplyCount
        }
        if (item.title == '待发货') {
          item.quantity = res.data.shippedCount
        }
        if (item.title == '待收货') {
          item.quantity = res.data.deliveryCount
        }
      })
    },
    //查询权限配置
    async getSysPlatformConfigByKeyList() {
      let { data } = await getSysPlatformConfigByKeyList([
        'PatientShowCoronavirusTest',
      ])
      data.forEach((item) => {
        for (let i = 0; i < this.menu_one.length; i++) {
          if ((item.configKey = this.menu_one[i].configKey)) {
            if (item.configValue === '0') {
              this.menu_one[i].display = true
            } else {
              this.menu_one[i].display = false
            }
            //  console.log(this.menu_one[i])
          }
        }
      })
    },
    // 去个人中心
    toPersonalInfo() {
      if (!this.hasInfo()) return

      uni.navigateTo({
        url: '/pages/personalCenter/personalInfo/index',
      })
    },
    // 跳转页面
    toPage(item) {
      console.log(item)
      uni.removeStorageSync('currentPatientId')
      this.$store.commit('setSelectedOrders', [])
      if (item.isNotPage) {
        if (!item.navigateToUrl) {
          uni.showToast({
            title: '平台暂未配置信息公示',
            icon: 'none',
          })
          return
        }
        window.location.replace(item.navigateToUrl)
        return
      }
      if (item.isBtn) {
        if (!this.telNo) {
          uni.showToast({
            title: '平台暂未配置电话',
            icon: 'none',
          })
          return
        }
        this.showTips = true
        return
      }
      if (item.noLogin) {
        uni.navigateTo({
          url: item.navigateToUrl,
        })
        return
      }
      if (!this.hasInfo()) return

      uni.navigateTo({
        url: item.navigateToUrl,
      })
    },
  },
}
</script>

<style scoped lang="scss">
.menu2-box {
  width: 100%;
  display: grid;
  grid-template-columns: repeat(4, 1fr); /* 每行4个元素 */
  gap: 20rpx; /* 固定间距 */
  justify-items: center; /* 居中对齐内容 */
  align-items: center; /* 垂直居中内容 */
}
.menu2 {
  background: white;
  margin-top: 20rpx;
  padding: 30rpx 30rpx;
  box-shadow: 0px 1px 4px rgba(121, 72, 234, 0.15);
  border-radius: 5px;
  .menu_item {
    display: flex;
    flex-direction: column;
    align-items: center;
    .item_icon {
      width: 40rpx;
      height: 40rpx;
      margin-bottom: 22rpx;
    }
  }
}
.menu1 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20rpx;

  .menu_item {
    width: 48%;
    border-radius: 5px;
    background: white;
    padding: 26rpx 48rpx;
    display: flex;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0px 1px 4px rgba(121, 72, 234, 0.15);
    .menu_item-title {
    }
  }
  .item_icon {
    width: 76rpx;
    height: 76rpx;
    margin-right: 26rpx;
  }
}
.title-1 {
  font-size: 24rpx;
  font-weight: 400;
  line-height: 50rpx;
  color: rgba(51, 51, 51, 1);
}
.title-2 {
  font-size: 20rpx;
  color: rgba(166, 166, 166, 1);
}
page {
  background: #f5f5f5;
}
.page-container {
  height: 100%;

  padding-bottom: 30upx;
  overflow-y: auto;
  box-sizing: border-box;
  position: relative;

  .menu {
    margin-top: 24rpx;
    box-shadow: 0px 8rpx 40rpx 0px rgba(0, 0, 0, 0.05);
  }
}

/* 公共样式 */
.box-container {
  box-sizing: border-box;
  margin-top: 24rpx;
  box-shadow: 0px 8px 40rpx 0rpx rgba(0, 0, 0, 0.05);
}

.list {
  height: 92rpx;
  border-bottom: 1px solid #ebebeb;
  box-sizing: border-box;
  color: #333333;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 30rpx;
}

.list .list {
  border-bottom: none;
}

.list:last-child {
  border-bottom: none;
}

.list image {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}

/* 顶部 */
.page-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: linear-gradient(
    90deg,
    rgba(103, 107, 223, 1) 0%,
    rgba(137, 131, 228, 1) 100%
  );
  padding: 20rpx 52rpx;
  color: #ffffff;
  font-size: 24rpx;
  border-radius: 0px 0px 32px 32px;
}

.top-left {
  display: flex;
  align-items: center;
}

.page-top image {
  width: 124rpx;
  height: 124rpx;
  margin-right: 32rpx;
  border-radius: 50%;
}

.order {
  background: #fff;
  border-radius: 16rpx;
  padding: 0 32rpx;
}

/* 我的订单 */
.order-list {
  @include flex(lr);
}

.myList {
  @include flex;
  flex-direction: column;
  padding: 34rpx 10rpx;
  color: #666666;
  font-size: 24rpx;
}

.badge {
  position: absolute;
  margin-left: 46rpx;
  z-index: 1;
}

.myList image {
  width: 56rpx;
  height: 56rpx;
  margin-bottom: 10rpx;
}
.page {
  height: calc(100% - 50px);
}

.wrapper {
  position: fixed;
  width: 100%;
  height: 100vh;
  left: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}

.wrapper .block {
  width: 606rpx;
  box-sizing: border-box;
  background: rgba(255, 255, 255, 1);
  border-radius: 16rpx;
  overflow: hidden;
  padding: 40rpx;
}

.block .title {
  padding-bottom: 64rpx;
  color: #333333;
  font-size: 32rpx;
  font-weight: 600;
  white-space: normal;
  text-align: center;
}

.block .bigTitle {
  padding-bottom: 64rpx;
  color: #333333;
  font-size: 32rpx;
  font-weight: 600;
  white-space: normal;
  text-align: center;
}

.btn_footer {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.btn_footer view {
  width: 242rpx;
  height: 68rpx;
  border-radius: 46rpx;
  color: #ffffff;
  font-weight: 600;
  @include bg_theme;
  font-size: 28rpx;
  text-align: center;
  line-height: 68rpx;
  box-sizing: border-box;
}

.btn_footer .cancel {
  @include border_theme;
  @include font_theme;
  background: transparent;
  margin-right: 36rpx;
}
</style>
