<template>
  <!-- 在线购药 -->
  <view class="prescription">
    <view class="drug_title" >
      <text>医生姓名：{{detail.docName}}</text>
    </view>
    <view class="drug_title" @click="toDetail">
      <text>挂号详情</text>
      <block >
        <uni-icons
          type="arrowright"
          color="#333"
          size="20"
          v-if="showIcon"
        ></uni-icons>
      </block>
    </view>
    <!-- 标题 -->
    <view class="drug_title" @click="click">
      <text>处方详情</text>
      <block v-if="payStatus == 1">
        <uni-icons
          type="arrowright"
          color="#333"
          size="20"
          v-if="showIcon"
        ></uni-icons>
      </block>

      <block v-else>
        <uni-icons type="arrowright" color="#333" size="20"></uni-icons>
      </block>
    </view>

    <!-- 药店 药品列表 -->
    <view class="pharmacy_list" v-for="(p, pn) in list" :key="pn">
      <view class="pharmacy">
        <!-- 药店名称 -->
        <text
          >{{ p.drugstoreName
          }}{{ p.isProprietary == 1 ? "（自营）" : "" }}</text
        >
        <text v-if="deliveryType==2"  class="go_drugstore" @click="showDrugstoreInfo(p)">
          去药店
        </text>
      </view>

      <!-- 药品列表 -->
      <view
        class="durg_list"
        @click="click"
        v-if="beforePayVisible == 1 || stautsList.includes(payStatus)"
      >
        <!-- 单个药品 -->
        <view
          class="durg_item"
          v-for="(d, dn) in p.drugShoppingOnlineOrderList"
          :key="dn"
        >
          <img
            v-if="d.drugImg"
            v-img="d.drugImg"
            :data-src="errUrl"
            class="left"
          />
          <image v-else class="left" src="/static/shop/drug.png"></image>
          <!-- 内容 -->
          <view class="right">
            <!-- 药品名称 -->
            <view class="drug_name">{{ d.drugName }}</view>
            <!-- 规格 -->
            <view class="drug_info">规格: {{ d.gg }}</view>
            <!-- 活动 -->
            <view class="drug_red" v-if="d.activeName">
              单品{{ d.activeName }}</view
            >
            <!-- 价位数量 -->
            <view class="right_menu">
              <text class="price"
                >￥{{ d.drugRealMoney | toFixed }}
                <text class="del" v-if="d.drugShouldMoney != d.drugRealMoney"
                  >￥{{ d.drugShouldMoney | toFixed }}</text
                >
              </text>
              <text class="num">x{{ Number(d.quan) }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 药品摘要 未支付时显示 -->
      <view
        class="durg_info"
        @click="click"
        v-if="beforePayVisible == 0 && !stautsList.includes(payStatus)"
      >
        <view class="info_item" v-if="x">
          <text class="item_name">西药方</text>
          <text class="item_num">共{{ x }}种药品</text>
          <text class="item_price">￥{{ xp.toFixed(2) }}</text>
        </view>

        <view class="info_item" v-if="z">
          <text class="item_name">草药方</text>
          <text class="item_num">共{{ z }}种药品</text>
          <text class="item_price">￥{{ zp.toFixed(2) }}</text>
        </view>

        <view class="info_item" v-if="s">
          <text class="item_name">食品级</text>
          <text class="item_num">共{{ s }}种药品</text>
          <text class="item_price">￥{{ sp.toFixed(2) }}</text>
        </view>
      </view>

      <!-- 物流信息 -->
      <view
        class="logistics"
        v-if="deliveryType == 1 && [3, 4, 10].includes(Number(payStatus))"
      >
        <view class="item">
          <text>药店：{{ p.drugstoreName }}</text>

          <text>
            <block v-if="p.logisticsStatus == 0">待发货</block>
            <block v-if="p.logisticsStatus == 1">待收货</block>
            <block v-if="p.logisticsStatus == 2">已收货</block>
          </text>
        </view>

        <view v-if="isScan == 1">
          <view v-for="(logisItem, logInx) in logisticList" :key="logInx">
            <view class="item" v-if="p.logisticsStatus > 0">
              物流公司：{{ logisItem.logisticsName }}
            </view>

            <view class="item" v-if="p.logisticsStatus > 0">
              物流单号：{{ logisItem.logisticsCode }}
            </view>
            <view class="item_menu">
              <!--            <button class="bg" v-if="p.logisticsStatus == 0" @click="tip">-->
              <!--              催物流-->
              <!--            </button>-->
              <button
                v-if="p.logisticsStatus > 0"
                @click="lockSZ(logisItem, p.drugstoreId)"
              >
                查看物流
              </button>
              <!--            <button-->
              <!--              class="bg"-->
              <!--              v-if="p.logisticsStatus == 1"-->
              <!--              @click="confirmSH(p.merchantsOrderNo)"-->
              <!--            >-->
              <!--              确认收货-->
              <!--            </button>-->
            </view>
          </view>
        </view>
      </view>
    </view>
      <!-- 药店信息弹窗 -->
    <uni-popup ref="drugstorePopup" type="center">
      <view class="drugstore-popup">
        <view class="popup-title">药店信息</view>
        <view class="popup-content">
          <view class="popup-item">
            <text class="item-label">药店名称</text>
            <text class="item-value">{{ currentDrugstore.drugstoreName || '{药店名称}' }}</text>
          </view>
          <view class="popup-item" @click="copyText(currentDrugstore.address || '')">
            <text class="item-label">地址</text>
            <view class="item-value-with-copy" >
              <text class="item-value">{{ currentDrugstore.address || '' }}</text>
              <text class="copy-btn">
                <image src="/static/images/copy.png" mode="widthFix" style="width: 30rpx;height: 30rpx;"></image>
              </text>
            </view>
          </view>
          <view class="popup-item" @click="copyText(currentDrugstore.telNo || '')">
            <text class="item-label">联系方式</text>
            <view class="item-value-with-copy">
              <text class="item-value">{{ currentDrugstore.telNo || '' }}</text>
              <text class="copy-btn" >
                <image src="/static/images/copy.png" mode="widthFix" style="width: 30rpx;height: 30rpx;"></image>
              </text>
            </view>
          </view>
        </view>
        <view class="popup-btn" @click="closeDrugstorePopup">确定</view>
      </view>
    </uni-popup>
  </view>

</template>

<script>
import { Toast } from "@/common/js/pay.js";
import { queryLogisticInfo } from "@/api/base.js";
import uniPopup from '@/components/uni-popup/uni-popup.vue';
export default {
  name: "Prescription",
  components: {
    uniPopup
  },
  props: [
    "list",
    "payStatus",
    "deliveryType",
    "otc",
    "beforePayVisible",
    "showIcon",
    "detail",
    "pharmacy"
  ],
  data() {
    return {
      z: 0,
      zp: 0,
      x: 0,
      xp: 0,
      s: 0,
      sp: 0,
      errUrl: require("../../../../static/shop/drug.png"),
      stautsList: ["3", "4", "10"],
      isScan: 0, // 0 不是扫码开方  1 是扫码开方
      logisticList: [], // 神州快递鸟自发货查询物流相关信息
      currentDrugstore: {}, // 当前选中药店信息
    };
  },
  mounted() {
    console.log("111111", this.$route.query);
    if (this.$route.query.isScan) {
      this.isScan = this.$route.query.isScan;
      if (this.isScan == 1) {
        this.logicInit();
      }
    }
    this.getTypeList();
  },
  methods: {
    // 显示药店信息弹窗
    showDrugstoreInfo(drugstore) {
      console.log("drugstore", this.pharmacy);
      this.currentDrugstore = this.pharmacy;
      this.$refs.drugstorePopup.open();
    },
    
    // 关闭药店信息弹窗
    closeDrugstorePopup() {
      this.$refs.drugstorePopup.close();
    },
    
    // 复制文本到剪贴板
    copyText(text) {
      uni.setClipboardData({
        data: text,
        success: function () {
          Toast('复制成功');
        },
        fail: function() {
          Toast('复制失败');
        }
      });
    },
    
    toDetail(){
      uni.navigateTo({
        url: "/pages/personalCenter/diagnosisRecord/detail?id=" + this.detail.regId,
      });
    },
    // 扫码购药 初始化 调用快递鸟查询物流公司相关信息
    logicInit() {
      if (this.list[0].logisticsStatus > 0) {
        queryLogisticInfo({
          orderNo: this.$route.query.orderNo,
        }).then((ret) => {
          console.log(ret);
          this.logisticList = ret.data;
        });
      }
    },
    click() {
      if (this.payStatus == 1 && !this.showIcon) return;
      this.$emit("click");
    },
    tip() {
      Toast("已催物流");
    },
    // 扫码购药查询物流轨迹
    lockSZ(item, drugstoreId) {
      if (!item.logisticsCode) {
        Toast("未查询到物流单号");
        return;
      }
      console.log(item, drugstoreId);
      uni.navigateTo({
        url:
          "/pages/personalCenter/myOrder/allOrder/prescriptionOrder/logisticsSZ?code=" +
          item.logisticsCode +
          "&subjectId=" +
          drugstoreId +
          "&name=" +
          item.logisticsName +
          "&orderNo=" +
          this.$route.query.orderNo,
      });

      // 历史逻辑  暂时废弃
      // uni.navigateTo({
      //   url:
      //     "/pages/personalCenter/myOrder/allOrder/prescriptionOrder/logistics?code=" +
      //     item.logisticsCode +
      //     "&tel=" +
      //     tel +
      //     "&name=" +
      //     name +
      //     "&businessId=" +
      //     this.detail.businessId,
      // });
    },
    // 查看物流
    lock(item) {
      console.log("item", item, this.detail);
      // 患者手机号
      let tel = this.tel || "1234";
      tel = tel.slice(-4);
      // 物流名称
      let name = item.logisticsCustomName || item.logisticsName;
      if (!item.logisticsCode) {
        Toast("暂无物流信息");
        return;
      }

      uni.navigateTo({
        url:
          "/pages/personalCenter/myOrder/allOrder/prescriptionOrder/logistics?code=" +
          item.logisticsCode +
          "&tel=" +
          tel +
          "&name=" +
          name +
          "&businessId=" +
          this.detail.businessId,
      });
    },
    confirmSH(merchantsOrderNo) {
      this.$emit("confirm", merchantsOrderNo);
    },
    // 区分药品数量 金额
    getTypeList() {
      let z = 0,
        x = 0,
        s = 0;
      let zp = 0,
        xp = 0,
        sp = 0;
      const list = this.list;
      list.forEach((v) => {
        v.drugShoppingOnlineOrderList.forEach((k) => {
          // 西药
          if (k.drugType == 1 || k.drugType == 2) {
            x += 1;
            xp += (Number(k.price) * 100 * Number(k.quan)) / 100;
          }
          // 草药
          if (k.drugType == 3) {
            z += 1;
            zp += (Number(k.price) * 100 * Number(k.quan)) / 100;
          }
          // 食品级
          if (k.drugType == 4) {
            s += 1;
            sp += (Number(k.price) * 100 * Number(k.quan)) / 100;
          }
        });
      });

      this.z = z;
      this.x = x;
      this.s = s;
      this.zp = zp;
      this.xp = xp;
      this.sp = sp;
    },
  },
};
</script>

<style lang="scss" scoped>
.prescription {
  width: 100%;
  padding: 0 24rpx 24rpx;
  background-color: #fff;
  font-size: 28rpx;
  border-radius: 16rpx 16rpx 0 0;

  .drug_title {
    @include flex(lr);
    font-size: 28rpx;
    height: 80rpx;
    border-bottom: 1px solid #eee;

    text {
      font-weight: bold;
    }
  }

  .pharmacy_list {
    border-bottom: 1px dashed #ebebeb;

    &:only-child {
      border: none;
    }

    .pharmacy {
      @include flex(lr);
      font-size: 28rpx;
      font-weight: bold;
      height: 88rpx;
      border-bottom: 1px solid #ebebeb;

      text::nth-child(2) {
        font-weight: normal;
      }

      text {
        color: #333;

        &.wait {
          color: #ff5050;
        }

        &.close {
          color: #999;
        }

        &.done {
          @include font_theme;
        }
      }
      .go_drugstore{
        color: white;
        font-size: 24rpx;
        background-color: rgb(131, 106, 255);
        padding: 10rpx 20rpx;
        border-radius: 10rpx;
      }
    }

    .status_buts {
      @include flex(right);
      height: 88rpx;

      text {
        @include border_theme;
        @include flex;
        font-size: 26rpx;
        margin-left: 24rpx;
        width: 158rpx;
        height: 56rpx;
        border-radius: 28rpx;
        @include bg_theme;
        color: #fff;

        &.one {
          background: none;
          @include font_theme;
        }
      }
    }

    .durg_list {
      .durg_item {
        @include flex;
        padding: 24rpx 0;

        &:last-child {
          .right {
            border-bottom: none;
          }
        }

        .left {
          width: 128rpx;
          height: 128rpx;
          margin-right: 24rpx;
          border: 1px solid #eee;
          border-radius: 8rpx;
          flex: none;
        }

        .right {
          flex: 1;
          min-height: 128rpx;
          @include flex(lr);
          flex-direction: column;
          align-items: stretch;
          border-bottom: 1px solid #eee;

          .drug_name {
            flex: 1;
            font-size: 28rpx;
            font-weight: bold;
          }

          .drug_info {
            flex: 1;
            font-size: 24rpx;
            color: #999;
          }

          .drug_red {
            flex: 1;
            font-size: 24rpx;
            color: red;
          }

          .right_menu {
            flex: 2;
            @include flex(lr);
            align-items: flex-end;

            .price {
              font-size: 28rpx;
              color: #ff3b30;
              font-weight: bold;

              .del {
                font-size: 20rpx;
                color: #999;
                text-decoration: line-through;
                margin-left: 10rpx;
              }
            }

            .num {
              font-size: 28rpx;
            }
          }
        }
      }
    }

    .durg_info {
      padding: 24rpx 0;

      .info_item {
        height: 76rpx;
        border-radius: 8rpx;
        background-color: #f5f5f5;
        @include flex;
        padding: 0 24rpx;
        font-size: 28rpx;
        color: #333;
        margin-top: 24rpx;

        &:nth-child(1) {
          margin-top: 0;
        }

        .item_name {
          width: 136rpx;
          flex: none;
        }

        .item_num {
          flex: 1;
        }

        .item_price {
          flex: none;
          color: red;
          flex: none;
        }
      }
    }
  }

  .logistics {
    padding-bottom: 24rpx;

    .item {
      line-height: 50rpx;
      @include flex(lr);
      color: #333;
    }

    .item_menu {
      @include flex(right);
      padding-top: 24rpx;

      button {
        padding: 0;
        margin: 0;
        box-sizing: border-box;
        width: 160rpx;
        height: 56rpx;
        border-radius: 28rpx;
        font-size: 26rpx;
        @include font_theme;
        @include border_theme;
        @include flex;
        margin-left: 24rpx;

        &.bg {
          @include bg_theme;
          color: #fff;
        }
      }
    }
  }
}

/* 药店信息弹窗样式 */
.drugstore-popup {
  width: 600rpx;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
}

.popup-title {
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
  padding: 30rpx 0;
  border-bottom: 1px solid #eee;
}

.popup-content {
  padding: 20rpx 30rpx;
}

.popup-item {
  margin-bottom: 20rpx;
}

.item-label {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 10rpx;
  display: block;
}

.item-value {
  font-size: 30rpx;
  color: #333;
  word-break: break-all;
}

.item-value-with-copy {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.item-value-with-copy .item-value {
  flex: 1;
}

.copy-btn {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 10rpx;
}

.popup-btn {
  width: 220rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 32rpx;
  color: #fff;
  background-color: #836AFF;
  margin: auto;
  border-radius: 50rpx;
  margin-bottom: 10px;
}
</style>
