<template>
  <nav>
    <span
      v-for="item in list"
      :class="{ act: index == item.val }"
      :key="item.val"
      @click="setNav(item)"
      >{{ item.label }}</span
    >
  </nav>
</template>

<script>
export default {
  name: 'Nav',
  data() {
    return {
      list: [
        {
          val: 0,
          label: '全部',
        },
        {
          val: 1,
          label: '待支付',
        },
        {
          val: 2,
          label: '待发货',
        },
        {
          val: 3,
          label: '待收货',
        },
        {
          val: 4,
          label: '已完成',
        },
      ],
      index: 0,
    };
  },
  methods: {
    setNav(v) {
      if (this.index == v.val) return;
      this.index = v.val;
      this.$emit('navChange', v.val);
    },
  },
};
</script>

<style lang="scss" scoped>
nav {
  @include flex(lr);
  padding: 0 24rpx;
  width: 100%;
  box-sizing: border-box;
  height: 84rpx;
  background-color: #fff;
  z-index: 2;
  position: fixed;
  left: 0;
  top: 0;

  span {
    font-size: 28rpx;
    color: #666;

    &.act {
      @include font_theme;
      font-weight: bold;
    }
  }
}
</style>
