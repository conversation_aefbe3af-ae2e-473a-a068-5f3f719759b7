/**
 * 微信支付
 * @description 用于微信支付
 * @param {Object} info 支付所需参数
 * @returns {Promise} Promise
 */
export const wxPay = (info) => {
  console.log(info, 'info')
  return new Promise((resolve, reject) => {
    WeixinJSBridge.invoke('getBrandWCPayRequest', info, (res) => {
      if (res.err_msg == 'get_brand_wcpay_request:ok') {
        resolve()
      }
      reject()
    })
  })
}

export function Toast(title) {
  uni.showToast({
    title,
    icon: 'none',
    duration: 3000,
  })
}

export function showModal(
  title,
  content,
  confirmText,
  cancelText,
  isShowCancel
) {
  uni.showModal({
    title: title,
    content: content,
    showCancel: isShowCancel,
    confirmText: confirmText,
    cancelText: cancelText,
    success: function (res) {
      if (res.confirm) {
        console.log('用户点击确定')
      } else if (res.cancel) {
        console.log('用户点击取消')
      }
    },
  })
}

// 选择支付方式
export function selePay(that) {
  return new Promise((resolve, reject) => {
    // 如果不可选支付
    if (!that.payList.length) {
      Toast('当前没有配置支付方式')
      return reject()
    }
    // 如果只有一个支付方式
    if (that.payList.length == 1) {
      let item = that.payList[0]
      if (item.receiptType == 1) {
        resolve({
          index: 1,
          item,
        })
      } else {
        resolve({
          index: 2,
          item,
        })
      }
      return
    }
    // 如果在线支付
    uni.showActionSheet({
      itemList: ['微信支付', '支付宝支付'],
      success(res) {
        let index = res.tapIndex + 1
        let item
        item = that.payList.filter((it) => it.receiptType == index)[0]
        if (!item) {
          Toast('暂不支持该支付方式')
          return reject()
        }
        resolve({
          index,
          item,
        })
      },
      fail(err) {
        reject(err)
      },
    })
  })
}
