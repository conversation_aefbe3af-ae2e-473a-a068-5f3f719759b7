<template>
  <view class="register_list" style="background: rgba(240, 242, 252, 1)">
    <!-- 浮动图标 -->
    <GOHOME />

    <view class="head" v-if="isShowFilter">
      <HMfilterDropdown
        :menuTop="80"
        :filterData="filterData"
        :defaultSelected="defaultSelected"
        :updateMenuName="true"
        @confirm="confirm"
        :isjz="true"
        dataFormat="Object"
      ></HMfilterDropdown>
    </view>

    <view class="list">
      <block v-for="(item, index) in list">
        <CARD :item="item" :key="index" @click="toDetail" />
      </block>
    </view>

    <view class="empty_list" v-if="!list.length">
      <image src="/static/images/question/empty_toast.png" />
      <view> 暂无诊疗记录 </view>
    </view>
  </view>
</template>

<script>
import GOHOME from '@/components/home/<USER>';

// 卡片
import CARD from './com/card.vue';

import HMfilterDropdown from '@/components/HM-filterDropdown/HM-filterDropdown.vue';
import { getAllDept } from '@/api/base.js';
import { selectRegisterListPage } from '@/api/register';
import { findIsEnabledPatientByUserId } from '@/api/user.js';

import myJsTools from '@/common/js/myJsTools.js';
export default {
  components: {
    GOHOME,
    CARD,
    HMfilterDropdown,
  },
  data() {
    return {
      list: [],
      query: {
        page: 1,
        limit: 10,
        patientIds: [],
        deptId: '',
        addStartTime: '',
        addEndTime: '',
      },
      total: 0,
      isShowFilter: false,
      filterData: [
        // {
        //   name: '科室',
        //   type: 'hierarchy',
        //   submenu: [],
        // },
        // {
        //   name: '时间',
        //   type: 'hierarchy',
        //   submenu: [
        //     {
        //       name: '七天内',
        //       value: '0',
        //     },
        //     {
        //       name: '一月内',
        //       value: '1',
        //     },
        //     {
        //       name: '三月内',
        //       value: '2',
        //     },
        //     {
        //       name: '半年内',
        //       value: '3',
        //     },
        //   ],
        // },
        {
          name: '切换就诊人',
          type: 'hierarchy',
          submenu: [],
        },
      ],
      defaultSelected: [],
    };
  },
  async onLoad() {
  },
  async onShow() {
    if(uni.getStorageSync('currentPatientId')){
      this.defaultSelected=uni.getStorageSync('currentPatientId')
      this.query.patientIds = uni.getStorageSync('currentPatientId')
    }else{
      this.defaultSelected=[]
      this.query.patientIds = uni.getStorageSync('patientIdList');
    }
    await this.getAllPatient();
    this.query.page=1
    this.list=[]
    this.getList();
  },
  onPullDownRefresh() {
    this.query.page = 1;
    this.list = [];
    this.getList();
  },
  onReachBottom() {
    if (this.total <= this.list.length) return;
    this.query.page++;
    this.getList();
  },
  methods: {
    // 获取就诊人列表
    async getAllPatient() {
      let { data } = await findIsEnabledPatientByUserId({
        userId: uni.getStorageSync('userId'),
      });
      if (!data || !data.length) {
        this.isShowFilter = false;
        return;
      }
      let submenu = [];
      for (let i = 0; i < data.length; i++) {
        submenu.push({
          value: data[i].patientId,
          name: data[i].patientName,
        });
      }
      this.filterData[0].submenu = submenu;
      this.isShowFilter = true;
    },
    // 获取列表
    async getList() {
      let {
        data: { rows, total },
      } = await selectRegisterListPage(this.query);
      if (this.query.page > 1) {
        this.list = [...this.list, ...rows];
      } else this.list = rows;
      this.total = total;
      uni.stopPullDownRefresh();
    },
    // 获取所有科室列表
    async getAllDept() {
      let {
        data: { rows },
      } = await getAllDept({
        deptName: '',
        page: 1,
        limit: 100,
      });
      let submenu = [];
      for (let i = 0; i < rows.length; i++) {
        submenu.push({
          value: rows[i].deptId,
          name: rows[i].deptName,
        });
      }
      this.filterData[0].submenu = submenu;
    },
    //接收菜单结果
    confirm(e) {
      console.log(e,88)
      let valueArr = e.value;
      this.query.page = 1;
      // let deptId = valueArr[0];
      // if (deptId[0]) {
      //   this.query.deptId = deptId[0];
      // } else {
      //   this.query.deptId = '';
      // }
      let time = valueArr[1];
      console.log(time,valueArr)
      if (time&&time[0]) {
        // this.query.addEndTime = myJsTools.getDate(new Date());
        // let timeValue = time[0];
        //
        // let startTime;
        // if (timeValue == 0) {
        //   startTime = myJsTools.getDate('day', -7);
        // } else if (timeValue == 1) {
        //   startTime = myJsTools.getDate('month', -1);
        // } else if (timeValue == 2) {
        //   startTime = myJsTools.getDate('month', -3);
        // } else if (timeValue == 3) {
        //   startTime = myJsTools.getDate('month', -6);
        // }
        // this.query.addStartTime = startTime;
      } else {
        this.query.addStartTime = '';
        this.query.addEndTime = '';
      }

      let patients = valueArr[0];
      if (patients&&patients[0]) {
        this.query.patientIds = patients;
        uni.setStorageSync('currentPatientId',patients)
      } else {
        this.query.patientIds = uni.getStorageSync('patientIdList');
      }
      console.log(this.query)
      this.getList();
    },
    // 去问诊详情
    toDetail(item) {
      uni.setStorageSync('hosId', item.hosId);
      uni.navigateTo({
        url: './detail?id=' + item.regId,
      });
    },
  },
};
</script>

<style scoped lang="scss">
page {
  background-color: #f5f5f5;
}
.head {
  position: sticky;
  top: 10px;
  width: 90%;
  height: 38px;
  z-index: 2;
  margin: auto;
  border-radius: 7px;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px 1px 4px  rgba(121, 72, 234, 0.15);
  //overflow: hidden;
}
.list {
  padding: 45rpx 32rpx 32rpx 32rpx;
}
/* 列表为空提示 */
.empty_list {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  font-weight: 400;
  color: rgba(158, 163, 173, 1);
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.empty_list image {
  margin-bottom: 40rpx;
  width: 386rpx;
  height: 324rpx;
}
</style>
