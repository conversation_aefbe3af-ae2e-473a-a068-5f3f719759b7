<template>
  <view class="details">
    <view class="medRecordCard">
      <!-- 头部 -->
      <Docter :infoDetail="pageParam" />

      <view class="patientInfo">
        <view class="name">
          <view class="label"> 就诊人 </view>
          <view class="content">
            {{ info.patientName }}
          </view>
        </view>
        <view class="name">
          <view class="label"> 问诊类型 </view>
          <view class="content"> {{ info.visitTypeName }}问诊 </view>
        </view>
        <view class="name" v-if="
            item.jzStatus == '2' || item.jzStatus == '3' || item.type == 'chat'
          ">
          <view class="label"> 问诊时间 </view>
          <view class="content">
            {{ info.inquiryTime }}
          </view>
        </view>
        <view class="name" v-if="item.jzStatus == '2'">
          <view class="label"> 剩余时间 </view>
          <view class="content">
            <uni-countdown color="#333;" background-color="none" border-color="none" :day="usedTime.days"
              :hour="usedTime.hours" :minute="usedTime.minutes" :show-colon="false" :second="0"></uni-countdown>
          </view>
        </view>
      </view>

      <view class="costDetailCard">
        <view class="ownCost">
          <view class="title">费用明细（自费）</view>
          <view v-for="(item, index) in pay" :key="index" class="payDetail">
            <view class="priceDetailName">{{ item.priceDetailName }}</view>
            <view class="totalPay">{{ item.totalPay }}</view>
          </view>
          <view class="total">合计：<text style="color: #ff0707">￥{{ sum }}</text></view>
        </view>

        <view>
<!--          <view class="title">医保担负明细</view>-->
<!--          <view class="payDetail">-->
<!--            <view class="priceDetailName">医保统筹支付</view>-->
<!--            <view class="totalPay">0</view>-->
<!--          </view>-->
<!--          <view class="payDetail">-->
<!--            <view class="priceDetailName">医保账户支付</view>-->
<!--            <view class="totalPay">0</view>-->
<!--          </view>-->
          <view class="title">优惠券抵扣明细</view>
          <view class="payDetail">
            <view class="priceDetailName">新用户立减</view>
            <view class="totalPay">0</view>
          </view>
          <view class="total">实际支付：<text style="color: #ff0707">￥{{ sum }}</text></view>
        </view>
      </view>

      <view class="patientDetailCard">
        <view class="infoView">
          <view class="title">标题</view>
          <view class="content">{{ info.recordsTitle }}</view>
        </view>
        <view class="infoView">
          <view class="title">首诊诊断</view>
          <view class="content">{{ info.diagnosisDisease }}</view>
        </view>
        <view class="infoView">
          <view class="title">首诊医院</view>
          <view class="content">{{ info.firstVisitHos }}</view>
        </view>
        <view class="infoView">
          <view class="title">患病时长</view>
          <view class="content">{{ info.sickTime }}{{ info.timeUnit || "天" }}</view>
        </view>
        <view class="infoView">
          <view class="title">病情描述</view>
          <view class="content">{{ info.diseaseDescription }}</view>
        </view>
        <view class="infoView" style="border: none">
          <view class="title">期望帮助</view>
          <view class="content">{{ info.expectHelp }}</view>
        </view>
      </view>

      <view class="conDescription" v-if="diseaseImg.length > 0">
        <view> 病情图片 <text>(仅限本人和接诊医生可看）</text> </view>
        <view class="images">
          <image @click="pricewImgs(item)" mode="aspectFill" :src="item" v-for="(item, index) in diseaseImg"
            :key="index"></image>
        </view>
      </view>

      <view class="conDescription" v-if="medicalImg.length > 0">
        <view> 其它照片 <text>(仅限本人和接诊医生可看）</text> </view>
        <view class="images">
          <image :src="item" mode="aspectFill" v-for="(item, index) in medicalImg" :key="index"></image>
        </view>
      </view>

      <view class="btnsList" v-if="item.jzStatus == 2 || item.jzStatus == 3">
        <view class="patientBtn" v-if="bottomIsShow.isPre == '1'" @click="openPrescription">
          <view class="title">本次处方</view>
          <image src="../../static/images/back_1.png" mode="" class="rightArrow"></image>
        </view>

        <view class="patientBtn" @click="openInretRecord">
          <view class="title">量表</view>
          <image src="../../static/images/back_1.png" mode="" class="rightArrow"></image>
        </view>

        <view class="patientBtn" v-if="visiStatus" @click="openLineCard">
          <view class="title">门诊预约</view>
          <image src="../../static/images/back_1.png" mode="" class="rightArrow"></image>
        </view>

        <view class="patientBtn" @click="getInspection" v-if="bottomIsShow.isLis == '1'">
          <view class="title">检验单</view>
          <image src="../../static/images/back_1.png" mode="" class="rightArrow"></image>
        </view>

        <view class="patientBtn" @click="openInspect" v-if="bottomIsShow.isPacs == '1'">
          <view class="title">本次检查单</view>
          <image src="../../static/images/back_1.png" mode="" class="rightArrow"></image>
        </view>
      </view>
    </view>

    <view class="bottom" v-if="item.jzStatus == '1'">
      <text>服务说明：医生将于{{
          configTime
        }}小时内接诊。如超时未接诊，系统将自动为您退款</text>
      <view style="display: flex; color: #14a0e6; align-items: center"><text>剩余时间：</text>
        <uni-countdown color="#14A0E6;" background-color="none" border-color="none" :day="usedTime.days"
          :hour="usedTime.hours" :minute="usedTime.minutes" :show-colon="false" :second="0"></uni-countdown>
      </view>
      <text>由于医生工作特殊性，接诊可能不及时，请您耐心等待</text>
    </view>

    <view class="btn" v-if="item.jzStatus == '2'" @click="openChat">
      去问诊沟通
    </view>

    <!-- 去掉评价入口 -->
    <view class="btn" v-if="item.jzStatus == '3' && false" @click="openEval">
      评价
    </view>

    <view class="btns" v-if="item.type == 'chat' && item.isSuccess == '1'">
      <view class="refund" @click="updateStatus(3)">拒绝</view>
      <view class="agree" @click="updateStatus(2)">同意</view>
    </view>
  </view>
</template>

<script>
  import Docter from "@/components/doctor_header/doctor_header.vue";
  import myJsTools from "@/common/js/myJsTools.js";
  import {
    getPatientRecords
  } from "@/api/chat.js";
  import {
    getConfigInfo,
    getRegPayInfo,
    getVisitRealType
  } from "@/api/base";
  import date from "@/utils/date";
  import {
    getIsPre
  } from "@/api/patient";
  import {
    findPatientByPatientId,
    getDocInfoById
  } from "@/api/chatCardDetail";
  import {
    updateMedicalAuthorizeStatus
  } from "@/api/chat";
  // 查接诊状态
  import {
    getPatientReceive
  } from "@/api/user.js";
  let num = 3;
  export default {
    components: {
      Docter,
    },
    data() {
      return {
        pageParam: {},
        item: "",
        diseaseImg: [],
        medicalImg: [],
        info: {},
        bottomIsShow: {},
        pay: [],
        sum: "",
        plmIds: [],
        usedTime: {},
        patientInfo: {},
        // 接诊时间
        configTime: "",
        // 访问类型
        visiStatus: "",
        isClick: false,
        // 医生id
        docId: "",
        // 就诊人id
        patientId: "",
      };
    },
    onLoad(options) {
      this.docId = options.docId;
      this.patientId = options.patientId;

      this.pageParam = {
        docId: options.docId,
        patientId: options.patientId,
      };
      this.item = {
        regId: options.regId,
        jzStatus: options.jzStatus,
        isSuccess: options.isSuccess || 0,
        type: options.type || "detail", //chat为病例授权
        authorizeId: options.authorizeId || "",
      };
      this.getIsPreFun();
    },
    created() {
      this.getDetail();
      this.getPay();
      this.getDocInfo();
      this.getPatientInfo();
      this.getVisitRealType();
    },
    onReady() {
      var pages = getCurrentPages(); //当前页面栈
      if (pages.length > 1) {
        var beforePage = pages[pages.length - 2]; //获取上一个页面实例对象
        beforePage.refreshIfNeeded = true;
      }
    },
    methods: {
      //去评价
      openEval() {
        uni.navigateTo({
          url: "/pages/personalCenter/myOrder/evaluation/evaluation?param=" +
            JSON.stringify(this.pageParam),
        });
      },
      //去问诊沟通
      async openChat() {
        // 是否点击
        if (this.isClick) return;
        this.isClick = true;
        let {
          deptId,
          deptName,
          docId,
          docName,
          hosId
        } = this.pageParam;

        let {
          patientId,
          patientImg,
          patientName
        } = this.patientInfo;

        // 组织聊天信息
        let obj = {
          deptId,
          deptName,
          docId,
          docName,
          hosId,
          patientId,
          patientImg,
          patientName,
          regId: this.item.regId,
        };
        try {
          // 获取接诊信息
          let res = await getPatientReceive({
            docId,
            patientId,
          });

          if (res.code != 20000) return;

          obj.extendInfo_2 = JSON.parse(res.data.extendInfo);

          uni.setStorageSync("chatItem", obj);

          uni.setStorageSync("hosId", hosId);
        } catch (e) {
          //TODO handle the exception
          this.isClick = false;
          return;
        }

        // 拼接聊天id
        let id = patientId.toLocaleLowerCase() + "," + docId.toLocaleLowerCase();
        // 读取本地消息
        await this.$store.dispatch("getChatListId", {
          chatId: id,
        });
        this.isClick = false;
        uni.navigateTo({
          url: "/pages/chatList/chatDetail?param=" + JSON.stringify({
            docId
          }),
        });
      },
      //本次处方
      openPrescription() {
        uni.navigateTo({
          url: "/pages/patientRecord/prescriptionList?regId=" +
            this.item.regId +
            "&docId=" +
            this.docId,
        });
      },
      //量表
      openInretRecord() {
        let param = {
          docId: this.docId,
          patientId: this.patientId,
        };
        uni.navigateTo({
          url: "/pages/patientRecord/interviewRecord?param=" + JSON.stringify(param),
        });
      },
      // 获取检验单
      getInspection() {
        let regId = this.item.regId;
        uni.navigateTo({
          url: "./lisList?regId=" + regId,
        });
      },
      openInspect() {
        let regId = this.item.regId;

        uni.navigateTo({
          url: "./inspectList?regId=" + regId + "&docId=" + this.docId,
        });
      },
      // 获取就诊人信息
      async getPatientInfo() {
        let patientId = this.patientId;
        let res = await findPatientByPatientId({
          patientId,
        });
        let patientInfo = res.data;
        this.patientInfo = patientInfo;
        uni.setStorageSync("nowHZinfo", patientInfo);
      },
      //修改状态 -  授权
      updateStatus(num) {
        let tips;
        if (num == 3) {
          tips =
            "您确定拒绝医生的授权申请吗？拒绝之后医生将看不到您此次的病例信息";
        } else {
          tips = "确认同意授权";
        }
        let _this = this;
        uni.showModal({
          title: "病例授权",
          content: tips,
          success: function(res) {
            if (res.confirm) {
              updateMedicalAuthorizeStatus({
                authorizeId: _this.item.authorizeId,
                authorizeStatus: num,
              }).then((res) => {
                if (res.code == 20000) {
                  uni.navigateBack({
                    delta: 1,
                  });
                }

              });
            }
          },
        });
      },
      // 获取医生简介
      async getDocInfo() {
        let res = await getDocInfoById({
          docId: this.pageParam.docId,
        });
        let infoDetail = res.data;
        if (res.data.lableName) {
          let arr = res.data.lableName.split(",");
          for (let i = 0; i < arr.length; i++) {
            let obj = {
              lableName: arr[i],
            };
            arr[i] = obj;
          }
          infoDetail.docLable = arr;
        }
        // this.pageParam = Object.assign({}, infoDetail);
        this.pageParam = infoDetail;

        // 如果存在医生头像
        if (this.pageParam.docImg) {
          myJsTools.downAndSaveImg(this.pageParam.docImg, (url) => {
            this.pageParam.docImg = url;
          });
        }
      },
      // 问诊详情
      async getDetail() {
        let res = await getPatientRecords({
          regId: this.item.regId,
        });
        this.info = res.data;

        if (this.item.jzStatus == "1" || this.item.jzStatus == "2") {
          let configTime;

          let {
            data: config
          } = await getConfigInfo();

          config.forEach((element) => {
            if (element.configKey == "inquiry_duration") {
              configTime = element.configValue || 24;
            }
            // 接诊时长内
            this.configTime = configTime;
            // 剩余时间
            this.usedTime = date.DateDifference(
              this.info.signTime,
              this.info.presentTime,
              configTime
            );
          });
        }
        this.pageParam.regCode = this.info.regCode;

        // 如果存在图片
        if (res.data.diseaseImg) {
          let diseaseImg = JSON.parse(res.data.diseaseImg);

          let arr = [];
          diseaseImg.forEach((element) => {
            myJsTools.downAndSaveImg(element, (url) => {
              arr.push(url);
            });
          });
          // 赋值
          this.diseaseImg = arr;
        }

        // 如果存在其他图片
        if (res.data.medicalImg) {
          let medicalImg = JSON.parse(res.data.medicalImg);

          let arr = [];

          medicalImg.forEach((element) => {
            myJsTools.downAndSaveImg(element, (url) => {
              arr.push(url);
            });

            this.medicalImg = arr;
          });
        }
      },
      // 初始化获取费用明细
      async getPay() {
        let res = await getRegPayInfo({
          regId: this.item.regId,
        });
        let totalPrice = 0;
        totalPrice = res.data.reduce(
          (totalPrice, item) => totalPrice + item.totalPay,
          0
        );
        this.sum = totalPrice;
        this.pay = res.data;
      },
      //预览图片
      pricewImgs(path) {
        myJsTools.previewImg(path);
      },
      // 获取按钮是否显示  本次处方 检验单
      async getIsPreFun() {
        let regId = this.item.regId;
        let para = {
          regId,
          useType: "2",
        };
        let res = await getIsPre(para);
        if (res.code != 20000) return;
        // let bottomIsShow = {};
        // isLab;  // 有无检验单   1 有 0 无
        // isPre;  // 有无本次处方 1 有 0 无
        // isPacs; // 有无本次检查 1 有 0 无
        this.bottomIsShow = res.data;
      },
      // 查询排班类型
      async getVisitRealType() {
        let obj = {
          docId: this.docId,
          patientId: this.patientId,
        };
        let res = await getVisitRealType(obj);
        if (!res.data) return;
        this.visiStatus = res.data.visitRealType;
      },
      openLineCard() {
        let param = {
          docId: this.pageParam.docId,
          patientId: this.patientInfo.patientId,
        };
        let status = this.visiStatus;
        uni.navigateTo({
          url: "../chatCardDetail/outpatientAppointment?param=" +
            JSON.stringify(param) +
            "&visiStatus=" +
            status,
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .details {
    padding-bottom: 30rpx;
  }

  .medRecordCard {
    margin: 0 32rpx;
    padding-bottom: 24upx;

    // 医生信息
    .doctor_box_top {
      padding: 28upx 24upx;
      border-radius: 16upx;
      margin-top: 24upx;
    }
  }

  .patientInfo {
    .name {
      border-bottom: 1rpx solid #ebebeb;
      display: flex;
      align-items: center;
      margin: 0 32rpx;
      padding: 26rpx 0;

      .label {
        width: 30%;
      }
    }

    .name:last-child {
      border: none;
    }
  }

  .costDetailCard {
    padding: 0 32rpx 26rpx;

    .ownCost {
      border-bottom: 1px solid #ebebeb;
    }

    .title {
      line-height: 40rpx;
      font-weight: 600;
      padding-top: 26rpx;
    }

    .payDetail {
      display: flex;
      align-items: center;
      padding-top: 40rpx;

      .priceDetailName {
        color: #999999;
        width: 40%;
      }

      .totalPay {
        width: 60%;
        text-align: right;
      }
    }

    .total {
      margin-top: 40rpx;
      font-weight: 600;
      padding-bottom: 40rpx;
      text-align: right;
    }
  }

  .patientDetailCard {
    .infoView {
      border-bottom: 1rpx solid #ebebeb;
      padding: 26rpx 0;
      margin: 0 32rpx;
      display: flex;
      align-items: center;

      .title {
        width: 30%;
        font-weight: 500;
      }
    }
  }

  .conDescription,
  .patientDetailCard,
  .costDetailCard,
  .patientInfo,
  .btnsList {
    background: #ffffff;
    border-radius: 16rpx;
    color: #333333;
    font-size: 28rpx;
    margin-top: 24rpx;
  }

  .conDescription {
    padding: 26rpx 32rpx;

    text {
      color: #999999;
    }

    image {
      width: 200rpx;
      height: 200rpx;
      margin-right: 10rpx;
    }

    .images {
      margin-top: 24rpx;
    }

    image:nth-child(3),
    image:nth-child(6),
    image:nth-child(9) {
      margin-right: 0px;
    }
  }

  .bottom {
    color: #333;
    font-size: 28rpx;
    background: #f0f0f0;
    margin-top: 24rpx;
    padding: 32rpx;

    /deep/.uni-countdown__splitor {
      @include font_theme;
    }
  }

  .btn {
    width: 686upx;
    @include bg_theme;
    color: #ffffff;
    @include flex;
    height: 90rpx;
    border-radius: 50rpx;
    font-size: 32rpx;
    text-align: center;
    margin: 36rpx 32rpx 0;
  }

  /deep/.uni-countdown__splitor {
    padding: 0px;
    margin: 0px;
  }

  /deep/.uni-countdown__number {
    margin: 0px;
    padding: 0px;
    width: 40rpx;
    height: 40rpx;
  }

  .btnsList {
    padding: 0 32rpx;

    .patientBtn {
      position: relative;
      padding: 26rpx 0;
      color: #333333;
      font-size: 28rpx;
      line-height: 40rpx;
      border-bottom: 1rpx solid #ebebeb;

      image {
        width: 44rpx;
        height: 44rpx;
        position: absolute;
        right: 0rpx;
        top: 24rpx;
      }
    }

    :last-child {
      border-bottom: none;
    }
  }

  .btns {
    padding: 32rpx;
    background: #f0f0f0;
    display: flex;

    view {
      width: 48%;
      text-align: center;
      line-height: 98rpx;
      height: 98rpx;
      color: #ffffff;
      font-size: 32rpx;
      border-radius: 50rpx;

      font-weight: 600;
    }

    .refund {
      background: #ffffff;
      color: #333333;

      margin-right: 16rpx;
    }

    .agree {
      @include bg_theme;
    }
  }
</style>