<template>
  <view class="docInfo">
    <view class="docUserInfo">
      <view class="docHead">
        <image
          :src="docImg ? docImg : '/static/images/docHead.png'"
          mode="aspectFill"
          class="docImg"
        ></image>
      </view>
      <view class="info">
        <view class="nameView">
          <text>{{ docInfo.docName }}</text>
          <view class="operate" v-if="pageType == 'service'">
            <view class="attentionBtn" @click="collectOperation">{{
              collectFlag ? '已关注' : '关注'
            }}</view>
            <view class="share">
              <image
                src="../../static/images/index/slices.png"
                class="shareIcon"
              ></image>
            </view>
          </view>
        </view>
        <view class="jobTitle">
          <text>{{ docInfo.deptName }} | {{ docInfo.docProf }}</text>
        </view>
        <view class="label">
          <text
            class="doc_label_blue"
            v-for="(item, index) in docInfo.docLable" :key="index"
            >{{ item.lableName || item }}</text
          >
        </view>
      </view>
    </view>
    <view class="jzInfo" v-if="pageType == 'service'">
      <view>
        <view class="tag">好评度</view>
        <view class="num">{{ docInfo.percentage }}%</view>
      </view>
      <view>
        <view class="tag">接诊量</view>
        <view class="num">{{ docInfo.consultationCount }}</view>
      </view>
<!--      <view>-->
<!--        <view class="tag">平均响应</view>-->
<!--        <view class="num">{{ docInfo.responseTime }}分钟</view>-->
<!--      </view>-->
    </view>
  </view>
</template>

<script>
import { Toast } from '@/common/js/pay.js';
import {
  addCollectDoctor,
  cancelCollectDoctor,
  findDoctorByID,
  getIsExist,
} from '../../api/base';
import { getDocInfoById } from '../../api/chatCardDetail';
import myJsTools from '@/common/js/myJsTools';

export default {
  name: 'docInfo',
  props: {
    docId: {
      default: '',
    },
    deptId: {
      default: '',
    },
    pageType: {
      default: 'service',
    },
  },
  data() {
    return {
      docInfo: {},
      docImg: '',
      collectFlag: false, //是否已关注
    };
  },
  created() {
    if (this.pageType != 'service') {
      this.getfindDoctorByID();
    } else {
      this.getDocInfo();
    }
    this.checkAttention();
  },
  methods: {
    // 获取医生信息
    getDocInfo() {
      getDocInfoById({
        docId: this.docId,
      }).then((res) => {
        let data = res.data;
        if (data.lableName) {
          data.docLable = data.lableName.split(',');
        }
        if (data.docImg) {
          myJsTools.downAndSaveImg(data.docImg, (url) => {
            this.docImg = url;
            this.$emit('docInfo', data);
          });
        } else {
          this.$emit('docInfo', data);
        }
        this.docInfo = Object.assign({}, data);
      });
    },
    // 获取医生信息
    getfindDoctorByID() {
      let para = {
        docId: this.docId,
        deptId: this.deptId,
      };
      findDoctorByID(para).then(async (res) => {
        let data = res.data;
        if (data.docImg) {
          myJsTools.downAndSaveImg(data.docImg, (url) => {
            this.docImg = url;
            data.docImg = url;
            this.$emit('docInfo', data);
          });
        } else {
          this.$emit('docInfo', data);
        }
        this.docInfo = Object.assign({}, data);
      });
    },
    // 获取该用户是否关注过医生
    checkAttention() {
      // 判断该医生是否收藏过
      if (uni.getStorageSync('userId')) {
        getIsExist({
          docId: this.docId,
          openid: uni.getStorageSync('wxInfo').openId,
        }).then((res) => {
          this.collectFlag = res.data;
        });
      }
    },
    // 关注和取消关注
    collectOperation() {
      if (wx.getStorageSync('userId')) {
        let obj = {
          docId: this.docId,
          userId: uni.getStorageSync('userId'),
          openid: uni.getStorageSync('wxInfo').openId,
          appid: uni.getStorageSync('appId'),
        };
        this.collectFlag = !this.collectFlag;
        if (this.collectFlag) {
          addCollectDoctor(obj).then((res) => {
            Toast('收藏成功');
          });
        } else {
          cancelCollectDoctor(obj).then((res) => {
            Toast('取消成功');
          });
        }
      } else {
        // this.showBind = true;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.docInfo {
  @include bg_theme;
  color: #ffffff;
  padding: 36rpx 32rpx 28rpx 32rpx;
}

.docUserInfo {
  display: flex;

  .docHead,
  .docHead uni-image {
    width: 128rpx;
    height: 128rpx;
    border-radius: 12upx;
  }

  .info {
    width: 100%;
    box-sizing: border-box;
    margin-left: 20rpx;

    .nameView {
      display: flex;
      align-items: center;

      text {
        font-size: 36rpx;
        line-height: 50rpx;
        display: inline-block;
        width: 63%;
        font-weight: 600;
      }

      .operate {
        display: flex;
      }
    }

    .share,
    uni-image {
      width: 40rpx;
      height: 40rpx;
      margin-left: 16rpx;
    }

    .attentionBtn {
      background: #ffffff;
      line-height: 40rpx;
      @include font_theme;
      border-radius: 4rpx;
      height: 40rpx;
      font-size: 26rpx;
      width: 120rpx;
      text-align: center;
    }

    .jobTitle {
      font-size: 32rpx;
      line-height: 44rpx;
    }

    .label {
      text {
        display: inline-block;
        background: #ffffff;
        line-height: 34rpx;
        padding: 0 16rpx;
        @include font_theme;
        border-radius: 16rpx;
        height: 34rpx;
        font-size: 22rpx;
      }
    }
  }
}

.jzInfo {
  background: rgba(255, 255, 255, 0.21);
  margin-top: 42rpx;
  display: flex;
  text-align: center;
  padding: 22rpx 0;

  view {
    flex: 1;
  }

  .tag {
    font-size: 24rpx;
    line-height: 34rpx;
    margin-bottom: 4rpx;
  }

  .num {
    font-size: 40rpx;
    font-weight: 600;
  }
}
</style>
