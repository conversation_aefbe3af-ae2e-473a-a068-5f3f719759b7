<template>
  <!-- 时间线 -->
  <view class="time_line">
    <!-- 时间段 -->
    <view class="line_item" v-if="returnTime">
      <text class="item">{{ returnTime }}</text>
      <text>患者退费</text>
    </view>

    <view class="line_item" v-if="invalidTime">
      <text class="item">{{ invalidTime }}</text>
      <text>检{{ isLis ? '验' : '查' }}单已失效</text>
    </view>

    <view class="line_item" v-if="uploadReportTime">
      <text class="item">{{ uploadReportTime }}</text>
      <text>已出报告</text>
    </view>

    <view class="line_item" v-if="verificationTime">
      <text class="item">{{ verificationTime }}</text>
      <text>检{{ isLis ? '验' : '查' }}中心检{{ isLis ? '验' : '查' }}</text>
    </view>

    <view class="line_item" v-if="receiveTime">
      <text class="item">{{ receiveTime }}</text>
      <text>检{{ isLis ? '验' : '查' }}中心接收样本</text>
    </view>

    <view class="line_item" v-if="ditributionTime">
      <text class="item">{{ ditributionTime }}</text>
      <text>采样地发出样本</text>
    </view>

    <view class="line_item" v-if="sampleTime">
      <text class="item">{{ sampleTime }}</text>
      <text>采样地采样</text>
    </view>

    <view class="line_item" v-if="signTime">
      <text class="item">{{ signTime }}</text>
      <text>患者签到</text>
    </view>

    <view class="line_item" v-if="payTime">
      <text class="item">{{ payTime }}</text>
      <text>患者支付</text>
    </view>

    <view class="line_item" v-if="addTime">
      <text class="item">{{ addTime }}</text>
      <text>医生开检{{ isLis ? '验' : '查' }}单</text>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    // 是否检验
    isLis: Boolean,
    // 开单时间
    addTime: String,
    // 支付时间
    payTime: String,
    // 签到时间
    signTime: String,
    // 采样时间
    sampleTime: String,
    // 发货时间
    ditributionTime: String,
    // 签收时间
    receiveTime: String,
    // 检查时间
    verificationTime: String,
    // 报告时间
    uploadReportTime: String,
    // 失效时间
    invalidTime: String,
    // 退费时间
    returnTime: String,
  },
};
</script>

<style lang="scss" scoped>
.time_line {
  font-size: 28rpx;
  color: #333;
  position: relative;
  background: #fff;
  border-radius: 8rpx;
  padding: 32rpx;
  margin-top: 16rpx;

  &::before {
    content: '';
    display: block;
    position: absolute;
    width: 1px;
    top: 60upx;
    left: 38upx;
    height: calc(100% - 110upx);
    @include bg_theme;
  }

  .line_item {
    font-size: 28upx;
    padding-left: 30upx;
    margin-bottom: 36upx;
    position: relative;
    @include flex(lr);

    .item {
      @include font_theme;
    }

    // 原点
    &::before {
      content: '';
      display: block;
      position: absolute;
      left: 0;
      top: 50%;
      margin-top: -8upx;
      width: 16upx;
      height: 16upx;
      border-radius: 50%;
      @include bg_theme;
    }

    &:last-child {
      margin-bottom: 0;
    }

    .time {
      font-size: 24upx;
    }
  }
}
</style>
