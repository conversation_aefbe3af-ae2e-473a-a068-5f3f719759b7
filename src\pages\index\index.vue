<!--
 * @Descripttion: 
 * @version: 
 * @Author: zhengyangyang
 * @Date: 2025-05-20 17:09:26
 * @LastEditors: zhengyangyang
 * @LastEditTime: 2025-07-04 15:16:03
-->
<template>
  <!-- 首页 -->
  <view>
    <img
      v-show="imgflag"
      class="home_img"
      ref="homeimg"
      :src="homeimg"
      alt=""
      @error="imageLoad"
    />

    <view class="home" :class="{ home_position: imgflag }">
      <!-- 搜索 -->
      <view class="search-p">
        <view class="search" @click="toPath('/pages/search/search')">
          <image src="/static/images/search/search.png" />
          <text>请输入您想要搜索的内容</text>
        </view>
      </view>
      <!-- 轮播图-->
      <SWIPERIMG :list="swiperImgList"></SWIPERIMG>
      <view style="padding: 32rpx">
        <!-- 卡片 -->
        <CARD v-if="fristList.length" :list="fristList" :unit="unit" />

        <!-- 卡片更多 -->
        <view
          class="card_more"
          v-if="numList.length > 1"
          @click="showMore = true"
          >查看更多<uni-icons
            type="arrowdown"
            color="#999"
            size="14"
          ></uni-icons
        ></view>
        <!-- 菜单 -->
        <MENU :isAll="isAll" />
        <!-- 轮播 -->
        <!-- <SWIPER v-if="recList.length" :list="recList" /> -->
        <!-- 特色科室 -->
        <MAINDEPT v-if="isMainDept"></MAINDEPT>
         <!-- AI助手 -->
         <view class="ai-assistant" @click="showInDevelopmentMessage">
          <view class="ai-assistant-content">
            <image src="/static/images/ai-icon/ai.png" style="width: 40rpx; height: 40rpx;" />
          </view>
        </view>
         
        <!-- 消息列表卡片 -->
        <view class="message-card">
          <view class="message-card-header">
            <view class="message-card-title">
              <TITLE title="消息列表" />
            </view>
            <view class="message-card-more" @click="toPath('/pages/article/index')">
              查看全部
              <uni-icons type="right" color="#999" size="14"></uni-icons>
            </view>
          </view>
          <scroll-view scroll-y class="message-list-scroll" :show-scrollbar="false">
            <view 
              class="message-item" 
              v-for="(item, index) in articleList" 
              :key="index"
              @click="toArticleDetail(item.articleId)"
            >
              <view class="message-item-title">
                <text class="message-title-text">{{item.title}}</text>
                <view class="message-item-time">{{(item.publicTime||'').split(" ")[0]}}</view>
              </view>
            </view>
            <view class="message-empty" v-if="articleList.length === 0">
              <text>暂无消息</text>
            </view>
          </scroll-view>
        </view>
         
        <!-- 特色专科-->
        <!-- <SPECIALTY v-if="specType" :speImg="spe.speImg"></SPECIALTY> -->
        <!-- 名医推荐-->
        <!--      <FAMOUS :list="famousDocList" v-if="famousDocList.length > 0"></FAMOUS>-->
        <!-- 我的医生 -->
        <DOCLIST :list="myList" v-if="myList.length" />

        <!-- 弹窗 -->
        <view class="zhe" v-show="showMore">
          <view class="pop">
            <view class="fiexd" @click="showMore = false">
              <uni-icons type="arrowdown" color="#666" size="26"></uni-icons>
            </view>
            <view class="warp">
              <!-- 卡片 -->
              <CARD v-if="numList.length" :list="numList" :unit="unit" />
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>
<script>
import TITLE from "@/pages/inspect/com/itemTitle.vue";
import MENU from "./com/menu.vue";
import CARD from "./com/card.vue";
import SWIPER from "./com/swiper.vue";
import DOCLIST from "./com/docList.vue";
import SWIPERIMG from "./com/swiperImg.vue";
import SPECIALTY from "./com/specialtySpecialty.vue";
import FAMOUS from "./com/famousDoc.vue";
import MAINDEPT from "./com/mainDept.vue";
import {
  randomDoctor,
  findDoctorByUserID,
  selectElectronicArticleListPage,
  getSysPlatformConfigByKeyList,
  patientHomeRecommendDoctor,
  queryPatientHomeCarouselMapListPage,
} from "@/api/base.js";
import { arcTagList } from "@/api/visit.js";

export default {
  name: "Home",
  components: {
    MENU,
    CARD,
    SWIPER,
    DOCLIST,
    SWIPERIMG,
    SPECIALTY,
    FAMOUS,
    MAINDEPT,
    TITLE,
  },
  data() {
    return {
      homeimg: "",
      imgflag: true,
      // 我的医生
      myList: [],
      // 推荐医生
      recList: [],
      // 号条
      numList: [],
      // 前三条
      fristList: [],
      // 是否开启智能导诊
      isAll: false,
      // 处方有效期
      unit: 7,
      // 显示更多
      showMore: false,
      swiperImgList: [],
      swiperTime: 5000,
      famousDocList: [],
      specType: false,
      isMainDept: false,
      spe: {
        speImg: "",
      },
      // 文章列表
      articleList: [],
      // 文章查询条件
      articleQuery: {
        publicStatus: 1,
        keywords: "",
        publicPort: 1,
        page: 1,
        labelId: null,
        limit: 8,
        isTop: 1
      }
    };
  },
  onLoad() {
    this.getMyDocList();
    //this.getRecDoc();
    this.getConfig();
    this.getFamousDocList();
    this.getSwiperImgList();
    this.getConfigInfoList();
    this.getArticles(); // 获取文章列表
  },
  onShow() {
    // this.$store.commit("SET_CHATLIST", []);
    if (uni.getStorageSync("userId")) this.getNumList();
    this.getimgUrl();
    this.getArticles(); // 刷新文章列表
    fetch({
      url: "http://*************/V5/role/collect",
      method: "POST",
      body: JSON.stringify(
        'mode=0&data={"access_token":"0ba8912635ce4f7d01311652de239435e3df137260c9a83fe13ec71d2fb049f4208c7a78ee64e6f4","balance":"0","channel":"toutiaotoufang","level":"87","mount":"","partyname":"","roleid":"4232935351779678","rolename":"S38424.%E6%87%A6%E5%BC%B1%E5%9B%A7%E9%A6%A8%E6%B0%B4","server_id":"1981090","sex":"0","sword":"0","time":"1710065027184","type":"2","vip":"1","sign":"61e16902fe770ce99ad1efb8cd6779d2"}'
      ),
    }).then((res) => {
      console.log(res.json(), "http://*************/V5/role/collect");
    });
  },
  methods: {
    // 获取文章列表
    async getArticles() {
      try {
        const { data } = await arcTagList(this.articleQuery);
        if (data && data.rows) {
          // 处理数据，添加置顶标记
          const articleData = data.rows.map(item => {
            return {
              ...item,
              isTop: item.isTop === 1 // 假设API返回的isTop为1表示置顶
            };
          });
          
          // 排序：置顶的文章优先显示
          // articleData.sort((a, b) => {
          //   if (a.isTop && !b.isTop) return -1;
          //   if (!a.isTop && b.isTop) return 1;
          //   return new Date(b.publicTime || 0) - new Date(a.publicTime || 0);
          // });
          
          // 限制最多显示8条
          this.articleList = articleData.slice(0, 8);
        } else {
          this.articleList = [];
        }
      } catch (error) {
        console.error('获取文章列表失败', error);
        this.articleList = [];
      }
    },
    
    // 跳转到文章详情
    toArticleDetail(id) {
      uni.navigateTo({
        url: `/pages/article/detail?articleId=${id}`
      });
    },
    
    // 显示开发中提示
    showInDevelopmentMessage() {
      uni.navigateTo({
        url: '/pages/aiAssistant/index'
      });
    },
    //获取全局参数
    async getConfigInfoList() {
      let res = await getSysPlatformConfigByKeyList();
      let newArr = res.data;
      newArr.forEach((item) => {
        if (item.configKey == "whetherToDisplaySpecialSpecialties") {
          this.specType = item.configValue == "1" ? true : false;
        }
        if (item.configKey == "whetherToDisplaySpecialSpecialtiesUrl") {
          this.spe.speImg = item.configValue;
          console.log("this.spe.speImg", this.spe.speImg);
        }
        if (item.configKey == "homepage_display_special_dept") {
          this.isMainDept = item.configValue == "1" ? true : false;
        }
      });
    },
    //查询患者端首页轮播图
    async getSwiperImgList() {
      let { data } = await queryPatientHomeCarouselMapListPage();
      this.swiperImgList = data;
      console.log("999", data);
    },
    //名医推荐
    async getFamousDocList() {
      let { data } = await patientHomeRecommendDoctor();
      if (data.length > 4) {
        this.famousDocList = data.slice(0, 4);
      } else {
        this.famousDocList = data;
      }
      console.log("this.famousDocList", this.famousDocList);
    },
    imageLoad() {
      this.imgflag = false;
    },
    getimgUrl() {
      if (process.env.NODE_ENV === "development") return;
      this.$nextTick((_) => {
        let href = window.location.host;
        let index = href.indexOf("index.html");
        // let img = href.substring(0, index) + "home.png";
        let img = CONFIG_ENV.VUE_APP_FIRMID + "/patientHome.png";
        this.homeimg = img;
      });
    },
    // 查询配置
    async getConfig() {
      let { data } = await getSysPlatformConfigByKeyList([
        "display_intelligent_diagnosis",
        "prescription_indate",
        "PatientShowCoronavirusTest",
      ]);
      // console.log(data);
      data.forEach((v) => {
        if (v.configKey == "prescription_indate") {
          this.unit = v.configValue;
        }
        if (v.configKey == "display_intelligent_diagnosis") {
          this.isAll = v.configValue == 0 ? false : true;
        }
      });
    },
    // 跳转
    toPath(url) {
      uni.navigateTo({
        url,
      });
    },
    // 查询关注过的医生
    async getMyDocList() {
      let { data } = await findDoctorByUserID({
        openid: uni.getStorageSync("wxInfo").openId,
        userId: uni.getStorageSync("userId") || "",
      });
      this.myList = data;
    },
    // 获取推荐医生
    async getRecDoc() {
      let { data } = await randomDoctor({
        openid: uni.getStorageSync("wxInfo").openId,
        userId: uni.getStorageSync("userId") || "",
      });
      this.recList = data;
    },
    // 查询电子号条
    async getNumList() {
      let list = uni.getStorageSync("patientIdList") || [];
      if (!list.length) return;
      let { data } = await selectElectronicArticleListPage({
        patientIds: list,
      });
      this.numList = data;
      // if (data.length > 3) {
      //   this.fristList = data.slice(0, 3);
      // } else {
      //   this.fristList = data;
      // }
      if (data.length > 1) {
        this.fristList = data.slice(0, 1);
      } else {
        this.fristList = data;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.search-p {
  background: linear-gradient(
    90deg,
    rgba(103, 107, 223, 1) 0%,
    rgba(137, 131, 228, 1) 100%
  );
  padding: 32rpx 28rpx;
}
page {
  background: #f5f5f5;
}
.home_img {
  width: 100%;
  height: 130rpx;
  vertical-align: top;
  // float: left;
}
.home {
  background: #ffffff;
  border-radius: 32rpx 32rpx 0 0;

  .search {
    @include flex(left);
    height: 60rpx;
    border-radius: 44upx;
    background-color: #fff;
    padding: 0 36upx;
    font-size: 32upx;
    color: $k-info-title;
    border: 2rpx solid #f5f5f5;
    background: rgba(255, 255, 255, 0.8);
    image {
      width: 44upx;
      height: 44upx;
      margin-right: 18upx;
    }
  }

  /* 消息列表卡片样式 */
  .message-card {
    background: #ffffff;
    border-radius: 12rpx;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
    margin-top: 24rpx;
    margin-bottom: 24rpx;
    padding: 24rpx;
    overflow: hidden;
  }

  .message-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
  }

  .message-card-title {
    display: flex;
    align-items: center;
    font-size: 32rpx;
    font-weight: 500;
    color: #333333;
  }

  .message-icon {
    width: 36rpx;
    height: 36rpx;
    margin-right: 8rpx;
  }

  .message-card-more {
    display: flex;
    align-items: center;
    font-size: 24rpx;
    color:  rgba(135, 79, 240, 1);
  }

  .message-list-scroll {
    max-height: 320rpx;
    overflow: auto;
  }

  .message-item {
    padding: 16rpx 0;
    border-bottom: 1rpx solid #f0f0f0;
  }

  .message-item:last-child {
    border-bottom: none;
  }

  .message-item-title {
    display: flex;
    align-items: center;
    margin-bottom: 8rpx;
  }

  .message-tag {
    background: rgba(131, 106, 255, 0.1);
    color: #836AFF;
    font-size: 20rpx;
    padding: 2rpx 8rpx;
    border-radius: 4rpx;
    margin-right: 8rpx;
  }

  .message-title-text {
    font-size: 28rpx;
    color: #333333;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
  }

  .message-item-time {
    font-size: 24rpx;
    color: #999999;
    text-align: right;
  }

  .message-empty {
    padding: 30rpx 0;
    text-align: center;
    color: #999999;
    font-size: 28rpx;
  }

  .card {
    margin-top: 24rpx;
  }

  .card_more {
    height: 80rpx;
    @include flex;
    font-size: 28rpx;
    color: #999;
    margin-top: 20rpx;
  }

  .swiper {
    //margin-top: 24rpx;
  }

  .doc_list {
    margin-top: 24rpx;
  }

  /* AI助手样式 */
  .ai-assistant {
    margin-top: 30rpx;
    margin-bottom: 20rpx;
    background-color: #fff;
    border-radius: 12rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
    height: 100rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .ai-assistant-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .ai-title {
    font-size: 32rpx;
    color: #333;
    font-weight: 500;
  }

  .zhe {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.5);
    @include flex(right);
    flex-direction: column;

    .pop {
      width: 100vw;
      background-color: #f5f5f5;
      max-height: 70vh;
      overflow-y: scroll;
      padding: 0 0 120rpx;
      border-radius: 32rpx 32rpx 0 0;
      position: relative;
      @include flex;
      flex-direction: column;

      .warp {
        padding: 0 32rpx;
        width: 100%;
        box-sizing: border-box;
        flex: 1;
        overflow-y: scroll;

        .card {
          margin-top: 0rpx;
        }
      }

      .fiexd {
        height: 60rpx;
        @include flex;
        background-color: #f5f5f5;
        z-index: 1;
        flex: none;
      }
    }
  }
}
.home_position {
  position: absolute;
  top: 90rpx;
}
</style>
