<template>
  <!-- 头部样式 -->
  <div class="doc_head">
    <!-- 医生信息 -->
    <div class="doc">
      <div
      >
        <image
          :src="info.docImg ? info.docImg : '/static/images/docHead.png'"
          :class="
          info.isOnline == 0 ? 'docImg_box_unOnline' : 'docImg_box_Online'
        "
          mode="aspectFill"
          alt=""
          class="head_url"
        />
        <div style="position: relative;margin-top: -15px;z-index: 10;text-align: center">
          <div v-if="info.isOnline == 0" style=" width: 37px;height: 15px;border-radius: 15px;background: #c7c7c7;margin: auto;display: flex;align-items: center;justify-content: space-evenly;color: #efefef">
            <span style="font-size: 8px">离线</span>
          </div>
          <div v-else style=" width: 37px;height: 15px;border-radius: 15px;background: #0AC2B2;margin: auto;display: flex;align-items: center;justify-content: space-evenly;color: #ffffff">
            <span style="font-size: 8px">在线</span>
          </div>
        </div>
      </div>
      <!-- 右侧 -->
      <div class="right">
        <div class="top">
          <div class="docName">
            <text class="name">{{ info.docName }}</text>
            <image
              @click="openZz"
              src="/static/images/index/uw.png"
              mode="aspectFill"
              alt=""
              class="dzzz"
            />
          </div>

          <div class="top_right">
            <!-- 认证 -->
            <div
              v-if="info.docAuditStatus == 2"
              style="
                box-sizing: border-box;
                width: 120rpx;
                display: flex;
                justify-content: center;
                align-items: center;
                margin-top: 8rpx;
              "
              @click="showProps"
            >
<!--              <span style="color: #ffffff; font-size: 12px">已认证</span>-->
<!--              <image-->
<!--                style="width: 24rpx; height: 24rpx; margin-left: 4rpx"-->
<!--                src="/static/images/yrz.png"-->
<!--                alt=""-->
<!--              />-->
              <image
                  style="width: 100rpx; height: 34rpx; margin-left: 4rpx"
                  src="/static/images/yrz.png"
                  alt=""
              />
            </div>
          </div>
        </div>
        <!-- 科室 职称 -->
       <p class="mid" style="margin-top: 5px">{{ info.deptName }} · {{ info.docProf }}</p>

        <div class="label_box" style="margin-top: 5px">
          <span v-if="info.workHosName!=='神州互联网医院'" style="display: inline-block;height: 16px;opacity: 1;border-radius: 4px;background: rgba(255, 195, 0, 1);color: white;padding: 3px 4px;">{{ info.hosLevelType }}</span>
          <span :style="{paddingLeft: info.workHosName==='神州互联网医院'?'0px':'0 6px'}">{{ info.workHosName }}</span>
        </div>
      </div>
    </div>

    <!-- 擅长 简介 -->
    <div class="desc" v-if="info.multiPointPractice == 1">
      <p class="item" :class="{ omit: shrinkFlag }" style="display: flex;color: rgba(153, 153, 153, 1)">
        <img src="/static/images/sc.png" alt="" style="margin-right: 10px;margin-top: 3px;height: 16px">
<!--        <span class="label">专业擅长</span>-->
        {{ info.specialties }}
      </p>

      <p class="item" :class="{ omit: shrinkFlag }" style="color: rgba(153, 153, 153, 1)">
        <span class="label" style="color: rgba(153, 153, 153, 1)">执业简介：</span>
        {{ info.docSynopsis }}
      </p>

      <p
        class="more"
        @click="shrinkFlag = !shrinkFlag"
        v-show="info.specialties.length > 40 || info.docSynopsis.length > 40"
      >
        <uni-icons
          v-show="shrinkFlag"
          type="arrowdown"
          color="#8AD0F3"
          size="20"
        ></uni-icons>
        <uni-icons
          v-show="!shrinkFlag"
          type="arrowup"
          color="#8AD0F3"
          size="20"
        ></uni-icons>
      </p>
    </div>

    <!-- 统计 -->
<!--    <div class="count">-->

<!--    </div>-->
    <!-- 弹框 -->
    <uni-popup ref="popup" type="center">
      <div class="zzCode">
        <div class="docName title">
          <text>电子证照</text>
          <image
            src="/static/images/index/close.png"
            mode="aspectFill"
            alt=""
            @click="closeZz"
            class="close"
          />
        </div>
        <div class="codeInfo">
          <img
            v-img="info.electronicLicense"
            class="zzPhoto"
            mode="aspectFill"
          />

        </div>
      </div>
    </uni-popup>
    <!-- 认证弹框 -->
    <uni-popup ref="popups" type="bottom">
      <div class="propsCon">
        <div class="docName title" style="justify-content: left;font-weight: 500;font-size: 14px">
          <text>认证信息</text>
        </div>
        <div class="codeInfo">
          <div style="margin-bottom: 40rpx; margin-top: 40rpx">
            <div>
              <image
                src="/static/images/zgz.png"
                class="zzPhoto"
                mode="aspectFill"
              />
              <p>资格证</p>
            </div>
            <image
              style="width: 40rpx; height: 40rpx"
              src="/static/images/success.png"
              alt=""
            />
          </div>
          <div style="margin-bottom: 40rpx">
            <div>
              <image
                src="/static/images/zyz.png"
                class="zzPhoto"
                mode="aspectFill"
              />
              <p>执业证</p>
            </div>
            <image
              style="width: 40rpx; height: 40rpx"
              src="/static/images/success.png"
              alt=""
            />
          </div>
          <div style="margin-bottom: 40rpx">
            <div>
              <image
                src="/static/images/rl.png"
                class="zzPhoto"
                mode="aspectFill"
              />
              <p style="display: flex; flex-direction: column">
                <span>人脸识别认证</span>
                <span style="font-size: 12px;color: rgba(153, 153, 153, 1);margin-top: 5px;"
                  >保障所有服务均由医生本人提供</span
                >
              </p>
            </div>
            <image
              style="width: 40rpx; height: 40rpx"
              src="/static/images/success.png"
              alt=""
            />
          </div>
        </div>
      </div>
    </uni-popup>
  </div>
</template>

<script>
import { Toast } from "../../../../common/js/pay";

export default {
  name: "Head",
  components: {},

  props: {
    info: {
      type: Object,
      default() {
        return {
          specialties: "",
          docSynopsis: "",
        };
      },
    },
    // 是否关注
    collectFlag: {
      type: Number | String,
      default: 0,
    },
  },
  data() {
    return {
      shrinkFlag: true,
    };
  },
  methods: {
    showProps() {
      this.$refs.popups.open();
    },
    openZz() {
      console.log(this.info.electronicLicense);
      if (!this.info.electronicLicense) {
        Toast("暂无电子证照");
        return;
      }
      this.$refs.popup.open();
    },
    closeZz() {
      this.$refs.popup.close();
    },
    collectOperation() {
      this.$emit("collect");
    },
    toShare() {
      this.$emit("share");
    },
  },
};
</script>

<style lang="scss" scoped>
.zx{
  width: 37px;
  height: 15px;
}
.docImg_box_unOnline {
  width: 128rpx;
  height: 128rpx;
  border-radius: 50%;
  border: 1px solid #CCCCCC;
  //border: 1px solid #eeeeee;
  //overflow: hidden;
}
.docImg_box_Online {
  border: 1px solid #0AC2B2;
  border-radius: 50%;
}
.docName {
  display: flex;
  align-items: center;
  .dzzz {
    width: 32rpx;
    height: 26rpx;
    margin-left: 24rpx;
  }
  .close {
    width: 32rpx;
    height: 32rpx;
    position: absolute;
    right: 24rpx;
    top: 24rpx;
  }
}
.zzCode {
  width: 90vw;
  font-size: 36rpx;
  font-weight: 800;
  .title {
    padding: 34rpx;
    text-align: center;
    justify-content: center;
    position: relative;
    background: #f6f6f6;
    border-radius: 20rpx 20rpx 0 0;
  }
  .codeInfo {
    background: #ffffff;
    text-align: center;
    padding: 38rpx;
    border-radius: 0 0 20rpx 20rpx;
    box-sizing: border-box;
    .zzPhoto {
      width: 614rpx;
      max-height: 1200rpx;
      display: inline-block;
    }
  }
}

.propsCon {
  width: 100%;
  background-color: #ffffff;
  padding: 40rpx;
  border-radius: 20rpx 20rpx 0 0;
  box-sizing: border-box;
  .title {
    font-size: 36rpx;
    font-weight: 800;
    text-align: center;
    justify-content: center;
    position: relative;
  }
  .codeInfo {
    display: flex;
    flex-direction: column;
    div {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .zzPhoto {
        width: 80rpx;
        height: 80rpx;
        margin-right: 50rpx;
      }
    }
  }
}

.doc_head {
  border-radius: 16rpx;
  background-color: #fff;
  padding: 0 24rpx 24rpx 24rpx;
  box-shadow: 0px 2px 4px  rgba(131, 106, 255, 0.2);
  .doc {
    @include flex;
    padding: 24rpx 0;

    .head_url {
      width: 128rpx;
      height: 128rpx;
      border-radius: 50%;
      flex: none;
    }

    .right {
      flex: 1;
      @include flex(lr);
      flex-direction: column;
      align-items: stretch;
      padding-left: 16rpx;
      min-height: 134rpx;

      .top {
        @include flex(lr);

        .name {
          font-size: 32rpx;
          font-weight: bold;
        }

        .top_right {
          @include flex;

          .gz {
            @include flex;
            width: 128rpx;
            height: 48rpx;
            border-radius: 24rpx;
            background-color: #e0f2fb;
            @include font_theme;
            font-size: 24rpx;

            .icon_gz {
              width: 32rpx;
              height: 32rpx;
              margin-right: 10rpx;
            }
          }

          .share {
            width: 40rpx;
            height: 40rpx;
            margin-left: 24rpx;
          }
        }
      }

      .mid {
        font-size: 28rpx;
        color: rgba(102, 102, 102, 1);
        font-weight: 500;
      }

      .label {
        @include flex(left);
        padding-top: 4rpx;
        overflow-x: scroll;

        span {
          padding: 0 12rpx;
          background-color: #e0f2fb;
          font-size: 22rpx;
          @include font_theme;
          @include flex;
          height: 34rpx;
          border-radius: 20rpx;
          margin-right: 24rpx;
          flex: none;
        }
      }
      .label_box {
        max-width: 60vw;
        padding-top: 4rpx;
        @include flex(left);
        overflow-x: scroll;
        span {
          padding: 0 12rpx;
          font-size: 22rpx;
          @include font_theme;
          @include flex;
          height: 34rpx;
          border-radius: 20rpx;
          margin-right: 24rpx;
          flex: none;
          color: rgba(51, 51, 51, 1);
        }
      }
    }
  }

  .desc {
    //border-bottom: 1px solid #eee;

    .item {
      font-size: 26rpx;
      color: #666;
      line-height: 40rpx;
      margin-bottom: 10rpx;

      &.omit {
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }

      .label {
        @include font_theme;
        padding-right: 12rpx;
        font-weight: bold;
      }
    }

    .more {
      @include flex;
      height: 40rpx;
      padding-bottom: 20rpx;

      .uni-icons {
        font-weight: bold;
      }
    }
  }

  .count {
    height: 88rpx;
    @include flex(left);
    font-size: 26rpx;
    color: #999;

    .item {
      margin-right: 12rpx;

      &:nth-child(1n + 2) {
        margin-left: 12rpx;
      }

      span {
        font-size: 30rpx;
        @include font_theme;
        font-weight: bold;
      }
    }
  }
}
</style>
