<template>
  <view>
    <view class="page-container">
      <!-- 医生头部 -->
      <Docter :infoDetail="appointInfoDetail" />

      <view class="bg_wh">
        <view class="person_info">
          <text>就诊人</text>
          <text>{{ patientInfo.patientName }}</text>
        </view>
        <view class="person_info">
          <text>问诊类型</text>
          <text>{{ appointInfoDetail.visitTypeName }}</text>
        </view>
        <view class="person_info">
          <text>服务类型</text>
          <text>{{ appointInfoDetail.type == 0 ? "咨询" : "复诊" }}</text>
        </view>
        <view class="person_info">
          <text>预约时间</text>
          <view class="time"
            >{{ appointReserveInfo.visitDate }}
            {{ appointReserveInfo.startTimeShow }}-{{
              appointReserveInfo.endTimeShow
            }}</view
          >
        </view>
      </view>
      <view class="bg_wh particulars">
        <view class="title_fw">费用明细（自费）</view>
        <template v-for="item in priceList">
          <view class="left_right">
            <text>{{ item.priceDetailName }}</text>
            <text>{{ item.totalPay }}</text>
          </view>
        </template>
        <view style="text-align: right; padding-top: 40rpx">
          合计：{{ detail.totalPay }}<text style="color: red"></text>
        </view>
<!--        <view class="title_fw">医保担负明细</view>-->
<!--        <view class="left_right">-->
<!--          <text>医保统筹支付</text>-->
<!--          <text>0</text>-->
<!--        </view>-->
<!--        <view class="left_right">-->
<!--          <text>医保账户支付</text>-->
<!--          <text>0</text>-->
<!--        </view>-->
        <view class="title_fw">优惠券抵扣明细</view>
        <view class="left_right">
          <text>新用户立减</text>
          <text>0</text>
        </view>
      </view>
      <!-- <view class="bg_wh">
				<view class="title_fw">订单说明</view>
				<view class="explain_font">
					单次{{appointInfoDetail.visitTypeName}}交流为15分钟，超时将自动挂断，您可继续通过图片、文字交流，交流自医生接诊{{inquiry_duration}}小时内有效。如医生{{ss_no_receive_duration}}小时未接诊，系统将为您退款，互联网医疗仅适用于常见疾病、慢性病复诊患者，急重诊患者请前往实体医疗机构就诊。
				</view>
				<view class="explain_font" v-if="appointReserveInfo.consultationCode==1">
					复诊时必须提供包含诊断的实体机构的病历资料针对同诊断复诊。复诊患者，医生将根据您的实际情况辩证开方、给出调理建议。
				</view>
				<view class="explain_font" wx:if="appointReserveInfo.consultationCode==1">
					在线复诊过程中，若由于您无法提供实体医疗机构的诊段证明，或医生无法在线得出您和您之前线下实体机构相同的诊断而无法为您开出处方和诊断的情况，将不会退还复诊费用。
				</view>
			</view> -->
    </view>
    <!-- 底部 -->
    <view class="footer">
      <view
        >实际支付：<text style="color: red">￥{{ detail.selfPay }}</text></view
      >
      <view :class="btnFlag ? 'pay' : 'btnactive'" @click="getPay"
        >立即支付</view
      >
    </view>
  </view>
</template>

<script>
import Docter from "@/components/doctor_header/doctor_header.vue";
import { apponitRegister } from "@/api/appoint.js";
import {
  getRegisterPayInfo,
  queryRegisterPayStatus,
  getIsExist,
  addCollectDoctor,
} from "@/api/base.js";
import Pay from "@/modules/pay";
// 支付相关
let PAY;

import myJsTools from "@/common/js/myJsTools.js";

let num = 3;
export default {
  components: {
    Docter,
  },
  data() {
    return {
      btnFlag: true,
      ghId: "",
      appointInfoDetail: {},
      appointReserveInfo: {},
      patientInfo: {},
      // 挂号分割返回的字段
      detail: {},
      priceList: [],
      prId: "",
      time: "",
      inquiry_duration: "", // 图文问诊时长
      ss_no_receive_duration: "",
      isShowDisease: false,
    };
  },
  async onLoad(option) {
    this.getPayList();
    this.isShowDisease = option.isShowDisease ? true : false;
    let patientInfo = uni.getStorageSync("patientInfo");
    let appointInfoDetail = uni.getStorageSync("appointInfoDetail");
    if (appointInfoDetail.docImg) {
      if (appointInfoDetail.docImg.indexOf("http") == -1) {
        myJsTools.downAndSaveImg(appointInfoDetail.docImg, (url) => {
          appointInfoDetail.docImg = url;
        });
      }
    }
    let appointReserveInfo = uni.getStorageSync("appointReserveInfo");
    let wholeArg = uni.getStorageSync("wholeArg");

    wholeArg.map((item) => {
      // 图文问诊时长
      if (item.configKey == "inquiry_duration") {
        this.inquiry_duration = item.configValue;
      }
      // 实时聊天未接诊，过期时长
      if (item.configKey == "ss_no_receive_duration") {
        this.ss_no_receive_duration = item.configValue;
      }
    });
    if (appointInfoDetail.isSwitch != "0") {
      appointInfoDetail.details = [
        {
          isMedicare: "0",
          priceDetailName: "互联网服务费",
          totalPay: appointInfoDetail.visitPrice,
          priceDetailId: "",
        },
      ];
    }
    let all = {
      ...patientInfo,
      ...appointInfoDetail,
      ...appointReserveInfo,
    };

    // 患者
    let {
      patientId,
      patientImg,
      patientName,
      idNo,
      birthDate,
      sex,
      sexCode,
      age,
      ageUnit,
      telNo,
      medicareNum,
    } = all;

    // 医生
    let {
      apw,
      deptId,
      deptName,
      details,
      dntName,
      docName,
      docId,
      docImgCopy: docImg,
      docTel,
      visitDate,
      visitDuration,
      visitTypeCode,
      visitTypeName,
      week,
      visitDate: appointDate,
      startTime: appointStartTime,
      endTime: appointEndTime,
      vrId,
      consultationCode: isSubsequent,
      prId,
      psaId,
    } = all;

    all = null;

    let obj = {
      // 患者信息
      patientId, // 患者id
      patientImg, // 患者头像
      patientName, // 患者姓名
      idNo, // 身份证号
      birthDate, // 出生日期
      sex, //性别
      sexCode, // 性别编码
      age, // 年龄
      ageUnit, // 年龄单位
      telNo, // 联系电话
      medicareNum, // 医保卡号
      // 医生信息
      apw, // 上下午
      deptId, // 科室id
      deptName, // 科室名称
      details, // 费用明细
      dntName, // 号别名称
      docId, // 医生id
      docImg, // 医生头像
      docName, // 医生名称
      docTel, // 医生电话
      visitDate, // 出诊日期
      visitDuration, // 出诊时长
      visitTypeCode, // 问诊类型
      visitTypeName, // 问诊类型名称
      week, //星期
      // 预约信息
      appointDate, // 预约日期
      appointStartTime, // 预约开始时间
      appointEndTime, // 预约结束时间
      vrId,
      isMedicare: 0, // 是否医保
      isSubsequent, // 服务类型，咨询，复诊
      medicareType: "", // 医保类型
      // 其他
      prId, // 患者病历id
      psaId, // 签署协议id
      regSource: 1, // 业务来源 app?
    };
    let res = await apponitRegister(obj);
    uni.setStorageSync("registration", res.data);
    let { regId, selfPay, time, ghId } = res.data.reg;

    appointInfoDetail.regId = regId;
    appointInfoDetail.cost = selfPay;
    appointInfoDetail.addTime = time;
    this.detail = res.data.reg;
    this.priceList = res.data.details;
    this.ghId = ghId;
    this.appointInfoDetail = appointInfoDetail;
    this.appointReserveInfo = appointReserveInfo;
    this.patientInfo = patientInfo;
    uni.setStorageSync("appointInfoDetail", appointInfoDetail);
  },
  methods: {
    // 收藏医生
    async getCollect() {
      let res = await getIsExist({
        docId: this.appointInfoDetail.docId,
        openid: uni.getStorageSync("wxInfo").openId,
      });
      if (res.data != 1) {
        let obj = {
          docId: this.appointInfoDetail.docId,
          userId: uni.getStorageSync("userId"),
          openid: uni.getStorageSync("wxInfo").openId,
          appid: uni.getStorageSync("appId"),
        };
        await addCollectDoctor(obj);
      }
    },

    // 获取支付方式
    async getPayList() {
      let hosId = uni.getStorageSync("hosId");
      PAY = new Pay(hosId);
    },

    // 点击支付
    async getPay() {
      this.btnFlag = false;
      let money = this.detail.selfPay;

      let { index, item } = await PAY.selePay(money);

      uni.showLoading({
        mask: true,
      });

      let para = {
        callId: item.appid,
        ghId: this.ghId,
        openid: uni.getStorageSync("wxInfo").openId,
        payType: index,
      };

      try {
        let res = await getRegisterPayInfo(para);

        uni.hideLoading();
        // 无需支付，返回成功，直接跳转
        if (res.data && res.data.success == "1") {
          this.getCollect();

          if (this.isShowDisease) {
            uni.reLaunch({
              url:
                "/pages/register/appointRegister/payState/index?isShowDisease=1&flag=1" +
                "&type=" +
                this.appointInfoDetail.visitTypeCode,
            });
          } else {
            uni.reLaunch({
              url:
                "/pages/register/appointRegister/payState/index?flag=1" +
                "&type=" +
                this.appointInfoDetail.visitTypeCode,
            });
          }

          return;
        }

        let info = res.data;

        // 微信支付
        if (index == 1) {
          this.wxPay(info);
          return;
        }
        // 支付宝
        this.btnFlag = true;
        uni.navigateTo({
          url:
            "/pages/pay/pay?price=" +
            money +
            "&ghId=" +
            this.ghId +
            "&url=" +
            btoa(info.url),
        });
      } catch (error) {
        console.log(error);
        this.btnFlag = true;
        uni.hideLoading();
      }
    },

    // 微信支付
    async wxPay(info) {
      try {
        await PAY.wxPay(info);
        this.getState();
      } catch (error) {
        PAY.Toast("取消支付");
        this.btnFlag = true;
      }
    },

    // 查询支付状态
    async getState() {
      if (num <= 0) return;
      uni.showLoading({
        mask: true,
      });
      try {
        let res = await queryRegisterPayStatus({
          ghId: this.ghId,
        });
        num--;
        uni.hideLoading();
        if (!res.data) return;
        if (res.data.regStatus == "2") {
          this.getCollect();

          if (this.isShowDisease) {
            uni.reLaunch({
              url:
                "/pages/register/appointRegister/payState/index?isShowDisease=1&flag=1" +
                "&type=" +
                this.appointInfoDetail.visitTypeCode,
            });
          } else {
            uni.reLaunch({
              url:
                "/pages/register/appointRegister/payState/index?flag=1" +
                "&type=" +
                this.appointInfoDetail.visitTypeCode,
            });
          }
          uni.reLaunch({
            url:
              "/pages/register/appointRegister/payState/index?flag=1" +
              "&type=" +
              this.appointInfoDetail.visitTypeCode,
          });
          return;
        } else {
          setTimeout(this.getState, 3000);
        }
      } catch (error) {
        uni.hideLoading();
      }
    },
  },
};
</script>

<style scoped lang="scss">
.page-container {
  padding: 24rpx 32rpx 120rpx;
  box-sizing: border-box;
  overflow-y: auto;
  background-color: #f5f5f5;

  .doctor_box_top {
    padding: 28upx 24upx;
    border-radius: 16upx;
  }
}

/* 主体内容 */
.bg_wh {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 0 24rpx;
  margin-top: 24rpx;
  font-size: 28rpx;
}

.bg_wh:first-child {
  margin-top: 0;
}

/* 问诊信息 */
.patient_box {
  margin-top: 40rpx;
  background-color: #ffffff;
  border-radius: 8rpx;
  padding: 0 32rpx;
}

.person_info {
  height: 92rpx;
  border-bottom: 1px solid #ebebeb;
  box-sizing: border-box;
  color: #333333;
  font-size: 28rpx;
  display: flex;
  align-items: center;

  text:first-child {
    width: 180rpx;
    flex: none;
  }

  .time {
    flex: 1;
  }
}

.person_info:last-child {
  border-bottom: none;
}

/* 费用明细 */
.particulars {
  /* padding-top: 26rpx; */
  padding-bottom: 26rpx;
}

.title_fw {
  color: #333333;
  font-weight: 600;
  padding-top: 26rpx;
}

.left_right {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #999999;
  padding-top: 40rpx;
}

.left_right text:last-child {
  color: #333333;
}

/* 服务说明 */
.explain_font {
  color: #999999;
  font-size: 28rpx;
  text-indent: 2em;
  padding: 20rpx 0;
}

/* 底部 */
.footer {
  width: 100%;
  position: fixed;
  left: 0;
  bottom: 0;
  height: 104rpx;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;
  font-size: 32rpx;
  box-sizing: border-box;
}

.pay {
  width: 160rpx;
  height: 60rpx;
  @include bg_theme;
  border-radius: 40rpx;
  text-align: center;
  line-height: 60rpx;
  color: #fff;
  font-size: 28rpx;
}

.footer .btnactive {
  width: 160rpx;
  height: 60rpx;
  border-radius: 40rpx;
  text-align: center;
  line-height: 60rpx;
  font-size: 28rpx;
  background-color: #ccc;
  color: #000;
}
</style>
