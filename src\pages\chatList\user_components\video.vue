<template>
  <!-- 视频消息 -->
  <view class="user_video">
    <!-- 气泡 -->
    <view class="video_content" @click.stop="video">
      <!-- 视频 -->
      <!-- <video
        class="cont_video"
        :src="videoSrc"
        :id="'v_' + id"
        @loadedmetadata="videoLoad"
        :poster="poster"
        x-webkit-airplay="allow"
        x5-video-player-type="h5"
        x5-video-player-fullscreen="true"
        x5-video-orientation="portraint"
        webkit-playsinline="true"
        playsinline="true"
        :controls="false"
        controls
      >
        <source :src="videoSrc" type="video/mp4" />
      </video> -->
      <iframe
        class="cont_video"
        ref="mapIframe"
        scrolling="no"
        :src="iframeSrc"
      ></iframe>
      <!-- 遮罩 -->
      <!-- <div class="zhe" v-show="!playing"></div> -->
    </view>
    <!-- 头像 -->
    <image
      @click="click"
      :src="imgUrl || '/static/images/docHead.png'"
      mode="aspectFill"
      class="user_img"
    />
  </view>
</template>

<script>
/* 
		以下参数解决安卓视频层级过高
		
		x-webkit-airplay="allow"
		
		启用H5播放器,是wechat安卓版特性
		x5-video-player-type="h5" 
		
		全屏设置，设置为 true 是防止横屏
		x5-video-player-fullscreen="true"
		
		播放器方向，landscape横屏，默认portraint竖屏
		x5-video-orientation="portraint"
		
		ios 10中设置可以让视频在小窗内播放
		webkit-playsinline="true"
		
		IOS微信浏览器支持小窗内播放
		playsinline="true"
	*/
let u = navigator.userAgent;
let isIOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);
export default {
  props: {
    // 头像
    imgUrl: {
      type: String,
      default: "",
    },
    // 地址
    videoSrc: {
      type: String,
      default: "",
    },
    // id
    id: {
      type: String,
      default: "",
    },
    // 封面
    poster: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      v: "",
      src: "",
      playing: false,
      iframeSrc: "",
    };
  },
  created() {
    let src = "videoCompent/video.html"
    this.iframeSrc = src;
    this.iframeSrc += "?add=" + this.videoSrc;
    console.log(this.iframeSrc)
  },
  async mounted() {
    let id = "v_" + this.id;
    let v = uni.createVideoContext(id);
    this.v = v;
  },
  methods: {
    click() {
      this.$emit("head");
    },
    async video() {},
    videoLoad(e) {},
  },
};
</script>

<style lang="scss" scoped>
.user_video {
  box-sizing: border-box;
  @include flex(right);
  padding-bottom: 30upx;
  align-items: flex-start;

  // 头像
  .user_img {
    width: 64upx;
    height: 64upx;
    border-radius: 4upx;
    margin-left: 16upx;
    flex: none;
  }

  // 内容
  .video_content {
    width: 280upx;
    position: relative;

    .zhe {
      position: absolute;
      width: 100%;
      height: 100%;
      z-index: 1;
      top: 0;
      left: 0;
    }

    .cont_video {
      width: 100%;
      border-radius: 16upx;
    }
  }
}
</style>
