<template>
  <view class="apply-page">
    <!-- 总金额和订单数量 -->
    <view class="amount-info">
      <text class="amount">总金额：¥{{companyInfo.amount|| totalMoney }}</text>
      <text class="count">共{{ companyInfo.amount?1:getSelectedOrders.length }}张</text>
    </view>

    <!-- 发票信息表单 -->
    <view class="form-content">
      <!-- 发票类型 -->
      <view class="form-item">
        <text class="label">发票类型</text>
        <picker
          @change="onInvoiceTypeChange"
          :value="invoiceTypeIndex"
          :range="invoiceTypes"
          range-key="name"
        >
          <view class="picker-value">
            {{ invoiceTypes.find(v=>v.id==companyInfo.invoiceType)?invoiceTypes.find(v=>v.id==companyInfo.invoiceType).name : '请选择' }}
            <uni-icons type="bottom" size="14" color="#666"/>
          </view>
        </picker>
      </view>

      <!-- 抬头类型 -->
      <view class="form-item">
        <text class="label">抬头类型</text>
        <view class="radio-group">
          <view
            class="radio-item"
            :class="{'active': companyInfo.titleType === '1'}"
            @click="selectTitleType('1')"
          >
            <text>个人</text>
          </view>
          <view
            class="radio-item"
            :class="{'active': companyInfo.titleType === '2'}"
            @click="selectTitleType('2')"
          >
            <text>企业</text>
          </view>
        </view>
      </view>

      <!-- 企业信息 -->
      <block v-if="companyInfo.titleType === '2'">
        <view class="form-item">
          <text class="label">发票抬头</text>
          <input
            type="text"
            v-model="companyInfo.companyName"
            placeholder="请输入企业名称"
          />
        </view>

        <view class="form-item">
          <text class="label">税号</text>
          <input
            type="text"
            v-model="companyInfo.taxNo"
            placeholder="请输入纳税人识别号"
          />
        </view>

        <view class="form-item">
          <text class="label">开户银行</text>
          <input
            type="text"
            v-model="companyInfo.bankName"
            placeholder="请输入开户银行"
          />
        </view>

        <view class="form-item">
          <text class="label">银行账号</text>
          <input
            type="text"
            v-model="companyInfo.bankAccount"
            placeholder="请输入银行账号"
          />
        </view>

        <view class="form-item">
          <text class="label">企业地址</text>
          <input
            type="text"
            v-model="companyInfo.companyAddress"
            placeholder="请输入企业地址"
          />
        </view>

        <view class="form-item">
          <text class="label">企业电话</text>
          <input
            type="text"
            v-model="companyInfo.phone"
            placeholder="请输入企业电话"
          />
        </view>
      </block>

      <!-- 发票抬头 -->
      <view class="form-item" v-if="companyInfo.titleType === '1'">
        <text class="label">发票抬头</text>
        <text class="value">{{companyInfo.invoiceTitle}}</text>
      </view>

      <!-- 发票内容 -->
      <view class="form-item">
        <text class="label">发票内容</text>
        <view class="radio-group">
          <view
            class="radio-item"
            :class="{'active': companyInfo.invoiceContentType === '1'}"
            @click="selectContentType('1')"
          >
            <text>明细</text>
          </view>
          <view
            class="radio-item"
            :class="{'active': companyInfo.invoiceContentType === '2'}"
            @click="selectContentType('2')"
          >
            <text>大类</text>
          </view>
        </view>
      </view>

      <!-- 身份证号 -->
      <view class="form-item" v-if="companyInfo.titleType === '1'">
        <text class="label">身份证号</text>
        <text class="value">{{companyInfo.idCardNo}}</text>
      </view>

      <!-- 手机号 -->
      <view class="form-item" v-if="companyInfo.titleType === '1'">
        <text class="label">手机号</text>
        <text class="value">{{companyInfo.phone}}</text>
      </view>

      <!-- 邮箱 -->
      <view class="form-item">
        <text class="label">邮箱</text>
        <input
          type="text"
          v-model="companyInfo.email"
          placeholder="请输入邮箱"
          @blur="validateEmail"
        />
      </view>
    </view>
    <!-- 提交按钮 -->
    <view class="bottom-btn">
      <button
        class="submit-btn"
        :disabled="!canSubmit"
        @click="showConfirm"
      >
        提交申请
      </button>
    </view>

    <!-- 确认弹窗 -->
    <uni-popup ref="confirmPopup" type="center">
      <uni-popup-dialog
        type="info"
        title="提示"
        content="请再次确认您提交的开票信息"
        :before-close="true"
        @confirm="submitApply"
        @close="hideConfirm"
      />
    </uni-popup>
  </view>
</template>

<script>
import { validateEmail } from '@/utils/validate.js'
import {mapGetters} from "vuex";
import {batchSave, getVisitingPersonInfo, szFinancialInvoiceInfo, szFinancialInvoiceUpdate} from "../../api/base";
import uniPopupDialog from "../../components/uni-popup/uni-popup-dialog.vue";
export default {
  components: {
    uniPopupDialog
  },
  data() {
    return {
      isSubmitting: false, // 节流控制状态
      orderIds: [], // 选中的订单ID
      totalAmount: '0.00', // 总金额
      orderCount: 0, // 订单数量
      invoiceTypes: [
        { id: '1', name: '普通发票' },
        { id: '2', name: '增值税发票' }
      ],
      invoiceTypeIndex: 0,
      companyInfo: {
        titleType: '', // personal/company
        invoiceContentType: '1', // detail/category
        invoiceTitle: '', // 发票抬头(就诊人姓名)
        idCardNo: '', // 身份证号
        phone: '', // 手机号
        email: '', // 邮箱
        taxNo: '',
        bankName: '',
        bankAccount: '',
        companyAddress: '',
        invoiceType:''
      },
      hasMultiPatients: false, // 是否存在多个就诊人
      statusMap: {
        1: '待开票',
        2: '驳回',
        3: '开票中',
        4: '待复核',
        5: '已开票',
        6: '重开票',
        7: '重开票待审核'
      },
      id:''
    }
  },

  computed: {
    ...mapGetters(['getSelectedOrders']),
    totalMoney(){
      return this.getSelectedOrders.reduce((total, order) => total + order.orderMoney, 0).toFixed(2)
    },
    canSubmit() {
      if(this.companyInfo.titleType === '1') {
        console.log(this.companyInfo.email,this.validateEmail(this.companyInfo.email))
        return this.companyInfo.email && this.validateEmail(this.companyInfo.email)&&this.companyInfo.invoiceType
      } else {
        return this.companyInfo.email &&
          this.validateEmail(this.companyInfo.email) &&
          this.companyInfo.companyName &&
          this.companyInfo.taxNo &&
          this.companyInfo.invoiceType
          // this.companyInfo.bankName &&
          // this.companyInfo.bankAccount &&
          // this.companyInfo.companyAddress &&
          // this.companyInfo.phone
      }
    }
  },

  onLoad(options) {
    // 编辑
    if(options.type==='modify'){
      this.id = options.id
      this.getDetail()
    }
    // 重开
    if(options.type==='reapply'){
      this.id = options.id
      this.getDetail()
    }
  },

  methods: {
    // 获取详情
    async getDetail() {
      try {
        const res = await szFinancialInvoiceInfo({
          pkId: this.id
        })
        this.companyInfo = res.data
        const request=await  getVisitingPersonInfo({
          patientId: this.companyInfo.patientId
        })
        if(request.data){
          this.companyInfo.invoiceTitle=request.data.patientName
          this.companyInfo.idCardNo=request.data.idNo
          this.companyInfo.phone=request.data.telNo
        }
      } catch(e) {
        console.error(e)
      }
    },

    // 选择发票类型
    onInvoiceTypeChange(e) {
      this.companyInfo.invoiceType = this.invoiceTypes[e.detail.value].id
    },

    // 选择抬头类型
    selectTitleType(type) {
      if(type === '1' &&this.getSelectedOrders.length ==1){
        this.companyInfo.invoiceTitle=this.getSelectedOrders[0].patientName
        this.companyInfo.idCardNo=this.getSelectedOrders[0].idCardNo
        this.companyInfo.phone=this.getSelectedOrders[0].phone
        this.companyInfo.titleType = type
        return;
      }
      // 判断this.getSelectedOrders中patientName是否一致
      if(type === '1' &&this.getSelectedOrders.length > 1) {
        const patientName = this.getSelectedOrders[0].patientName
        const patientNames = this.getSelectedOrders.map(v => v.patientName)
        // patientNames数组去重
        console.log( [...new Set(patientNames)])
        if([...new Set(patientNames)].length>1) {
          uni.showToast({
            title: '您选中的订单存在多位就诊人，抬头类型不可选择个人',
            icon: 'none'
          })
          return
        }
        this.companyInfo.invoiceTitle=this.getSelectedOrders[0].patientName
        this.companyInfo.idCardNo=this.getSelectedOrders[0].idCardNo
        this.companyInfo.phone=this.getSelectedOrders[0].phone
      }

      this.companyInfo.titleType = type
    },

    // 选择发票内容类型
    selectContentType(type) {
      this.companyInfo.invoiceContentType = type
    },

    // 校验邮箱
    validateEmail(email) {
      return validateEmail(email)
    },

    // 显示确认弹窗
    showConfirm() {
      this.$refs.confirmPopup.open()
    },

    // 隐藏确认弹窗
    hideConfirm() {
      this.$refs.confirmPopup.close()
    },

    // 提交申请
    async submitApply() {
      if (this.isSubmitting) return;
      this.isSubmitting = true;
      try {
        const selectedOrders=this.getSelectedOrders.map(v=>{
          return {
            orderNo:v.orderNo,
            patientId:v.patientId,
            orderType:v.orderType,
            amount:v.orderMoney,
            ...this.companyInfo
          }
        })
        if(this.companyInfo.pkId){
          await szFinancialInvoiceUpdate(this.companyInfo)
        }else{
          await batchSave(selectedOrders)
        }
        uni.showToast({
          title: '申请成功',
          icon: 'success'
        })

        setTimeout(() => {
          this.isSubmitting = false;
          uni.navigateBack()
        }, 1500)
      }catch (e) {
        this.isSubmitting = false;
      }
    },

    // 添加状态转换方法
    getStatusText(status) {
      return this.statusMap[status] || status
    }
  }
}
</script>

<style lang="scss" scoped>
.apply-page {
  min-height: 100vh;
  background: #F5F6FA;
  padding-bottom: 120rpx;
}

.amount-info {
  background: #fff;
  padding: 32rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .amount {
    font-size: 32rpx;
    color: #333;
    font-weight: 500;
  }

  .count {
    font-size: 28rpx;
    color: #666;
  }
}

.form-content {
  background: #fff;
  padding: 0 32rpx;

  .form-item {
    display: flex;
    align-items: center;
    min-height: 100rpx;
    border-bottom: 1rpx solid #eee;

    .label {
      width: 160rpx;
      font-size: 28rpx;
      color: #333;
    }

    .value {
      flex: 1;
      font-size: 28rpx;
      color: #666;
    }

    input {
      flex: 1;
      font-size: 28rpx;
      color: #333;
    }

    .picker-value {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 28rpx;
      color: #666;
    }

    .radio-group {
      flex: 1;
      display: flex;
      align-items: center;

      .radio-item {
        padding: 12rpx 32rpx;
        border: 1rpx solid #ddd;
        border-radius: 32rpx;
        margin-right: 20rpx;
        font-size: 26rpx;
        color: #666;

        &.active {
          background: linear-gradient(135deg, #516afb 0%, #859bff 99.97%);
          border-color:#6174ff;
          color: #fff;
        }
      }
    }
  }
}

.bottom-btn {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 16rpx 32rpx;
  background: #fff;

  .submit-btn {
    width: 100%;
    height: 88rpx;
    line-height: 88rpx;
    background:linear-gradient(135deg, #516afb 0%, #859bff 99.97%);
    color: #fff;
    font-size: 32rpx;
    border-radius: 44rpx;

    &[disabled] {
      background: #ccc;
    }
  }
}
</style>