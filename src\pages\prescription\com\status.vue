<template>
  <view class="status">
    <image style="width: 24px; height: 24px" src="/static/pre/路径 <EMAIL>" />
    <view v-if="status == 1" style="display: flex; flex-direction: column">
      <block v-if="deliveryType == 2">
        <view class="h2">待取药</view>
        <view class="status_info"
          >请尽快到店取药，超时未取，将不会为您保留药品</view
        >
      </block>

      <block v-else style="display: flex; flex-direction: column">
        <view class="h2">待发货</view>
        <view class="status_info">正在加急给您发货，请耐心等待</view>
      </block>
    </view>

    <view v-if="status == 3" style="display: flex; flex-direction: column">
      <view class="h2">待收货</view>
      <view class="status_info">已为您发货，请注意查收</view>
    </view>

    <view v-if="status == 5" style="display: flex; flex-direction: column">
      <block v-if="deliveryType == 2">
        <view class="h2"> 已取药 </view>
        <view class="status_info">已成功取药</view>
      </block>
      <block v-else style="display: flex; flex-direction: column">
        <view class="h2">已签收</view>
        <view class="status_info">已成功确认签收</view>
      </block>
    </view>

    <view v-if="status == 9" style="display: flex; flex-direction: column">
      <view class="h2">已失效</view>
      <view class="status_info">您的处方已失效</view>
    </view>

    <view v-if="status == 10" style="display: flex; flex-direction: column">
      <view class="h2">已退费</view>
      <view class="status_info">已为您退费，请注意查收</view>
    </view>

    <image class="icon" src="/static/pre/路径 <EMAIL>" />
  </view>
</template>

<script>
export default {
  name: "Status",
  props: {
    status: {
      type: Number | String,
      default: -1,
    },
    deliveryType: {
      type: String | Number,
      default: 1,
    },
  },
};
</script>

<style lang="scss" scoped>
.status {
  width: 100%;
  height: 180rpx;
  background: linear-gradient(90.8deg, #3b60ef 60%, #2cc793 130%);
  color: #fff;
  padding: 34rpx 36rpx;
  display: flex;

  .h2 {
    font-size: 38rpx;
    font-weight: bold;
    padding-left: 7px;
  }

  &_info {
    font-size: 24rpx;
    margin-top: 14rpx;
    position: relative;
    z-index: 1;
    padding-left: 7px;
  }

  .icon {
    width: 88rpx;
    height: 88rpx;
    position: absolute;
    top: 30rpx;
    right: 32rpx;
  }
}
</style>
