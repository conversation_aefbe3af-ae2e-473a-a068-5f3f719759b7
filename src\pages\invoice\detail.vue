<template>
  <div class="detail">
    <!-- 容器 -->
    <div class="list">
      <div class="item">
        <span class="bold"> 订单编号：{{ info.orderNo }} </span>

        <span class="status">{{ info.irStatusName }}</span>
      </div>

      <div class="item">
        <span class="bold">发票信息</span>
      </div>

      <div class="item">
        <span class="label">发票类型</span>
        <span class="value">{{ info.invoiceTypeName }}</span>
      </div>
      <div class="item">
        <span class="label">发票抬头</span>
        <span class="value">{{ info.invoiceHeaderName }}</span>
      </div>
      <!-- 个人 -->
      <block v-if="info.invoiceHeader == 1">
        <div class="item">
          <span class="label">个人名称</span>
          <span class="value">{{ info.personalOrCompanyName }}</span>
        </div>
        <div class="item" v-if="info.personalOrCompanyTelNo">
          <span class="label">手机号码</span>
          <span class="value">{{ info.personalOrCompanyTelNo }}</span>
        </div>
      </block>

      <block v-else>
        <div class="item">
          <span class="label">公司名称</span>
          <span class="value">{{ info.personalOrCompanyName }}</span>
        </div>
        <div class="item">
          <span class="label">公司税号</span>
          <span class="value">{{ info.companyDutyParagraph }}</span>
        </div>
        <div class="item" v-if="info.companyAddress">
          <span class="label">公司地址</span>
          <span class="value">{{ info.companyAddress }}</span>
        </div>
        <div class="item" v-if="info.personalOrCompanyTelNo">
          <span class="label">公司电话</span>
          <span class="value">{{ info.personalOrCompanyTelNo }}</span>
        </div>
        <div class="item" v-if="info.depositaryBank">
          <span class="label">开户银行</span>
          <span class="value">{{ info.depositaryBank }}</span>
        </div>
        <div class="item" v-if="info.bankAccount">
          <span class="label">银行账号</span>
          <span class="value">{{ info.bankAccount }}</span>
        </div>
      </block>
    </div>

    <div class="list mt">
      <div class="item">
        <span class="label">收件人</span>
        <span class="value">{{ info.receiverName }}</span>
      </div>
      <div class="item">
        <span class="label">联系电话</span>
        <span class="value">{{ info.receiverTelNo }}</span>
      </div>
      <div class="item">
        <span class="label">详细地址</span>
        <span class="value">{{ info.receiverAddress }}</span>
      </div>
    </div>

    <div class="list mt" v-if="info.irStatus == 3">
      <div class="item red">
        <span class="label">驳回理由</span>
        <span class="value">{{ info.reasonsForFailure }}</span>
      </div>
    </div>

    <FOOTER @click="submit" @leftClick="toDetail" :showLeft="info.irStatus > 2">
      <template #left>
        <span>订单详情</span>
      </template>
      <span v-if="info.irStatus == 2">订单详情</span>
      <span v-if="info.irStatus == 3">修改</span>
      <span v-if="info.irStatus == 4">物流信息</span>
    </FOOTER>
  </div>
</template>

<script>
import FOOTER from '@/components/footer_button/button.vue';
import { getInvoiceDetail } from '@/api/invoice';

export default {
  name: 'DetailInvoice',
  components: {
    FOOTER,
  },
  data() {
    return {
      irId: '',
      info: {},
    };
  },
  onLoad(v) {
    this.irId = v.irId;
    this.getDetail();
  },
  onShow() {
    if (this.info.irStatus == 3) {
      this.getDetail();
    }
  },
  methods: {
    async getDetail() {
      let { data } = await getInvoiceDetail(this.irId);
      this.info = data;
    },
    // 去详情
    toDetail() {
      const { orderNo, orderType, source, businessId, hosId } = this.info;

      uni.setStorageSync('hosId', hosId);

      // 挂号
      if (orderType == 1) {
        uni.navigateTo({
          url: '/pages/order/detail/register?orderNo=' + orderNo,
        });
        return;
      }

      // 处方
      if (orderType == 3) {
        uni.navigateTo({
          url:
            '/pages/order/detail/drugStatus?orderNo=' +
            orderNo +
            '&source=' +
            source,
        });
        return;
      }

      // 自定义服务
      if (orderType == 4) {
        uni.navigateTo({
          url: '/pages/chatCardDetail/customService?id=' + businessId,
        });
      }

      // 检验
      if (orderType == 5) {
        uni.navigateTo({
          url: '/pages/order/detail/lis?orderNo=' + orderNo,
        });
        return;
      }

      // 检查
      if (orderType == 6) {
        uni.navigateTo({
          url: '/pages/order/detail/pacs?orderNo=' + orderNo,
        });
        return;
      }
    },
    // 提交
    submit() {
      const {
        irStatus,
        receiverTelNo,
        logisticsCode,
        logisticsCompany,
      } = this.info;

      // 申请中 -> 订单详情
      if (irStatus == 2) {
        this.toDetail();
        return;
      }
      // 已开票 -> 物流信息
      if (irStatus == 4) {
        let telNo = receiverTelNo.slice(-4);
        uni.navigateTo({
          url:
            '/pages/personalCenter/myOrder/allOrder/prescriptionOrder/logistics?code=' +
            logisticsCode +
            '&name=' +
            logisticsCompany +
            '&tel=' +
            telNo,
        });
        return;
      }
      // 已驳回 -> 修改
      if (irStatus == 3) {
        uni.navigateTo({
          url: '/pages/invoice/input?irId=' + this.irId,
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
page {
  background: #f5f5f5;
}
.detail {
  padding: 24rpx 24rpx 124rpx 24rpx;

  .list {
    border-radius: 8rpx;
    background: #fff;
    padding: 0 24rpx;

    &.mt {
      margin-top: 24rpx;
    }

    .item {
      @include flex(lr);
      align-items: flex-start;
      font-size: 28rpx;
      border-bottom: 1px solid #f5f5f5;
      padding: 24rpx 0;

      &.red {
        color: red;
      }

      &:last-child {
        border-bottom: none;
      }

      .bold {
        font-weight: bold;
      }

      .status {
        color: red;
        font-weight: bold;
      }

      .label {
        flex: none;
        width: 180rpx;
      }

      .value {
        flex: 1;
      }
    }
  }
}
</style>
