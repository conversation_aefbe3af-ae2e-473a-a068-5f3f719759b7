<template>
	<!-- 文字全屏放大 -->
	<view class="text_zoom" @click="click">
		<view v-html="text"></view>
	</view>
</template>

<script>
	export default {
		name: 'text_zoom',
		props: {
			text: {
				type: String,
				default: ''
			}
		},
		methods: {
			click() {
				this.$emit('concal');
			}
		}
	}
</script>

<style lang="scss" scoped>
	.text_zoom {
		width: 100vw;
		height: 100vh;
		padding: 30upx;
		position: fixed;
		z-index: 999;
		background-color: #fff;
		@include flex;
		top: 0;
		font-size: 44upx;
		box-sizing: border-box;
		word-break: break-all;
		
		/deep/ img {
			width: 40px !important;
			height: 40px !important;
		}
	}
</style>
