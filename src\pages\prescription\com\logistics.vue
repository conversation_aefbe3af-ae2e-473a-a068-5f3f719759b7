<template>
  <!-- 物流信息 -->
  <div class="logistics">
    <p class="title">物流信息</p>

    <!-- 药店列表 -->
    <div class="list">
      <!-- 单个 -->
      <div class="list_item" v-for="(item, index) in list" :key="index">
        <div class="item_text">
          <span class="label">药店名称</span>
          <span class="name">{{ item.drugStoreName }}</span>
          <span class="status wait" v-if="item.logisticsStatus == 0"
            >待发货</span
          >
          <span class="status done" v-if="item.logisticsStatus == 1"
            >待收货</span
          >
          <span class="status" v-if="item.logisticsStatus == 2">已收货</span>
        </div>

        <div class="item_text" v-if="item.telNo !== ''" >
          <span class="label">药店电话</span>
          <span class="cell-phone" @click="call" >{{item.telNo}}</span>
        </div>

        <div class="item_text" v-if="item.logisticsStatus > 0">
          <span class="label">物流公司</span>
          <span class="name">{{
            item.logisticsCustomName || item.logisticsName
          }}</span>
        </div>

        <div class="item_text" v-if="item.logisticsCode">
          <span class="label">物流单号</span>
          <span class="name">{{ item.logisticsCode }}</span>
        </div>

        <div class="but">
          <span
            class="confirm"
            v-if="item.logisticsStatus == 0"
            @click="showToast"
            >催物流</span
          >

          <span
            class="lock"
            v-if="item.logisticsStatus == 1"
            @click="toLogistics(item)"
            >查看物流</span
          >
          <span
            class="confirm"
            v-if="item.logisticsStatus == 1"
            @click="click(item)"
            >确认收货</span
          >

          <span
            class="confirm"
            v-if="item.logisticsStatus == 2"
            @click="toLogistics(item)"
            >查看物流</span
          >
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { Toast } from '@/common/js/pay.js';
export default {
  name: 'Logistics',
  props: {
    list: {
      type: Array,
      default: () => [],
    },
    tel: String,
    detail:{
      type: Object,
      default: () => {}
    }
  },
  methods: {
    call() {
      this.list.forEach(item => {
        if (item.telNo) {
          uni.makePhoneCall({
            phoneNumber: item.telNo,
          });
        }
      });
    },
    showToast() {
      Toast('已为您催物流');
    },
    toLogistics(item) {
      // 患者手机号
      let tel = this.tel || '1234';
      tel = tel.slice(-4);
      // 物流名称
      let name = item.logisticsCustomName || item.logisticsName;
      if (!item.logisticsCode) {
        Toast('暂无物流信息');
        return;
      }
      console.log('item',item,this.detail)
      uni.navigateTo({
        url:
          '/pages/personalCenter/myOrder/allOrder/prescriptionOrder/logistics?code=' +
          item.logisticsCode +
          '&tel=' +
          tel +
          '&name=' +
          name+'&businessId='+this.detail.orderBussId,
      });
    },
    click(item) {
      this.$emit('click', item);
    },
  },
};
</script>

<style lang="scss" scoped>
.logistics {
  background: #fff;
  border-radius: 8rpx;
  margin-top: 24rpx;

  .title {
    height: 92rpx;
    @include flex(left);
    font-size: 28rpx;
    font-weight: bold;
    padding-left: 32rpx;
  }

  .list {
    color: #333;

    .list_item {
      padding: 12rpx 32rpx;
      border-top: 1px solid #f5f5f5;

      .item_text {
        @include flex(lr);
        font-size: 28rpx;
        line-height: 60rpx;

        .label {
          padding-right: 40rpx;
          flex: none;
        }

        .name {
          @include hide;
          flex: 1;
        }
        .cell-phone{
          color: #14A0E6;
          flex: 1;
        }

        .status {
          padding-left: 40rpx;
        }

        .status {
          flex: none;

          &.wait {
            color: #ff5050;
          }

          &.done {
            @include font_theme;
          }
        }
      }

      .but {
        @include flex(right);
        padding: 24rpx 0 12rpx;

        span {
          width: 160rpx;
          height: 60rpx;
          border-radius: 30rpx;
          @include flex;
          font-size: 28rpx;
          @include border_theme;

          &.lock {
            @include font_theme;
          }

          &.confirm {
            @include bg_theme;
            color: #fff;
            margin-left: 32rpx;
          }
        }
      }
    }
  }
}
</style>
