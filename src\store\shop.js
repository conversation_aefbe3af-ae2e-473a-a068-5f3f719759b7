import {
  addShoppingCart,
  queryShoppingCartGroupByDrugStore,
  updateShoppingCart,
  deleteShoppingCart,
} from '@/api/shop.js'
import { Toast, showModal } from '@/common/js/pay.js'
export default {
  namespaced: true,
  state: () => ({
    // 购物车列表
    shopList: [],
    scanCheckDrugList: [],
    // 失效
    unableList: [],
    // 购物车总价
    totalMoney: 0,
    // 药品总数量
    drugNum: 0,
    // 药店id列表
    drugStoreList: [],
    checkStore: {
      drugstoreName: '',
      drugstoreId: '',
    },
    drugDetailStore: {
      drugstoreName: '',
      drugstoreId: '',
    },
  }),
  mutations: {
    // 设置
    SET_SHOP_LIST(state, val) {
      state.shopList = val
      let num = 0
      val.forEach((v) => {
        v.shoppingCartList.forEach((k, i) => {
          num += Number(k.quan)
        })
      })
      state.drugNum = num
    },
    // 失效药
    SET_UNABLE_LIST(state, val) {
      state.unableList = val || []
    },
    // 购物价位
    SET_PRICE(state, num) {
      state.totalMoney = num
    },
    // 设置已选药店id
    SET_DRUGSTORE_LIST(state, arr) {
      state.drugStoreList = arr
    },
    SETCHECKSTORE(state, obj) {
      state.checkStore = obj
    },
    SET_DrugDetailStore(state, obj) {
      state.drugDetailStore = obj
    },
    SET_SCAN_CHECK_DRUG_LIST(state, val) {
      state.scanCheckDrugList = val
    },
  },
  actions: {
    // 扫码查询
    async setScanCheckDrugList({ commit }, arr) {
      commit('SET_SCAN_CHECK_DRUG_LIST', arr)
    },
    // 设置列表
    setList({ commit }, data) {
      const { drugStoreList, shoppingCartUnableList } = data
      commit('SET_SHOP_LIST', drugStoreList)
      commit('SET_UNABLE_LIST', shoppingCartUnableList)
    },
    // 获取列表
    async getCartList({ dispatch }) {
      let res = await queryShoppingCartGroupByDrugStore()
      dispatch('setList', res.data)
    },
    // 新增购物车
    async addItem({ dispatch }, item) {
      let res = await addShoppingCart(item)
      dispatch('setList', res.data)
    },
    async addScanItem({ dispatch, state }, item) {
      state.scanCheckDrugList.push(item)
    },
    // 更新购物车
    async upShopList({ dispatch }, obj) {
      let res = await updateShoppingCart(obj)
      dispatch('setList', res.data)
    },
    async changeDrugQuan({ dispatch }, item) {
      let { drugKc, quan, yfkcId, amount } = item
      // 特定药品ID的最大数量限制
      if (item.drugId === '72d263bd0ebd48e49e9063320efa0ff7') {
        if (amount > 30) {
          showModal(
            '',
            '同一就诊人限购 30 盒，若需购买30盒以上，请重新扫码添加新就诊人再次下单、或在补充就诊人信息页面切换/添加新就诊人。',
            '我已知晓，关闭',
            '取消',
            false
          )
          return Promise.reject()
        }
      }
      if (amount <= 0) {
        // 调用移除
        await dispatch('removeDurgItem', [yfkcId])
        return
      }
      // 如果没有库存
      if (drugKc == 0) {
        Toast('已达库存上限')
        return Promise.reject()
      }
      // 当前数量 >= 库存数量
      if (Number(amount) >= Number(drugKc)) {
        Toast('已达库存上限')
        return Promise.reject()
      }
      let res = await queryShoppingCartGroupByDrugStore()
      let drugStore = res.data.drugStoreList.find(
        (v) => v.drugStoreID === '574a40ea01424f7a8b62762b37ff58e2'
      )
      if (!drugStore) {
        await dispatch('addItem', { quan: amount, yfkcId, drugKc })
        return
      }
      if (drugStore && !drugStore.shoppingCartList.length) {
        await dispatch('addItem', { quan: amount, yfkcId, drugKc })
        return
      }
      // 调用更新
      await dispatch('upShopList', {
        quan: amount,
        yfkcId,
        drugKc,
      })
    },
    // 药品增加
    async addDrugItem({ dispatch, state }, item) {
      let { drugKc, quan, yfkcId } = item
      quan = quan * 1
      // 卫艾欣药品特殊处理，获取传入的增加数量
      const amount = item.amount || 1
      console.log(amount, 'amount', quan)

      // 特定药品ID的最大数量限制
      if (item.drugId === '72d263bd0ebd48e49e9063320efa0ff7') {
        if (quan + amount > 30) {
          showModal(
            '',
            '同一就诊人限购 30 盒，若需购买30盒以上，请重新扫码添加新就诊人再次下单、或在补充就诊人信息页面切换/添加新就诊人。',
            '我已知晓，关闭',
            '取消',
            false
          )
          return Promise.reject()
        }
      }

      // 如果没有库存
      if (drugKc == 0) {
        Toast('已达库存上限')
        return Promise.reject()
      }
      // 新增
      if (quan == 0) {
        quan = amount
        if (item.isScan) {
          state.scanCheckDrugList.push({ ...item, quan })
          return
        }
        await dispatch('addItem', { quan, yfkcId, drugKc })
        return
      }
      // 当前数量 >= 库存数量
      if (Number(quan) >= Number(drugKc)) {
        Toast('已达库存上限')
        return Promise.reject()
      }
      quan += amount
      if (item.isScan) {
        let find = state.scanCheckDrugList.find((v) => v.drugId == item.drugId)
        if (find) {
          find.quan = quan
        } else {
          state.scanCheckDrugList.push({ ...item, quan })
        }
        return
      }
      // 调用更新
      await dispatch('upShopList', {
        quan,
        yfkcId,
        drugKc,
      })
    },
    // 数量减少
    async reduceDrugItem({ dispatch, state }, item) {
      let { drugKc, quan, yfkcId } = item
      // 卫艾欣药品特殊处理，获取传入的减少数量
      const amount = item.amount || 1

      // 当前数量 可以减少
      if (Number(quan) > amount) {
        quan -= amount
        if (item.isScan) {
          let find = state.scanCheckDrugList.find(
            (v) => v.drugId == item.drugId
          )
          if (find) {
            find.quan = quan
          } else {
            state.scanCheckDrugList.push({ ...item, quan })
          }
          return
        }
        await dispatch('upShopList', {
          quan,
          yfkcId,
          drugKc,
        })
      } else {
        if (item.isScan) {
          state.scanCheckDrugList = state.scanCheckDrugList.filter(
            (v) => v.drugId != item.drugId
          )
          return
        }
        console.log(item, 'item')
        // 调用移除
        await dispatch('removeDurgItem', [yfkcId])
      }
    },
    // 移除药品
    async removeDurgItem({ dispatch }, arr) {
      let res = await deleteShoppingCart(arr)
      dispatch('setList', res.data)
    },
    // 清空购物车
    async clearCart({ dispatch, state }) {
      let arr = []
      state.shopList.forEach((v) => {
        v.shoppingCartList.forEach((k) => {
          arr.push(k.yfkcId)
        })
      })
      if (arr.length == 0) return
      await dispatch('removeDurgItem', arr)
    },
    // 清空失效
    async clearUnable({ dispatch, state }) {
      let arr = []
      state.unableList.forEach((v) => {
        arr.push(v.yfkcId)
      })
      if (arr.length == 0) return
      await dispatch('removeDurgItem', arr)
    },
  },
}
