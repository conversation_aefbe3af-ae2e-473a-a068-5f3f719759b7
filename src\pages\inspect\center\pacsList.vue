<template>
  <!-- 检验中心列表 -->
  <view class="list">
    <!-- 循环 -->
    <view
      class="item"
      v-for="(item, index) in list"
      :key="index"
      @click="itemClick(item.dpoId)"
    >
      <img
        v-img="item.organImg"
        v-if="item.organImg"
        :data-src="err"
        alt=""
        class="img"
      />
      <image
        class="img"
        v-else
        src="/static/images/Pharmacy-default.png"
      ></image>
      <!-- 机构名 -->
      <view class="left">
        <text class="title">{{ item.organName }}</text>
        <text class="info"
          >{{ item.provinceName }}{{ item.cityName }}{{ item.address }}</text
        >
      </view>
      <!-- 右侧 -->
      <view class="right">
        <!-- 导航 -->
        <view class="address">
          <image class="icon" src="/static/inspection/dh.png" />
          <text>去这里</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getPacsList<PERSON>oOrgan } from '@/api/inspect';
export default {
  name: 'List',
  data() {
    return {
      err: require('../../../static/images/Pharmacy-default.png'),
      list: [],
      query: {
        latitude: '',
        longitude: '',
        ppiId: '',
      },
    };
  },
  onLoad(opt) {
    if (!opt.id) return;
    this.query.ppiId = opt.id;
    this.init();
  },
  methods: {
    // 初始化询问
    init() {
      let that = this;
      uni.showModal({
        title: '提示',
        content: '需要获取您当前位置',
        success(res) {
          console.log(res)
          if (res.confirm) {
            that.getLocation();
          } else if (res.cancel) {
            that.getList();
          }
        },
      });
    },
    // 获取经纬度
    getLocation() {
      let that = this;
      uni.getLocation({
        type: 'wgs84',
        complete(res) {
          let { longitude, latitude } = res;
          // 如果存在
          if (longitude) {
            that.query.longitude = longitude;
            that.query.latitude = latitude;
          }
          // 调用接口
          that.getList();
        },
      });
    },
    // 获取列表
    async getList() {
      let { data } = await getPacsListToOrgan(this.query);
      this.list = data;
    },
    // 机构点击
    itemClick(dpoId) {
      let pages = getCurrentPages();
      // 获取上一页
      let prev = pages[pages.length - 2];
      prev.dpoId = dpoId;
      uni.navigateBack();
    },
  },
};
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}
page {
  background: #f5f5f5;
}
.list {
  width: 100%;
  background: #fff;

  .item {
    padding: 0 32rpx;
    height: 154rpx;
    @include flex(lr);
    border-bottom: 1px solid #d8d8d8;

    .img {
      flex: none;
      width: 106rpx;
      height: 106rpx;
      border-radius: 8rpx;
      margin-right: 24rpx;
    }

    .left {
      flex: 1;
      padding-right: 24rpx;

      .title {
        font-size: 28rpx;
        color: #333;
        font-weight: bold;
        display: block;
      }

      .info {
        display: block;
        font-size: 24rpx;
        color: #666;
        margin-top: 10rpx;
        line-height: 34rpx;
      }
    }

    .right {
      flex: none;
      @include flex;

      .address {
        @include flex;
        flex-direction: column;

        .icon {
          width: 64rpx;
          height: 64rpx;
        }

        text {
          font-size: 28rpx;
          line-height: 40rpx;
          @include font_theme;
        }
      }
    }
  }
}
</style>
