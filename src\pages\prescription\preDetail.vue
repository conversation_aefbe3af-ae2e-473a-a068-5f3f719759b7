<template>
  <div class="pre_detail">
    <!-- 状态 -->
    <STATUS :status="status" :deliveryType="deliveryType" />

    <div class="detail">
      <!-- 摘要 -->
      <INFO :detail="detail" :code="codeUrl" />

      <!-- 时间 -->
      <TIMELINE v-if="timeList.length" :list="timeList" />

      <!-- 处方 -->
      <DRUGS :list="prescriptions" v-if="prescriptions.length && !isPayShow" />

      <!-- 处方详情 -->
      <DRUGDETAIL
        :preferentialAmount="detail.preferentialAmount || 0"
        :totalMoney="detail.totalMoney || 0"
        :orderMoney="detail.orderMoney || 0"
        :list="prescriptions"
        :dosageCodeList="drugList"
        :deliveryType="deliveryType"
        v-if="prescriptions.length && isPayShow"
      />

      <!-- 收货地址 -->
      <ADDRESS v-if="deliveryType == 1" :detail="addressDetail" />

      <!-- 药店地址 -->
      <DRUGSTORE v-if="deliveryType == 2" :detail="drugAddress" />

      <!-- 物流 -->
      <!--      <LOGISTICS-->
      <!--        v-if="deliveryType == 1 && status < 9"-->
      <!--        :list="drugList"-->
      <!--        :tel="deliveryType == 1 ? addressDetail.deliveryTelNo : ''"-->
      <!--        :detail="detail"-->
      <!--        @click="confirmSH"-->
      <!--      />-->

      <!-- 挂号信息 -->
      <ROW text="电子处方" @click="toPdf" />
      <ROW class="row_last" @click="toReg" text="挂号信息" />

      <FOOTER v-if="status == 1" @click="applyRefund"> 申请退费 </FOOTER>
    </div>
  </div>
</template>

<script>
import STATUS from "./com/status.vue";
import INFO from "./com/info.vue";
import TIMELINE from "./com/timeLine.vue";
import DRUGS from "./com/drugs.vue";
import DRUGDETAIL from "./com/drugDetail.vue";
import DRUGSTORE from "./com/drugStore.vue";
import ADDRESS from "./com/address.vue";
import ROW from "./com/row.vue";
import LOGISTICS from "./com/logistics.vue";
import { getPatientPrescriptionBusinessOrderInfo } from "@/api/cf";
import { queryGetMedicine, confirmGoods } from "@/api/order";
import FOOTER from "@/components/footer_button/button.vue";
// 下载pdf
import lockPDF from "@/common/js/downPDF.js";
import { getYxOrderStatus } from "../../api/base";

export default {
  name: "PreDetail",
  components: {
    STATUS,
    INFO,
    TIMELINE,
    DRUGS,
    DRUGDETAIL,
    ADDRESS,
    ROW,
    LOGISTICS,
    DRUGSTORE,
    FOOTER,
  },
  data() {
    return {
      businessId: "",
      // 基础信息
      detail: {},
      // 快递地址
      addressDetail: {},
      // 药店地址
      drugAddress: {},
      // 不同药店物流
      drugList: [],
      // 地址类型 1 快递 2 自提
      deliveryType: 0,
      // 二维码
      codeUrl: "",
      // 状态
      status: -1,
      // 时间线
      timeList: [],
      // 是否支付前可见
      isShow: false,
      // 药品信息
      prescriptions: [],
      proBusinessOrderInfo:{}
    };
  },
  computed: {
    // 是否支付前可见
    isPayShow() {
      // 9 失效 10 退费
      let wait = ["9", "10"];
      // 如果状态为以上两种 则需要判断处方是否支付前可见
      if (wait.includes(this.status) && this.isShow) {
        return true;
      } else if (wait.includes(this.status) && !this.isShow) {
        return false;
      } else {
        return true;
      }
    },
  },
  onLoad(opt) {
    this.businessId = opt.businessId;
  },
  onShow() {
    if (this.businessId) {
      this.getDetail();
    }
  },
  methods: {
    async applyRefund() {
      if (!this.detail.orderNo) {
        return;
      }
      const res = await getYxOrderStatus({ orderNo: this.detail.orderNo });
      if (res.data && res.data.shipping_status * 1 > 2) {
        uni.showToast({
          title: "订单已发货，不可退费",
          icon: "none",
        });
        return;
      }
      uni.navigateTo({
        url: `/pages/prescription/refundOfFees?orderNo=${this.detail.orderNo}&totalMoney=${this.proBusinessOrderInfo.orderMoney}`,
      });
    },
    // 获取详情
    async getDetail() {
      let { data } = await getPatientPrescriptionBusinessOrderInfo({
        businessId: this.businessId,
        splitSingleShowFlag: "",
      });
      if (!data || !data.length) return;
      let {
        proAdressInfo,
        proBusinessInfo,
        drugStore,
        diags,
        proBusinessOrderInfo,
        beforePayVisible,
        prescriptions,
      } = data[0];

      // 药品信息
      this.prescriptions = prescriptions;
      // 药品拆分
      // this.setDrugs(prescriptions);
      // 详情
      this.detail = proBusinessInfo;
      this.proBusinessOrderInfo= proBusinessOrderInfo
      // 状态
      let status = proBusinessOrderInfo.orderStatus;
      if (status === null) {
        status = proBusinessInfo.status;
        proBusinessOrderInfo.orderStatus = status;
      }
      console.log(proBusinessOrderInfo.orderStatus);
      this.status = status;
      // 已退费并且不可见
      if (status > 8 && beforePayVisible == 0) {
        this.isShow = false;
      } else {
        this.isShow = true;
      }
      // 类型
      this.deliveryType = proAdressInfo.deliveryType;
      // 快递
      if (proAdressInfo.deliveryType == 2) {
        this.drugAddress = proAdressInfo;
        if (status < 8) this.getCode();
      } else {
        this.addressDetail = proAdressInfo;
      }

      let arr = [];
      diags.forEach((v) => {
        arr.push(v.diagName);
      });
      // 诊断拼接
      this.detail.diagName = arr.toString();
      this.drugList = drugStore;

      // 时间相关
      proBusinessOrderInfo.addOpenTime = proBusinessInfo.addOpenTime;
      proBusinessOrderInfo.updateTime = proBusinessInfo.updateTime;
      this.setTimeList(proBusinessOrderInfo, proBusinessInfo);
      console.log(this.detail,'orderMoney',this.proBusinessOrderInfo)
    },
    // 获取取药码
    async getCode() {
      let { orderBussId } = this.detail;
      if (!orderBussId) return;
      let { data } = await queryGetMedicine(orderBussId);
      if (data.drugCode) {
        this.codeUrl = data.drugCode;
      }
    },
    // 查看pdf
    async toPdf() {
      let url = await lockPDF(this.businessId);
      window.location.href = url;
    },
    // 确认收货
    async confirmSH(item) {
      await confirmGoods({
        businessId: this.detail.orderBussId,
        merchantsOrderNo: item.merchantsOrderNo,
      });
      this.getDetail();
    },
    // 新增中西药拆分数组
    setDrugs(arr) {
      let z = [];
      let z_name = "";
      let x = [];
      let x_name = "";
      let s = [];
      let s_name = "";
      let cl = 0;
      if (!arr.length) return;
      // 草药付数
      let herbalNum = 0;
      // 循环
      arr.forEach((v) => {
        const type = v.proPrescriptionMasterVO.prescriptionType;
        // 西药
        if (type == "1" || type == "2") {
          x = [...x, ...v.details];
          x_name = v.proPrescriptionMasterVO.prescriptionTypeName;
        }
        // 食品
        if (type == "4") {
          s = [...s, ...v.details];
          s_name = v.proPrescriptionMasterVO.prescriptionTypeName;
        }
        // 中药
        if (type == "3") {
          z = [...z, ...v.details];
          z_name = v.proPrescriptionMasterVO.prescriptionTypeName;
          // 几副药
          herbalNum += v.proPrescriptionMasterVO.herbalNum;
          // 判断几个草药方
          cl += 1;

          // 中药用法
          this.zy_use =
            v.proPrescriptionMasterVO.dduName ||
            "口服" +
              "，每日" +
              v.proPrescriptionMasterVO.rc +
              "次，每次" +
              v.proPrescriptionMasterVO.rj +
              "剂，用药" +
              v.proPrescriptionMasterVO.days +
              "天";
        }
        this.z = z;
        this.z_name = z_name;
        this.x = x;
        this.x_name = x_name;
        this.s = s;
        this.s_name = s_name;
        this.cl_num = cl;
        this.herbalNum = herbalNum;
      });
    },
    // 设置时间线
    setTimeList(obj) {
      let {
        payTime,
        receiveTime,
        returnTime,
        deliveryTime,
        addOpenTime,
        updateTime,
        orderStatus,
      } = obj;
      this.timeList = [
        {
          label: "处方失效",
          time: orderStatus == 9 ? updateTime : "",
        },
        {
          label: "患者确认签收",
          time: receiveTime,
        },
        {
          label: "药店发货",
          time: deliveryTime,
        },
        {
          label: "患者退费",
          time: returnTime,
        },
        {
          label: "患者支付",
          time: payTime,
        },
        {
          label: "医生开处方",
          time: addOpenTime,
        },
      ];
    },
    // 挂号信息
    toReg() {
      let url =
        "/pages/personalCenter/diagnosisRecord/detail?id=" + this.detail.regId;
      uni.navigateTo({
        url,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}

page {
  background-color: #f0f2fc;
}

.pre_detail {
  .detail {
    padding: 0 32rpx 32rpx 32rpx;
    margin-top: -32rpx;
    height: calc(100vh - 280rpx);
    overflow: auto;
  }

  .drugs,
  .address {
    margin-top: 24rpx;
  }

  .row_last {
    margin-top: 1px;
  }
}
</style>
