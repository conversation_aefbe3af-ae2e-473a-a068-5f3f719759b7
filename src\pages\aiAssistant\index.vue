<template>
  <view class="ai-container">
    <!-- AI Assistants Grid -->
    <view class="ai-grid">
      <view
        v-for="(item, index) in aiAssistants"
        :key="index"
        class="ai-item"
        @click="selectAssistant(item)"
      >
        <view class="ai-icon-container">
          <image :src="item.icon" class="ai-icon"></image>
        </view>
        <text class="ai-name">{{ item.name }}</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      aiAssistants: [
        {
          name: 'AI肿瘤医生',
          icon: '/static/images/ai-icon/AI肿瘤医生.png',
          id: 'tumor',
          hasAiTag: true
        },
        {
          name: 'AI免疫医生',
          icon: '/static/images/ai-icon/AI免疫医生.png',
          id: 'immune',
          hasAiTag: true
        },
        {
          name: 'AI分诊护士',
          icon: '/static/images/ai-icon/AI营养师.png',
          id: 'nutrition',
          hasAiTag: true
        },
        {
          name: 'AI营养师',
          icon: '/static/images/ai-icon/矢量智能对象@2x(2).png',
          id: 'nutrition2',
          hasAiTag: true,
          path: '/pages/qcAi/chat'
        },
        // {
        //   name: '木兰【妇科肿瘤医生】',
        //   subName: '妇科肿瘤医生',
        //   icon: '/static/images/ai-icon/木兰【妇科肿瘤医生】.png',
        //   id: 'mulan',
        //   hasAiTag: false
        // },
        // {
        //   name: '福星【儿童肥胖专家】',
        //   subName: '儿童肥胖专家',
        //   icon: '/static/images/ai-icon/福星【儿童肥胖专家】.png',
        //   id: 'fuxing',
        //   hasAiTag: false
        // }
      ]
    }
  },
  methods: {
    goBack () {
      uni.navigateBack();
    },
    selectAssistant (item) {
      if (item.path) {
        uni.navigateTo({
          url: item.path
        });
      } else {
        uni.showToast({
          title: '正在开发中，敬请期待',
          icon: 'none',
          duration: 2000
        });
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.ai-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 32rpx;
  height: 88rpx;
  background: linear-gradient(90deg, rgba(103, 107, 223, 1) 0%, rgba(137, 131, 228, 1) 100%);

  .back-icon, .more-icon {
    width: 44rpx;
    height: 44rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    .icon {
      font-size: 40rpx;
      color: #fff;
    }
  }

  .title {
    font-size: 36rpx;
    font-weight: 500;
    color: #fff;
  }
}

.search-bar {
  display: flex;
  align-items: center;
  height: 60rpx;
  background-color: #fff;
  border-radius: 30rpx;
  margin: 20rpx 32rpx;
  padding: 0 24rpx;

  .search-icon {
    width: 36rpx;
    height: 36rpx;
    margin-right: 10rpx;
  }

  .search-placeholder {
    font-size: 28rpx;
    color: #999;
  }
}

.ai-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 32rpx;
  background-color: #fff;
  border-radius: 20rpx;
  margin: 10rpx 20rpx;
}

.ai-item {
  width: 33.33%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40rpx;
}

.ai-icon-container {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 12rpx;
}

.ai-icon {
  width: 100%;
  height: 100%;
}

.ai-tag {
  position: absolute;
  right: 0;
  bottom: 0;
  background-color: #f5222d;
  color: #fff;
  font-size: 20rpx;
  border-radius: 10rpx;
  padding: 2rpx 6rpx;
}

.ai-name {
  font-size: 26rpx;
  color: #333;
  text-align: center;
}

.bottom-nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100rpx;
  background-color: #fff;
  border-top: 1px solid #eee;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 0 20rpx;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;

  .nav-icon {
    width: 48rpx;
    height: 48rpx;
    margin-bottom: 4rpx;
  }

  .nav-text {
    font-size: 22rpx;
    color: #999;
  }

  &.active {
    .nav-text {
      color: #836AFF;
    }
  }
}
</style>