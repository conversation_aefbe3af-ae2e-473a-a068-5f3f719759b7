<template>
  <!-- 单个 -->
  <view class="card" @click="click(item)">
    <!-- 编号 -->
    <view class="item_title">
      <view>
        <img src="../../../../static/路径 <EMAIL>" alt="" />
        <span>预约号：{{ item.regCode }}</span>
      </view>
      <text
        class="status"
        :class="{
          wait: item.status == 1 || item.status == 2 || item.status == 4,
          act: item.status == 5,
        }"
        >{{ item.statusName }}</text
      >
    </view>

    <!-- 相关信息 -->
    <view class="item_cont">
      <view class="left_info">
        {{ item.docName }}
        <span
          style="
            color: #666666;
            font-size: 12px;
            padding-left: 9px;
            font-weight: normal;
          "
          >{{ item.deptName }}
        </span></view
      >
      <view class="left_info"> 医院：{{ item.hosName }} </view>
      <view class="left_info">
        号别：{{ item.dntName }}
        <!-- 右箭头 -->
        <uni-icons type="arrowright" color="#A6A6A6" size="16"></uni-icons>
      </view>
      <view class="left_info"> 问诊类型：{{ item.receiveType }} </view>
      <view class="left_info"> 就诊人：{{ item.patientName }} </view>
    </view>

    <!-- 时间 -->
    <view class="item_time"> 预约时间：{{ item.appointTime }} </view>
  </view>
</template>

<script>
export default {
  props: {
    item: {
      type: Object,
      default: () => {},
    },
  },
  methods: {
    click(item) {
      this.$emit("click", item);
    },
  },
};
</script>

<style lang="scss" scoped>
.card {
  padding: 0 32rpx;
  border-radius: 8px;
  background: #fff;
  margin-bottom: 24rpx;
  box-shadow: 0px 1px 4px rgba(121, 72, 234, 0.15);

  &:last-child {
    margin-bottom: 0;
  }

  .item_title {
    height: 35px;
    @include flex(lr);
    font-size: 12px;
    color: #333;
    border-bottom: 1px solid #ebebeb;
    view {
      display: flex;
      align-items: center;
      img {
        width: 14px;
        height: 14px;
        margin-right: 2px;
      }
    }

    .status {
      font-weight: normal;
      color: #333;
      width: 54px;
      height: 22px;
      border-radius: 212px;
      background: #ebf0fe;
      text-align: center;
      line-height: 22px;
      color: #3b60ef;
      &.wait {
        color: #ff3b30;
      }

      &.act {
        @include font_theme;
      }
    }
  }

  .item_cont {
    padding: 10rpx 0;
    border-bottom: 1px solid #ebebeb;
    .left_info:first-child {
      justify-content: flex-start;
    }

    .left_info {
      @include flex(lr);
      font-size: 12px;
      color: #999999;
      line-height: 50rpx;

      &:first-child {
        font-weight: bold;
        color: #333;
        font-size: 14px;
      }
    }
  }

  .item_time {
    height: 35px;
    @include flex(lr);
    font-size: 12px;
    color: #333;
  }
}
</style>
