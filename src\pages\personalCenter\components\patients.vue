<template>
  <!-- 就诊人列表 -->
  <view class="patient_list">
    <!-- 循环 -->
    <view
      class="patient_item"
      v-for="(item, index) in list"
      :key="index"
      @click="click(item)"
    >
      <view class="item_basic">
        <view>{{ item.patientName }}</view>
      </view>
      <view class="item_card"> {{ item.sex }} {{ item.age }}岁</view>
      <view class="item_card"> 身份证号：{{ item.idNo }} </view>
      <uni-icons
        type="arrowright"
        size="20"
        color="#fff"
        class="navigate-icon"
      ></uni-icons>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    list: {
      type: Array,
      default: [],
    },
  },
  onload(v) {},
  methods: {
    click(item) {
      this.$emit("click", item);
    },
  },
};
</script>

<style lang="scss" scoped>
.patient_list {
  width: 100%;
  box-sizing: border-box;
  // padding: 20upx 32upx;

  .patient_item {
    background: linear-gradient(135.9deg, #777cf6 0%, #7e9cf7 100%);
    border-radius: 16rpx;
    margin: 24rpx auto 0;
    width: 100%;
    padding: 38rpx 30rpx 18rpx 38rpx;
    position: relative;
    box-sizing: border-box;

    .item_basic {
      display: flex;
      font-size: 38rpx;
      line-height: 50rpx;
      color: #fff;
      font-weight: bold;

      .ml64 {
        margin-left: 64rpx;
      }
    }

    .item_card {
      margin-top: 8rpx;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      color: #dbe4ff;
      line-height: 40rpx;
    }

    .navigate-icon {
      position: absolute;
      right: 32rpx;
      top: 50%;
      transform: translate(0, -50%);
    }
  }
}
</style>
