<template>
  <div class="pacs" @click="click">
    <p class="title">
      <span class="bold">检查单</span>
      <span class="red">未开票</span>
    </p>

    <p class="code">订单编号：{{ item.orderNo }}</p>
    <p class="info">
      检查项目： {{ item.pacsLisOrderListNewQueryVO.itemName }}
    </p>
    <p class="info">就诊人： {{ item.pacsLisOrderListNewQueryVO.docName }}</p>
    <p class="info">支付时间：{{ item.addTime }}</p>

    <div class="last">
      <p>
        开票金额：
        <span>￥{{ item.orderMoney | toFixed }}</span>
      </p>
      <span class="button">
        申请开票
      </span>
    </div>
  </div>
</template>

<script>
export default {
  name: "Pacs",
  props: {
    item: {
      type: Object,
      default: () => ({
        pacsLisOrderListNewQueryVO: {},
      }),
    },
  },
  methods: {
    click() {
      this.$emit("click");
    },
  },
};
</script>

<style lang="scss" scoped>
.pacs {
  padding: 0 32rpx;
  background: #fff;
  border-radius: 8rpx;
  font-size: 28rpx;
  margin-bottom: 24rpx;

  .title {
    height: 88rpx;
    @include flex(lr);
    border-bottom: 1px solid #f5f5f5;

    .bold {
      font-weight: bold;
    }

    .red {
      color: red;
    }
  }

  .code {
    font-weight: bold;
    margin-top: 24rpx;
  }

  .info {
    margin-top: 24rpx;
    color: #666;
  }

  .last {
    margin-top: 24rpx;
    height: 102rpx;
    @include flex(lr);
    border-top: 1px solid #f5f5f5;

    p {
      @include flex;

      span {
        color: red;
        font-weight: bold;
      }
    }

    .button {
      width: 180rpx;
      height: 60rpx;
      @include flex;
      border-radius: 30rpx;
      @include bg_theme;
      color: #fff;
      font-weight: bold;
    }
  }
}
</style>
