<template>
  <!-- 图片消息 -->
  <view class="user_imgs">
    <!-- 气泡 -->
<!--    <view class="img_content" @click="click">-->
<!--      <image class="content_img" :src="imgSrc" mode="widthFix" />-->
<!--    </view>-->
    <view>
      <view class="patientName">{{ patientName }}</view>
      <view class="img_content" @click="click">
        <image class="content_img" :src="imgSrc" mode="widthFix" />
      </view>
    </view>
    <!-- 头像 -->
    <image
      :src="imgUrl || '/static/images/docHead.png'"
      @click="head"
      mode="aspectFill"
      class="user_img"
    />
  </view>
</template>

<script>
export default {
  props: {
    // 头像
    imgUrl: {
      type: String,
      default: '',
    },
    // 地址
    imgSrc: {
      type: String,
      default: '',
    },
    patientName:{}
  },
  methods: {
    // 预览图片
    click() {
      uni.previewImage({
        urls: [this.imgSrc],
      });
    },
    // 点击头像
    head() {
      this.$emit('click');
    },
  },
};
</script>

<style lang="scss" scoped>
.user_imgs {
  box-sizing: border-box;
  @include flex(right);
  padding-bottom: 30upx;
  align-items: flex-start;

  // 头像
  .user_img {
    width: 64upx;
    height: 64upx;
    border-radius: 4upx;
    margin-left: 16upx;
    flex: none;
  }

  // 内容
  .img_content {
    width: 280upx;

    // 图片
    .content_img {
      width: 100%;
      height: auto;
      border-radius: 16upx;
    }
  }
}
image{will-change: transform}

</style>
