<template>
  <view class="footer_button">
    <button class="left" @click="left" v-if="showLeft">
      <slot name="left" />
    </button>
    <button class="left red" v-if="isLeft" @click="left">
      <slot name="isLeft" />
    </button>
    <button class="red" v-if="isCw" @click="click">
      <slot />
    </button>
    <button class="com" v-if="isDef" @click="click">
      <slot />
    </button>
  </view>
</template>

<script>
export default {
  props: {
    showLeft: Boolean,
    isLeft: Boolean,
    isDef: {
      type: Boolean,
      default: true,
    },
    isCw: Boolean,
  },
  methods: {
    click() {
      this.$emit("click");
    },
    left() {
      this.$emit("leftClick");
    },
  },
};
</script>

<style lang="scss" scoped>
.footer_button {
  width: 100%;
  height: 108upx;
  position: fixed;
  bottom: 0;
  padding: 0 16upx;
  @include flex;
  background: #f0f2fc;
  box-sizing: border-box;
  left: 0;
  border-radius: 16upx 16upx 0 0;
  box-shadow: 0 0 10rpx #ddd;

  button {
    margin: 0 16rpx;
    flex: 1;
    height: 84upx;
    border-radius: 42upx;
    background: linear-gradient(135deg, #516afb 0%, #859bff 99.97%);
    color: #fff;
    font-size: 32upx;
    @include flex;

    &.left {
      background: none;
      @include font_theme;
      @include border_theme;
    }
  }
}
.red {
  background: red !important;
  color: white !important;
  border-color: red !important;
}
</style>
