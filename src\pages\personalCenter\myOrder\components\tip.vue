<template>
  <view class="status-container">
    <view class="tip">
      <image src="/static/images/index/red_bg.png" v-if="!isBlue"></image>
      <image src="/static/images/index/blue_bg.png" v-if="isBlue"></image>
      <text>{{ obj.title }}</text>
    </view>
    <text>{{ obj.toast }}</text>
  </view>
</template>

<script>
export default {
  name: 'Tip',
  props: {
    obj: Object,
    isBlue: Boolean,
  },
};
</script>

<style lang="scss" scoped>
.status-container {
  background-color: #fff;
  padding: 30rpx;
  box-sizing: border-box;
  font-size: 24rpx;
  font-weight: 600;
  color: #333333;
  line-height: 17px;
}

.tip {
  width: 182rpx;
  height: 56rpx;
  color: #fff;
  font-size: 28rpx;
  font-weight: 600;
  color: #ffffff;
  line-height: 56rpx;
  position: relative;
  margin-bottom: 24rpx;
}

.tip image {
  display: block;
  width: 182rpx;
  height: 56rpx;
}

.tip text {
  position: absolute;
  width: 156rpx;
  height: 56rpx;
  top: 0;
  left: 0;
  @include flex;
}
</style>
