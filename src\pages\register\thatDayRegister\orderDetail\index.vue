<template>
  <view>
    <view class="page-container">
      <!-- 头部 -->
      <Docter :infoDetail="infoDetail" />

      <view class="bg_wh">
        <view class="person_info">
          <text>就诊人</text>
          <text>{{ detail.patientName }}</text>
        </view>
        <view class="person_info">
          <text>问诊类型</text>
          <text>{{ infoDetail.visitTypeName }}</text>
        </view>
        <view class="person_info">
          <text>服务类型</text>
          <text>{{ infoDetail.type == 0 ? "咨询" : "复诊" }}</text>
        </view>
        <view class="person_info">
          <text>{{
            infoDetail.visitTypeCode == 1 ? "问诊时间" : "预约时间"
          }}</text>
          <text>{{ time }}</text>
        </view>
      </view>

      <!-- 明细 -->
      <view class="bg_wh particulars" style="margin-bottom: 10px">
        <view class="title_fw">费用明细（自费）</view>
        <template v-for="item in priceList">
          <view class="left_right">
            <text>{{ item.priceDetailName }}</text>
            <text>{{ item.totalPay }}</text>
          </view>
        </template>
        <view style="padding-top: 20rpx">
          合计：<text style="color: #ff0707; font-weight: 500"
            >￥ {{ detail.totalPay }}</text
          >
        </view>
        <!--        <view class="title_fw">医保担负明细</view>-->
        <!--        <view class="left_right">-->
        <!--          <text>医保统筹支付</text>-->
        <!--          <text>0</text>-->
        <!--        </view>-->
        <!--        <view class="left_right">-->
        <!--          <text>医保账户支付</text>-->
        <!--          <text>0</text>-->
        <!--        </view>-->
        <view class="title_fw">优惠券抵扣明细</view>
        <view
          class="left_right"
          style="justify-content: left; color: rgba(255, 141, 26, 1)"
        >
          <text>新用户立减</text>
          <text style="color: rgba(255, 141, 26, 1); margin-left: 5px"
            >￥ 0</text
          >
        </view>
      </view>

      <view class="bg_wh" v-if="false">
        <view class="title_fw">订单说明</view>
        <view class="explain_font">
          互联网诊疗仅适用常见疾病、慢性病复诊患者，急重诊患者请前往实体医疗机构就诊。
        </view>
        <view class="explain_font" v-if="infoDetail.type == 1">
          复诊时必须提供包含诊断的实体机构的病历资料
          针对同诊断复诊。复诊患者，医生将根据您的实际情况辩证开方、给出调理建议。
        </view>
        <view class="explain_font" wx:if="infoDetail.type==1">
          在线复诊过程中，若由于您无法提供实体医疗机构的诊段证明，或医生无法在线得出您和您之前线下实体机构相同的诊断而无法为您开出处方和诊断的情况，将不会退还复诊费用
        </view>
      </view>
    </view>
    <!-- 底部 -->
    <view class="footer">
      <view class="footer_label"
        >实际支付：
        <text style="color: red"
          >￥
          <text style="font-size: 17px">{{ detail.selfPay }}</text>
        </text>
      </view>
      <view :class="btnFlag ? 'pay' : 'btnactive'" @click="btnFlag && getPay()">
        立即支付
      </view>
    </view>
  </view>
</template>

<script>
import myJsTools from "@/common/js/myJsTools.js";
import Docter from "@/components/doctor_header/doctor_header.vue";
import {
  getRegisterPayInfo,
  queryRegisterPayStatus,
  cancelRegister,
  getIsExist,
  addCollectDoctor,
} from "@/api/base.js";
import { saveRegister } from "@/api/chatCardDetail";
import Pay from "@/modules/pay";
// 支付相关
let PAY;
let num = 3;
export default {
  components: {
    Docter,
  },
  data() {
    return {
      btnFlag: true,
      ghId: "",
      infoDetail: {},
      // 挂号分割返回的字段
      detail: {},
      priceList: [],
      prId: "",
      time: "",
      isShowDisease: false,
    };
  },
  async onLoad(option) {
    this.prId = option.prId;
    this.isShowDisease = option.isShowDisease ? true : false;
    this.time = myJsTools.formatTime(new Date());
    let info = uni.getStorageSync("patientInfo");
    let infoDetail = uni.getStorageSync("infoDetail");
    this.getPayList();
    if (infoDetail.isSwitch != "0") {
      infoDetail.details = [
        {
          isMedicare: "0",
          priceDetailName: "互联网服务费",
          totalPay: infoDetail.visitPrice,
          priceDetailId: "",
        },
      ];
    }
    // 患者
    let {
      age,
      ageUnit,
      birthDate,
      idNo,
      patientName,
      patientImg,
      sex,
      sexCode,
      telNo,
    } = info;

    let {
      deptId,
      apw,
      deptName,
      visitDuration,
      dntName,
      docId,
      docName,
      docImgCopy: docImg,
      docTel,
      psaId,
      type: isSubsequent,
      visitDate,
      visitTypeCode,
      visitTypeId,
      visitTypeName,
      week,
      details,
      vrId,
    } = infoDetail;

    let obj = {
      age,
      ageUnit,
      birthDate,
      idNo,
      patientName,
      patientImg,
      sex,
      sexCode,
      telNo,
      deptId,
      apw,
      deptName,
      visitDuration,
      dntName,
      docId,
      docName,
      docImg,
      docTel,
      psaId,
      isSubsequent,
      visitDate,
      visitTypeCode,
      visitTypeId,
      visitTypeName,
      week,
      details,
      vrId,
      isMedicare: "0",
      patientId: uni.getStorageSync("patientId"),
      // 病例资料id
      prId: this.prId,
      regSource: "1",
    };
    let res = await saveRegister(obj);
    uni.setStorageSync("registration", res.data);
    let { reg, details: detail } = res.data;
    infoDetail.regId = reg.regId;
    infoDetail.cost = reg.selfPay;
    infoDetail.sexCode = sexCode;
    infoDetail.addTime = this.time;
    this.detail = reg;
    this.priceList = detail;
    this.ghId = reg.ghId;
    this.infoDetail = infoDetail;
    uni.setStorageSync("infoDetail", infoDetail);
  },
  methods: {
    // 获取支付方式
    getPayList() {
      let hosId = uni.getStorageSync("hosId");
      PAY = new Pay(hosId);
    },

    // 收藏医生
    async getCollect() {
      let res = getIsExist({
        docId: this.infoDetail.docId,
        openid: uni.getStorageSync("wxInfo").openId,
      });
      if (res.data != 1) {
        let obj = {
          docId: this.infoDetail.docId,
          userId: uni.getStorageSync("userId"),
          openid: uni.getStorageSync("wxInfo").openId,
          appid: uni.getStorageSync("appId"),
        };
        await addCollectDoctor(obj);
      }
    },
    // 点击支付
    async getPay() {
      this.btnFlag = false; // 设置按钮禁用
      let money = this.detail.selfPay;

      let { index, item } = await PAY.selePay(money);

      uni.showLoading({
        mask: true,
      });

      let para = {
        callId: item.appid,
        ghId: this.ghId,
        openid: uni.getStorageSync("wxInfo").openId,
        payType: index,
      };

      try {
        let res = await getRegisterPayInfo(para);
        uni.hideLoading();
        // 无需支付，返回成功，直接跳转
        if (res.data && res.data.success == "1") {
          this.getCollect();
          this.btnFlag = true;

          if (this.isShowDisease) {
            uni.reLaunch({
              url:
                "/pages/register/thatDayRegister/payState/index?isShowDisease=1&flag=1" +
                "&type=" +
                this.infoDetail.visitTypeCode,
            });
          } else {
            uni.reLaunch({
              url:
                "/pages/register/thatDayRegister/payState/index?flag=1" +
                "&type=" +
                this.infoDetail.visitTypeCode,
            });
          }
          return;
        }
        let info = res.data;

        if (index == 1) {
          // 微信支付
          this.wxPay(info);
          return;
        } else {
          this.btnFlag = true;
          uni.reLaunch({
            url:
              "/pages/pay/pay?price=" +
              money +
              "&ghId=" +
              this.ghId +
              "&url=" +
              btoa(info.url),
          });
        }
      } catch (error) {
        console.log(error);
        uni.hideLoading();
        this.btnFlag = true;
      }
    },

    // 微信支付
    async wxPay(info) {
      try {
        await PAY.wxPay(info);
        this.getState();
      } catch (error) {
        PAY.Toast("取消支付");
        this.btnFlag = true;
      }
    },

    // 查询支付状态
    async getState() {
      if (num <= 0) {
        await cancelRegister({
          ghId: this.ghId,
        });
        uni.reLaunch({
          url: "/pages/register/thatDayRegister/payState/index?flag=" + "0",
        });
        return;
      }
      uni.showLoading({
        mask: true,
      });
      let res = await queryRegisterPayStatus({
        ghId: this.ghId,
      });
      num--;
      // if (!res.data) return;
      if (res.data && res.data.regStatus == "2") {
        uni.hideLoading();
        this.getCollect();
        if (this.isShowDisease) {
          uni.navigateTo({
            url:
              "/pages/register/thatDayRegister/payState/index?isShowDisease=1&flag=1" +
              "&type=" +
              this.infoDetail.visitTypeCode,
          });
        } else {
          uni.navigateTo({
            url:
              "/pages/register/thatDayRegister/payState/index?flag=1" +
              "&type=" +
              this.infoDetail.visitTypeCode,
          });
        }

        return;
      } else {
        // 三秒后台调用
        setTimeout(this.getState, 3000);
      }
    },
  },
};
</script>

<style scoped lang="scss">
::v-deep .doctor_box_top {
  width: 100%;
}
.page-container {
  padding: 0rpx 0rpx;
  box-sizing: border-box;
  overflow-y: auto;
  background-color: white;
  min-height: calc(100vh - 50px);
  .doctor_box_top {
    padding: 28upx 24upx;
    border-radius: 16upx;
  }
}

/* 主体内容 */
.bg_wh {
  width: 90%;
  margin: auto;
  border-radius: 16rpx;
  padding: 0 24rpx;
  margin-top: 24rpx;
  font-size: 28rpx;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px 1px 4px rgba(121, 72, 234, 0.15);
}

.bg_wh:first-child {
  margin-top: 0;
}

/* 问诊信息 */
.patient_box {
  margin-top: 40rpx;
  background-color: #ffffff;
  border-radius: 8rpx;
  padding: 0 32rpx;
}

.person_info {
  height: 80rpx;
  box-sizing: border-box;
  color: rgba(102, 102, 102, 1);
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.person_info text:first-child {
  width: 180rpx;
}

.person_info:last-child {
  border-bottom: none;
}

/* 费用明细 */
.particulars {
  /* padding-top: 26rpx; */
  padding-bottom: 26rpx;
}

.title_fw {
  color: #333333;
  font-weight: 600;
  padding-top: 26rpx;
}

.left_right {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #999999;
  padding-top: 40rpx;
}

.left_right text:last-child {
  color: #333333;
}

/* 服务说明 */
.explain_font {
  color: $k-info-title;
  font-size: 28rpx;
  // text-indent: 2em;
  padding: 20rpx 0 10upx;
}

/* 底部 */
.footer {
  width: 100%;
  position: fixed;
  left: 0;
  bottom: 0;
  height: 140rpx;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;
  box-sizing: border-box;
  box-shadow: 0px 4upx 8upx 0px rgba(0, 0, 0, 0.5);

  .footer_label {
    color: $k-title;
    font-size: 28upx;
  }
}

.pay {
  width: 408rpx;
  height: 72rpx;
  background: linear-gradient(
    135deg,
    rgba(81, 106, 251, 1) 0%,
    rgba(133, 155, 255, 1) 99.97%
  );
  border-radius: 40rpx;
  text-align: center;
  line-height: 72rpx;
  color: #fff;
  font-size: 28rpx;
}

.footer .btnactive {
  width: 160rpx;
  height: 60rpx;
  border-radius: 40rpx;
  text-align: center;
  line-height: 60rpx;
  font-size: 28rpx;
  background-color: #ccc;
  color: #000;
}
</style>
