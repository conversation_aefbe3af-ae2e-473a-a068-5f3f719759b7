<template>
  <view class="page-container">
    <view class="query-container" v-if="isShowFilter">
      <HMfilterDropdown
        :menuTop="80"
        :filterData="filterData"
        :defaultSelected="defaultSelected"
        :updateMenuName="true"
        @confirm="confirm"
        dataFormat="Object"
      ></HMfilterDropdown>
    </view>

    <view class="detail">
      <template v-if="prescriptionDatas.length > 0">
        <!-- 列表 -->
        <LIST
          :list="prescriptionDatas"
          :flag="flag"
          @click="toDetails"
          @checkCF="checkCF"
        />

        <!-- 加载更多 -->
        <view v-if="isShowMore">
          <uni-load-more :status="status"></uni-load-more>
        </view>
      </template>
      <view class="empty_list" v-else>
        <image src="/static/images/question/empty_toast.png" />
        <view> 暂无处方记录 </view>
      </view>
    </view>
    <FooterButton v-if="flag === 'check'" @click="submit">确定</FooterButton>
  </view>
</template>

<script>
import HMfilterDropdown from "@/components/HM-filterDropdown/HM-filterDropdown.vue";
import { getAllDept, getPreBussList } from "@/api/base.js";
import { findIsEnabledPatientByUserId } from "@/api/user.js";
import myJsTools from "@/common/js/myJsTools.js";
import LIST from "./com/list.vue";
import FooterButton from "@/components/footer_button/button.vue";
export default {
  components: {
    HMfilterDropdown,
    LIST,
    FooterButton,
  },
  data() {
    return {
      flag: "",
      // 查询科室列表
      deptListQuery: {
        deptName: "",
        page: 1,
        limit: 100,
      },

      // 查询诊疗记录列表
      prescriptionDatas: [],
      listQuery: {
        page: 1,
        limit: 10,
        patients: [],
        deptId: "",
        startTime: "",
        endTime: "",
      },
      total: "",
      isShowMore: false,
      status: "loading",
      filterData:[],
      filterDataAll: [
        {
          name: "科室",
          type: "hierarchy",
          submenu: [],
          hide: false
        },
        {
          name: "时间",
          type: "hierarchy",
          hide: false,
          submenu: [
            {
              name: "七天内",
              value: "0",
            },
            {
              name: "一月内",
              value: "1",
            },
            {
              name: "三月内",
              value: "2",
            },
            {
              name: "半年内",
              value: "3",
            },
          ],
        },
        {
          name: "就诊人",
          type: "hierarchy",
          submenu: [],
          hide: false
        }
      ],
      defaultSelected: [],
      urlType: "",
      isShowFilter: false,
      checkCf: {},
    };
  },
  onLoad(option) {
    this.filterDataAll[2].hide= option.selectedPatientId?true:false
    this.filterData=this.filterDataAll.filter(v=>!v.hide)
    console.log("option", option);
    this.flag = option.flag
    this.urlType = option.type;
    this.listQuery.patients = uni.getStorageSync("patientIdList") || [];
    if(option.selectedPatientId){
      this.listQuery.patients = [option.selectedPatientId]
    }
    this.getAllDept();
    this.getAllPatient();
    this.getListQuery();
  },
  onPullDownRefresh() {
    this.listQuery.page = 1;
    this.listQuery.deptId = "";
    this.listQuery.startTime = "";
    this.listQuery.endTime = "";
    this.listQuery.jzStatus = "";
    this.prescriptionDatas = [];
    this.getListQuery();
  },
  onReachBottom() {
    this.getMore();
  },
  methods: {
    checkCF(value) {
      this.checkCf = value;
    },
    submit() {
      console.log(this.checkCf);
      if (!this.checkCf || !this.checkCf.businessId) {
        uni.showToast({
          title: "请选择一个处方",
          icon: "none",
        });
        return;
      }
      uni.setStorageSync("checkCf", this.checkCf);
      uni.navigateBack({
        delta: 1,
      });
    },
    // 查询我的处方列表
    async getListQuery() {
      let res = await getPreBussList(this.listQuery);
      this.total = res.data.total;
      if (this.flag === "check") {
        this.prescriptionDatas = this.prescriptionDatas.concat(
          (res.data.rows || []).filter((v) => v.status != 1 && !v.orderNo)
        );
        if (
          this.total > 10 &&
          this.prescriptionDatas.length < 10 &&
          this.status != "noMore"
        ) {
          this.getMore();
        }
      } else {
        this.prescriptionDatas = this.prescriptionDatas.concat(res.data.rows);
      }
      uni.stopPullDownRefresh();
    },
    // 去处方详情
    toDetails(item) {
      let { ywStatus, hosId, businessId, status } = item;
      uni.removeStorageSync("nowAddress");
      uni.removeStorageSync("pharmacyAddress");
      uni.setStorageSync("hosId", hosId);
      let url;
      if (ywStatus < 3) {
        url =
          "/pages/prescription/prescriptionDetail?businessId=" +
          businessId +
          "&status=" +
          status;
      } else {
        url = "/pages/prescription/preDetail?businessId=" + businessId;
      }
      // 跳转
      uni.navigateTo({
        url,
      });
    },
    // 获取所有科室列表
    async getAllDept() {
      let { data } = await getAllDept(this.deptListQuery);
      if (!data || !data.rows.length) return;
      let list = data.rows;
      let submenu = [];
      for (let i = 0; i < list.length; i++) {
        submenu.push({
          value: list[i].deptId,
          name: list[i].deptName,
        });
      }
      this.filterData[0].submenu = submenu;
    },
    // 获取就诊人列表
    async getAllPatient() {
      let res = await findIsEnabledPatientByUserId({
        userId: uni.getStorageSync("userId"),
      });
      let data = res.data;
      if (!res.data || res.data.length == 0) {
        this.isShowFilter = false;
        return;
      }
      let submenu = [];
      for (let i = 0; i < data.length; i++) {
        submenu.push({
          value: data[i].patientId,
          name: data[i].patientName,
        });
      }
      if(this.filterData[2]){
        this.filterData[2].submenu = submenu;
      }
      this.isShowFilter = true;
    },
    //接收菜单结果
    confirm(e) {
      let indexArr = e.index;
      let valueArr = e.value;
      this.listQuery.page = 1;
      let deptId = valueArr[0];
      if (deptId[0]) {
        this.listQuery.deptId = deptId[0];
      } else {
        this.listQuery.deptId = "";
      }
      let time = valueArr[1];
      if (time[0]) {
        this.listQuery.endTime =
          myJsTools.getDate(new Date()) + " " + "23:59:59";
        let timeValue = time[0];

        let startTime;
        if (timeValue == 0) {
          startTime = myJsTools.getDate("day", -7);
        } else if (timeValue == 1) {
          startTime = myJsTools.getDate("month", -1);
        } else if (timeValue == 2) {
          startTime = myJsTools.getDate("month", -3);
        } else if (timeValue == 3) {
          startTime = myJsTools.getDate("month", -6);
        }
        this.listQuery.startTime = startTime + " " + "00:00:00";
      } else {
        this.listQuery.startTime = "";
        this.listQuery.endTime = "";
      }

      let patients = valueArr[2];
      if(this.filterData.length>2){
        if (patients[0]) {
          this.listQuery.patients = patients;
        } else {
          this.listQuery.patients = uni.getStorageSync("patientIdList");
        }
      }
      this.prescriptionDatas = [];
      this.getListQuery();
    },
    // 查看更多
    getMore() {
      this.isShowMore = true;
      // 共几页
      let num = Math.ceil(this.total / this.listQuery.limit);
      if (this.listQuery.page < num) {
        this.listQuery.page += 1;
        this.getListQuery();
        this.isShowMore = false;
      } else {
        this.status = "noMore";
      }
    },
  },
};
</script>

<style scoped lang="scss">
body {
  background-color: #f0f2fc;
}
.page-container {
  box-sizing: border-box;
  background-color: #f0f2fc;
}

/* 筛选条件 */
.query-container {
  width: 100%;
  position: sticky;
  top: 0;
  z-index: 13;
  background-color: #f0f2fc;
  padding: 20rpx 34rpx 0;
}

/* 列表为空提示 */
.empty_list {
  @include flex;
  flex-direction: column;
  font-size: 26rpx;
  font-weight: 400;
  color: rgba(158, 163, 173, 1);
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.empty_list image {
  margin-bottom: 40rpx;
  width: 386rpx;
  height: 324rpx;
}
</style>
