<template>
  <view class="nucleic_content">
    <view class="heigth_24"></view>
    <view class="nucleic_head">
      <view class="nucleic_head_conten">
        <view class="nucleic_headuser">
          <img
            class="nucleic_headportrait"
            :src="
              Patient.patientImg
                ? Patient.patientImg
                : './static/images/index/portrait.png'
            "
          />
          <view class="nucleic_usermsg">
            <view class="usermsgname">{{ Patient.patientName }}</view>
            <view class="usermsgid">{{
              Patient.shenfen
                ? Patient.shenfen.replace(/(.{6}).*(.{4})/, "$1********$2")
                : ""
            }}</view>
          </view>
        </view>

        <img
          @click="goLisOrderList"
          src=" ./static/images/index/switch.png"
          class="headswidch"
        />
      </view>
    </view>
    <view v-if="Patient.shenfen">
      <view class="nucleic_list_head">
        <view>核酸检测</view>
      </view>
      <view class="nucleic_list_centent">
        <view class="height_12px"></view>
        <template v-if="uncleicList && uncleicList.length">
          <view
            class="nucleic_list_item"
            v-for="(item, index) in this.uncleicList"
            :key="index"
            :class="{
              item_margin: index !== 0,
              item_border: index !== len,
            }"
          >
            <view class="nucleic_item_msg">
              <view class="nucleic_item_cy">
                <view>采样时间：</view>
                <view class="cy_color">{{ item.checkTime }}</view>
              </view>
              <view class="nucleic_item_cy">
                <view>报告时间：</view>
                <view class="cy_color">{{ item.reportTime }}</view>
              </view>
              <view class="nucleic_item_cy">
                <view>{{ item.institutions }}</view>
              </view>
            </view>
            <view
              class="nucleic_item_flag"
              :class="{ flag_clor: item.result !== '阴性' }"
              >{{ item.result }}</view
            >
          </view>
        </template>
        <view class="empty_list" v-else>
          <image src="/static/images/index/nucleic_emity.png" />
          <view>暂无数据</view>
          <!-- <iframe class="empty_list" src="./demo/new_file.html" frameborder="0"></iframe> -->
        </view>
      </view>
      <view class="botton_text">
        <view class="height_16"></view>
        <view class="botton_text_title">服务说明：</view>
        <view class="botton_text_msg"
          >1.本服务支持查询本人14日内核酸检测结果。</view
        >
        <view class="botton_text_msg"
          >2.相关查询结果基于卫生健康部门数据库,根据各地检测数据提供,正在不断汇聚更新中。</view
        >
      </view>
    </view>
    <view class="empty_list empty_list_margin" v-else>
      <image src="/static/images/index/nucleic_emity.png" />
      <view>暂不支持查询，请先维护就诊人身份证号</view>
    </view>
  </view>
</template>
<script>
import { getPatientList, getPatientNucleicInfo } from "@/api/user.js";
import { Toast } from "@/common/js/pay.js";
import myJsTools from "@/common/js/myJsTools.js";
export default {
  name: "nucleicOrderList",
  components: {},
  data() {
    return {
      Patient: "",
      uncleicList: null,
      len: 0,
    };
  },
  onShow() {},
  onLoad(user) {
    this.getPatientList(user.patientInfo || "");

  },
  
  methods: {
    //  His palms are sweaty knees weak arms are heavy
    // There's vomit on his sweater already Mom's spaghetti
    // He's nervous but on the surface he looks calm and ready
    //To drop bombs But he keeps on forgettin.
    //What he wrote down The whole crowd goes so loud
    
    // 获取就诊人列表
    async getPatientList(user) {
      uni.showLoading();
      let Patient;
      if (user) {
        Patient = JSON.parse(user);
        setTimeout(uni.hideLoading, 300);
      } else {
        let userId = uni.getStorageSync("userId");
        try {
          let { data } = await getPatientList({
            userId,
          });

          // console.log(data);
          if (!data) {
            Toast("请添加就诊人");
            setTimeout(() => {
              uni.redirectTo({
                url: `/pages/personalCenter/patientManage/index?action=uncleic`,
              });
            }, 2000);
          } else {
            Patient = data[0];
          }
          setTimeout(uni.hideLoading, 300);
        } catch (err) {
          throw err;
        }
      }

      if (!Patient) return;
      // console.log(Patient);
      if (Patient.patientImg) {
        myJsTools.downAndSaveImg(Patient.patientImg, (url) => {
          Patient.patientImg = url;
        });
      }
      let idcardReg =
        /^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$|^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/;
      if (idcardReg.test(Patient.idNo)) {
        Patient.shenfen = Patient.idNo;
        let dto = {
          idNo: Patient.shenfen,
          patienName: Patient.patientName,
        };
        getPatientNucleicInfo(dto).then((res) => {
          if (res.code != 20000) return;
          // setTimeout(uni.hideLoading, 300);
          this.uncleicList = res.data;
          this.len = this.uncleicList.length - 1;
        });
      } else {
        Patient.shenfen = "";
      }
      this.Patient = Patient;
    },
    goLisOrderList() {
      uni.redirectTo({
        url: `/pages/personalCenter/patientManage/index?action=uncleic`,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.nucleic_content {
  width: 100%;
  height: 100vh;
  box-sizing: border-box;
  background: #f5f5f5;
  padding: 0 32rpx 32rpx 32rpx;
  .heigth_24 {
    height: 24rpx;
  }
  .nucleic_head {
    box-sizing: border-box;
    height: 192rpx;
    background: #ffffff;
    border-radius: 16rpx;
    padding: 32rpx;
    .nucleic_head_conten {
      position: relative;
      width: 100%;
      height: 100%;

      display: flex;
      justify-content: space-between;
      box-sizing: border-box;
      .nucleic_headuser {
        flex: 1;
        display: flex;
        .nucleic_headportrait {
          width: 128rpx;
          height: 128rpx;
          // background: antiquewhite;
        }
        .nucleic_usermsg {
          margin-left: 20rpx;
          display: flex;
          flex-direction: column;
          justify-content: center;
          .usermsgname {
            font-size: 36rpx;
            color: #333333;
            font-weight: bold;
            line-height: 50rpx;
          }
          .usermsgid {
            margin-top: 6rpx;
            color: #666666;
            font-size: 24rpx;
            line-height: 34rpx;
          }
        }
      }
      .headswidch {
        width: 40rpx;
        height: 40rpx;
        cursor: pointer;
        position: absolute;
        right: 0;
        top: 50%;
        transform: translate(0, -50%);
      }
    }
  }
  .nucleic_list_head {
    width: 100%;
    height: 80rpx;
    margin-top: 22rpx;
    background: #fff;
    border-radius: 16rpx 16rpx 0 0;
    box-sizing: border-box;
    padding: 0rpx 0 18rpx 24rpx;
    view {
      font-size: 32rpx;
      line-height: 80rpx;
      color: #333333;
      font-weight: bold;
    }
  }
  .nucleic_list_centent {
    width: 100%;
    margin-top: 2rpx;
    padding: 0 38rpx 24rpx 24rpx;
    box-sizing: border-box;
    border-radius: 0 0 16rpx 16rpx;
    background: #fff;
    // background: antiquewhite;
    // height: 300px;
    .height_12px {
      height: 24rpx;
    }
    .nucleic_list_item {
      box-sizing: border-box;
      width: 100%;
      height: 196rpx;
      background: #fff;
      display: flex;
      justify-content: space-between;
      // background: aquamarine;
      padding-bottom: 24rpx;
      .nucleic_item_msg {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .nucleic_item_cy {
          display: flex;
          font-size: 32rpx;
          color: #999999;
          line-height: 32rpx;
          .cy_color {
            color: #333333;
          }
        }
      }
      .nucleic_item_flag {
        width: 106rpx;
        height: 106rpx;
        border-radius: 50%;
        background: #deeee3;
        margin-top: 40rpx;
        font-size: 40rpx;
        text-align: center;
        line-height: 106rpx;
        color: #1c9c52;
        font-weight: bold;
      }
      .flag_clor {
        background: #ffd2d2;
        color: #ff5050;
      }
    }
    .item_border {
      border-bottom: 2rpx dashed #e0e0e0;
    }
    .item_margin {
      margin-top: 24rpx;
    }
  }
  .botton_text {
    font-size: 32rpx;
    line-height: 44rpx;
    padding-bottom: 32rpx;
    .height_16 {
      height: 32rpx;
    }
    .botton_text_title {
      color: #333333;
      font-weight: bold;
    }
    .botton_text_msg {
      margin-top: 12rpx;
      color: #949494;
    }
  }
  .empty_list {
    @include flex;
    flex-direction: column;
    font-size: 28rpx;
    font-weight: 400;
    color: #999999;
    margin-top: 5rpx;
    text-align: center;
  }
  .empty_list image {
    margin-bottom: 40rpx;
    width: 376rpx;
    height: 244rpx;
    margin-left: 20rpx;
  }
  .empty_list_margin {
    margin-top: 68rpx;
  }
}
</style>