<template>
  <div class="result">
    <div class="detail">
      <div class="msg">
        <!-- 图标 -->
        <image src="/static/inspection/suc.png" class="icon" />
        <text>支付成功</text>
      </div>

      <div class="ewm" v-if="drugCode">
        <QRCODE
          :val="drugCode"
          :size="300"
          background="#ffffff"
          foreground="#000000"
          pdground="#000000"
          icon="/static/images/logo-img.png"
          :iconSize="45"
          onval
          loadMake
        />
        <p class="num">{{ drugCode }}</p>

        <p>请到药房出示取药码拿药</p>
      </div>

      <div class="default" v-else>
        <span>提示</span>
        <p>支付成功，等待审核中，如有疑问，请到药房咨询</p>
      </div>

      <div class="info">
        <div class="item">
          <span>姓名</span>
          <p>{{ patientName }}</p>
        </div>
        <div class="item">
          <span>手机号</span>
          <p>{{ telNo }}</p>
        </div>
        <div class="item">
          <span>订单号</span>
          <p>{{ orderNo }}</p>
        </div>
      </div>
    </div>

    <FOOTER v-if="businessId" @click="toSeleAddress"> 我要快递到家 </FOOTER>
  </div>
</template>

<script>
import { getFinshPay } from '@/api/order.js';

import FOOTER from '@/components/footer_button/button.vue';
import QRCODE from '@/components/tki-qrcode/tki-qrcode.vue';

export default {
  name: 'Result',
  components: {
    FOOTER,
    QRCODE,
  },
  data() {
    return {
      orderNo: '',
      drugCode: '',
      patientName: '',
      telNo: '',
      businessId: '',
    };
  },
  onLoad(v) {
    this.orderNo = v.orderNo;
    this.getDetail();
  },
  methods: {
    toSeleAddress() {
      uni.redirectTo({
        url:
          '/pages/address/index?action=quick&orderNo=' +
            this.orderNo +
            '&businessId=' +
            this.businessId || '',
      });
    },
    // 查询详情
    async getDetail() {
      let {
        data: { drugCode, patientName, telNo, businessId, businessCode },
      } = await getFinshPay(this.orderNo);
      if (businessCode) this.drugCode = drugCode;
      this.patientName = patientName;
      this.telNo = telNo;
      this.businessId = businessId;
    },
  },
};
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}
page {
  background-color: #f5f5f5;
}
.result {
  padding-bottom: 120rpx;

  .detail {
    padding: 24rpx 32rpx;

    .msg {
      height: 266rpx;
      background: #ffffff;
      border-radius: 8rpx;
      @include flex;
      flex-direction: column;

      .icon {
        width: 104rpx;
        height: 104rpx;
      }

      text {
        text-align: center;
        font-size: 36rpx;
        font-weight: bold;
        color: #15a0e6;
        margin-top: 16rpx;
      }
    }

    .ewm {
      margin-top: 24rpx;
      @include flex;
      flex-direction: column;
      padding: 44rpx 0;
      border-radius: 8rpx;
      color: #333;
      background-color: #fff;
      font-weight: bold;

      .num {
        font-size: 32rpx;
        margin-top: 16rpx;
        word-break: break-word;
        text-align: center;
        max-width: 80%;
      }

      p {
        font-size: 36rpx;
        margin-top: 40rpx;
      }
    }

    .default {
      background-color: #fff;
      border-radius: 8rpx;
      margin-top: 24rpx;
      @include flex;
      align-items: flex-start;
      padding: 24rpx 32rpx;

      span {
        font-size: 32rpx;
        color: #333;
        flex: none;
        width: 170rpx;
      }

      p {
        font-size: 32rpx;
      }
    }

    .info {
      background-color: #fff;
      border-radius: 8rpx;
      padding-left: 32rpx;
      margin-top: 24rpx;

      .item {
        @include flex(left);
        border-bottom: 1px solid #f5f5f5;
        height: 92rpx;
        font-size: 32rpx;

        &:last-child {
          border-bottom: none;
        }

        span {
          font-weight: bold;
          flex: none;
          width: 170rpx;
        }

        p {
          flex: 1;
        }
      }
    }
  }
}
</style>
