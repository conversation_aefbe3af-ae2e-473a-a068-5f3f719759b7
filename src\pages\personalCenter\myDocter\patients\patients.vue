<template>
  <!-- 选择就诊人 -->
  <view class="sele_patient">
    <!-- 就诊人列表 -->
    <view class="sele_list" v-if="patientList.length">
      <PATIENT :list="patientList" @click="toDocter" />
    </view>

    <EMPT v-else title="暂无就诊人" />
  </view>
</template>

<script>
// 就诊人组件
import PATIENT from "../../components/patients.vue";
// 空组件
import EMPT from "../empt/empt.vue";
import { getPatientList } from "@/api/user.js";
export default {
  components: {
    PATIENT,
    EMPT,
  },
  data() {
    return {
      // 就诊人列表
      patientList: [],
      pageType: "",
    };
  },
  onLoad(options) {
    this.pageType = options.pageType;
  },
  created() {
    this.getPatientList();
  },
  methods: {
    // 跳转相关医生列表
    toDocter(item) {
      // 存在没有头像的情况
      let img = item.patientImg || "";
      // 就诊人id
      uni.navigateTo({
        url:
          "../index?patientId=" +
          item.patientId +
          "&patientImg=" +
          img +
          "&pageType=" +
          this.pageType,
      });
    },
    // 获取就诊人列表
    async getPatientList() {
      let userId = uni.getStorageSync("userId");
      let res = await getPatientList({
        userId,
      });
      let data = res.data;
      if (data.length) {
        this.patientList = data;
      } else {
        this.patientList = [];
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.sele_patient {
  width: 100%;

  .sele_list {
    padding: 20upx 32upx;
  }
}
</style>
