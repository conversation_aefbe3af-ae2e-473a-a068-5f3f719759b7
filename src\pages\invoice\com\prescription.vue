<template>
  <!-- 在线购药 -->
  <view class="prescription" @click="click">
    <!-- 订单号 -->
    <view class="order_num">
      <text>处方</text>
      <text class="status wait">未开票</text>
    </view>
    <view class="order_num">
      <text>订单编号：{{ item.orderNo }}</text>
    </view>
    <!-- 药店 药品列表 -->
    <view
      class="pharmacy_list"
      v-for="(p, pn) in item.orderDrugStoreListNewVOList"
      :key="pn"
    >
      <view class="pharmacy">
        <!-- 药店名称 -->
        <text
          >{{ p.drugstoreName
          }}{{ p.isProprietary == 1 ? "（自营）" : "" }}</text
        >
      </view>

      <view class="durg_list">
        <!-- 单个药品 -->
        <view
          class="durg_item"
          v-for="(d, dn) in p.drugShoppingOnlineOrderList"
          :key="dn"
        >
          <img
            v-if="d.drugImg"
            v-img="d.drugImg"
            :data-src="errUrl"
            class="left"
          />
          <image v-else class="left" src="/static/shop/drug.png"></image>
          <!-- 内容 -->
          <view class="right">
            <!-- 药品名称 -->
            <view class="drug_name">{{ d.drugName }}</view>
            <!-- 规格 -->
            <view class="drug_info">规格: {{ d.gg }}</view>
            <!-- 活动 -->
            <view class="drug_red" v-if="d.activeName">
              单品{{ d.activeName }}</view
            >
            <!-- 价位数量 -->
            <view class="right_menu">
              <text class="price"
                >￥{{ d.drugRealMoney | toFixed }}
                <text class="del" v-if="d.drugShouldMoney != d.drugRealMoney"
                  >￥{{ d.drugShouldMoney | toFixed }}</text
                >
              </text>
              <text class="num">x{{ Number(d.quan) }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 下单时间 -->
    <view class="time"> 下单时间：{{ item.addTime }} </view>

    <!-- 统计 -->
    <view class="count">
      <p>
        开票金额：
        <span>￥{{ item.orderRealMoney | toFixed }}</span>
      </p>
      <span class="button">
        申请开票
      </span>
    </view>
  </view>
</template>

<script>
export default {
  name: "Prescription",
  props: ["item"],
  data() {
    return {
      errUrl: require("../../../static/shop/drug.png"),
    };
  },
  methods: {
    click() {
      this.$emit("click");
    },
  },
};
</script>

<style lang="scss" scoped>
.prescription {
  width: 100%;
  padding: 0 24rpx 24rpx;
  background-color: #fff;
  margin-bottom: 24rpx;
  font-size: 28rpx;
  border-radius: 8rpx;
  box-shadow: 0 0 20rpx #ddd;
  box-sizing: border-box;

  .order_num {
    @include flex(lr);
    font-size: 28rpx;
    font-weight: bold;
    height: 88rpx;
    border-bottom: 1px solid #f5f5f5;

    &:first-child {
      height: 88rpx;
    }

    text::nth-child(2) {
      font-weight: normal;
    }

    .status {
      font-weight: normal;

      &.wait {
        color: red;
      }

      &.done {
        @include font_theme;
      }
    }
  }

  .pharmacy_list {
    border-bottom: 1px dashed #ebebeb;

    &:only-child {
      border: none;
    }

    .pharmacy {
      @include flex(lr);
      font-size: 28rpx;
      font-weight: bold;
      height: 88rpx;
      border-bottom: 1px solid #ebebeb;

      text::nth-child(2) {
        font-weight: normal;
      }

      text {
        color: #333;

        &.wait {
          color: #ff5050;
        }

        &.close {
          color: #999;
        }

        &.done {
          @include font_theme;
        }
      }
    }

    .durg_list {
      .durg_item {
        @include flex;
        padding: 24rpx 0;

        &:last-child {
          .right {
            border-bottom: none;
          }
        }

        .left {
          width: 128rpx;
          height: 128rpx;
          margin-right: 24rpx;
          border: 1px solid #eee;
          border-radius: 8rpx;
          flex: none;
        }

        .right {
          flex: 1;
          min-height: 128rpx;
          @include flex(lr);
          flex-direction: column;
          align-items: stretch;
          border-bottom: 1px solid #eee;

          .drug_name {
            flex: 1;
            font-size: 28rpx;
            font-weight: bold;
          }

          .drug_info {
            flex: 1;
            font-size: 24rpx;
            color: #999;
          }

          .drug_red {
            flex: 1;
            font-size: 24rpx;
            color: red;
          }

          .right_menu {
            flex: 2;
            @include flex(lr);
            align-items: flex-end;

            .price {
              font-size: 28rpx;
              color: #ff3b30;
              font-weight: bold;

              .del {
                font-size: 20rpx;
                color: #999;
                text-decoration: line-through;
                margin-left: 10rpx;
              }
            }

            .num {
              font-size: 28rpx;
            }
          }
        }
      }
    }
  }

  .time {
    height: 80rpx;
    @include flex(left);
    border-bottom: 1px solid #f5f5f5;
  }

  .count {
    @include flex(lr);
    height: 88rpx;
    font-size: 28rpx;

    p {
      @include flex;

      span {
        color: red;
        font-weight: bold;
      }
    }

    .button {
      width: 180rpx;
      height: 60rpx;
      @include flex;
      border-radius: 30rpx;
      @include bg_theme;
      color: #fff;
      font-weight: bold;
    }
  }
}
</style>
