<template>
  <view class="service" @click="toPath">
    <!-- 类型 状态 -->
    <view class="label">
      <text class="title">自定义服务</text>

      <text class="wait" v-if="item.payStatus == 1">待支付</text>
      <text class="done" v-if="item.payStatus == 2">已支付</text>
      <text class="close" v-if="item.payStatus == 9">交易关闭</text>

      <text v-if="item.payStatus == 7">已退费</text>
    </view>
    <!-- 时间 内容 -->
    <view class="cont">
      <view class="time">时间：{{ item.addTime }}</view>
      <view class="price">
        <text>总价:￥{{ item.totalPay }}</text>
        <text>优惠价:￥{{ item.deMoney }}</text>
        <text class="bold">实付款:￥{{ item.orderMoney }}</text>
      </view>
    </view>
    <!-- 按钮 -->
    <view class="but" v-if="item.payStatus == 1">
      <text @click.stop="toPath">去支付</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'Service',
  props: ['item'],
  methods: {
    // 不同状态跳转
    toPath() {
      const { customBussinessId } = this.item.remark;
      const { hosId } = this.item;
      uni.setStorageSync('hosId', hosId);

      uni.navigateTo({
        url: '/pages/chatCardDetail/customService?id=' + customBussinessId,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.service {
  width: 100%;
  padding: 24rpx;
  background-color: #fff;
  margin-bottom: 24rpx;
  font-size: 28rpx;
  border-radius: 8rpx;
  box-shadow: 0 0 20rpx #ddd;

  .label {
    @include flex(lr);
    font-weight: bold;

    text {
      color: #333;

      &.wait {
        color: #ff5050;
      }

      &.close {
        color: #999;
      }

      &.done {
        @include font_theme;
      }
    }
  }

  .cont {
    padding: 24rpx 0;

    .time {
      color: #6c6c6c;
    }

    .price {
      margin-top: 15rpx;
      color: #6c6c6c;
      text-align: right;

      text {
        margin-left: 10rpx;
      }

      .bold {
        color: #333;
        font-weight: bold;
      }
    }
  }

  .but {
    @include flex(right);

    text {
      @include flex;
      width: 158rpx;
      height: 56rpx;
      border-radius: 28rpx;
      @include bg_theme;
      color: #fff;
      font-size: 26rpx;
    }
  }
}
</style>
