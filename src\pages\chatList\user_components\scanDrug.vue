<template>
  <view class="user_continue">
    <view>
      <view class="patientName">{{ patientName }}</view>
      <!-- 卡片 -->
      <view class="continue_content" @click="click">
        <!-- 标题 -->
        <view class="content-top">
          <image src="/static/images/chat/continue-cat.png" class="cont_icon" />
          <view class="content_title">预支付药品订单</view>
        </view>
        <!-- 内容 -->
        <view v-for="(item,index) in drug" :key="index" class="content_cont">
          <view v-for="(drug,didx) in item.ls||item.drugShoppingOnlineOrderList" :key="didx" class="drugItem">
            <view class="drugItem-line">
              <view>{{drug.drugName}}</view>
              <view>×{{drug.quan*1}}</view>
            </view>
            <view class="drugItem-line">
              <view>规格：{{drug.gg}}</view>
              <view style="color: red">￥{{drug.realyPay}}</view>
            </view>
          </view>
          <view class="totalPrice" style="color: red">￥{{ getTotalPrice(item.ls||item.drugShoppingOnlineOrderList) | toFixed}}</view>
        </view>
      </view>
    </view>
    <!-- 头像 -->
    <image
      @click="head"
      :src="imgUrl || '/static/images/docHead.png'"
      mode="aspectFill"
      class="user_img"
    />
  </view>
</template>

<script>
export default {
  props: {
    // 头像
    imgUrl: {
      type: String,
      default: '',
    },
    // 病名
    title: {
      type: String,
      default: '',
    },
    name:{
      type: String,
      default: '慢病续方'
    },
    drug:{
      type:Array,
      default: () => []
    },
    patientName:{}
  },
  methods: {
    getTotalPrice(scanCheckDrugList){
     return scanCheckDrugList.reduce((a,b)=>{
        return a +this.calculateTotalPrice(b.price,b.quan)
      },0)
    },
    click() {
      this.$emit('click');
    },
    head() {
      this.$emit('head');
    },
    calculateTotalPrice(unitPrice, quantity) {
      // 假设 unitPrice 是以元为单位的小数，quantity 是整数
      // 将单价转换为以分为单位的整数
      const priceInCents = Math.round(unitPrice * 100);
      const totalPriceInCents = priceInCents * quantity;
      // 将总价转换回以元为单位的小数
      const totalPrice = totalPriceInCents / 100;
      return totalPrice;
    },
  },
};
</script>

<style scoped lang="scss">
.totalPrice{
  line-height: 35px;
  text-align: right;
  padding-right: 10px;
}
.drugItem-line{
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  line-height: 25px;
}
.drugItem{
  flex: 1;
  padding: 10px;
  border-bottom: 1px solid #ececec;
}
.content-top{
  display: flex;
  align-items: center;
  background: rgba(118,121,226,0.41) !important;
  line-height: 30px;
}
.user_continue {
  box-sizing: border-box;
  @include flex(right);
  padding-bottom: 30upx;
  align-items: flex-start;

  // 头像
  .user_img {
    width: 64upx;
    height: 64upx;
    border-radius: 4upx;
    margin-left: 16upx;
    flex: none;
  }
  // 左侧图标
  .cont_icon {
    width: 40upx;
    height: 40upx;
    border-radius: 8upx;
    margin-right: 15upx;
    flex: none;
    margin-left: 10px;
  }
  // 内容
  .continue_content {
    width: 516upx;
    color: #333;
    border: 1px solid #ececec;
    box-sizing: border-box;
    border-radius: 32upx 8upx 32upx 32upx;

    // 标题
    .content_title {
      font-size: 28upx;
      color: $k-title;
      font-weight: bold;
    }

    // 底部内容
    .content_cont {


      // 右侧文案
      .cont_right {
        flex: 1;
        min-height: 80upx;
        @include flex(lr);
        flex-direction: column;
        align-items: stretch;

        // 标题
        .cont_right_title {
          font-size: 28upx;
          color: $k-title;
          font-weight: bold;
        }

        // 描述
        .cont_right_info {
          font-size: 24upx;
          color: $k-info-title;
        }
      }
    }
  }
}
</style>
