import { getReceiptWay } from '@/api/order';

function Toast(title) {
  uni.showToast({
    title,
    icon: 'none',
  });
}

/**
 * 封装支付 微信 支付宝
 * */
class Pay {
  // 支付方式
  payList = [];

  // 构造函数
  constructor(subjectId) {
    if (subjectId) this.getPayList(subjectId);
  }

  // 根据主体id 查询支付信息
  async getPayList(subjectId) {
    let { data } = await getReceiptWay({
      subjectId,
    });
    this.payList = data;
  }

  // 选择支付方式 是否无需支付
  async selePay(money = 1) {
    let that = this;
    if (money == 0) return Promise.resolve({ index: 0, item: { appid: 1 } });
    // 需要支付
    return new Promise((resolve, reject) => {
      // 如果不可选支付
      if (!that.payList.length) {
        Toast('当前没有配置支付方式');
        return reject();
      }
      // 如果只有一个支付方式
      if (that.payList.length == 1) {
        let item = that.payList[0];
        if (item.receiptType == 1) {
          resolve({
            index: 1,
            item,
          });
        } else {
          resolve({
            index: 2,
            item,
          });
        }
        return;
      }
      // 如果在线支付
      uni.showActionSheet({
        itemList: ['微信支付', '支付宝支付'],
        success(res) {
          let index = res.tapIndex + 1;
          let item;
          item = that.payList.filter((it) => it.receiptType == index)[0];
          if (!item) {
            Toast('暂不支持该支付方式');
            return reject();
          }
          resolve({
            index,
            item,
          });
        },
        fail(err) {
          reject(err);
        },
      });
    });
  }

  // 微信支付
  async wxPay(info) {
    return new Promise((resolve, reject) => {
      WeixinJSBridge.invoke('getBrandWCPayRequest', info, (res) => {
        if (res.err_msg == 'get_brand_wcpay_request:ok') {
          resolve();
        }
        reject();
      });
    });
  }

  // 提示
  Toast(str) {
    Toast(str);
  }
}

/*
	使用方法
	import Pay from '@/modules/pay';

	初始化 传入主体id 内部根据主体id 查询支持的支付方式
	const pay = new Pay(id);

	选择支付方式 index 支付方式 1 微信 2 支付宝
	let {index, item} = await pay.selePay();

	直接调用微信支付
	await pay.wxPay(info);

*/

export default Pay;
