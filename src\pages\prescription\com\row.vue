<template>
  <view class="row" @click="click">
    {{ text }}
    <text>
      <slot>
        <uni-icons type="arrowright" color="#666" />
      </slot>
    </text>
  </view>
</template>

<script>
export default {
  name: 'Row',
  props: ['text'],
  methods: {
    click() {
      this.$emit('click');
    },
  },
};
</script>

<style lang="scss" scoped>
.row {
  width: 100%;
  margin-top: 24rpx;
  background: #fff;
  border-radius: 8rpx;
  height: 88rpx;
  @include flex(lr);
  font-size: 28rpx;
  color: #333;
  padding: 0 32rpx;
  box-sizing: border-box;
}
</style>
