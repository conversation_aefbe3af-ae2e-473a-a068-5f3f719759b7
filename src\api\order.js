import http from "../common/request/request";
// 获取订单详情中的信息(post请求)
export function getDivisionPrescriptionInfo(param = {}) {
  return http({
    url: "business/proPrescriptionController/getDivisionPrescriptionInfo",
    param,
    method: "post",
  });
}
// 缴费分割(post请求)
export function saveReceipt(param = {}) {
  return http({
    url: "business/paymentBusiness/saveReceipt",
    param,
    method: "post",
  });
}

// 修改缴费非待缴费明细(post请求)
export function changeNotToPaymentDetails(param = {}) {
  return http({
    url: "business/paymentBusiness/changeNotToPaymentDetails",
    param,
    method: "post",
  });
}

// 挂号订单详情(post请求)
export function allRegisterOrder(param = {}) {
  return http({
    url: "business/bussinessOrder/allRegisterOrder",
    param,
    method: "post",
  });
}

// 获取自定义服务订单支付信息
export function getPayInfo(param = {}) {
  return http({
    url: "business/proCustomBusiness/getPayInfo",
    param,
    method: "post",
  });
}

// 查询支付结果-支付状态
export function queryServePayStatus(param = {}) {
  return http({
    url: "business/proCustomBusiness/queryRegisterPayStatus",
    param,
    method: "post",
  });
}

// 获取缴费支付的订单信息(微信支付加密串)
export function getRegisterPayInfo(param = {}) {
  return http({
    url: "business/paymentBusiness/getRegisterPayInfo",
    param,
    method: "post",
  });
}
// 获取处方支付结果
export function getPresPayStatue(param = {}) {
  return http({
    url: "business/paymentBusiness/queryRegisterPayStatus",
    param,
    method: "post",
  });
}

// 我的-处方待支付详情
export function toPayForOrderDetails(param = {}) {
  return http({
    url: "business/bussinessOrder/toPayForOrderDetails",
    param,
    method: "post",
  });
}

// 根据订单编号获取物流信息
export function findPayOrderInfo(param = {}) {
  return http({
    url: "order/payOrder/findPayOrderInfo",
    param,
    method: "post",
  });
}

// 根据处方id获取物流单号
export function getLogisticsInfo(param = {}) {
  return http({
    url: "order/payOrder/getLogisticsInfo",
    param,
    method: "post",
  });
}

// 确认收货
export function confirmGoods(param = {}) {
  return http({
    url: "order/logistics/confirmGoods",
    param,
    method: "post",
  });
}

// 获取待评价列表
export function toEvaluateOrderListPage(param = {}) {
  return http({
    url: "order/payOrder/toEvaluateOrderListPage",
    param,
    method: "post",
  });
}

// 查询订单平均类型信息
export function queryEvaluateType(regId) {
  return http({
    url: "order/payOrder/queryEvaluateType",
    param: {
      regId,
    },
    method: "post",
  });
}

// 保存挂号订单评价信息
export function saveEvaluate(param = {}) {
  return http({
    url: "order/payOrder/saveEvaluate",
    param,
    method: "post",
  });
}

// 撤销挂号(post请求)
export function cancel(param = {}) {
  return http({
    url: "business/paymentBusiness/cancel",
    param,
    method: "post",
  });
}

// 获取检查单支付信息
export function getInspectPayInfo(param = {}) {
  return http({
    url: "business/proPacsInfoController/getPayInfo",
    param,
    method: "post",
  });
}

// 获取检查单支付信息
export function getInspectPayStatus(param = {}) {
  return http({
    url: "business/proPacsInfoController/queryRegisterPayStatus",
    param,
    method: "post",
  });
}

// 查询收款方式
export function getReceiptWay(param = {}) {
  return http({
    url: "basic/dicReceiptWay/getReceiptWay",
    param,
    method: "post",
  });
}

// 撤销订单
export function cancelOrder(param = {}) {
  return http({
    url: "order/payOrder/cancelOrder",
    param,
    method: "post",
  });
}

// 查询取药码
export function queryGetMedicine(businessId) {
  return http({
    url: "order/payOrder/queryGetMedicine",
    param: {
      businessId,
    },
    method: "post",
  });
}

// 查看电子处方
export function getPdf(businessId) {
  return http({
    url: "business/diagnosisforweb/getPrescriptionPdfName",
    param: {
      businessId,
    },
    method: "post",
  });
}

// 查看电子处方
export function getPdfs(businessId) {
  return http({
    url: "business/diagnosisforweb/getPrescriptionPdfNameList",
    param: {
      businessIds: [businessId],
    },
    method: "post",
  });
}

// 订单列表(0520)
export function orderListPageNew(param) {
  param.patients = uni.getStorageSync("patientIdList");
  return http({
    url: "order/payOrderPatient/orderListPageNew",
    param,
    method: "post",
  });
}

// 查询订单详情
export function orderDetailNew(param) {
  return http({
    url: "order/payOrderPatient/orderDetailNew",
    param,
    method: "post",
  });
}

// 查询订单详情(新)
export function orderDetailByMall(param) {
  return http({
    url: "order/payOrderPatient/orderDetailByMall",
    param,
    method: "post",
  });
}

// 扫码开方下单
export function createScanCodeOrder(param) {
  return http({
    url: "order/scanCodePrescribing/createScanCodeOrder",
    param,
    method: "post",
  });
}

// 根据msgId 查询扫码开方状态
export function findOrderByMsgid(msgid) {
  return http({
    url: "order/payOrder/findOrderByMsgid",
    param: {
      msgid,
    },
    method: "post",
  });
}

// 患者端-挂号订单详情
export function pregOrderDetailNew(param) {
  return http({
    url: "order/payOrderPatient/pregOrderDetailNew",
    param,
    method: "post",
  });
}

/**
 * 快捷开方-复购
 * @description 根据业务Id查询订单包含物流状态
 * @param businessId 业务单号
 * */

export function findOrderByBusinessId(param) {
  return http({
    url: "order/payOrder/findOrderByBusinessId",
    param,
    method: "post",
  });
}

/**
 * @description 快捷开方-更改订单中患者
 * @param orderNo 订单号
 * @param patientId 就诊人id
 * */

export function updateOrderPatient(param) {
  return http({
    url: "business/fastPrescription/updateOrderPatient",
    param,
    method: "post",
  });
}

/**
 * @description 快捷开方-更改订单中物流
 * @param orderNo 订单号
 * @param deliveryTelNo 收件人手机号
 * @param deliveryName 收件人名称
 * @param deliveryAddressDetail 收件人地址
 * */

export function updateOrderDelivery(param) {
  return http({
    url: "business/fastPrescription/updateOrderDelivery",
    param,
    method: "post",
  });
}

/**
 * @description 快捷开方-处方信息
 * @param businessId 业务id
 * @param docId 医生id
 * @param dpmpId 药品
 * @param type 类型 1 首次 2 复购
 * */

export function queryFastPrescription(param) {
  return http({
    url: "business/fastPrescription/queryFastPrescription",
    param,
    method: "post",
  });
}

/**
 * 快捷开方
 * @description 创建扫码快捷开方订单
 * @param appid appid
 * @param docId 医生id
 * @param dpmpId 药品
 * @param callId 主体
 * @param msgId 消息
 * @param openid 用户openid
 * @param payType 支付方式 1 微信 2 支付宝 3 业务 4 线下
 * */

export function createScanCodeFastPrescription(param) {
  return http({
    url: "business/fastPrescription/createScanCodeFastPrescription",
    param,
    method: "post",
  });
}

// 扫码开方 更换支付方式
export function scanCodeOrderUpdatePayType(param) {
  return http({
    url: "order/scanCodePrescribing/scanCodeOrderUpdatePayType",
    param,
    method: "post",
  });
}

/**
 * 快捷开方
 * @description 查询扫码快捷开方订单支付状态
 * @param businessId 业务id
 * @param orderNo 订单号
 * @param payType 支付方式 1 微信 2 支付宝 3 业务 4 线下
 * */

export function getScanCodeFastPrescriptionOrderStatus(param) {
  return http({
    url: "business/fastPrescription/getScanCodeFastPrescriptionOrderStatus",
    param,
    method: "post",
  });
}

/**
 * 快捷开方-复购
 * @description 复购查询详情
 * @param businessId 业务id
 * */

export function saveProPayOrderRepurchase(param) {
  return http({
    url: "business/proPayOrderController/saveProPayOrderRepurchase",
    param,
    method: "post",
  });
}

/**
 * 快捷开方-复购
 * @description 创建复购订单
 * @param {Object} param
 * */

export function createSecondFastPrescription(param) {
  return http({
    url: "business/fastPrescription/createSecondFastPrescription",
    param,
    method: "post",
  });
}

/**
 * 快捷开方-复购
 * @description 修改复购订单
 * @param {Object} param
 * */

export function updateSecondFastPrescriptionOrder(param) {
  return http({
    url: "business/fastPrescription/updateSecondFastPrescriptionOrder",
    param,
    method: "post",
  });
}

/**
 * 快捷开方-复购
 * @description 查询复购订单支付状态
 * @param {String} orderNo 订单号
 * @param {String} payType 支付方式
 * */

export function getSecondFastPrescriptionOrderStatus(param) {
  return http({
    url: "business/fastPrescription/getSecondFastPrescriptionOrderStatus",
    param,
    method: "post",
  });
}

/**
 * 快捷开方-复购
 * @description 获取支付完成信息
 * @param {String} orderNo 订单号
 * */

export function getFinshPay(orderNo) {
  return http({
    url: "business/fastPrescription/getFinshPay",
    param: { orderNo },
    method: "post",
  });
}

// 正常扫码开方 已存在订单支付
export function createScanCodeOrderUpdatePayType(param) {
  return http({
    url: "order/scanCodePrescribing/createScanCodeOrderUpdatePayType",
    param,
    method: "post",
  });
}

// 代付生成支付订单信息
export function getHelpPayCreateResult(param) {
  return http({
    url: "order/payOrder/getHelpPayCreateResult",
    param,
    method: "post",
  });
}

// 患者端-订单详情代付无需登录
export function orderDetailNoLogin(orderNo) {
  return http({
    url: "order/payOrderPatient/orderDetailNoLogin",
    param: { orderNo },
    method: "post",
  });
}

// 查询支付状态（代付）  处方查询支付状态
export function queryRegisterPayStatusHelpPay(param) {
  return http({
    url: "business/paymentBusiness/queryRegisterPayStatusHelpPay",
    param,
    method: "post",
  });
}

// 查询商城订单的状态（代付）  商城查询支付状态
export function queryMallOrderStatusHelpPay(param) {
  return http({
    url: "order/onlineMallOrder/queryMallOrderStatusHelpPay",
    param,
    method: "post",
  });
}

// 查询全部订单的状态(待收货等)
export function orderCount() {
  let param = {
    patients: [],
  };
  param.patients = uni.getStorageSync("patientIdList");
  return http({
    url: "order/payOrderPatient/orderCount",
    param,
    method: "post",
  });
}

// 查询待评价
export function AllOrderCount() {
  let param = {
    patients: [],
  };
  param.patients = uni.getStorageSync("patientIdList");
  return http({
    url: "/order/payOrder/AllOrderCount",
    param,
    method: "post",
  });
}
//权限配置
export function getSysPlatformConfigByKeyList(arr = []) {
  return http({
    url: "basic/sysPlatformConfig/getSysPlatformConfigByKeyList",
    param: {
      configKeyList: arr,
    },
    method: "post",
  });
}
export function getPatientChat2(param) {
  return http({
    url: "basic/proPatientBusiness/getPatientChat",
    param,
    method: "post",
  });
}

export function getPatientChatListSM(param) {
  return http({
    url: "basic/proPatientBusiness/getPatientChatListSM",
    param,
    method: "post",
  });
}
export function getPatientChatStatusByRegId(param) {
  return http({
    url: "basic/proPatientBusiness/getPatientChatStatusByRegId",
    param,
    method: "post",
  });
}
export function findOrderByBusinessIds(param) {
  return http({
    url: "order/payOrder/findOrderByBusinessId",
    param,
    method: "post",
  });
}
export function getPatientChatSM(param) {
  return http({
    url: "basic/proPatientBusiness/getPatientChatSM",
    param,
    method: "post",
  });
}