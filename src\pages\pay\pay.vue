<template>
  <!-- 支付宝支付 -->
  <view class="pay_detail">
    <!-- 要支付的价位 -->
    <view class="pay_price">¥{{ price || 0 }}</view>
    <!-- 文案 -->
    <view class="pay_info">
      若您支付成功，请点击我已支付成功；若您支付失败，可点击重新支付进行再次支付或者点击取消支付，取消本次支付
    </view>
    <!-- 按钮 -->
    <view class="pay_buttons">
      <view class="but_suc" @click="getStatus">我已支付成功</view>
      <view class="but_res" @click="restPay">重新支付</view>
      <view class="but_canl" @click="goBack">取消支付</view>
    </view>

    <!-- 引导 -->
    <Tips v-show="showTip" />
  </view>
</template>

<script>
import {
  // 转诊/挂号/处方
  getPresPayStatue,
  // 自定义服务
  queryServePayStatus,
  // 快捷开方
  getScanCodeFastPrescriptionOrderStatus,
  // 复购
  getSecondFastPrescriptionOrderStatus,
  // 代付正常处方
  queryRegisterPayStatusHelpPay,
  // 代付商城购药
  queryMallOrderStatusHelpPay,
  updateOrderPatient,
} from '@/api/order.js';

import { queryMallOrderStatus, queryFastMallOrderStatus } from '@/api/shop.js';

import { findPatientInfoByPatientId } from '@/api/user.js';

import { createFastPrescription } from '@/api/base.js';

import {
  queryLisRegisterPayStatus,
  queryPacsRegisterPayStatus,
} from '@/api/inspect';
import { Toast } from '@/common/js/pay.js';
import Tips from '@/components/tips/tips.vue';

export default {
  name: 'PayDetail',
  components: {
    Tips,
  },
  data() {
    return {
      // 微信内 需要提示
      showTip: true,
      // 是否第一次
      isFrist: true,
      // 支付价位
      price: '',
      // 挂号
      ghId: '',
      // 检查单
      ppiId: '',
      // 支付地址
      url: '',
      // 扫码开方
      orderNo: '',
      // 快捷开方
      quick: '',
      // 自定义服务
      customBussinessId: '',
      // 检验单
      pliId: '',
      // 复购
      repeat: '',
      // 处方模板
      dpmpId: '',
      // 是否新的商城购药
      isNewShop: '',
      // 代付来源
      source: '',
    };
  },
  onLoad(e) {
    this.getIsWx();
    this.price = e.price;
    let url = e.url;
    if (e.pliId) this.pliId = e.pliId;
    if (e.ghId) this.ghId = e.ghId;
    if (e.ppiId) this.ppiId = e.ppiId;
    if (e.orderNo) this.orderNo = e.orderNo;
    if (e.isNewShop) this.isNewShop = e.isNewShop;
    if (e.quick) this.quick = e.quick;
    if (e.dpmpId) this.dpmpId = e.dpmpId;
    if (e.repeat) this.repeat = e.repeat;
    if (e.customBussinessId) this.customBussinessId = e.customBussinessId;
    if (e.source) this.source = e.source;
    if (e.openId) sessionStorage.setItem('openId', e.openId);
    this.url = atob(url);
    // 如果微信环境 不请求数据
    if (this.showTip) return;

    // window.open(atob(url));
    this.toPay();
  },

  onHide() {
    this.isFrist = false;
    this.showTip = false;
  },
  onShow() {
    // 非第一次
    if (!this.isFrist) {
      this.getStatus();
    }
  },
  methods: {
    getIsWx() {
      let isWx;
      const ua = window.navigator.userAgent.toLowerCase();
      if (
        ua.match(/MicroMessenger/i) == 'micromessenger' ||
        ua.match(/_SQ_/i) == '_sq_'
      ) {
        isWx = true;
      } else {
        isWx = false;
      }
      this.showTip = isWx;
    },
    goBack() {
      uni.navigateBack();
    },
    // 跳转支付
    toPay() {
      window.location.href = this.url;
    },
    // 重新支付
    restPay() {
      this.getIsWx();
      if (this.showTip) return;
      this.toPay();
    },

    // 查询状态
    async getStatus() {
      uni.showLoading();
      let res;
      // 挂号
      if (this.ghId && !this.source) {
        res = await getPresPayStatue({ ghId: this.ghId });
      }
      // 自定义
      if (this.customBussinessId) {
        res = await queryServePayStatus({
          customBussinessId: this.customBussinessId,
        });
      }
      // 检查单
      if (this.ppiId) {
        res = await queryPacsRegisterPayStatus(this.ppiId);
      }
      // 扫码开方 / 代付
      if (this.orderNo) {
        // 不是代付
        if (!this.source) {
          // 新购药流程
          if (this.isNewShop) {
            res = await queryFastMallOrderStatus(this.orderNo);
          } else {
            res = await queryMallOrderStatus(this.orderNo);
          }
        } else {
          let obj = {
            orderNo: this.orderNo,
            ghId: this.ghId,
            openid: sessionStorage.getItem('openId'),
          };
          // 正常处方 代付
          if (this.source == 1) {
            res = await queryRegisterPayStatusHelpPay(obj);
          } else {
            res = await queryMallOrderStatusHelpPay(obj);
          }
        }
      }
      // 快捷开方
      if (this.quick) {
        res = await getScanCodeFastPrescriptionOrderStatus({
          orderNo: this.quick,
        });
      }
      // 检验单
      if (this.pliId) {
        res = await queryLisRegisterPayStatus(this.pliId);
      }
      // 复购
      if (this.repeat) {
        res = await getSecondFastPrescriptionOrderStatus({
          orderNo: this.repeat,
          payType: 2,
        });
      }

      uni.hideLoading();

      if (res.code != 20000) return;

      if (this.orderNo) {
        // 代付
        if (this.source) {
          // 正常处方有reg 商城没有
          res.data.regStatus = res.data.regStatus || res.data.orderStatus;
        } else {
          res.data.regStatus = res.data.orderStatus;
        }
      }

      // 快捷开方
      if (this.quick) {
        res.data.regStatus = res.data.status;
      }

      if (res.data.regStatus == 2) {
        Toast('支付成功');
        setTimeout(this.success, 1500);
      } else {
        uni.showModal({
          title: '支付结果',
          content: '系统查询到您还未支付成功，请点击重新支付进行支付',
          showCancel: false,
        });
      }
    },
    // 支付成功
    async success() {
      // 代付
      if (this.source) {
        uni.showModal({
          title: '支付结果',
          content: '订单支付成功，请回到微信',
          showCancel: false,
        });
        return;
      }
      // 扫码
      if (this.orderNo) {
        uni.reLaunch({
          url: '/pages/scanCode/success',
        });
        return;
      }
      // 快捷开方
      if (this.quick) {
        let patientIdList = uni.getStorageSync('patientIdList') || [];
        // 没有就诊人
        if (!patientIdList.length) {
          uni.redirectTo({
            url:
              '/pages/quick/info?orderNo=' +
              this.quick +
              '&dpmpId=' +
              this.dpmpId,
          });
          return;
        }
        // 只有一个就诊人
        if (patientIdList.length == 1) {
          let { data: detail } = await findPatientInfoByPatientId(
            patientIdList[0]
          );
          await createFastPrescription({
            ...detail,
            orderNo: this.quick,
            dpmpId: this.dpmpId,
          });
          await updateOrderPatient({
            patientId: detail.patientId,
            orderNo: this.orderNo,
          });

          uni.redirectTo({
            url: '/pages/quick/result?orderNo=' + this.quick,
          });
          return;
        }
        // 选择就诊人
        if (patientIdList.length > 1) {
          uni.redirectTo({
            url:
              '/pages/quick/sele?orderNo=' +
              this.quick +
              '&dpmpId=' +
              this.dpmpId,
          });
        }
        return;
      }
      // 复购
      if (this.repeat) {
        // 去支付成功页
        uni.reLaunch({
          url: '/pages/quick/result?orderNo=' + this.repeat,
        });
        return;
      }

      // 去首页
      uni.reLaunch({
        url: '/pages/index/index',
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.pay_detail {
  width: 100%;
  height: 100vh;

  .tips {
    height: 100vh;
    position: fixed;
    top: 0;
  }

  .pay_price {
    font-size: 72upx;
    font-weight: bold;
    text-align: center;
    color: #333;
    margin-top: 200upx;
  }

  .pay_info {
    font-size: 28upx;
    width: 536upx;
    margin: 30upx auto;
    line-height: 40upx;
  }

  .pay_buttons {
    padding: 0 75upx;

    view {
      width: 100%;
      height: 84upx;
      border-radius: 42upx;
      font-size: 32upx;
      font-weight: bold;
      @include flex;
      margin-top: 40upx;

      &.but_suc {
        @include bg_theme;
        color: #fff;
      }

      &.but_res {
        @include font_theme;
        @include border_theme;
      }

      &.but_canl {
        background-color: #d8d8d8;
        color: #fff;
      }
    }
  }
}
</style>
