<template>
  <div class="protocol">
    <div
      v-html="content"
      :class="['protocolContent',type==1000?'btn-kb':'']"
      style="padding: 10px; background: #ffffff; font-size: 14px;"
    ></div>
<!--    <FooterButton v-if="type==8" @click="approve">同意</FooterButton>-->
    <div v-if="type==11" class="readAndAgree">
      {{ showReadAndAgreeConfirm?'':'已阅读并同意' }}
    </div>
  </div>
</template>

<script>
import { findVisitAgreement } from '../../api/base';
import FooterButton from "@/components/footer_button/button.vue";
import {
  selectInformConsent,
} from "@/api/user.js";
export default {
  name: 'protocol',
  components: {
    FooterButton,
  },
  data() {
    return {
      title: '',
      content: '',
      showReadAndAgreeConfirm:true,
      type: '', //1 用户协议  3 价格与服务协议
    };
  },
  onLoad(options) {
    this.type = options.type;
    if( this.type==11){
      this.selectInformConsent()
    }
    if (this.type == 1) {
      this.title = '平台用户与服务协议';
    } else if (this.type == 2) {
      this.title = '隐私协议';
    } else if (this.type == 3) {
      this.title = '价格与服务协议';
    }
    this.getXy();
  },
  methods: {
    approve(){
      uni.setStorageSync("isAgree",1)
      uni.navigateBack({
        delta: 1
      })
    },
    async selectInformConsent(){
      const data={
        userId:  uni.getStorageSync("userId")
      }
      const res=await selectInformConsent(data)
      if(res.data){
        this.showReadAndAgreeConfirm=res.data.isEmpower!=1
      }
    },
    async getXy() {
      let { data } = await findVisitAgreement({
        agreementType: this.type,
      });
      this.content = data.agreementContent;
    },
  },
};
</script>

<style scoped>
.protocol{
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.protocolContent{
  flex: 1;
  overflow: auto;
}
.readAndAgree{
  text-align: center;
  line-height: 40px;
  font-size: 15px;
}
.btn-kb{
  padding-bottom: 50px !important;
}
</style>
