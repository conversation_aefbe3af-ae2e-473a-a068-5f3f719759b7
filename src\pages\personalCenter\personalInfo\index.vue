<template>
  <view class="page">
    <view class="page-container">
      <view class="personal-info">
        <view class="list">
          <text class="title">头像</text>
          <view class="info">
            <image :src="headimgurl" mode="aspectFill"></image>
          </view>
        </view>
        <view class="list">
          <text class="title">名字</text>
          <view class="info">
            <text>{{ patientInfo.userName }}</text>
          </view>
        </view>
        <view class="list">
          <text class="title">身份证号</text>
          <view class="info">
            <input
              v-model="patientInfo.idNo"
              @blur="cardIdBlur"
              v-if="isEdit"
            />
            <text v-else>{{ patientInfo.idNo }}</text>
          </view>
        </view>
        <view class="list">
          <text class="title">性别</text>
          <view class="info">
            <text>{{ patientInfo.sex }}</text>
          </view>
        </view>
        <view class="list">
          <text class="title">年龄</text>
          <view class="info">
            <text>{{ patientInfo.age }}</text>
          </view>
        </view>
        <view class="input-box">
          <text>手机号</text>
          <input
            :disabled="!isShowEditBtn"
            type="number"
            :value="patientInfo.telNo"
            maxlength="11"
            v-model="patientInfo.telNo"
            placeholder="请输入手机号"
          />
        </view>
      </view>
    </view>
    <view
      style="background-color: #f3f3f6; padding: 10px 20px"
      v-if="isShowEditBtn"
    >
      <view
        style="background-color: #ffe6bf; border-radius: 5px; padding: 7px 10px"
      >
        <text style="color: #a1672d; font-size: 10px"
          >请使用新手机号码收取验证码</text
        >
      </view>
    </view>
    <view class="editor-btn">
      <button @click="navToEditPhone">更换手机号
      </button>
    </view>
  </view>
</template>

<script>
import { Toast } from "@/common/js/pay.js";
import {
  getPatientPersonalCenterInfo,
  updatePatientPersonalCenterInfo,
} from "@/api/user.js";
import regex from "@/common/js/regex.js";
export default {
  data() {
    return {
      headimgurl: "",
      patientInfo: {},
      isEdit: false,
      isShowEditBtn: false,
    };
  },
  onShow() {
    this.headimgurl = uni.getStorageSync("wxInfo").headimgurl;
    this.getInfo();
  },

  methods: {
    async getInfo() {
      let { data } = await getPatientPersonalCenterInfo({
        userId: uni.getStorageSync("userId"),
        appid: uni.getStorageSync("appId"),
      });
      this.patientInfo = data;
      if (data.idNo == "******************") {
        this.isEdit = true;
      }
    },
    // 校验身份证号,并获取用户性别,年龄
    cardIdBlur(e) {
      let cardId = e.detail.value;
      // if(!cardId) return false;
      let form = this.patientInfo;
      if (regex.idNoBlur(cardId)) {
        if (regex.disCriCard(cardId, "sex") == 2) {
          form.sex = "女";
        } else {
          form.sex = "男";
        }
        form.sexCode = regex.disCriCard(cardId, "sex");
        form.age = regex.disCriCard(cardId, "age");
        return true;
      }
      return false;
    },
    navToEditPhone() {
      uni.navigateTo({
        url: '/pages/personalCenter/personalInfo/editorTelNo/index'
      });
    },
  },
};
</script>

<style scoped lang="scss">
.page-container {
  box-sizing: border-box;
  overflow-y: auto;
}

/* 用户信息显示 */
.personal-info {
  padding: 32rpx;
  box-sizing: border-box;
  background: #ffffff;
}

.list {
  width: 100%;
  height: 92rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 2rpx solid #ebebeb;
}
.list:last-child {
  border-bottom: none;
}

.list .title {
  font-size: 28rpx;
  font-weight: 400;
  color: #333333;
}

.list .info {
  font-size: 28rpx;
  font-weight: 400;
  color: #999999;
}

.info {
  input {
    text-align: right;
  }
}

.info image {
  width: 68rpx;
  height: 68rpx;
  border-radius: 50%;
}

.flex-right {
  display: flex;
  align-items: center;
  justify-content: right;
}
/* 确认修改用户信息按钮*/
.editor-btn {
  width: 100%;
  height: 104upx;
  padding: 0 32upx;
  background: #fff;
  box-sizing: border-box;
  position: fixed;
  left: 0;
  bottom: 0;
  @include flex;

  button {
    @include flex;
    width: 100%;
    height: 84rpx;
    @include bg_theme;
    border-radius: 42upx;
    font-size: 32upx;
    color: #fff;
  }
}
.input-box {
  width: 100%;
  display: flex;
  height: 92rpx;
  border-radius: 4rpx;
  box-sizing: border-box;
  line-height: 92rpx;
  border-bottom: 1rpx solid #ebebeb;
}

.input-box text {
  display: inline-block;
  width: 112rpx;
  flex: none;
  font-size: 28rpx;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
}

.input-box .icon {
  width: 40rpx;
}

.input-box input {
  flex: 1;
  height: 100%;
  line-height: 100%;
  padding-left: 40rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: rgba(102, 102, 102, 1);
}
</style>
