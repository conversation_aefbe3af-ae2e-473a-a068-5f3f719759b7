import http from '../common/request/request';

// 查询可以开票的订单分页
export function getCanGiveInvoiceOrderPage(param) {
  return http({
    url: 'order/invoiceRecord/getCanGiveInvoiceOrderPage',
    param,
    method: 'post',
  });
}

// 查询我的发票分页
export function getMyInvoiceRecordPage(param) {
  return http({
    url: 'order/invoiceRecord/getMyInvoiceRecordPage',
    param,
    method: 'post',
  });
}

// 查询发票详情
export function getInvoiceDetail(irId) {
  return http({
    url: 'order/invoiceRecord/getInvoiceDetail',
    param: { irId },
    method: 'post',
  });
}

// 提交发票
export function submitInvoiceInfo(param) {
  param.patientUserId = uni.getStorageSync('userId');
  return http({
    url: 'order/invoiceRecord/submitInvoiceInfo',
    param,
    method: 'post',
  });
}

// 修改发票信息
export function editInvoiceInfo(param) {
  param.patientUserId = uni.getStorageSync('userId');
  return http({
    url: 'order/invoiceRecord/editInvoiceInfo',
    param,
    method: 'post',
  });
}
