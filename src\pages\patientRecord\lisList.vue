<template>
  <!-- 我的检验单 -->
  <view class="lis_list">
    <!-- 医生 -->
    <DOCINFO :info="detail" />

    <!-- 就诊 -->
    <view class="cont">
      <view class="cont_item">
        <text class="label">就诊人</text>
        <text>{{ detail.patientName }}</text>
      </view>

      <view class="cont_item">
        <text class="label">主诉</text>
        <text>{{ detail.recordsTitle }}</text>
      </view>

      <view class="cont_item">
        <text class="label">问诊时间</text>
        <text>{{ detail.signTime }}</text>
      </view>
    </view>

    <!-- 列表 -->
    <view class="list">
      <!-- 单个 -->
      <view
        class="list_item"
        v-for="item in list"
        :key="item.ppiId"
        @click="toDetail(item)"
      >
        <!-- 状态 -->
        <view class="item_title">
          <text>检验编号：{{ item.ppiCode }}</text>
          <text class="status">{{ item.statusName }}</text>
        </view>
        <!-- 诊断 -->
        <view class="item_diag"> 诊断：{{ item.ppiDiag }} </view>
        <!-- 检查项 -->
        <view class="item_project">
          <view class="project_name">检验项</view>

          <block v-if="item.lisListDVO">
            <!-- 列表 -->
            <view
              class="project_item"
              v-for="(obj, index) in item.lisListDVO"
              :key="index"
            >
              <text
                >{{ index + 1 }}. {{ obj.lisItemName }} -
                {{ obj.sampleTypeName }} - {{ obj.sampleBodyName }}</text
              >
              <text>￥{{ (obj.cost || 0).toFixed(2) }}</text>
            </view>
          </block>
        </view>
        <!-- 合计 -->
        <view class="project_price">
          <view
            >合计：<text>￥{{ (item.cost || 0).toFixed(2) }}</text></view
          >
          <view v-if="item.status > 1"
            >实际支付：<text>￥{{ (item.payCost || 0).toFixed(2) }}</text></view
          >
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getProLisList } from '@/api/base.js';

import DOCINFO from './com/doc.vue';

export default {
  name: 'LisList',
  components: {
    DOCINFO,
  },
  data() {
    return {
      regId: '',
      detail: {},
      list: [],
    };
  },
  onLoad(option) {
    this.regId = option.regId;
  },
  onShow() {
    this.getInit();
  },
  methods: {
    async getInit() {
      let { data } = await getProLisList({
        regId: this.regId,
      });
      this.list = data.lisListMVO;
      this.detail = data;
    },
    // 去详情
    toDetail(item) {
      let url;
      let { paymentType, status, pliId } = item;
      // 不存在支付方式
      if (!paymentType && status == 1) {
        url = '/pages/inspect/lisOrder?id=' + pliId;
        // 待支付 且 线上支付
      } else if (status == 1 && paymentType == 2) {
        url = '/pages/inspect/pay/lis?id=' + pliId;
      } else {
        url = '/pages/inspect/lisDetails?id=' + pliId;
      }
      uni.navigateTo({ url });
    },
  },
};
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}
page {
  background-color: #f5f5f5;
}
.lis_list {
  padding: 24rpx 32rpx;

  .cont {
    margin-top: 24rpx;
    background-color: #fff;
    border-radius: 8rpx;
    padding: 10rpx 24rpx;

    &_item {
      @include flex(left);
      height: 60rpx;

      text {
        color: #333;
        font-size: 28rpx;
      }

      .label {
        flex: none;
        width: 180rpx;
      }
    }
  }

  .list {
    margin-top: 24rpx;

    .list_item {
      background-color: #fff;
      border-radius: 8rpx;
      margin-bottom: 24rpx;
      padding: 0 32rpx;
      font-size: 28rpx;
      color: #333;

      .item_title {
        height: 88rpx;
        @include flex(lr);
        font-weight: bold;

        .status {
          color: #ff3b30;
          font-weight: normal;
        }
      }

      .item_diag {
        height: 88rpx;
        @include flex(left);
        border-bottom: 1px solid #ebebeb;
        border-top: 1px solid #ebebeb;
      }

      .item_project {
        padding-top: 10rpx;

        .project_name {
          font-weight: bold;
          line-height: 50rpx;
        }

        .project_item {
          min-height: 60rpx;
          @include flex(lr);
        }
      }

      .project_price {
        line-height: 80rpx;
        @include flex(right);

        view {
          margin-left: 24rpx;
        }

        text {
          color: red;
        }
      }
    }
  }
}
</style>
