<template>
  <view class="page">
    <view class="page-container">
      <view class="meterial_box">
        <!-- 头部 -->
        <Docter :infoDetail="docInfo" />

        <!-- 问诊信息 -->
        <view class="info">
          <view class="info_item">
            <text class="info_item_label">就诊人</text>
            <text>{{ appointSignInfo.patientName }}</text>
          </view>
          <view class="info_item">
            <text class="info_item_label">问诊类型</text>
            <text>{{ info.visitTypeName }}问诊</text>
          </view>
          <view class="info_item">
            <text class="info_item_label">预约时间</text>
            <text>{{ appointSignInfo.extendInfo.appointTime }}</text>
          </view>
        </view>

        <!-- 费用明细 -->
        <view class="details">
          <view class="details_item">
            <text class="details_item_title">费用明细（自费）</text>
            <view class="details_item_box">
              <view v-for="(item,index) in pay" wx:for-item="item" :key="index">
                <text>{{ item.priceDetailName }}</text>
                <text>{{ item.totalPay }}</text>
              </view>
            </view>
            <view class="details_item_money"
              >合计：<text>¥{{ sum }}</text></view
            >
          </view>
          <view class="detail_line"></view>
<!--          <view class="details_item">-->
<!--            <text class="details_item_title">医保担负明细</text>-->
<!--            <view class="details_item_box">-->
<!--              <view>-->
<!--                <text>医保统筹支付</text>-->
<!--                <text>0</text>-->
<!--              </view>-->
<!--              <view>-->
<!--                <text>医保账户支付</text>-->
<!--                <text>0</text>-->
<!--              </view>-->
<!--            </view>-->
<!--          </view>-->
          <view class="details_item">
            <text class="details_item_title">优惠券抵扣明细</text>
            <view class="details_item_box">
              <view>
                <text>新用户立减</text>
                <text>0</text>
              </view>
            </view>
            <view class="details_item_money"
              >实际支付：<text>¥{{ sum }}</text></view
            >
          </view>
        </view>
        <!-- 病情描述文字信息 -->
        <view class="info">
          <view class="info_item">
            <text class="info_item_label">标题</text>
            <text>{{ info.recordsTitle }}</text>
          </view>
          <view class="info_item">
            <text class="info_item_label">首诊诊断</text>
            <text>{{ info.diagnosisDisease }}</text>
          </view>
          <view class="info_item">
            <text class="info_item_label">首诊医院</text>
            <text>{{ info.firstVisitHos }}</text>
          </view>
          <view class="info_item">
            <text class="info_item_label">患病时长</text>
            <text>{{ info.sickTime }}{{ info.timeUnit }}</text>
          </view>
          <view class="info_item">
            <text class="info_item_label">病情描述</text>
            <text>{{ info.diseaseDescription }}</text>
          </view>
          <view class="info_item">
            <text class="info_item_label">期望帮助</text>
            <text>{{ info.expectHelp }}</text>
          </view>
        </view>

        <!-- 病情，其他照片 -->
        <view class="condition" v-if="diseaseImgOne.length != 0">
          <view class="condition_title"
            >病情照片<text>(仅限本人和接诊医生可看）</text></view
          >
          <view class="condition_box">
            <view class="condition_box_item" v-for="(item,index) in diseaseImgOne" :key="index">
              <image :src="item" mode="" @click="previewImg(item)"></image>
            </view>
          </view>
        </view>
        <view class="condition" v-if="medicalImgOne.length != 0 && false">
          <view class="condition_title"
            >其他照片<text>(仅限本人和接诊医生可看）</text></view
          >
          <view class="condition_box">
            <view
              class="condition_box_item"
              v-for="(item, index) in medicalImgOne"
              :key="index"
            >
              <image
                :src="item"
                @click="previewImg(item)"
                mode="aspectFill"
              ></image>
            </view>
          </view>
        </view>
      </view>
    </view>

    <view class="sign_box">
      <view class="sign_msg"
        >{{
          appointSignInfo.extendInfo.appointTime
        }}进行签到，若您预期未签到本次预约将作废，您需要重新预约。</view
      >
      <view class="sign_btn_box">
        <view class="sign_btn_1" @click="cancelAppoint">退预约</view>
        <view class="sign_btn_2" @click="confirmSign">立即签到</view>
      </view>
    </view>

    <propCenter
      v-if="cancelBox"
      @cancelPropCenter="setHide"
      @confirmPropCenter="setRegisterCancel"
      :type="2"
    >
      您确定要退预约吗？
    </propCenter>
  </view>
</template>

<script>
import { Toast } from "@/common/js/pay.js";
import propCenter from "@/components/propCenter/propCenter.vue";
import Docter from "@/components/doctor_header/doctor_header.vue";
import { getPatientRecords } from "@/api/chat.js";
import { getRegPayInfo, findDoctorByID } from "@/api/base.js";
import { cancelAppointRegister, signConfirm } from "@/api/appoint.js";
import myJsTools from "@/common/js/myJsTools.js";
export default {
  components: {
    propCenter,
    Docter,
  },
  data() {
    return {
      appointSignInfo: {},
      docInfo: {},
      pay: [],
      sum: 0,
      info: {},
      diseaseImgOne: [],
      medicalImgOne: [],
      cancelBox: false,
    };
  },
  onLoad(option) {
    this.appointSignInfo = JSON.parse(option.appointSignInfo);
    this.getDocInfo();
    this.getPay();
    this.getDrag();
  },
  methods: {
    // 预览图片
    previewImg(img) {
      myJsTools.previewImg(img);
    },
    // 医生信息
    async getDocInfo() {
      let { docId, deptId } = this.appointSignInfo;
      let { data } = await findDoctorByID({
        docId,
        deptId,
      });
      this.docInfo = data;
      if (data.docImg) {
        myJsTools.downAndSaveImg(data.docImg, (url) => {
          this.docInfo.docImg = url;
        });
      }
    },
    // 费用明细
    async getPay() {
      let res = await getRegPayInfo({
        regId: this.appointSignInfo.regId,
      });
      let totalPrice = 0;
      this.pay = res.data;
      for (let i = 0; i < res.data.length; i++) {
        totalPrice += parseFloat(res.data[i].totalPay);
      }
      this.sum = totalPrice.toFixed(2);
    },
    // 病历信息
    async getDrag() {
      let res = await getPatientRecords({
        regId: this.appointSignInfo.regId,
      });
      this.info = res.data;
      if (res.data.diseaseImg != "") {
        let diseaseImg = JSON.parse(res.data.diseaseImg);
        diseaseImg.forEach((element) => {
          myJsTools.downAndSaveImg(element, (url) => {
            let arr = this.diseaseImgOne;
            arr.push(url);
            this.diseaseImgOne = arr;
          });
        });
      }
      if (res.data.medicalImg != "") {
        let medicalImg = JSON.parse(res.data.medicalImg);
        medicalImg.forEach((element) => {
          myJsTools.downAndSaveImg(element, (url) => {
            let arr = this.medicalImgOne;
            arr.push(url);
            this.medicalImgOne = arr;
          });
        });
      }
    },
    // 退预约
    cancelAppoint() {
      this.cancelBox = true;
    },
    // 退预约取消
    setHide() {
      this.cancelBox = false;
    },
    // 退预约确定
    async setRegisterCancel() {
      this.cancelBox = false;
      let ghId = this.appointSignInfo.extendInfo.ghId;
      await cancelAppointRegister({
        ghId,
      });
      Toast("取消预约成功");
      uni.navigateBack({
        delta: 1,
      });
    },
    // 立即签到
    async confirmSign() {
      const { patientId, docId, extendInfo } = this.appointSignInfo;
      const ghId = extendInfo.ghId;
      let res = await signConfirm({
        ghId,
      });
      let para = {
        patientId,
        docId,
      };
      this.$store.commit("setEmptyList", para);
      Toast("签到成功");
      uni.switchTab({
        url: "/pages/chatList/index",
      });
    },
  },
};
</script>

<style scoped lang="scss">
.page-container {
  box-sizing: border-box;
  padding-bottom: 290rpx;

  .doctor_box_top {
    padding: 28upx 24upx;
    border-radius: 16upx;
  }
}

/* 上侧医生信息 */
.meterial_box {
  width: 92%;
  margin: 0 auto;
  padding-top: 24rpx;
  padding-bottom: 30rpx;
}

.menu {
  padding: 28rpx 24upx;
  overflow: hidden;
  zoom: 1;
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 1);
  border-radius: 16rpx;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.05);
}

/* 问诊信息 */
.info,
.details,
.condition,
.foot {
  padding-left: 4%;
  background: rgba(255, 255, 255, 1);
  border-radius: 16rpx;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.05);
  margin-top: 24rpx;
}

.info_item {
  padding: 26rpx 0;
  font-size: 28rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  line-height: 40rpx;
  border-bottom: 2rpx solid #f5f5f5;
  display: flex;
}

.info_item_label {
  display: block;
  width: 33%;
}

.info_item .time {
  font-weight: 600;
  @include font_theme;
}

/* 费用明细 */

.details {
  overflow: hidden;
  zoom: 1;
  padding: 26rpx 0 14rpx 4%;
  margin-bottom: 24rpx;
}

.details_item {
  overflow: hidden;
  zoom: 1;
}

.details_item_title {
  font-size: 28rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 600;
  color: rgba(51, 51, 51, 1);
  line-height: 40rpx;
  margin-bottom: 32rpx;
  display: block;
}

.details_item_box view {
  padding-right: 4%;
  display: flex;
  justify-content: space-between;
  margin-bottom: 40rpx;
}

.details_item_box view text {
  font-size: 28rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: rgba(153, 153, 153, 1);
  line-height: 28rpx;
}

.details_item_box view text:last-child {
  color: rgba(51, 51, 51, 1);
}

.details_item_money {
  font-size: 28rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 600;
  color: rgba(51, 51, 51, 1);
  padding-right: 4%;
  float: right;
  margin-bottom: 40rpx;
}

.details_item_money text {
  color: #ff0707;
}

.detail_line {
  height: 2rpx;
  background: rgba(235, 235, 235, 1);
  margin-bottom: 38rpx;
}

/* 病情,其他照片 */

.condition {
  padding: 26rpx 4%;
}

.condition_title {
  font-size: 28rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  line-height: 40rpx;
  margin-bottom: 20rpx;
}

.condition_title text {
  color: rgba(153, 153, 153, 1);
  margin-left: 20rpx;
}

.condition_box {
  overflow: hidden;
  zoom: 1;
  display: flex;
  flex-wrap: wrap;
  width: 100%;
}

.condition_box_item {
  position: relative;
  width: 32%;
  margin-right: 2%;
}

.condition_box_item:nth-child(3n) {
  margin-right: 0;
}

.condition_box_item:before {
  content: "";
  display: block;
  padding-top: 100%;
}

.condition_box_item image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.foot {
  overflow: hidden;
  zoom: 1;
}

.foot_item {
  padding: 26rpx 4%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.foot_item image {
  width: 44rpx;
  height: 44rpx;
}

.foot_line {
  height: 1px;
  background: rgba(235, 235, 235, 1);
}

/* 底部信息 */
.sign_box {
  width: 92%;
  position: fixed;
  bottom: 0;
  padding: 0 4%;
  background: #f5f5f5;
  z-index: 1;
  // overflow: hidden;
  zoom: 1;
  padding-bottom: 32rpx;
  padding-top: 24rpx;
}

.sign_msg {
  margin-bottom: 40rpx;
  white-space: space;
  font-size: 28rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  line-height: 40rpx;
}

.sign_btn_box {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sign_btn_box view {
  width: 48%;
  background: rgba(255, 255, 255, 1);
  border-radius: 50rpx;
  border: 1px solid rgba(236, 236, 236, 1);
  font-size: 32rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 600;
  color: rgba(51, 51, 51, 1);
  line-height: 44rpx;
  padding: 28rpx 0;
  text-align: center;
}

.sign_btn_box .sign_btn_2 {
  @include bg_theme;
  color: rgba(255, 255, 255, 1);
}
</style>
