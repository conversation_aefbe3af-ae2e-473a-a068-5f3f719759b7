/*
  Author: 王可 (<EMAIL>)
  map.js (c) 2021
  Desc: description
  Created:  2021/11/2上午9:55:09
  Modified: 2021/11/2上午10:22:48
*/

// 引入jsonp
import { jsonp } from 'vue-jsonp';

// 腾讯地图key
const key = require('@/common/request/config.js').mapKey;

// 根据ip 获取经纬度
const getIpUrl = 'https://apis.map.qq.com/ws/location/v1/ip';

// 经纬度逆解析
const getCity = 'https://apis.map.qq.com/ws/geocoder/v1/';

/**
 * 根据当前ip 获取城市以及经纬度信息
 * @returns {Object} 城市名，经纬度
 * */

export const getCityById = async () => {
  let res = await jsonp(getIpUrl, {
    key,
    output: 'jsonp',
  });
  console.log(res);
  if (res.status != 0) {

    return Promise.resolve({
      lat: 39.908802,
      lng: 116.397502,
      city: '北京市',
      province: '北京市'
    });
  }
  const { lat, lng } = res.result.location;
  const { city, province } = res.result.ad_info;
  let info = {
    lat,
    lng,
    city,
    province,
  };
  return Promise.resolve(info);
};

/**
 * 根据经纬度获取省市区
 * @param {String} 经纬度逗号拼接
 * @returns {Object} 省市区
 */
export const getCityByLocation = async (location) => {
  let {
    result: {
      ad_info: { province, city, district },
    },
  } = await jsonp(getCity, {
    location,
    key,
    output: 'jsonp',
  });

  return Promise.resolve({ province, city, district });
};
