<template>
  <!-- 左侧助手 -->
  <div class="guid_user">
    <!-- 文本内容 -->
    <view class="user_cont">
      <text class="user_cont_text">
        <slot></slot>
      </text>
    </view>
    <!-- 头像 -->
    <image
      class="user_head"
      :src="url || '/static/images/guidance/tx.png'"
      mode="aspectFill"
    />
    <!-- 文本内容 -->
    <!-- <view class="user_cont">
      <text class="user_cont_text">
        <slot></slot>
      </text>
    </view> -->
  </div>
</template>

<script>
export default {
  name: "UserText",
  data() {
    return {
      url: "",
    };
  },
  created() {
    this.url = uni.getStorageSync("wxInfo").headimgurl;
  },
};
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}
.guid_user {
  @include flex(right);
  justify-content: flex-end;
  align-items: flex-start;
  // flex-direction: row-reverse;
  padding-bottom: 32rpx;
  .user_head {
    width: 64rpx;
    height: 64rpx;
    margin-left: 16px;
    border-radius: 50%;
    flex: none;
  }

  .user_cont {
    max-width: 500rpx;
    padding: 20rpx 24rpx;
    font-size: 28rpx;
    background: #d6f1ff;
    color: #333;
    border-radius: 32rpx 8rpx 32rpx 32rpx;
    border: 1px solid rgba(0, 0, 0, 0.1);

    .user_cont_text {
      word-break: break-all;
    }
  }
}
</style>
