<template>
  <div class="doc">
    <div class="item">
      <input
        type="tel"
        placeholder-class="pls"
        placeholder="请输入手机号"
        v-model="telNo"
        maxlength="11"
      />
    </div>

    <div class="item">
      <input
        type="text"
        placeholder-class="pls"
        placeholder="请输入验证码"
        v-model="code"
        maxlength="6"
      />
      <span :class="num < 60 ? 'act' : ''" @click="sendPhoneCode">{{
        msg
      }}</span>
    </div>

    <div class="footer">
      <button @click="send">注册</button>
    </div>
  </div>
</template>

<script>
import { sendCaptcha } from '@/api/user.js';
import { agentUserInviteDocRegister } from '@/api/base';

import CONFIG from '@/common/request/config';

import { Toast } from '@/common/js/pay.js';

let timer;

export default {
  name: 'DocSign',
  data() {
    return {
      supplierId: '',
      userId: '',
      telNo: '',
      code: '',
      msg: '获取验证码',
      num: 60,
    };
  },
  onLoad(v) {
    let { supplierId, userId } = v;
    this.supplierId = supplierId;
    this.userId = userId;
  },
  methods: {
    // 单独发送验证码
    async sendPhoneCode() {
      let telNo = this.telNo;
      if (this.num < 60) return;
      if (!telNo.length) {
        Toast('请输入手机号');
        return;
      }
      if (telNo.length != 11) {
        Toast('手机号位数错误');
        return;
      }
      try {
        await sendCaptcha({ telNo });
        timer = setInterval(() => {
          this.num--;
          if (this.num <= 0) {
            clearInterval(timer);
            this.msg = '获取验证码';
            this.num = 60;
          } else {
            this.msg = this.num + 's后重新获取';
          }
        }, 1000);
      } catch (error) {}
    },
    // 发送
    async send() {
      if (!this.telNo) {
        Toast('请填写手机号');
        return;
      }
      if (!this.code) {
        Toast('请填写验证码');
        return;
      }

      let obj = {
        userId: this.userId,
        telNo: this.telNo,
        supplierId: this.supplierId,
        captcha: this.code,
      };

      await agentUserInviteDocRegister(obj);

      let url = CONFIG.downLoadAppUrl;

      window.location.replace(url);
    },
  },
};
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}
page {
  background-color: #fff;
}

.pls {
  font-size: 28rpx;
}

.doc {
  padding: 100rpx 75rpx 0;

  .item {
    height: 90rpx;
    border-bottom: 1px solid #f5f5f5;
    font-size: 28rpx;
    @include flex(lr);
    margin-bottom: 20rpx;

    input {
      flex: 1;
    }

    span {
      @include font_theme;

      &.act {
        color: #999;
      }
    }
  }

  .footer {
    margin-top: 500rpx;

    button {
      height: 84rpx;
      border-radius: 42rpx;
      @include bg_theme;
      color: #fff;
      font-size: 32rpx;
    }
  }
}
</style>
