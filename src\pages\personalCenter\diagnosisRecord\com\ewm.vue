<template>
  <view class="ewm">
    <QRCODE
      ref="qrcode"
      :val="code"
      :size="300"
      background="#ffffff"
      foreground="#000000"
      pdground="#000000"
      icon="/static/images/logo-img.png"
      :iconSize="45"
      onval
      loadMake
    />

    <text>{{ code }}</text>
  </view>
</template>

<script>
import QRCODE from '@/components/tki-qrcode/tki-qrcode.vue';

export default {
  components: {
    QRCODE,
  },
  props: ['code'],
};
</script>

<style lang="scss" scoped>
.ewm {
  background-color: #fff;
  border-radius: 8rpx;
  padding: 40rpx 32rpx 32rpx;
  @include flex;
  flex-direction: column;

  text {
    font-size: 32rpx;
    margin-top: 20rpx;
  }
}
</style>
