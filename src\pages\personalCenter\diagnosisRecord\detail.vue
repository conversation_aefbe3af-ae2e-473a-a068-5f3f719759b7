<template>
  <!-- 挂号详情 -->
  <view class="detail">
    <!-- 状态 -->
    <STATUS
      :status="detail.status"
      :time="detail.appointSignTime"
      :queueNumber="detail.queueNumber"
      :frontNum="num"
      :detail="detail"
    />

    <view class="cont">
      <!-- 摘要 -->
      <INFO :detail="detail" />

      <!-- 时间线 -->
      <TIMELINE v-if="detail.status > 1" :list="timeList" />

      <!-- 病情描述 -->
      <DESCINFO v-if="desc.regId && detail.isOffLine == 0" :item="desc" />
      <FastInfo v-if="showItem.isPre == 0 && detail.bussType ==7" :item="quickDetail"/>
      <!-- 链接 -->
      <view class="link">
        <!-- 处方 -->
        <view class="link_item" v-if="showItem.isElec == 1" @click="goEpr">
          <text>电子病历</text>
          <uni-icons type="arrowright" color="#666" />
        </view>
        <!-- 处方 -->
<!--        <view class="link_item" v-if="showItem.isPre == 1" @click="goPre">-->
<!--          <text>处方信息</text>-->
<!--          <uni-icons type="arrowright" color="#666" />-->
<!--        </view>-->
        <view class="link_item" v-if="showItem.isPacs == 1" @click="toPacs">
          <text>检查信息</text>
          <uni-icons type="arrowright" color="#666" />
        </view>
        <view class="link_item" v-if="showItem.isLis == 1" @click="toLis">
          <text>检验信息</text>
          <uni-icons type="arrowright" color="#666" />
        </view>
        <view class="link_item" v-if="showItem.isLb == 1" @click="toLb">
          <text>量表</text>
          <uni-icons type="arrowright" color="#666" />
        </view>
      </view>

      <!-- 二维码 -->
      <EWM :code="detail.regCode" v-if="detail.isOffLine == 1" />
    </view>

    <!-- 底部 -->
    <view class="footer" v-if="detail.status == 2">
      <!-- 线上 -->
      <block v-if="detail.isOffLine == 0">
        <button @click="show = true">退预约</button>
        <button class="sign" @click="sign">立即签到</button>
      </block>

      <block v-else>
        <button class="cancel" @click="show = true">退预约</button>
      </block>
    </view>

    <propCenter
      v-if="show"
      @cancelPropCenter="show = false"
      @confirmPropCenter="cancalAppoint"
      :type="2"
    >
      您确定要退预约吗？
    </propCenter>

    <FOOTER @click="toPay" :isLeft="true" v-if="detail.status == 1" @leftClick="leftClick">
      <template #isLeft>
        取消问诊
      </template>
      去支付
    </FOOTER>
    <FOOTER v-if="detail.status == 4" :isDef="false" :isCw="true"  @click="cancleIs">
      取消问诊
    </FOOTER>
  </view>
</template>

<script>
import { selectRegisterDetail } from '@/api/register';
import { getPatientRecords } from '@/api/chat';
import { getIsPre } from '@/api/patient';
import { signConfirm, cancelAppointRegister } from '@/api/appoint';
import { pregOrderDetailNew } from '@/api/order';
import {
  myRegQueue,
  queryRegisterPayStatus,
  getRegisterPayInfo,
} from '@/api/base';
import propCenter from '@/components/propCenter/propCenter.vue';
import { wxPay } from '@/common/js/pay';

import STATUS from './com/status.vue';
import INFO from './com/info.vue';
import TIMELINE from './com/timeLine.vue';
import DESCINFO from './com/desc.vue';
import EWM from './com/ewm.vue';
import FOOTER from '@/components/footer_button/button.vue';
import FastInfo from "./com/fast.vue";
import { Toast } from '@/common/js/pay.js';
let num = 3;

import Pay from '@/modules/pay';
import {cancelRegister, cancelWaitOrder, paymentBusinessrefund, queryQuickPrescription} from "../../../api/base";
import {basicgetPatientChatList} from "../../../api/chat";
import {getGhCanPay, getPatientReceiveStatus} from "../../../api/user";
let PAY;

export default {
  name: 'Detail',
  components: {
    STATUS,
    INFO,
    TIMELINE,
    DESCINFO,
    EWM,
    propCenter,
    FOOTER,
    FastInfo
  },
  data() {
    return {
      regId: '',
      detail: {},
      desc: {},
      // 显示按钮
      showItem: {},
      timeList: [],
      num: '',
      show: false,
      orderNo: '',
      order: {},
      ghId: '',
      flag:'',
      quickDetail:{
        quickPrescriptionDetailVOS:[],
        quickPrescriptionDiags:[]
      }
    };
  },
  async onLoad(opt) {
    if (!opt.id) return;
    this.regId = opt.id;
    this.flag=opt.flag
    if(this.flag==='ksxf'){
      uni.setNavigationBarTitle({
        title: '快速续方',
      });
    }
   await this.getDetail();
    this.getDesc();
   await this.getPre();
    if(this.showItem.isPre == 0 && this.detail.bussType ==7) {
      await this.queryQuickPrescription()
    }
  },
  methods: {
   async queryQuickPrescription(){
      const res = await queryQuickPrescription({
        regId: this.regId,
      });
      this.quickDetail = res.data;
    },
    leftClick(){
      const data={
        regId: this.regId,
        ghId: this.detail.ghId,
      }
      cancelWaitOrder(data).then(res=>{
        uni.showToast({
          title: '取消成功！',
          icon: 'none',
        })
        setTimeout(()=>{
          uni.navigateBack({
            delta: 1
          })
        },1000)
      })
    },
   async cancleIs(){
     let info = await pregOrderDetailNew({
       orderNo: this.orderNo,
     });
     if(info.data.statusName=='待接诊'){
       const data={
         regId: this.regId,
         ghId: this.detail.ghId,
       }
       paymentBusinessrefund(data).then(v=>{
         uni.showToast({
           title: '取消成功！',
           icon: 'none',
         })
         setTimeout(()=>{
           uni.navigateBack({
             delta: 1
           })
         },1000)
       })
     }
     if(info.data.statusName=='接诊中'){
       uni.showToast({
         title: '医生已接单',
         icon: 'none',
       })
       let patientId= this.desc.patientId
       let docId= this.desc.docId
       let chatList = this.$store.getters.getChatList;
       let list = this.$store.getters.getChatListDoc;
       let id = patientId + ',' + docId;
       let item;
       // 如果是数组
       this.$store.commit('SET_CHATLIST', []);//清空(解决只显示该患者与当前医生的消息)
       if (Array.isArray(chatList)) {
         if (list.length) {
           for (let i = 0; i < list.length; i++) {
             let v = list[i];
             if (v.id == id) {
               item = v;
               break;
             }
           }
         } else {
           await this.$store.dispatch('getChatListId', {
             chatId: id,
           });
           item = this.$store.getters.getChatList;
         }
       } else {
         await this.$store.dispatch('getChatListId', {
           chatId: id,
         });
         item = this.$store.getters.getChatList;
       }
       let basicgetPatient = await basicgetPatientChatList({
         page:1,
         limit:50,
         paramLists: [
           {
             patientId,
             docId,
           },
         ],
       });
       item.dataVal = basicgetPatient.data.rows[0];
       let pars = {
         docId,
         docName:info.data.docName,
         patientId,
         userId: uni.getStorageSync('userId'),
       };
       item = { ...item, ...pars };
       console.log('item',item)
       this.$store.commit('SET_CHATLIST', item);
       uni.setStorageSync('chatItem', item.dataVal);
       let param = {
         docId,
       };
       let url = '/pages/chatList/chatDetail?param=' + JSON.stringify(param);
       setTimeout(()=>{
         uni.navigateTo({
           url: url
         });
       },500)
     }
    },
    // 获取支付方式
    async getPayList(hosId) {
      PAY = new Pay(hosId);
    },
    // 获取排队号
    async getNum() {
      let { data } = await myRegQueue(this.regId);
      this.num = data;
    },
    // 获取详情
    async getDetail() {
      let { data } = await selectRegisterDetail(this.regId);
      this.detail = data;
      let arr = [
        {
          name: '结束问诊',
          time: data.endTime || '',
        },
        {
          name: '医生接诊',
          time: data.receiveTime || '',
        },
        {
          name: '患者退费',
          time: data.returnTime || '',
        },
        {
          name: '患者签到',
          time: data.isAppoint == 1 ? data.signTime || '' : '',
        },
        {
          name: '患者支付',
          time: data.payTime || '',
        },
      ];
      this.timeList = arr;
      this.orderNo = data.orderNo;
      this.num = data.frontNum;
      if (data.status == 1) {
        if (data.orderNo) {
          this.getOrder();
        } else {
          this.getPayList(data.hosId);
        }
      }

      // this.getNum();
    },
    // 获取病情描述
    async getDesc() {
      let { data } = await getPatientRecords({
        regId: this.regId,
      });
      if (!data) return;
      let { medicalImg, diseaseImg } = data;
      data.diseaseImg = diseaseImg ? JSON.parse(diseaseImg) : [];
      data.medicalImg = medicalImg ? JSON.parse(medicalImg) : [];
      this.desc = data;
    },
    // 查询是否存在处方
    async getPre() {
      let { data } = await getIsPre({
        regId: this.regId,
        useType: 2,
      });
      this.showItem = data;
    },
    // 跳转本次处方
    goPre() {
      uni.navigateTo({
        url:
          '/pages/patientRecord/prescriptionList?regId=' +
          this.regId +
          '&docId=' +
          this.desc.docId,
      });
    },
    goEpr() {
      uni.navigateTo({
        url: './epr?regId=' + this.regId,
      });
    },
    // 本次检查
    toPacs() {
      uni.navigateTo({
        url: '/pages/patientRecord/inspectList?regId=' + this.regId,
      });
    },
    // 本次检验
    toLis() {
      uni.navigateTo({
        url: '/pages/patientRecord/lisList?regId=' + this.regId,
      });
    },
    // 去量表
    toLb() {
      let param = {
        docId: this.desc.docId,
        patientId: this.desc.patientId,
      };
      uni.navigateTo({
        url:
          '/pages/patientRecord/interviewRecord?param=' + JSON.stringify(param),
      });
    },
    // 签到
    async sign() {
      await signConfirm({
        regId: this.regId,
        ghId: this.detail.ghId,
      });
      const { docId, patientId } = this.desc;
      let para = {
        patientId,
        docId,
      };
      this.$store.commit('setEmptyList', para);
      this.getDetail();
    },
    // 退预约
    async cancalAppoint() {
      await cancelAppointRegister({
        regId: this.regId,
        ghId: this.detail.ghId,
      });
      this.show = false;
      this.getDetail();
    },
    // 获取订单信息
    async getOrder() {
      let { data } = await pregOrderDetailNew({
        orderNo: this.orderNo,
      });
      this.order = data;
    },
    // 去支付
   async toPay() {
     let obj = {
       docId: this.detail.docId,
       patientId:this.detail.patientId,
       visitTypeCode: 1,
     };
     let res = await getGhCanPay(obj);
      if(res.data==0){
        Toast("当前在此医生有待接诊/接诊中的挂号单，不能重复挂号");
        return
      }
     console.log(PAY)
      if (PAY) {
        this.toOrderPay();
        return;
      }
      let { payStr, payType, orderMoney, ghId } = this.order;
      this.ghId = ghId;
      payStr = JSON.parse(payStr);
      if (payType == 1) {
        this.wxPay(payStr);
        return;
      }
      if (payType == 2) {
        uni.navigateTo({
          url:
            '/pages/pay/pay?price=' +
            orderMoney +
            '&ghId=' +
            ghId +
            '&url=' +
            btoa(payStr.url),
        });
        return;
      }
      Toast('缺少支付类型参数');
    },
    // 未生成订单支付
    async toOrderPay() {
      let { ghId, selfPay } = this.detail;
      this.ghId = ghId;
      let money = selfPay;

      let { index, item } = await PAY.selePay(money);

      uni.showLoading({
        mask: true,
      });

      let para = {
        callId: item.appid,
        ghId: this.ghId,
        openid: uni.getStorageSync('wxInfo').openId,
        payType: index,
      };

      try {
        let res = await getRegisterPayInfo(para);

        uni.hideLoading();
        // 无需支付，返回成功，直接跳转
        if (res.data && res.data.success == '1') {
          this.getDetail();
          return;
        }

        let info = res.data;

        // 微信支付
        if (index == 1) {
          this.wxPay(info);
          return;
        }
        // 支付宝
        uni.navigateTo({
          url:
            '/pages/pay/pay?price=' +
            money +
            '&ghId=' +
            this.ghId +
            '&url=' +
            btoa(info.url),
        });
      } catch (error) {
        this.getDetail();
        uni.hideLoading();
      }
    },
    // 微信支付
    async wxPay(info) {
      try {
        await wxPay(info);
        this.getStatus();
      } catch (error) {
        Toast('您已取消支付');
      }
    },
    // 查询状态
    async getStatus() {
      if (num <= 0) {
        clearTimeout(timer);
        return;
      }
      num--;
      let {
        data: { regStatus },
      } = await queryRegisterPayStatus({
        ghId: this.ghId,
      });

      if (regStatus == 2) {
        this.getDetail();
        return;
      }

      timer = setTimeout(this.getStatus(), 2000);
    },
  },
};
</script>

<style lang="scss" scoped>
page {
  background-color: rgba(240, 242, 252, 1) !important;
}
* {
  box-sizing: border-box;
}
.detail {
  padding-bottom: 128rpx;
  background-color: rgba(240, 242, 252, 1) !important;
  .cont {
    margin-top: -30rpx;
    padding: 0 32rpx;

    .desc {
      margin-top: 24rpx;
    }

    .link {
      background-color: #fff;
      border-radius: 12rpx;
      margin-top: 24rpx;
      padding: 0 32rpx;
      box-shadow: 0px 1px 4px  rgba(121, 72, 234, 0.15);
      &_item {
        height: 92rpx;
        @include flex(lr);
        font-size: 14px;
        color: rgba(51, 51, 51, 1);
        border-bottom: 1px solid #ebebeb;

        &:last-child {
          border-bottom: none;
        }
      }
    }

    .ewm {
      margin-top: 24rpx;
    }
  }

  .footer {
    width: 100%;
    height: 108rpx;
    background-color: #fff;
    box-shadow: 0px 0px 2rpx 0px rgba(0, 0, 0, 0.1);
    border-radius: 16rpx 16rpx 0px 0px;
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 2;
    @include flex(lr);
    padding: 0 32rpx;

    button {
      width: 330rpx;
      height: 84rpx;
      @include border_theme;
      @include font_theme;
      font-size: 32rpx;
      @include flex;
      border-radius: 42rpx;

      &.cancel {
        width: 100%;
        @include bg_theme;
        color: #fff;
      }

      &.sign {
        @include bg_theme;
        color: #fff;
      }
    }
  }
}
</style>
