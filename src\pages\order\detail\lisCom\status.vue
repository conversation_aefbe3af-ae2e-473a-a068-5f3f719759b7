<template>
  <!-- 状态 -->
  <view class="status">
    <block v-if="status == 0">
      <view class="h2">
        <image class="icon" src="/static/inspection/x.png" />
        交易关闭
      </view>

      <view class="info"> 订单已关闭 </view>
    </block>

    <block v-if="status == 1">
      <view class="h2">
        <image class="icon" src="/static/inspection/time.png" />
        待支付
      </view>

      <view class="info">
        剩余{{ addTime }}，订单将自动取消，请尽快支付哦
      </view>
    </block>

    <block v-if="status == 2">
      <view class="h2">
        <image class="icon" src="/static/inspection/rl.png" />
        待签到
      </view>

      <view class="info"> 请于{{ time }},进行签到 </view>
    </block>

    <block v-if="status == 3">
      <view class="h2">
        <image class="icon" src="/static/inspection/wait.png" />
        等待检{{ isLis ? '验' : '查' }}中
      </view>

      <view class="info">
        您当前为第<text>{{ queueNumber }}</text> 位，前面还有
        {{ num }} 位，请耐心等待</view
      >
    </block>

    <block v-if="status == 4">
      <view class="h2">
        <image class="icon" src="/static/inspection/up.png" />
        等待上传报告
      </view>

      <view class="info"> 请耐心等待... </view>
    </block>

    <block v-if="status == 5">
      <view class="h2">
        <image class="icon" src="/static/inspection/done.png" />
        交易完成
      </view>

      <view class="info"> 本次服务已完成，点击下方项目卡片，查看报告吧 </view>
    </block>

    <block v-if="status == 6">
      <view class="h2">
        <image class="icon" src="/static/inspection/x.png" />
        已退费
      </view>

      <view class="info"> 已为您退费，预计15个工作日内到账，请注意查收 </view>
    </block>

    <block v-if="status == 7">
      <view class="h2">
        <image class="icon" src="/static/inspection/wait.png" />
        待取样
      </view>

      <view class="info">
        您当前为第<text>{{ queueNumber }}</text> 位，前面还有
        {{ num }} 位，请耐心等待</view
      >
    </block>

    <block v-if="status == 8">
      <view class="h2">
        <image class="icon" src="/static/inspection/wait.png" />
        待配送
      </view>

      <view class="info"> 采样完成，正在加急向检验中心配送</view>
    </block>

    <block v-if="status == 9">
      <view class="h2">
        <image class="icon" src="/static/inspection/wait.png" />
        待签收
      </view>

      <view class="info"> 样本已配送，请耐心等待</view>
    </block>
  </view>
</template>

<script>
export default {
  name: 'Status',
  props: {
    status: {
      type: Number | String,
      default: 1,
    },
    addTime: {
      type: String,
      default: '',
    },
    time: {
      type: String,
      default: '',
    },
    num: {
      type: String | Number,
      default: '',
    },
    queueNumber: {
      type: String | Number,
      default: '',
    },
    isLis: {
      type: Boolean,
      default: false,
    },
  },
  filters: {
    time(val) {
      if (!val) return val;
      let start = new Date(val).getTime();
      let end = new Date().getTime();
      let c = 1800 - (end - start) / 1000;
      if (c <= 0) return 0;
      let m = parseInt((c / 60) % 60);
      let s = parseInt(c % 60);
      if (m < 10) m = '0' + m;
      if (s < 10) s = '0' + s;
      return m + '分' + s + '秒';
    },
  },
};
</script>

<style lang="scss" scoped>
.status {
  height: 216rpx;
  background: #ff5050;
  padding: 32rpx;
  color: #fff;
  box-sizing: border-box;

  .h2 {
    font-size: 48rpx;
    font-weight: bold;
    @include flex(left);

    .icon {
      width: 44rpx;
      height: 44rpx;
      margin-right: 16rpx;
    }
  }

  .info {
    font-size: 24rpx;
    margin-top: 24rpx;

    text {
      font-size: 36rpx;
      font-weight: bold;
      padding: 0 8rpx;
    }
  }
}
</style>
