<template>
  <!-- 名医推荐-->
  <view class="fam_list">
    <!-- 顶部固定 -->
    <div class="fixed">
      <view class="search-container">
        <uni-search-bar placeholder="请输入医生、科室、医院名称" cancelButton="none" @confirm="search"
                        @input="changeInput"></uni-search-bar>
      </view>
    </div>
    <div class="list_warp">
      <!-- 循环 -->
      <template v-for="(item,index) in famousDocList" >
        <view class="doc_box" :key="index" @click="toDoc(item)">
          <img  v-if="item.docImg" v-img="item.docImg" :data-src="errUrl" mode="aspectFill" class="header_img"/>
          <image mode="aspectFill" class="header_img" v-else src="/static/images/docHead.png"></image>
          <view class="doc_box_right">
            <view class="doc_box_right_top">
              <view class="doc_box_right_name">
                <view>{{ item.docName }} {{ item.docProf }}</view>
                <view><text class="dep" v-for="(deptName, index) in item.dept">{{ deptName }}</text>
                  {{
                    isShowWorkHosName ? item.workHosName : item.hosName
                  }}</view>
              </view>
            </view>
            <!-- 描述 -->
            <view class="doc_box_right_content">
              <text>好评数:{{ item.percentage }}%</text>
              <text class="long">|</text>
              <text>接诊量:{{ item.consultationCount }}</text>
<!--              <text class="long">|</text>-->
<!--              <text>平均响应:{{ item.responseTime }}分钟</text>-->
            </view>
            <!-- 标签 -->
            <view class="little_label">
              <template v-for="element in item.docLable">
                <text>{{ element.lableName }}</text>
              </template>
            </view>
          </view>
        </view>
      </template>
    </div>
    <!-- 医生列表-->
<!--    <view class="list-box">-->
<!--      <view class="list-box-1" v-for="(item,index) in famousDocList" :key="index">-->
<!--        <view class="list-card" @click="toDoc(item)">-->
<!--          <img class="doc_head" v-if="item.docImg" v-img="item.docImg" :data-src="errUrl" />-->
<!--          <image class="doc_head" v-else src="/static/images/docHead.png"></image>-->
<!--          <view class="doc_name">-->
<!--            <text>{{item.docName}}</text>-->
<!--          </view>-->
<!--          <view class="desc_two">-->
<!--            <text>{{item.deptName}}</text>-->
<!--            <text>{{item.docProf}}</text>-->
<!--          </view>-->
<!--          <view class="host">-->
<!--            <text>{{isShowWorkHosName ? item.workHosName : item.hosName}}</text>-->
<!--          </view>-->
<!--          &lt;!&ndash; 标签 &ndash;&gt;-->
<!--          <view class="desc_tag" v-if="item.docLable.length>3">-->
<!--           <view v-for="(l, lk) in item.docLable.slice(0,3)" class="desc_tag_1">-->
<!--              <view class="desc_tag_text">{{ l.lableName }}</view>-->
<!--            </view>-->
<!--          </view>-->
<!--          <view class="desc_tag" v-else>-->
<!--           <view v-for="(l, lk) in item.docLable" class="desc_tag_1">-->
<!--              <view class="desc_tag_text">{{ l.lableName }}</view>-->
<!--            </view>-->
<!--          </view>-->
<!--        </view>-->
<!--      </view>-->
<!--    </view>-->
  </view>
</template>

<script>
  import {
    patientHomeRecommendDoctor,
  } from "@/api/base.js";
  export default {
    data() {
      return {
        errUrl: require("../../../static/images/docHead.png"),
        isShowWorkHosName: uni.getStorageSync("isShowWorkHosName") || false,
        famousDocList: [],
        docListQuery:{}
      }
    },
    onLoad() {
      this.getFamousDocList();
    },
    methods: {
      // 模糊搜索
      search(e) {
        this.famousDocList = [];
        this.docListQuery.searchKey = e.value;
        this.getFamousDocList();
      },
      // 清空搜索框时,重置
      changeInput(e) {
        if (e.value == "") {
          this.famousDocList = [];
          this.docListQuery.searchKey = "";
          this.getFamousDocList();
        }
      },
      //名医推荐
      async getFamousDocList() {
        let {
          data
        } = await patientHomeRecommendDoctor({searchKey: this.docListQuery.searchKey});
        this.famousDocList = data;
      },
      // 去医生主页
      toDoc(item) {
        // 医院id
        uni.setStorageSync("hosId", item.hosId);
        uni.navigateTo({
          url: "/pages/register/docHomePage/index?docId=" + item.docId,
        });
      },
    }
  }
</script>
<style lang="scss" scoped>
  .fam_list {
    background: #fff;
    border-radius: 8rpx;

    .fam_title {
      padding: 0 32rpx;
      height: 80rpx;
      @include flex(left);
      display: flex;
      justify-content: space-between;
    }
  }

  .list-box {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    padding: 20rpx 0;
    margin-left: 20rpx;
  }

  .list-card {
    padding: 16rpx;
    background-color: #FBFBFD;
    margin-right: 20rpx;
    margin-bottom: 20rpx;
    border-radius: 8rpx;
    height: 280rpx;
    text-align: center;
  }

  .list-box-1 {
    width: 50%;
    text-align: center;
  }

  .card_more {
    height: 80rpx;
    @include flex;
    font-size: 28rpx;
    color: #999;
  }

  .doc_head {
    width: 104rpx;
    height: 104rpx;
    border-radius: 50%;
    flex: none;
    object-fit: cover;

  }

  .doc_name {
    font-size: 32rpx;
    color: #333;
    font-weight: bold;
  }

  .desc_tag {
    margin-top: 16rpx;
    display: flex;
    flex-direction: row;
    justify-content: center;

    // text {
    //   padding: 0 12rpx;
    //   height: 36rpx;
    //   border-radius: 18rpx;
    //   background-color: #e8f6fd;
    //   @include font_theme;
    //   font-size: 22rpx;
    //   @include flex;

    // }
  }
  .desc_tag_text{
   padding: 0 12rpx;
   height: 36rpx;
   line-height: 36rpx;
   border-radius: 18rpx;
   background-color: #e8f6fd;
   @include font_theme;
   font-size: 22rpx;
   //@include flex;
   overflow: hidden; //块元素超出隐藏
   max-width: 80rpx;
   text-overflow: ellipsis; //文本超出块元素时以省略号的形式显示
   white-space: nowrap; //规定段落中的文本不进行换行
  }
  .desc_tag_1{
    margin: 0 2rpx;
    }
  .desc_two {
    margin-top: 8rpx;
    font-size: 24rpx;
    color: #333;

    text:last-child {
      margin-left: 24rpx;
    }
  }

  .host {
    margin-top: 8rpx;
    font-size: 24rpx;
    color: #a6aab2;
    /* 超出部分...代替*/
    //width: 150px;
    width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    overflow: hidden;
  }
  .page {
    display: flex;
    flex-direction: column;
  }

  /* 搜索框样式 */
  .search-container {
    width: 100%;
    position: relative;
    z-index: 14;
  }

  /deep/.uni-searchbar {
    padding: 0 20upx;
    height: 88rpx;
    border-bottom: 2rpx solid #ebebeb;
  }

  /deep/.uni-searchbar__box {
    height: 88rpx;
    background: #ffffff !important;
    border-radius: 0px !important;
    border: none;
  }

  .fixed {
    width: 100%;
    position: sticky;
    height: 85rpx;
    flex: none;
    top: 0;
    z-index: 9;
  }

  .search-container {
    position: relative;
    z-index: 15;
  }

  /* 筛选条件 */
  .query-container {
    width: 100%;
    height: 84rpx;
  }

  /* 医生列表 */
  .doc_list_box {
    // padding: 32rpx;
    box-sizing: border-box;
    overflow-y: auto;
    background: #fff;
    flex: 1;




  }
  .doc_box {
    border-bottom: 1px solid $k-hr-color;
    @include flex(lr);
    align-items: flex-start;
    padding: 24rpx 0;
    position: relative;

    .header_img {
      width: 104rpx;
      height: 104rpx;
      border-radius: 8upx;
      margin-right: 20rpx;
    }
  }
  .list_warp {
    padding: 32rpx;
  }
  // 标签
  .little_label {
    width: 100%;

    text {
      display: inline-block;
      background: #e8f6fd;
      border-radius: 18rpx;
      font-size: 22rpx;
      @include font_theme;
      margin-right: 16upx;
      padding: 0 12rpx;
      height: 36rpx;
      line-height: 36upx;
      font-weight: 400;
    }
  }

  .doc_box .doc_box_right {
    flex: 1;
  }

  .doc_box .hint_box {
    display: flex;
  }

  .doc_box .hint_box view {
    width: 88rpx;
    height: 36rpx;
    border-radius: 8rpx;
    color: #ffffff;
    font-size: 24rpx;
    text-align: center;
    line-height: 36rpx;
  }

  .doc_box .hint_box view {
    margin-left: 10rpx;
  }

  .doc_box .hint_box view:first-child {
    margin-left: 0;
  }

  .doc_box .hint_box .one {
    background-color: #ffb541;
  }

  .doc_box .hint_box .two {
    background-color: #23b067;
  }

  .doc_box .hint_box .three {
    @include bg_theme;
  }

  .doc_box .doc_box_right_top {
    display: flex;
    justify-content: space-between;
  }

  .doc_box .doc_box_right_name {
    font-size: 32rpx;
    font-weight: 600;
    color: rgba(68, 68, 68, 1);
  }

  .doc_box .doc_box_right_name view:last-child {
    font-weight: 500;
    font-size: 28rpx;
    color: #444444;
    padding-top: 10rpx;
  }

  // 描述
  .doc_box_right_content {
    @include flex(lr);
    font-size: 24rpx;
    color: #a6aab2;
    padding: 6upx 0;
  }

  /* 转诊标志 */
  .zz-logo {
    width: 88rpx;
    height: 36rpx;
    border-radius: 8rpx;
    color: #ffffff;
    background: #159f5c;
    font-size: 24rpx;
    text-align: center;
    line-height: 36rpx;
    position: absolute;
    top: 70rpx;
    right: 0;
  }

  /* 列表为空提示 */
  .empty_list {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 26rpx;
    font-weight: 400;
    color: rgba(158, 163, 173, 1);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .dep {
    margin-right: 8rpx;
  }

  .empty_list image {
    margin-bottom: 40rpx;
    width: 386rpx;
    height: 324rpx;
  }
</style>
