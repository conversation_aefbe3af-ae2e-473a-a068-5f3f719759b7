<template>
  <div class="logistics">
    <TITLE title="物流信息" />

    <div class="top">
      <span>{{ detail.samplingOrgName }}</span>
      <image src="/static/inspection/right.png" class="icon" />
      <span>{{ detail.lisOrgName }}</span>
    </div>

    <div class="item">
      <span class="bold">物流信息:</span> {{ detail.logisticsName }}
    </div>

    <div class="item">
      <span class="bold">物流单号:</span> {{ detail.logisticsCode }}
      <span class="copy" @click="copy">复制</span>
    </div>
  </div>
</template>

<script>
import TITLE from './itemTitle.vue';
import { Toast } from '@/common/js/pay.js';
export default {
  name: 'Logistics',
  props: {
    detail: {
      type: Object,
      default: () => {},
    },
  },
  components: {
    TITLE,
  },
  methods: {
    copy() {
      uni.setClipboardData({
        data: 'hello',
        success() {
          Toast('复制成功');
        },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.logistics {
  margin-top: 16rpx;
  background: #fff;
  padding: 24rpx 32rpx 32rpx;
  border-radius: 8rpx;
  font-size: 28rpx;

  .top {
    @include flex;
    padding: 24rpx 0;

    .icon {
      width: 44rpx;
      height: 44rpx;
      flex: none;
    }

    span {
      width: 40%;
      padding: 0 24rpx;
      text-align: center;
    }
  }

  .item {
    font-size: 26rpx;
    color: #666;
    line-height: 50rpx;

    .bold {
      font-weight: bold;
      color: #333;
      padding-right: 14rpx;
    }

    .copy {
      @include font_theme;
      padding-left: 14rpx;
    }
  }
}
</style>
