<template>
  <div class="input_invoice">
    <div class="list">
      <!-- 抬头类型 -->
      <div class="item">
        <span class="label">抬头类型</span>
        <div class="right">
          <p :class="{ act: type == 0 }" @click="setType(0)">
            <image v-show="type == 0" src="/static/shop/sele_act.png" />
            <image v-show="type == 1" src="/static/shop/sele.png" />
            <span>普通发票</span>
          </p>
          <p :class="{ act: type == 1 }" @click="setType(1)">
            <image v-show="type == 1" src="/static/shop/sele_act.png" />
            <image v-show="type == 0" src="/static/shop/sele.png" />
            <span>增值税发票</span>
          </p>
        </div>
      </div>

      <!-- 发票抬头 -->
      <div class="item">
        <span class="label">发票抬头</span>
        <div class="right">
          <p
            v-show="type == 0"
            :class="{ act: titleType == 0 }"
            @click="setTitleType(0)"
          >
            <image v-show="titleType == 0" src="/static/shop/sele_act.png" />
            <image v-show="titleType == 1" src="/static/shop/sele.png" />
            <span>个人/非企业单位</span>
          </p>
          <p :class="{ act: titleType == 1 }" @click="setTitleType(1)">
            <image v-show="titleType == 1" src="/static/shop/sele_act.png" />
            <image v-show="titleType == 0" src="/static/shop/sele.png" />
            <span>单位</span>
          </p>
        </div>
      </div>

      <block v-if="titleType == 0">
        <!-- 个人名称 -->
        <div class="item">
          <span class="label x">个人名称</span>
          <div class="right">
            <input
              type="text"
              v-model="info.personalOrCompanyName"
              placeholder="请输入您的姓名"
            />
          </div>
        </div>

        <!-- 手机号码 -->
        <div class="item">
          <span class="label">手机号码</span>
          <div class="right">
            <input
              type="number"
              v-model="info.personalOrCompanyTelNo"
              placeholder="请输入联系人手机号码"
            />
          </div>
        </div>
      </block>

      <block v-else>
        <!-- 公司名称 -->
        <div class="item">
          <span class="label x">公司名称</span>
          <div class="right">
            <input
              type="text"
              v-model="info.personalOrCompanyName"
              placeholder="请输入公司名称"
            />
          </div>
        </div>

        <!-- 公司税号 -->
        <div class="item">
          <span class="label x">公司税号</span>
          <div class="right">
            <input
              type="text"
              v-model="info.companyDutyParagraph"
              placeholder="请输入15-20位纳税人识别号"
            />
          </div>
        </div>

        <!-- 公司地址 -->
        <div class="item">
          <span class="label" :class="{ x: titleType == 1 && type == 1 }"
            >公司地址</span
          >
          <div class="right">
            <input
              type="text"
              v-model="info.companyAddress"
              placeholder="请输入公司注册地址"
            />
          </div>
        </div>

        <!-- 公司电话 -->
        <div class="item">
          <span class="label" :class="{ x: titleType == 1 && type == 1 }"
            >公司电话</span
          >
          <div class="right">
            <input
              type="number"
              v-model="info.personalOrCompanyTelNo"
              placeholder="请输入公司注册电话"
            />
          </div>
        </div>

        <!-- 开户银行 -->
        <div class="item">
          <span class="label" :class="{ x: titleType == 1 && type == 1 }"
            >开户银行</span
          >
          <div class="right">
            <input
              type="text"
              v-model="info.depositaryBank"
              placeholder="请输入开户银行"
            />
          </div>
        </div>

        <!-- 银行账号 -->
        <div class="item">
          <span class="label" :class="{ x: titleType == 1 && type == 1 }"
            >银行账号</span
          >
          <div class="right">
            <input
              type="number"
              v-model="info.bankAccount"
              placeholder="请输入银行账号"
            />
          </div>
        </div>
      </block>
    </div>

    <!-- 选择收货地址 -->
    <div class="address">
      <div class="sele" @click="toAddress">
        <span>选择收货地址</span>

        <uni-icons type="arrowright" size="24" color="#666" />
      </div>

      <block v-if="info.receiverAddress">
        <div class="addr_item">
          <span class="label">收件人</span>
          <p class="right">{{ info.receiverName }}</p>
        </div>
        <div class="addr_item">
          <span class="label">联系电话</span>
          <p class="right">{{ info.receiverTelNo }}</p>
        </div>
        <div class="addr_item">
          <span class="label">详细地址</span>
          <p class="right">
            {{ info.receiverAddress }}
          </p>
        </div>
      </block>
    </div>

    <!-- 发票须知 -->
    <div class="tip">
      <p class="title">发票须知</p>

      <div class="cont">
        <p>1.目前可开具的发票类型为纸质发票</p>
        <p>2.开票金额为用户实际支付的金额</p>
        <p>3.纸质发票会在15个工作日内快递发至您的地址，快递方式为快递到付</p>
      </div>
    </div>

    <FOOTER @click="save">确定</FOOTER>
  </div>
</template>

<script>
import FOOTER from '@/components/footer_button/button.vue';
import { Toast } from '@/common/js/pay.js';
import {
  submitInvoiceInfo,
  getInvoiceDetail,
  editInvoiceInfo,
} from '@/api/invoice';
export default {
  name: 'InputInvoice',
  components: {
    FOOTER,
  },
  data() {
    return {
      // 0 普通发票 1 增值税发票
      type: 0,
      // 0 个人 1 单位
      titleType: 0,
      // 信息
      info: {
        // 发票抬头 1个人 2单位
        invoiceHeader: '',
        // 发票类型 1普通 2 增值税
        invoiceType: '',
        // 银行账户
        bankAccount: '',
        // 公司地址
        companyAddress: '',
        // 公司税号
        companyDutyParagraph: '',
        // 开户银行
        depositaryBank: '',
        // 发票金额
        invoiceMoney: '',
        // 订单编号
        orderNo: '',
        // 个人或公司名称
        personalOrCompanyName: '',
        // 个人或公司电话
        personalOrCompanyTelNo: '',
        // 收件人详细地址
        receiverAddress: '',
        // 收件人姓名
        receiverName: '',
        // 收件人电话
        receiverTelNo: '',
      },
    };
  },
  onLoad(v) {
    const { orderMoney, orderNo, irId } = v;

    if (irId) {
      this.irId = irId;
      this.getDetail();
      return;
    }

    this.info.orderNo = orderNo;
    this.info.invoiceMoney = orderMoney;
  },
  onShow() {
    const address = uni.getStorageSync('invoice_address');
    if (address) {
      const { telNo, deliveryName, addressArea, addressDetail } = address;
      this.info.receiverAddress = addressArea + addressDetail;
      this.info.receiverName = deliveryName;
      this.info.receiverTelNo = telNo;
      uni.removeStorageSync('invoice_address');
    }
  },
  methods: {
    async getDetail() {
      let { data } = await getInvoiceDetail(this.irId);
      this.type = Number(data.invoiceType) - 1;
      this.titleType = Number(data.invoiceHeader) - 1;
      this.info = data;
    },
    // 设置抬头类型
    setType(n) {
      if (n == 1) {
        this.setTitleType(1);
      }
      this.type = n;
    },
    // 设置抬头
    setTitleType(n) {
      this.titleType = n;
    },
    // 选择地址
    toAddress() {
      uni.navigateTo({
        url: '/pages/address/index?action=invoice',
      });
    },
    // 保存
    async save() {
      let info = this.info;

      const { titleType, type } = this;

      if (type == 0) {
        // 个人
        if (titleType == 0) {
          if (!info.personalOrCompanyName) {
            Toast('请填写您的姓名');
            return;
          }
        }

        // 单位
        if (titleType == 1) {
          if (!info.personalOrCompanyName) {
            Toast('请填写公司名称');
            return;
          }

          if (!info.companyDutyParagraph) {
            Toast('请填写公司税号');
            return;
          }
        }
      } else {
        if (!info.personalOrCompanyName) {
          Toast('请填写公司名称');
          return;
        }

        if (!info.companyDutyParagraph) {
          Toast('请填写公司税号');
          return;
        }

        if (!info.companyAddress) {
          Toast('请填写公司地址');
          return;
        }

        if (!info.personalOrCompanyTelNo) {
          Toast('请填写公司电话');
          return;
        }

        if (!info.depositaryBank) {
          Toast('请填写开户银行');
          return;
        }

        if (!info.bankAccount) {
          Toast('请填写银行账号');
          return;
        }
      }

      if (!info.receiverAddress) {
        Toast('请选择收货地址');
        return;
      }

      info.invoiceType = type + 1;

      info.invoiceHeader = titleType + 1;

      console.log(info);

      if (this.irId) {
        await editInvoiceInfo(info);
      } else {
        await submitInvoiceInfo(info);
      }

      uni.navigateBack();
    },
  },
};
</script>

<style lang="scss" scoped>
page {
  background: #f5f5f5;
}
.input_invoice {
  padding: 24rpx 24rpx 120rpx 24rpx;

  .list {
    padding: 0 24rpx;
    border-radius: 8rpx;
    background: #fff;

    .item {
      @include flex(lr);
      font-size: 28rpx;
      height: 92rpx;
      border-bottom: 1px solid #f5f5f5;

      &:last-child {
        border-bottom: none;
      }

      .label {
        flex: none;
        width: 180rpx;
        position: relative;

        &.x::before {
          content: '*';
          color: red;
          position: absolute;
          left: -16rpx;
        }
      }

      .right {
        flex: 1;
        @include flex(lr);

        input {
          flex: 1;
        }

        p {
          @include flex(left);

          &:last-child {
            min-width: 40%;
          }

          image {
            width: 32rpx;
            height: 32rpx;
            margin-right: 12rpx;
          }
        }
      }
    }
  }

  .address {
    margin-top: 24rpx;
    background: #fff;
    border-radius: 8rpx;
    padding: 0 24rpx;

    .sele {
      height: 92rpx;
      @include flex(lr);
      font-size: 28rpx;

      span {
        font-weight: bold;
      }
    }

    .addr_item {
      padding: 24rpx 0;
      @include flex(lr);
      align-items: flex-start;
      font-size: 28rpx;
      border-top: 1px solid #f5f5f5;

      .label {
        flex: none;
        width: 180rpx;
      }

      .right {
        flex: 1;
      }
    }
  }

  .tip {
    padding: 0 24rpx;
    margin-top: 28rpx;

    .title {
      font-size: 28rpx;
      color: #333;
    }

    .cont {
      padding: 24rpx 0;

      p {
        font-size: 24rpx;
        color: #999;
        line-height: 36rpx;
      }
    }
  }
}
</style>
