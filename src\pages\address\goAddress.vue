<template>
  <view>
    <web-view
      :webview-styles="webviewStyles"
      :src="urls"
      class="webClass"
    ></web-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      webviewStyles: {
        progress: {
          color: "#FF3333",
        },
      },
      urls: "",
    };
  },
  onLoad(option) {
    const Address = JSON.parse(option.address);
    console.log(Address)
    this.urls = `https://apis.map.qq.com/uri/v1/geocoder?coord=${Address.latitude},${Address.longitude}&referer=互联网医院`;
  },
};
</script>

<style scoped>
.webClass {
  margin-top: 44px;
}
</style>
