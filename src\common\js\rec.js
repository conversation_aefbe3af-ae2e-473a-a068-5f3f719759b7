import { Toast } from "@/common/js/pay.js";
// ios上使用微信sdk录音流程
import { getJSSDKSign } from "@/api/share.js";

// 引入sdk
const jweixin = require("jweixin-module");

const BenzAMRRecorder = require("benz-amr-recorder");

let WebIM = require("utils/WebIM")["default"];

import Recorder from "recorder-core";
import "recorder-core/src/engine/mp3";
import "recorder-core/src/engine/mp3-engine";
import "recorder-core/src/engine/beta-amr";
import "recorder-core/src/engine/beta-amr-engine";

// 下载微信文件
import { downLoadVoiceSuCai } from "@/common/axios/wx.js";

var wxForce;

// 网页录音
var rec;

let u = navigator.userAgent;

// 判断是否是 iOS终端
let isIOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);
// iOS或者强制使用wx
if (isIOS || wxForce) {
    wxForce = true;
    wxInit();
}

// 初始化微信sdk
function wxInit() {
    // 参数
    let para = {
        appid: uni.getStorageSync("appId"),
        url: window.location.href.split("#")[0],
    };

    // 调用接口
    getJSSDKSign(para).then((res) => {
        var info = res.data;
        jweixin.config({
            debug: false,
            appId: uni.getStorageSync("appId"),
            timestamp: info.timestamp,
            nonceStr: info.nonceStr,
            signature: info.signature,
            // 录音相关 开始录音 停止录音 到达60s停止录音 上传录音
            jsApiList: [
                "startRecord",
                "stopRecord",
                "onVoiceRecordEnd",
                "playVoice",
                "uploadVoice",
            ],
        });
    });
    jweixin.error(function(res) {

    });
    jweixin.ready(function() {

    });
}
//初始化录音授权
function InitRec() {
    jweixin.startRecord({
        success: function() {
            stopRec();
        },
        cancel: function() {},
    });
}
// 开始录音
function startRec(isWeixin = false) {
    return new Promise((resolve, reject) => {
        console.log(wxForce)
        if (isWeixin) {
            // 微信自带授权
            jweixin.startRecord({
                success(res) {
                    console.log("微信录音成功", res)
                    resolve();
                },
                fail(err) {
                    console.log(err)
                    reject();
                },
            });
        } else {
            rec = Recorder({
                type: "mp3",
                sampleRate: 16000,
                bitRate: 16,
            });
            // 初始化
            rec.open(
                function() {
                    // 开始录音
                    rec.start();
                    resolve();
                },
                function(msg, isUserNotAllow) {
                    console.log(
                        (isUserNotAllow ? "UserNotAllow，" : "") + "无法录音:" + msg
                    );
                    reject(msg);
                }
            );
        }
    });
}

// 结束录音
function stopRec(isWeixin = false) {
    if (isWeixin) {
        return new Promise((resolve, reject) => {
            // 停止录音
            jweixin.stopRecord({
                success: function(res) {
                    // 调用上传
                    jweixin.uploadVoice({
                        localId: res.localId,
                        // 默认为1，显示进度提示
                        isShowProgressTips: 0,
                        success: function(resp) {
                            console.log("查看resp", resp);
                            console.log("查看resp", resp.serverId);

                            // 下载二进制文件到本地 调用接口
                            downLoadVoiceSuCai(resp.serverId)
                                .then((blob) => {
                                    console.log("上传接口查看日志", blob);
                                    // 使用插件
                                    let amr = new BenzAMRRecorder();
                                    // 通过微信sdk录音上传下载后没有时长
                                    amr.initWithBlob(blob).then(() => {
                                        // 获取当前音频时长
                                        let duration = amr.getDuration();
                                        // 转化格式
                                        blob = WebIM.utils.parseDownloadResponse(blob);
                                        // 返回数据
                                        resolve({ blob, duration });
                                    });
                                })
                                .catch((err) => {
                                    reject();
                                });
                        },
                    });
                },
            });
        });
    } else {
        return new Promise((resolve, reject) => {
            // 二进制 以及时长
            rec.stop(
                (blob, duration) => {
                    console.log("录音时长", blob)
                        // 读取时长
                    duration = Math.floor(duration / 1000);
                    // 判断时长
                    if (duration < 1) {
                        Toast("录音时长不可小于1秒");
                        reject();
                    }
                    // 转换格式
                    blob = WebIM.utils.parseDownloadResponse(blob);
                    // 接收二进制 以及时长
                    resolve({
                        blob,
                        duration,
                    });
                },
                (msg) => {
                    rec = null;
                    Toast(msg);
                    reject(msg);
                },
                true
            ); //自动调用close
        });
    }
}

// 从微信下载二进制音频到本地
function downAudio() {
    // 请求后台接口返回数据
}

// 测试微信播放录音
function playRec(id) {
    jweixin.playVoice({
        localId: id,
    });
}

export default {
    startRec,
    stopRec,
    playRec,
    InitRec,
};