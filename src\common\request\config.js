// 获取app下载地址
function getSystemDownLoadAppUrl(env = 'YXL') {
  const u = navigator.userAgent;

  const isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);

  let href;

  if (isiOS) {
    if (env === 'YXL') {
      href =
        'https://apps.apple.com/cn/app/%E5%8C%BB%E5%B0%8F%E9%B9%BF/id1535751271';
    } else if (env === 'JYK') {
      href =
        'https://apps.apple.com/cn/app/%E9%87%91%E7%9B%8A%E5%BA%B7/id1559000822';
    }
  } else {
    if (env === 'YXL') {
      href = 'https://a.app.qq.com/o/simple.jsp?pkgname=com.qinwenting.cow';
    } else if (env === 'JYK') {
      href = 'https://a.app.qq.com/o/simple.jsp?pkgname=com.qinwenting.itp';
    }
  }

  return href;
}

const {
  VUE_APP_FIRMID,
  VUE_APP_AGORA,
  VUE_APP_SERVE,
  VUE_APP_OSS,
  VUE_APP_ROOTURL,
  VUE_APP_SHARE,
  VUE_APP_ENV,
  VUE_APP_MAPKEY,
  VUE_APP_AISERVE // 1:测试环境 2:正式环境
  // } = process.env;
} = CONFIG_ENV;

module.exports = {
  version: '1.3.7',
  clientType: 'G',
  serverUrl: VUE_APP_SERVE,
  ossUrl: VUE_APP_OSS,
  rootUrl: VUE_APP_ROOTURL,
  shareLogoUrl: VUE_APP_SHARE,
  firmId: VUE_APP_FIRMID,
  downLoadAppUrl: getSystemDownLoadAppUrl(VUE_APP_ENV),
  appid: VUE_APP_AGORA,
  mapKey: VUE_APP_MAPKEY,
  aiServerUrl: VUE_APP_AISERVE
};
