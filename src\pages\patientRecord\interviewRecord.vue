<template>
  <view class="records">
    <view v-if="questionList.length > 0">
      <scroll-view
        class="content-container"
        scroll-y="true"
        @scrolltolower="getMore"
      >
        <view class="cardList">
          <view
            class="card"
            v-for="(item, index) in questionList"
            :key="index"
            @click="openDetail(item)"
          >
            <view class="title">
              <view class="didName">{{ item.didName }}</view>
              <view
                :class="
                  item.isFeedback == '未填写' ? 'status red' : 'status blue'
                "
                >{{ item.isFeedback }}</view
              >
            </view>
            <view class="sendTime">
              {{ item.sendTime }}
            </view>
            <view class="total">共{{ item.topicCount }}题</view>
          </view>
        </view>
      </scroll-view>
    </view>
    <view v-else class="empty">
      <image src="../../static/images/question/empty_toast.png" mode=""></image>
      <view>暂无量表</view>
    </view>
  </view>
</template>

<script>
import { getAllQuestionByDoc } from '../../api/chatCardDetail';
export default {
  data() {
    return {
      listQuery: {
        limit: 100,
        page: 1,
        docId: '',
        patientId: '',
      },
      pageParam: {},
      questionList: [],
    };
  },
  onLoad(options) {
    // 医生id 患者id
    this.pageParam = JSON.parse(options.param);

    this.listQuery.docId = this.pageParam.docId;
    this.listQuery.patientId = this.pageParam.patientId;
  },
  created() {
    this.getAllQuestionFun();
  },
  methods: {
    async getAllQuestionFun() {
      let res = await getAllQuestionByDoc(this.listQuery);
      this.questionList = res.data.rows;
    },
    openDetail(item) {
      if (item.isFeedback == '未填写') {
        uni.navigateTo({
          url:
            '/pages/chatCardDetail/questionnaire?param=' + JSON.stringify(item),
        });
      } else {
        uni.navigateTo({
          url:
            '/pages/chatCardDetail/questionnaireRead?param=' +
            JSON.stringify(item),
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.cardList {
  padding: 24rpx 32rpx;
  .card {
    background: #ffffff;
    border-radius: 16rpx;
    font-size: 28rpx;
    color: $k-title;
    padding: 26rpx 32rpx;
    margin-top: 24rpx;

    .title {
      font-weight: 600;
      justify-content: space-between;
      line-height: 44rpx;
      view {
        display: inline-block;
      }
      .didName {
        width: 85%;
      }
    }
    .sendTime {
      color: $k-info-title;
      font-size: 24rpx;
      line-height: 36rpx;
      margin-top: 16rpx;
      padding-bottom: 26rpx;
      border-bottom: 1rpx solid #ebebeb;
    }
    .total {
      color: $k-sub-title;
      font-size: 22rpx;
      line-height: 36rpx;
      padding-top: 32rpx;
    }
    .red {
      color: #ff5050;
    }
    .blue {
      @include font_theme;
    }
  }
}

.empty {
  text-align: center;
  color: $k-title;
  font-size: 28rpx;
  width: 100%;
  height: 450upx;
  position: absolute;
  top: 50%;
  margin-top: -225upx;

  image {
    width: 386rpx;
    height: 324rpx;
    margin-bottom: 40rpx;
  }
}
</style>
