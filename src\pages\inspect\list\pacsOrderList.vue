<template>
  <!-- 检验订单列表 -->
  <view class="lis_order_list">
    <view class="head" v-if="isShowFilter">
      <HMfilterDropdown
        :menuTop="80"
        :filterData="filterData"
        :defaultSelected="defaultSelected"
        :updateMenuName="true"
        @confirm="confirm"
        dataFormat="Object"
      ></HMfilterDropdown>
    </view>

    <!-- 列表 -->
    <LIST @click="toDetail" v-if="list.length" :list="list" />

    <!-- 空 -->
    <EMPTY v-if="!list.length" />
  </view>
</template>

<script>
import HMfilterDropdown from '@/components/HM-filterDropdown/HM-filterDropdown.vue';
import LIST from '../com/orderList.vue';
import EMPTY from '@/pages/order/components/empty.vue';
import { myPacsList } from '@/api/inspect';
import { getAllDept } from '@/api/base.js';
import { findIsEnabledPatientByUserId } from '@/api/user.js';
import myJsTools from '@/common/js/myJsTools.js';

export default {
  name: 'PacsOrderList',
  components: {
    LIST,
    EMPTY,
    HMfilterDropdown,
  },
  data() {
    return {
      query: {
        limit: 10,
        page: 1,
        caller: 2,
        // 就诊人ids
        patientIds: uni.getStorageSync('patientIdList'),
        docId: '',
        startTime: '',
        endTime: '',
      },
      total: 0,
      list: [],
      isShowFilter: false,
      filterData: [
        {
          name: '科室',
          type: 'hierarchy',
          submenu: [],
        },
        {
          name: '时间',
          type: 'hierarchy',
          submenu: [
            {
              name: '七天内',
              value: '0',
            },
            {
              name: '一月内',
              value: '1',
            },
            {
              name: '三月内',
              value: '2',
            },
            {
              name: '半年内',
              value: '3',
            },
          ],
        },
        {
          name: '就诊人',
          type: 'hierarchy',
          submenu: [],
        },
      ],
      defaultSelected: [],
    };
  },
  onLoad() {
    this.getAllPatient();
    this.getAllDept();
    this.getList();
  },
  onPullDownRefresh() {
    this.query.page = 1;
    this.getList();
  },
  onReachBottom() {
    if (this.list.length >= this.total) return;
    this.query.page++;
    this.getList();
  },
  methods: {
    toDetail(item) {
      let { paymentType, status, ppiId } = item;
      let url;
      // 待支付 线上
      if (paymentType == 2 && status == 1) {
        url = '../pay/pacs?id=' + ppiId;
      } else if (!paymentType && status == 1) {
        url = '../pacsOrder?id=' + ppiId;
      } else {
        url = '../pacsDetails?id=' + ppiId;
      }
      uni.navigateTo({
        url,
      });
    },
    // 获取列表
    async getList() {
      let {
        data: { rows, total },
      } = await myPacsList(this.query);
      if (this.query.page > 1) {
        this.list = [...this.list, ...rows];
      } else {
        this.list = rows;
      }
      this.total = total;
    },
    // 获取就诊人列表
    async getAllPatient() {
      let { data } = await findIsEnabledPatientByUserId({
        userId: uni.getStorageSync('userId'),
      });
      if (!data || !data.length) {
        this.isShowFilter = false;
        return;
      }
      let submenu = [];
      for (let i = 0; i < data.length; i++) {
        submenu.push({
          value: data[i].patientId,
          name: data[i].patientName,
        });
      }
      this.filterData[2].submenu = submenu;
      this.isShowFilter = true;
    },
    // 获取所有科室列表
    async getAllDept() {
      let {
        data: { rows },
      } = await getAllDept({
        deptName: '',
        page: 1,
        limit: 100,
      });
      let submenu = [];
      for (let i = 0; i < rows.length; i++) {
        submenu.push({
          value: rows[i].deptId,
          name: rows[i].deptName,
        });
      }
      this.filterData[0].submenu = submenu;
    },
    // 筛选
    confirm(e) {
      let valueArr = e.value;
      this.query.page = 1;
      let deptId = valueArr[0];
      if (deptId[0]) {
        this.query.deptId = deptId[0];
      } else {
        this.query.deptId = '';
      }
      let time = valueArr[1];
      if (time[0]) {
        this.query.endTime = myJsTools.getDate(new Date());
        let timeValue = time[0];

        let startTime;
        if (timeValue == 0) {
          startTime = myJsTools.getDate('day', -7);
        } else if (timeValue == 1) {
          startTime = myJsTools.getDate('month', -1);
        } else if (timeValue == 2) {
          startTime = myJsTools.getDate('month', -3);
        } else if (timeValue == 3) {
          startTime = myJsTools.getDate('month', -6);
        }
        this.query.startTime = startTime;
      } else {
        this.query.startTime = '';
        this.query.endTime = '';
      }

      let patients = valueArr[2];
      if (patients[0]) {
        this.query.patientIds = patients;
      } else {
        this.query.patientIds = uni.getStorageSync('patientIdList');
      }
      this.getList();
    },
  },
};
</script>

<style lang="scss" scoped>
page {
  background: #f5f5f5;
}
.head {
  position: sticky;
  top: 0;
  z-index: 1;
}
</style>
