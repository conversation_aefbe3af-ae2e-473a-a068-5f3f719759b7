<template>
  <!-- 检查单/检验单 -->
  <view class="chat_inspection">
    <!-- 头像 -->
    <image
      @click="head"
      :src="imgUrl || '/static/images/docHead.png'"
      mode="aspectFill"
      class="chat_user_img"
    />
    <view>
      <view class="chatName">{{chatName}}</view>
      <!-- 卡片 -->
      <view class="inspection_content" @click="click">
        <!-- 标题 -->
        <view class="content_title">
          <text v-if="!isInspect">检查单</text>
          <text v-if="isInspect">检验单</text>
        </view>

        <!-- 内容 -->
        <view class="content_cont">
          <!-- 检查单 -->
          <image
              v-if="!isInspect"
              src="/static/images/chat/jcd.png"
              class="cont_icon"
          />
          <!-- 检验单 -->
          <image
              v-if="isInspect"
              src="/static/images/chat/lisOrder.png"
              class="cont_icon"
          />
          <!-- 右侧文案 -->
          <view class="cont_right">
            <!-- 标题 -->
            <view class="cont_right_title">
              <text>诊断：{{ title }}</text>
              <!-- <text v-if="isInspect">检验单</text> -->
            </view>
            <!-- 描述 -->
            <view class="cont_right_info">
              <text v-if="!isInspect">医生给你发送了检查单</text>
              <text v-if="isInspect">医生给你发送了检验单</text>
            </view>
          </view>
        </view>

        <!-- 状态 -->
        <view class="inspection_status">
          <!-- 0 已失效 -->
          <image
              src="/static/images/chat/invalid.png"
              class="status_icon"
              v-if="status == 0"
          />
          <!-- 1 待支付 -->
          <image
              src="/static/images/chat/un-pay-order.png"
              class="status_icon"
              v-if="status == 1"
          />
          <!-- 2 3 待核验 -->
          <image
              src="/static/images/chat/d-verification.png"
              class="status_icon"
              v-if="status == 2 || status == 3"
          />
          <!-- 4 5 已核验 -->
          <image
              src="/static/images/chat/y-verification.png"
              class="status_icon"
              v-if="status == 4 || status == 5"
          />
          <!-- 6 退费 -->
          <image
              src="/static/images/chat/refund.png"
              class="status_icon"
              v-if="status == 6"
          />
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    // 头像
    imgUrl: {
      type: String,
      default: '',
    },
    // 标题
    title: {
      type: String,
      default: '',
    },
    // 描述
    info: {
      type: String,
      default: '',
    },
    // 状态
    status: {
      type: [Number, String],
      default: 0,
    },
    // 是否检验单
    isInspect: {
      type: Boolean,
      default: false,
    },
    chatName:{}
  },
  methods: {
    head() {
      this.$emit('head');
    },
    click() {
      this.$emit('click');
    },
  },
};
</script>

<style scoped lang="scss">
.chat_inspection {
  max-width: 600upx;
  box-sizing: border-box;
  @include flex(left);
  padding-bottom: 30upx;
  align-items: flex-start;

  // 头像
  .chat_user_img {
    width: 64upx;
    height: 64upx;
    border-radius: 4upx;
    margin-right: 16upx;
    flex: none;
  }

  // 内容
  .inspection_content {
    width: 516upx;
    padding: 24upx;
    background-color: #fff;
    color: #333;
    border: 1px solid #ececec;
    border-radius: 8upx 32upx 32upx 32upx;
    position: relative;

    // 标题
    .content_title {
      font-size: 28upx;
      color: $k-title;
      font-weight: bold;
    }

    // 底部内容
    .content_cont {
      @include flex(left);
      align-items: flex-start;
      margin-top: 20upx;

      // 左侧图标
      .cont_icon {
        width: 80upx;
        height: 80upx;
        border-radius: 8upx;
        margin-right: 24upx;
        flex: none;
      }

      // 右侧文案
      .cont_right {
        flex: 1;
        min-height: 80upx;
        @include flex(lr);
        flex-direction: column;
        align-items: stretch;

        // 标题
        .cont_right_title {
          font-size: 28upx;
          color: $k-title;
          font-weight: bold;
        }

        // 描述
        .cont_right_info {
          font-size: 24upx;
          color: $k-info-title;
        }
      }
    }

    // 处方状态
    .inspection_status {
      width: 100%;
      height: 82upx;
      margin-top: -10upx;
      position: absolute;
      top: 0;
      left: 0;
      padding-right: 32upx;
      @include flex(right);
      box-sizing: border-box;

      // 图标
      .status_icon {
        width: 50upx;
        height: 82upx;
        margin-left: 20upx;
      }
    }
  }
}
</style>
