<template>
  <div class="add">
    <!-- 抬头类型 -->
    <div class="type">
      <span class="title">抬头类型</span>
      <div class="right">
        <p @click="invoiceType = 1">
          <image
            v-show="invoiceType == 2"
            src="/static/images/question/radio.png"
          />
          <image
            v-show="invoiceType == 1"
            src="/static/images/question/radio_active.png"
          />
          <span>个人/非企业单位</span>
        </p>

        <p @click="invoiceType = 2">
          <image
            v-show="invoiceType == 1"
            src="/static/images/question/radio.png"
          />
          <image
            v-show="invoiceType == 2"
            src="/static/images/question/radio_active.png"
          />
          <span>单位</span>
        </p>
      </div>
    </div>

    <div class="type">
      <span class="title red">抬头名称</span>
      <input
        type="text"
        class="right"
        v-model="invoiceTitle"
        :placeholder="
          invoiceType == 1 ? '建议填写个人姓名/店名' : '请输入公司抬头名称'
        "
      />
    </div>

    <div class="type" v-if="invoiceType == 2">
      <span class="title red">公司税号</span>
      <input
        type="text"
        class="right"
        v-model="invoiceTaxNo"
        placeholder="请输入公司税号"
      />
    </div>

    <div class="type">
      <span class="title">需求备注</span>
      <input
        type="text"
        v-model="invoiceMemo"
        class="right"
        placeholder="如需备注请填写"
      />
    </div>

    <FOOTER @click="confirm">确定</FOOTER>
  </div>
</template>

<script>
import { addItem, getList } from './store';
import { Toast } from '@/common/js/pay';
import FOOTER from '@/components/footer_button/button.vue';
export default {
  name: 'Add',
  components: { FOOTER },
  data() {
    return {
      // 发票类型 1 个人 2 单位
      invoiceType: 1,
      // 发票抬头
      invoiceTitle: '',
      // 发票税号
      invoiceTaxNo: '',
      // 需求备注
      invoiceMemo: '',
    };
  },
  onLoad() {},
  methods: {
    // 设置类型
    setType(v) {
      this.invoiceTaxNo = '';
      this.invoiceType = v;
    },
    // 保存
    confirm() {
      const { invoiceTitle, invoiceType, invoiceTaxNo, invoiceMemo } = this;
      if (!invoiceTitle) {
        Toast('请填写抬头名称');
        return;
      }

      if (invoiceType == 2) {
        if (!invoiceTaxNo) {
          Toast('请填写公司税号');
          return;
        }
      }

      const list = getList();

      let item = list.find(
        (item) =>
          item.invoiceTitle === invoiceTitle && item.invoiceType === invoiceType
      );

      if (item) {
        Toast('已存在重复发票，不可新增');
        return;
      }

      let obj = {
        invoiceTitle,
        invoiceType,
        invoiceTaxNo,
        invoiceMemo,
      };
      addItem(obj);
      uni.navigateBack();
    },
  },
};
</script>

<style lang="scss" scoped>
.add {
  padding: 0 32rpx;
}

.type {
  height: 92rpx;
  @include flex(left);
  font-size: 28rpx;
  border-bottom: 1px solid #f5f5f5;

  .title {
    flex: none;
    width: 160rpx;

    &.red {
      position: relative;

      &::before {
        content: '*';
        color: red;
        position: absolute;
        left: -14rpx;
        top: 0;
      }
    }
  }

  .right {
    flex: 1;
    @include flex(left);
    font-size: 28rpx;

    p {
      @include flex;

      &:first-child {
        margin-right: 38rpx;
      }

      image {
        width: 32rpx;
        height: 32rpx;
        margin-right: 10rpx;
      }
    }
  }
}

.footer_button {
  left: 0;
}
</style>
