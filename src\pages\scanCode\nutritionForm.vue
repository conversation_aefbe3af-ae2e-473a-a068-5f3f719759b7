<template>
  <view class="questionnaire">
    <!-- <view class="header">
      <view class="title">{{ saveInfo.didName }}</view>
      <view class="tips">感谢您能抽出几分钟时间来参加量表，为了方便医生了解病情，请认真作答，谢谢！</view>
    </view> -->
    <view class="quesView">
      <view
        v-for="(item, index) in saveInfo.saveReplyInquiringDiagnosisTopics"
        :key="index"
      >
        <!-- 单选 -->
        <view class="radioView quesAnswerView" v-if="item.didtType == 1">
          <view class="didName"
            >{{ index + 1 }}.{{ item.didtName }}
            <text v-if="item.isMust == 1" style="color: red">*</text>
          </view>
          <radio-group @change="radioChange($event, item, index)">
            <label
              class="uni-list-cell uni-list-cell-pd"
              v-for="(radioItem, radioIndex) in item.answerInfo"
              :key="radioIndex"
            >
              <view>
                <radio
                  style="transform: scale(0.7)"
                  :value="JSON.stringify(radioItem.optionCode)"
                  :checked="radioItem.checked"
                />
                <text>{{ radioItem.optionName }}</text>
              </view>
            </label>
          </radio-group>
        </view>
        <!-- 多选 -->
        <view class="checkView quesAnswerView" v-if="item.didtType == 2">
          <view class="didName"
            >{{ index + 1 }}.{{ item.didtName }}
            <text v-if="item.isMust == 1" style="color: red">*</text>
          </view>
          <checkbox-group @change="checkboxChange($event, item, index)">
            <label
              class="uni-list-cell uni-list-cell-pd"
              v-for="(checkItem, checkIndex) in item.answerInfo"
              :key="checkIndex"
            >
              <view>
                <checkbox
                  style="transform: scale(0.7)"
                  :value="JSON.stringify(checkItem.optionCode)"
                  :checked="checkItem.checked"
                />
                <text>{{ checkItem.optionName }}</text>
              </view>
            </label>
          </checkbox-group>
        </view>
        <!-- 填空题 -->
        <view class="contentView quesAnswerView" v-if="item.didtType == 3">
          <view class="didName"
            >{{ index + 1 }}.{{ item.didtName }}
            <text v-if="item.isMust == 1" style="color: red">*</text>
          </view>
          <view class="content">
            <input
              class="uni-input"
              type="number"
              placeholder-style="color:#999999;font-size:13px;"
              placeholder="请输入数字"
              v-if="item.answerLimit == '^d{n}$'"
              v-model="item.checkedAnswer"
              @focus="handleInputFocus(index)"
              @blur="handleInputBlur"
            />
            <textarea
              placeholder-style="color:#999999;font-size:13px;"
              v-else
              :placeholder="item.answerLimit ? '请输入文字' : '请输入文字'"
              :auto-height="false"
              v-model="item.checkedAnswer"
              @focus="handleInputFocus(index)"
              @blur="handleInputBlur"
            />
          </view>
        </view>
        <!-- 下拉题 -->
        <view class="selectView quesAnswerView" v-if="item.didtType == 4">
          <view class="uni-title uni-common-pl didName"
            >{{ index + 1 }}.{{ item.didtName }}
            <text v-if="item.isMust == 1" style="color: red">*</text>
          </view>
          <picker
            @change="bindPickerChange($event, item, index)"
            :value="item.pickerIndex"
            :range="item.answerInfo"
            range-key="optionName"
          >
            <view class="uni-input dateInput" v-if="item.pickerIndex >= 0"
              >{{ item.answerInfo[item.pickerIndex].optionName }}
              <image
                src="../../static/images/question/down.png"
                class="downIcon"
              ></image>
            </view>
            <view class="uni-input dateInput dateInputPlac" v-else
              >请选择答案
              <image
                src="../../static/images/question/down.png"
                class="downIcon"
              ></image>
            </view>
          </picker>
        </view>
        <!-- 上传图片 -->
        <view class="imgView quesAnswerView" v-if="item.didtType == 5">
          <view class="didName"
            >{{ index + 1 }}.{{ item.didtName }}
            <text v-if="item.isMust == 1" style="color: red">*</text>
          </view>
          <view
            class="dateInput"
            @click="selectImg(item, index)"
            v-if="!item.checkedAnswer"
          >
            <image src="../../static/images/up_img.png" class="imgIcon"></image>
            <text>选择图片（2M以内）</text>
            <image
              src="../../static/images/question/image-editor.png"
              class="editIcon"
            ></image>
          </view>
          <view v-if="item.checkedAnswer" @click.stop="selectImg(item, index)">
            <image
              :src="item.checkedAnswer"
              class="upImg"
              mode="aspectFill"
            ></image>
            <image
              src="../../static/images/question/image-delete.png"
              class="delIcon"
              @click.stop="item.checkedAnswer = ''"
            ></image>
          </view>
        </view>
        <!-- 评价 -->
        <view class="contentView quesAnswerView" v-if="item.didtType == 6">
          <view class="didName"
            >{{ index + 1 }}.{{ item.didtName }}
            <text v-if="item.isMust == 1" style="color: red">*</text>
          </view>
          <rate
            @change="rateChange($event, item, index)"
            size="60"
            :star_empty="require('../../static/images/question/assess.png')"
            :star_fill="
              require('../../static/images/question/assess_active.png')
            "
          ></rate>
        </view>
        <!-- 时间 -->
        <view class="timeView quesAnswerView" v-if="item.didtType == 7">
          <view class="didName"
            >{{ index + 1 }}.{{ item.didtName }}
            <text v-if="item.isMust == 1" style="color: red">*</text>
          </view>
          <picker
            mode="time"
            :value="time"
            start="00:00"
            end="23:59"
            @change="bindTimeChange($event, item, index)"
          >
            <view class="uni-input dateInput" v-if="item.checkedAnswer"
              >{{ item.checkedAnswer }}
              <image
                src="../../static/images/question/down.png"
                class="downIcon"
              ></image>
            </view>
            <view class="uni-input dateInput dateInputPlac" v-else
              >请选择时间
              <image
                src="../../static/images/question/down.png"
                class="downIcon"
              ></image>
            </view>
          </picker>
        </view>
        <!-- 日期 -->
        <view class="dateView quesAnswerView" v-if="item.didtType == 8">
          <view class="didName"
            >{{ index + 1 }}.{{ item.didtName }}
            <text v-if="item.isMust == 1" style="color: red">*</text>
          </view>
          <picker
            mode="date"
            :value="date"
            start="1900-01-01"
            end="3030-12-31"
            @change="bindDateChange($event, item, index)"
          >
            <view class="uni-input dateInput" v-if="item.checkedAnswer"
              >{{ item.checkedAnswer }}
              <image
                src="../../static/images/question/down.png"
                class="downIcon"
              ></image>
            </view>
            <view class="uni-input dateInput dateInputPlac" v-else
              >请选择日期
              <image
                src="../../static/images/question/down.png"
                class="downIcon"
              ></image>
            </view>
          </picker>
        </view>
      </view>
    </view>
    <!-- 患者选择弹窗 -->
    <view class="patient-selector-overlay" v-if="showPatientSelector">
      <view class="patient-selector-container" @click.stop>
        <view class="selector-header">
          <view class="selector-title">请选择就诊人</view>
        </view>
        <scroll-view scroll-y class="patient-list-container">
          <view class="patient-list">
            <view
              v-for="(patient, index) in patientList"
              :key="index"
              class="patient-item"
              @click="selectPatient(patient)"
            >
              <text>{{ patient.patientNameStr }}</text>
            </view>
            <view class="patient-item add-new" @click="goToAddPatient">
              <text>添加新就诊人</text>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>
    <!-- 提交成功弹窗 -->
    <view class="success-dialog-overlay" v-if="showSuccessDialog">
      <view class="success-dialog-container">
        <view class="success-dialog-content">
          <view class="success-dialog-message">
            正在为您生成专属营养评估报告...
          </view>
          <view class="success-dialog-message">
            您的专属AI营养师正在根据您刚刚填写的健康与饮食信息进行专业分析和智能解读，确保每一项建议都贴合您的实际情况。
          </view>
          <view class="success-dialog-message">
            感谢您的配合，结果马上就好
          </view>
        </view>
        <view class="success-dialog-footer">
          <button class="success-dialog-btn" @click="handleSuccessConfirm">
            确认
          </button>
        </view>
      </view>
    </view>

    <view class="commit" @click="prepareSubmit">
      <button :disabled="isSubmitting">
        {{ isSubmitting ? '提交中...' : '提交' }}
      </button>
    </view>

    <!-- 语音输入按钮 -->
    <view class="voice-input-fixed">
      <view 
        class="voice-input-btn" 
        :class="{active: isRecording}"
        @click="toggleVoiceInput"
      >
        <image src="/static/images/mic.png" class="mic-icon"></image>
        <text v-if="isRecording" class="recording-text">录音中</text>
      </view>
    </view>
    
    <!-- 隐藏的VoiceCommon组件，只用于处理语音功能 -->
    <view style="position:absolute; opacity: 0; pointer-events: none;">
      <VoiceCommon 
        ref="voiceCommon" 
        :isRealTime="true"
        @getVoice="handleVoiceResult" 
        @init="initVoice"
        :disFlag="false"
      />
    </view>
  </view>
</template>

<script>
import { Toast } from '@/common/js/pay.js'
import {
  getQuestionDetailEmpty,
  saveReplyInquiringDiagnosisAnswerCustom,
  setQuestionRead,
} from '@/api/chatCardDetail'
import {
  getPatientList,
  judgePatientFirstQuestionnaire,
  checkFirstFill,
} from '@/api/user.js'
import rate from '@/components/rate/rate.vue'
import { uploadImg } from '@/api/oss'
import myJsTools from '@/common/js/myJsTools.js'
import {
  invokeWorkflow,
  stopWorkflow,
  generateNutritionReport,
  nutritionStreamGenerate
} from '@/api/qcAi.js'
import { insertRecord, saveOriginal } from '@/api/user.js'
import { mapActions } from 'vuex'
import VoiceCommon from '@/components/VoiceCommon.vue'

export default {
  components: { rate, VoiceCommon },
  data() {
    return {
      pageParam: '',
      saveInfo: '',
      time: '',
      pickerIndex: -1,
      date: '',
      feedbackId: '',
      showPatientSelector: false,
      patientList: [],
      selectedPatient: null,
      showSuccessDialog: false,
      flag: '',
      docId: '',
      projectId: '',
      workflowSessionId: '',
      isWorkflowRunning: false,
      message_id: '',
      input_schema: '',
      lastNodeId: '',
      diagnosis: '',
      isSubmitting: false,
      isLoading: false,
      didOnlyId: '',
      isRecording: false, // 是否在录音中
      focusedIndex: -1, // 当前聚焦的输入框索引
      recorderManager: null, // 录音管理器
      oldMsg: '',
      type: '',
    }
  },
  onLoad(option) {
    this.flag = option.flag
    this.docId = option.docId
    this.projectId = option.projectId
    this.type = option.type
    if (this.type === 'yy') {
      this.didOnlyId = '8bf714f4734246b8a79baeafa797aa99'
    } else if (this.type === 'cd') {
      this.didOnlyId = '273307d950f84e698793f59041d1da5a'
    } else if (this.type === 'jm') {
      this.didOnlyId = '775cee52cd0e4f23985084e3eb058078'
    } else {
      this.didOnlyId = '82ce0d37d66a4f968592f25d0548e2cf'
    }
    this.pageParam = JSON.parse(option.param || '{}')
    
    // 直接获取传递的patientId参数
    if (option.patientId) {
      this.pageParam.patientId = option.patientId
    }
  },
  onShow() {
    this.isSubmitting = false
    this.getPatientList()
    if(!this.pageParam.patientId){
      this.showPatientSelector = true
    }
  },
  created() {
    this.getQuestionDetailEmptyFun()
    this.$nextTick(() => {
      this.$refs.voiceCommon.init()
    })
  },

  methods: {
    ...mapActions({
      createReportAction: 'ai/createReportByAiWorkFlow'
    }),
    initVoice(){
      this.isRecording = false
      uni.showToast({
        title: '连接成功，请点击开始录音',
        icon: 'none'
      })
    },
    // 获取就诊人列表
    async getPatientList() {
      let userId = uni.getStorageSync('userId')
      let { data } = await getPatientList({
        userId,
      })
      if (!data) {
        this.patientList = []
        return
      }
      this.patientList = (data || []).map((v) => {
        return {
          ...v,
          patientNameStr: `${v.patientName}(${v.sex}-${v.age})`,
        }
      })
      
      // 如果有传入的患者ID，直接设置为选中状态
      if (this.pageParam.patientId) {
        const selectedPatient = this.patientList.find(p => p.patientId === this.pageParam.patientId);
        if (selectedPatient) {
          this.selectedPatient = selectedPatient;
        }
      }
    },
 // 选择就诊人
 async selectPatient(patient) {
      console.log(this.flag, '===========flag')
      if (this.flag == 'ai') {
        this.selectedPatient = patient
        this.showPatientSelector = false
      } else {
        this.selectedPatient = patient
        // 校验患者是否已近填过首次问卷
        let { data } = await checkFirstFill({
          userId: uni.getStorageSync('userId'),
          patientId: patient.patientId,
          projectId: this.projectId,
          didOnlyId: this.didOnlyId,
        })
        console.log(data, '===========data')
        if (data) {
          this.showPatientSelector = false
          uni.showToast({
            title: '该就诊人已填写过问卷，即将跳转',
            icon: 'none',
            duration: 1500,
          })
          setTimeout(() => {
            uni.setStorageSync('nutritionFormId', data)
            uni.navigateTo({
              url: `/pages/shop/scan?flag=${this.flag}&docId=${this.docId}&projectId=${this.projectId}&didOnlyId=${this.didOnlyId}`,
            })
          }, 1000)
          return
        }
        this.showPatientSelector = false
      }
    },

    // 前往添加新就诊人页面
    goToAddPatient() {
      uni.navigateTo({
        url: '/pages/personalCenter/patientManage/addPatient/index',
      })
    },
    // 准备提交表单
    prepareSubmit() {
      // 首先验证表单
      let saveInfo = this.saveInfo
      let saveReplyInquiringDiagnosisTopics =
        saveInfo.saveReplyInquiringDiagnosisTopics

      for (let i = 0; i < saveReplyInquiringDiagnosisTopics.length; i++) {
        let el = saveReplyInquiringDiagnosisTopics[i]
        let isMust = el.isMust
        if (isMust == 1) {
          // 如果为必填
          if (!el.checkedAnswer) {
            Toast('第' + el.didtCode + '题为必选项，请选择')
            return
          }
        }
      }
      if (this.isSubmitting) {
        return
      }
      this.isSubmitting = true
      this.saveQuestionAnswerFun()
    },

    // 保存验证
    saveQuestionAnswerFun() {
      if (!this.selectedPatient) {
        this.isSubmitting = false
        // 如果没有选中的患者，则从参数中获取
        if (this.pageParam.patientId) {
          // 再次尝试从列表中获取患者信息
          const patient = this.patientList.find(p => p.patientId === this.pageParam.patientId);
          if (patient) {
            this.selectedPatient = patient;
          } else {
            Toast('无法获取就诊人信息，请返回重试')
            return
          }
        } else {
          Toast('请选择就诊人')
          return
        }
      }

      let saveInfo = this.saveInfo
      let saveReplyInquiringDiagnosisTopics =
        saveInfo.saveReplyInquiringDiagnosisTopics

      for (let i = 0; i < saveReplyInquiringDiagnosisTopics.length; i++) {
        let el = saveReplyInquiringDiagnosisTopics[i]

        if (el.didtType == '1' || el.didtType == '4') {
          // 单选 下拉
          for (let j = 0; j < el.answerInfo.length; j++) {
            if (el.answerInfo[j].optionCode == el.checkedAnswer) {
              el.answerInfo[j].checked = true
              el.value = el.answerInfo[j].optionName
            } else {
              el.answerInfo[j].checked = false
            }
          }
        } else if (el.didtType == '2') {
          // 多选
          for (let j = 0; j < el.answerInfo.length; j++) {
            if (
              el.checkedAnswer.indexOf(
                JSON.stringify(el.answerInfo[j].optionCode)
              ) != -1
            ) {
              el.answerInfo[j].checked = true
            } else {
              el.answerInfo[j].checked = false
            }
          }
          el.value = el.answerInfo
            .filter((item) => item.checked)
            .map((item) => item.optionName)
            .join(',')
        } else if (el.didtType == '3') {
          // 填空
          let reg = new RegExp(el.answerLimit)
          if (el.answerLimitName != '数字' && el.answerLimitName != '文本') {
            if (!reg.test(el.checkedAnswer) && el.checkedAnswer) {
              Toast('第' + el.didtCode + '题答案类型为' + el.answerLimitName)
              this.isSubmitting = false
              return
            }
          }
          el.answerInfo = el.checkedAnswer
          el.value = el.checkedAnswer
        } else {
          el.answerInfo = el.checkedAnswer
          el.value = el.checkedAnswer
        }
      }

      // 设置选择的患者ID
      this.saveInfo.patientId = this.selectedPatient.patientId

      this.onImageUpLoad()
    },

    async onImageUpLoad() {
      uni.showLoading({
        title: '提交中',
        mask: true,
      })
      let data = this.saveInfo.saveReplyInquiringDiagnosisTopics
      let m = false
      for (let j = 0; j < data.length; j++) {
        let el = data[j]
        if (el.didtType == '5' && el.checkedAnswer) {
          await uploadImg({
            folderType: 15,
            imgBody: el.checkedAnswer.split(',')[1],
            otherId: this.pageParam.docId,
          }).then((res) => {
            el.answerInfo = res.data.url
            el.checkedAnswer = res.data.url
            this.$set(this.saveInfo.saveReplyInquiringDiagnosisTopics, j, el)
          })
        }
        if (j == data.length - 1) {
          this.confirmSaveQuestion()
        }
      }
    },
    // 保存量表提交方法
    async confirmSaveQuestion() {
      console.log(this.saveInfo, '===========saveInfo')
      let saveInfo = JSON.parse(JSON.stringify(this.saveInfo))
      let saveReplyInquiringDiagnosisTopics =
        saveInfo.saveReplyInquiringDiagnosisTopics
      for (let i = 0; i < saveReplyInquiringDiagnosisTopics.length; i++) {
        let didtType = saveReplyInquiringDiagnosisTopics[i].didtType
        saveReplyInquiringDiagnosisTopics[i].checkedAnswer =
          saveReplyInquiringDiagnosisTopics[i].checkedAnswer.toString()
        if (didtType == '1' || didtType == '2' || didtType == '4') {
          saveReplyInquiringDiagnosisTopics[i].answerInfo = JSON.stringify(
            saveReplyInquiringDiagnosisTopics[i].answerInfo
          )
        }
      }
      saveInfo.feedbackId = this.feedbackId
      let para = saveInfo
      para.fillingType = this.flag == 'ai' ? '3' : '1'
      para.projectId = this.projectId
      para.docId = this.docId
      try {
        const res = await saveReplyInquiringDiagnosisAnswerCustom(para)
        console.log(res)
        uni.setStorageSync('nutritionFormId', res.data)
        uni.hideLoading()
        // 随机营养评估报告
        if (this.type === 'yy') {
          await this.createSJReportByAiWorkFlow()
          this.isSubmitting = false
          return
        } else if (this.type === 'cd') {
          await this.createCDReportByAiWorkFlow()
          this.isSubmitting = false
          return
        } else if (this.type === 'jm') {
          await this.createJMReportByAiWorkFlow()
          this.isSubmitting = false
          return
        }
        this.showSuccessDialog = true
      } catch (error) {
        uni.hideLoading()
        this.isSubmitting = false
      }
    },
    async createCDReportByAiWorkFlow() {
      uni.$emit('startCDWorkFlow', {
        isAi: true,
        params: this.saveInfo.saveReplyInquiringDiagnosisTopics,
        patient: this.selectedPatient,
      })
      uni.navigateBack({
        delta: 1, //想要返回的层级
      })
    },
    async createJMReportByAiWorkFlow() {
      uni.$emit('startJMWorkFlow', {
        isAi: true,
        params: this.saveInfo.saveReplyInquiringDiagnosisTopics,
        patient: this.selectedPatient,
      })
      uni.navigateBack({
        delta: 1, //想要返回的层级
      })
    },
    // 随机营养评估报告
    async createSJReportByAiWorkFlow() {
      uni.$emit('startWorkFlow', {
        isAi: true,
        params: this.saveInfo.saveReplyInquiringDiagnosisTopics,
        patient: this.selectedPatient,
      })
      console.log(
        '===========createSJReportByAiWorkFlow',
        this.saveInfo.saveReplyInquiringDiagnosisTopics
      )
      uni.navigateBack({
        delta: 1, //想要返回的层级
      })
      return
      uni.showLoading({
        title: '生成报告中...',
        mask: true,
      })
      try {
        // 第一步：发起工作流执行
        const { data } = await invokeWorkflow({
          workflow_id: 'e7a6505cdef14802b10c5597341f7588',
        })
        console.log(data, '===========workflowData')
        const workflowData = data.data
        if (workflowData && workflowData.session_id) {
          this.workflowSessionId = workflowData.session_id
          this.isWorkflowRunning = true
          // 处理工作流返回的事件
          if (workflowData.events && workflowData.events.length > 0) {
            for (const event of workflowData.events) {
              if (event.event === 'guide_word' && event.output_schema) {
                // 显示工作流的引导语
              }
              if (event.event === 'input') {
                this.message_id = event.message_id
                this.input_schema = event.input_schema
                this.lastNodeId = event.node_id
              }
            }
          }
          uni.showLoading({
            title: '生成报告中...',
            mask: true,
          })
          console.log(this.input_schema, '===========input_schema')
          const values = this.saveInfo.saveReplyInquiringDiagnosisTopics
          // 第四步：再次调用工作流，传入完整的聊天记录
          const { data: summaryData } = await invokeWorkflow({
            sessionId: this.workflowSessionId,
            input: {
              [this.lastNodeId]: {
                name: this.selectedPatient.patientName,
                sex: this.selectedPatient.sex,
                age: this.selectedPatient.age,
                height: values[0].value,
                weight: values[1].value,
                month_ago_weight: values[2].value,
                dietary_situation_1month: values[3].value,
                Current_eating_status: values[4].value,
                Reasons_affecting_eating: values[5].value,
              },
            },
            message_id: this.message_id,
          })
          // 处理返回的总结内容
          if (summaryData.data && summaryData.data.events) {
            for (const event of summaryData.data.events) {
              if (event.output_schema && event.output_schema.message) {
                // this.receiveAiMessage(event.output_schema.message);
              }
              if (event.event === 'stream_msg' && event.status === 'end') {
                // 生成的营养评估表报告
                console.log(
                  event.output_schema.message,
                  '===========event.output_schema.message'
                )
                const res = await saveOriginal({
                  reportType: '5',
                  userId: uni.getStorageSync('userId'),
                  docId: this.docId,
                  patientId: this.selectedPatient.patientId,
                  reportContent: event.output_schema.message,
                  ifNeedDocAudit: 0,
                  projectId: this.projectId,
                  patientName: this.selectedPatient.patientName,
                })
                // 保存营养评估表报告
                await insertRecord({
                  wxTitle: '您的AI营养评估报告已生成，请查看',
                  receiverId: uni.getStorageSync('userId'),
                  patientId: this.selectedPatient.patientId,
                  ext: JSON.stringify({
                    reportId: res.data.pkId,
                  }),
                  hxMessage: res.data.pkId,
                })
              }
            }
          }
          // 第五步：停止工作流
          await stopWorkflow({
            sessionId: this.workflowSessionId,
          })
          uni.hideLoading()
          uni.navigateBack({
            delta: 1, //想要返回的层级
          })
        }
      } catch (error) {
        console.error('营养评估表报告生成异常:', error)
        uni.hideLoading()
      } finally {
      }
    },
    async createReportByAiWorkFlow() {
      uni.showLoading({
        title: '生成营养评估表报告中...',
        mask: true,
      })
      try {
        // 将聊天记录转换为字符串格式
        const value = ''

        // 第一步：发起工作流执行
        const { data } = await invokeWorkflow({
          workflow_id: '82b59e1f37ff46958e00e96a4ed39f59',
        })
        console.log(data, '===========workflowData')
        const workflowData = data.data
        if (workflowData && workflowData.session_id) {
          this.workflowSessionId = workflowData.session_id
          this.isWorkflowRunning = true
          // 处理工作流返回的事件
          if (workflowData.events && workflowData.events.length > 0) {
            for (const event of workflowData.events) {
              if (event.event === 'guide_word' && event.output_schema) {
                // 显示工作流的引导语
              }
              if (event.event === 'input') {
                this.message_id = event.message_id
                this.input_schema = event.input_schema
                this.lastNodeId = event.node_id
              }
            }
          }
          console.log(this.input_schema, '===========input_schema')
          const values = this.saveInfo.saveReplyInquiringDiagnosisTopics
          // 请求工作流-后台生成营养评估报告
          await generateNutritionReport({
            sessionId: this.workflowSessionId,
            input: {
              [this.lastNodeId]: {
                name: this.selectedPatient.patientName,
                sex: this.selectedPatient.sex,
                age: this.selectedPatient.age,
                diagnosis: values[0].value,
                height: values[1].value,
                weight: values[2].value,
                weight_1month_before: values[3].value,
                weight_6months_before: values[4].value,
                weight_change: values[5].value,
                appetite_intake: values[6].value,
                diet_type: values[7].value,
                gastrointensitinal_symptoms: values[8].value,
                daily_function: values[9].value,
              },
            },
            message_id: this.message_id,
            workflow_id: '82b59e1f37ff46958e00e96a4ed39f59',
            stream: false,
            docId: this.docId,
            patientId: this.selectedPatient.patientId,
            projectId: this.projectId,
            szAiReportVo: {
              reportType: '5',
              userId: uni.getStorageSync('userId'),
              docId: this.docId,
              patientId: this.selectedPatient.patientId,
              reportContent: '',
              ifNeedDocAudit: 0,
              projectId: this.projectId,
              patientName: this.selectedPatient.patientName,
            },
          })
          // 第四步：再次调用工作流，传入完整的聊天记录
          //  const { data: summaryData } = await invokeWorkflow({
          //   sessionId: this.workflowSessionId,
          //   input: {
          //     [this.lastNodeId]: {
          //       name: this.selectedPatient.patientName,
          //       sex: this.selectedPatient.sex,
          //       age: this.selectedPatient.age,
          //       diagnosis: values[0].value,
          //       height: values[1].value,
          //       weight: values[2].value,
          //       weight_1month_before: values[3].value,
          //       weight_6months_before: values[4].value,
          //       weight_change: values[5].value,
          //       appetite_intake: values[6].value,
          //       diet_type: values[7].value,
          //       gastrointensitinal_symptoms: values[8].value,
          //       daily_function: values[9].value
          //     }
          //   },
          //   message_id: this.message_id
          // });
          // // 处理返回的总结内容
          // if (summaryData.data && summaryData.data.events) {
          //   for (const event of summaryData.data.events) {
          //     if (event.output_schema && event.output_schema.message) {
          //       // this.receiveAiMessage(event.output_schema.message);
          //     }
          //     if (event.event === 'stream_msg' && event.status === 'end') {
          //       // 生成的营养评估表报告
          //       console.log(event.output_schema.message, "===========event.output_schema.message");
          //       // 保存营养评估表报告
          //       const res = await saveOriginal({
          //         reportType: '5',
          //         userId: uni.getStorageSync('userId'),
          //         docId: this.docId,
          //         patientId: this.selectedPatient.patientId,
          //         reportContent: event.output_schema.message,
          //         ifNeedDocAudit: 0,
          //         projectId: this.projectId
          //       });
          //       // 保存营养评估表报告
          //       await insertRecord({
          //         wxTitle: '患者' + this.selectedPatient.patientName + '的营养评估报告已生成，请查看',
          //         receiverId: this.docId,
          //         ext: JSON.stringify({
          //           reportId: res.data.pkId,
          //           reportType: '5',
          //           qcStatus: '6'
          //         }),
          //         hxMessage: res.data.pkId
          //       });
          //       await insertRecord({
          //         wxTitle: '您的营养评估报告已生成，请查看',
          //         receiverId: uni.getStorageSync('userId'),
          //         ext: JSON.stringify({
          //           reportId: res.data.pkId,
          //           reportType: '5',
          //           qcStatus: '6'
          //         }),
          //         hxMessage: res.data.pkId
          //       });
          //     }
          //   }
          // }
          // 第五步：停止工作流
          // await stopWorkflow({
          //   sessionId: this.workflowSessionId
          // });
          uni.hideLoading()
        }
      } catch (error) {
        console.error('营养评估表报告生成异常:', error)
        uni.hideLoading()
      } finally {
      }
    },
    // 单选选择赋值
    radioChange(evt, item, index) {
      item.checkedAnswer = parseInt(evt.detail.value)
      this.$set(this.saveInfo.saveReplyInquiringDiagnosisTopics, index, item)
    },
    // 多选选择赋值
    checkboxChange(evt, item, index) {
      item.checkedAnswer = evt.detail.value
      this.$set(this.saveInfo.saveReplyInquiringDiagnosisTopics, index, item)
    },
    // 下拉选择
    bindPickerChange(evt, item, index) {
      this.pickerIndex = evt.detail.value
      item.checkedAnswer = item.answerInfo[evt.detail.value].optionCode
      item.pickerIndex = evt.detail.value
      this.$set(this.saveInfo.saveReplyInquiringDiagnosisTopics, index, item)
    },
    // 评分
    rateChange(evt, item, index) {
      item.checkedAnswer = evt.value
      this.$set(this.saveInfo.saveReplyInquiringDiagnosisTopics, index, item)
    },
    // 时间选择
    bindTimeChange(evt, item, index) {
      item.checkedAnswer = evt.detail.value
      this.$set(this.saveInfo.saveReplyInquiringDiagnosisTopics, index, item)
    },
    // 日期选择
    bindDateChange(evt, item, index) {
      console.log(evt.detail.value,'evt.detail.value')
      // 不能选未来时间
      if(new Date(evt.detail.value) > new Date()) {
        uni.showToast({
          title: '不能选未来时间',
          icon: 'none'
        })
        return
      }
      item.checkedAnswer = evt.detail.value
      this.$set(this.saveInfo.saveReplyInquiringDiagnosisTopics, index, item)
    },
    // 图片选择
    selectImg(item, index) {
      let _this = this
      uni.chooseImage({
        count: 1, //默认9
        sizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有
        success: function (res) {
          for (let i = 0; i < res.tempFiles.length; i++) {
            let el = res.tempFiles[i]
            myJsTools.setImgZip(el, (dataUrl) => {
              item.checkedAnswer = dataUrl
              _this.$set(
                _this.saveInfo.saveReplyInquiringDiagnosisTopics,
                index,
                item
              )
            })
          }
        },
      })
    },
    // 获取量表题目
    async getQuestionDetailEmptyFun() {
      let res = await getQuestionDetailEmpty({ didOnlyId: this.didOnlyId })
      let info = res.data
      let saveInfo = {
        didId: info.didId,
        didName: info.didName,
        didOnlyId: info.didOnlyId || this.didOnlyId,
        docId: info.docId || this.docId,
        docName: info.docName || '',
        patientId: '',
        saveReplyInquiringDiagnosisTopics: [],
      }
      // #ifdef H5
      document.title = saveInfo.didName
      // #endif
      uni.setNavigationBarTitle({
        title: saveInfo.didName,
      })
      let replyInquiringDiagnosisTopics = []
      for (let i = 0; i < info.docQuestionListVO.length; i++) {
        let obj = info.docQuestionListVO[i]
        let seveInfoList = {}
        seveInfoList.answerInfo = JSON.parse(obj.optionInfo) || ''
        seveInfoList.checkedAnswer = ''
        seveInfoList.didtCode = obj.didtCode
        seveInfoList.didtId = obj.didtId
        seveInfoList.didtName = obj.didtName
        seveInfoList.didtType = obj.didtType
        seveInfoList.answerLimit = obj.answerLimit
        seveInfoList.answerLimitName = obj.answerLimitName
        seveInfoList.isMust = obj.isMust
        seveInfoList.pickerIndex = -1
        replyInquiringDiagnosisTopics.push(seveInfoList)
      }
      saveInfo.saveReplyInquiringDiagnosisTopics = replyInquiringDiagnosisTopics
      this.saveInfo = Object.assign({}, saveInfo)
    },
    handleSuccessConfirm() {
      if(this.flag!=='ai'){
        // 调用Vuex中的action生成报告
        this.$store.dispatch('ai/createReportByAiWorkFlow', {
          patientInfo: this.selectedPatient,
          docId: this.docId,
          patientId: this.selectedPatient.patientId,
          projectId: this.projectId,
          valuesData:{
            name: this.selectedPatient.patientName,
            sex: this.selectedPatient.sex,
            age: this.selectedPatient.age,
            diagnosis: this.saveInfo.saveReplyInquiringDiagnosisTopics[0].value,
            height: this.saveInfo.saveReplyInquiringDiagnosisTopics[1].value,
            weight: this.saveInfo.saveReplyInquiringDiagnosisTopics[2].value,
            weight_1month_before: this.saveInfo.saveReplyInquiringDiagnosisTopics[3].value,
            weight_6months_before: this.saveInfo.saveReplyInquiringDiagnosisTopics[4].value,
            weight_change: this.saveInfo.saveReplyInquiringDiagnosisTopics[5].value,
            appetite_intake: this.saveInfo.saveReplyInquiringDiagnosisTopics[6].value,
            diet_type: this.saveInfo.saveReplyInquiringDiagnosisTopics[7].value,
            gastrointensitinal_symptoms: this.saveInfo.saveReplyInquiringDiagnosisTopics[8].value,
            daily_function: this.saveInfo.saveReplyInquiringDiagnosisTopics[9].value,
          },
          szAiReportVo: {
            reportType: '5',
            userId: uni.getStorageSync('userId'),
            docId: this.docId,
            patientId: this.selectedPatient.patientId,
            reportContent: '',
            ifNeedDocAudit: 0,
            projectId: this.projectId,
            patientName: this.selectedPatient.patientName,
          },
          workflow_id: '82b59e1f37ff46958e00e96a4ed39f59',
          callback: () => {
            this.showSuccessDialog = true
            this.isSubmitting = false
            const loadingMsg = {
              content: '点击购药',
              sender: 'ai',
              timestamp: new Date().toLocaleString(),
            };
            setTimeout(() => {
              this.$store.commit('ai/ADD_MESSAGE_LIST', loadingMsg);
            }, 2000)
          }
        })
        uni.navigateBack({
          delta: 1, //想要返回的层级
        })
        this.showSuccessDialog = false
        return
      }
      this.showSuccessDialog = false
      uni.navigateTo({
        url: `/pages/shop/scan?flag=${this.flag}&docId=${this.docId}&projectId=${this.projectId}&didOnlyId=${this.didOnlyId}`,
      })
    },
    // 处理输入框聚焦
    handleInputFocus(index) {
      this.focusedIndex = index;
      this.oldMsg = this.saveInfo.saveReplyInquiringDiagnosisTopics[index].checkedAnswer;
       // 如果正在录音，则停止
       this.isRecording = false;
        if (this.$refs.voiceCommon) {
          this.$refs.voiceCommon.stopRecord({
            changedTouches: [{ clientY: this.$refs.voiceCommon.touchStartY }]
          });
        }
    },
    
    // 处理输入框失焦
    handleInputBlur() {
      // 延迟设置失焦，避免点击语音按钮时就失去焦点
      // setTimeout(() => {
      //   this.focusedIndex = -1;
      // }, 200);
    },
    
    // 切换语音输入状态
    toggleVoiceInput() {
      if(this.focusedIndex === -1) {
        uni.showToast({
          title: '请先选择题目',
          icon: 'none'
        })
        return
      }
      if (this.isRecording) {
        // 如果正在录音，则停止
        this.isRecording = false;
        if (this.$refs.voiceCommon) {
          this.$refs.voiceCommon.stopRecord({
            changedTouches: [{ clientY: this.$refs.voiceCommon.touchStartY }]
          });
        }
      } else {
        // 开始录音
        this.isRecording = true;
        
        // 使用VoiceCommon组件的方法开始录音
        if (this.$refs.voiceCommon) {
          this.$refs.voiceCommon.startRecord({
            touches: [{ clientY: 0 }]
          });
        }
      }
    },
    
    // 处理语音识别结果
    handleVoiceResult(text) {
      console.log(text,'handleVoiceResult',this.focusedIndex)
      if (this.focusedIndex >= 0) {
        const item = this.saveInfo.saveReplyInquiringDiagnosisTopics[this.focusedIndex];
        
        // 设置语音识别结果到输入框
        item.checkedAnswer = this.oldMsg + text;
        this.$set(this.saveInfo.saveReplyInquiringDiagnosisTopics, this.focusedIndex, item);
        
        // 校验输入内容
        // this.validateInputContent(item);
        
        // 重置录音状态
        // this.isRecording = false;
      }
    },
    
    // 校验输入内容
    validateInputContent(item) {
      if (!item.checkedAnswer) return;
      
      // 如果有输入限制，进行校验
      if (item.answerLimit && item.answerLimit !== '^d{n}$' && item.answerLimit !== '文本') {
        let reg = new RegExp(item.answerLimit);
        if (!reg.test(item.checkedAnswer)) {
          Toast(`输入内容类型需为${item.answerLimitName}`);
        }
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.header {
  background: #ffffff;
  margin: 24rpx 32rpx 0px;
  font-size: 28rpx;
  padding: 32rpx 24rpx;
  box-shadow: 0 0 20rpx rgba(198, 204, 206, 0.2);
  border-radius: 16rpx;
  .title {
    color: #333333;
    line-height: 44rpx;
  }
  .tips {
    color: #999999;
    line-height: 36rpx;
    margin-top: 10rpx;
  }
}

// 评分
/deep/ .rate-media-cell {
  @include flex;
}

/deep/ .uni-radio-input.uni-radio-input-checked:before {
  content: '';
  width: 20upx;
  height: 20upx;
  border-radius: 10upx;
  display: inline-block;
  @include bg_theme;
}

/deep/ .uni-radio-input-checked {
  background: none !important;
}

.quesView {
  padding: 0 32rpx 120rpx;
  .quesAnswerView {
    margin-top: 24rpx;
    background: #ffffff;
    font-size: 28rpx;
    padding: 32rpx 24rpx;
    box-shadow: 0 0 20rpx rgba(198, 204, 206, 0.2);
    border-radius: 16rpx;
    .didName {
      color: #333333;
      font-weight: 600;
      line-height: 44rpx;
    }
    text {
      color: #666666;
      font-size: 26rpx;
      line-height: 40rpx;
    }
    /deep/uni-radio,
    uni-checkbox {
      margin-top: 16px;
    }

    /deep/uni-checkbox .uni-checkbox-input.uni-checkbox-input-checked:before {
      font-size: 23px;
      font-weight: 800;
    }
  }
  .contentView {
    /deep/.uni-textarea-textarea {
      font-size: 26rpx;
      line-height: 36rpx;
    }
    .content {
      border: 1px solid #d5d5d5;
      margin-top: 24rpx;
      padding: 24rpx;

      textarea {
        width: 100%;
        height: 124upx;
      }
    }
  }
  .dateView,
  .timeView,
  .selectView,
  .imgView {
    .dateInput {
      border: 1px solid #d5d5d5;
      margin-top: 24rpx;
      color: $k-title;
      padding: 20rpx 24rpx;
      position: relative;
      font-size: 28rpx;
    }
    .downIcon {
      width: 32rpx;
      height: 32rpx;
      position: absolute;
      right: 24rpx;
    }
    .dateInputPlac {
      color: $k-info-title;
    }
  }
  .imgView {
    .dateInput {
      @include flex(left);
      text {
        color: $k-info-title;
      }
    }
    .imgIcon {
      width: 32rpx;
      height: 34rpx;
      margin-right: 12upx;
    }
    .editIcon {
      width: 32rpx;
      height: 32rpx;
      position: absolute;
      right: 24rpx;
    }
  }
}
.commit {
  position: fixed;
  bottom: 0;
  width: 100%;
  height: 108upx;
  padding: 0 32upx;
  @include flex;
  border-radius: 16upx 16upx 0 0;
  background-color: #fff;
  box-sizing: border-box;

  button {
    width: 100%;
    height: 84upx;
    border-radius: 42upx;
    @include flex;
    font-size: 32upx;
    color: #fff;
    @include bg_theme;
  }
}

.imgView {
  position: relative;
  .upImg {
    margin-top: 24rpx;
    width: 80%;
  }
  .delIcon {
    width: 32rpx;
    height: 32rpx;
    position: absolute;
    right: 60rpx;
    top: 80rpx;
  }
}

/* 患者选择弹窗样式 */
.patient-selector-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.patient-selector-container {
  width: 80%;
  background-color: #fff;
  border-radius: 10px;
  padding: 20px;
  
  .selector-header {
    margin-bottom: 15px;
    
    .selector-title {
      color: #333;
      font-size: 16px;
      font-weight: bold;
      padding: 10px 0;
      border-bottom: 1px solid #ececec;
      text-align: center;
    }
  }
  
  .patient-list-container {
    height: 300px;
    overflow: hidden;
  }
  
  .patient-list {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    padding: 0 5px;
    
    .patient-item {
      width: 48%;
      background-color: #f9f6ff;
      border-radius: 8px;
      padding: 15px 10px;
      margin-bottom: 10px;
      text-align: center;
      
      text {
        font-size: 14px;
        color: #333;
      }
      
      &.add-new {
        background-color: #f9f9f9;
        border: 1px dashed #ccc;
        
        text {
          color: #666;
        }
      }
      
      &:active {
        opacity: 0.8;
      }
    }
  }
}

/* 提交成功弹窗样式 */
.success-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.success-dialog-container {
  width: 80%;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  padding: 30rpx;
}

.success-dialog-content {
  text-align: left;
}

.success-dialog-title {
  font-size: 28rpx;
  font-weight: normal;
  color: #333;
  margin-bottom: 15rpx;
}

.success-dialog-option {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
  margin-left: 10rpx;
}

.success-dialog-message {
  color: #666;
  font-size: 30rpx;
  line-height: 1.5;
  margin: 25rpx 0;
  padding: 0 10rpx;
}

.success-dialog-footer {
  margin-top: 30rpx;
  text-align: center;
}

.success-dialog-btn {
  width: 50%;
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 40rpx;
  background-color: #6e61fd;
  color: #fff;
  font-size: 30rpx;
  text-align: center;
}

// 固定在底部的语音输入按钮
.voice-input-fixed {
  position: fixed;
  bottom: 130rpx; // 位于提交按钮上方
  right: 40rpx;
  z-index: 999;
  
  .voice-input-btn {
    width: 80rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f5f5;
    border-radius: 50%;
    padding: 15rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.2);
    border: 1px solid #6e61fd;
    
    &.active {
      width: 100% !important;
      padding: 15rpx 50rpx 15rpx 25rpx;
      border-radius: 40rpx;
    }
    
    .mic-icon {
      width: 40rpx;
      height: 40rpx;
    }
    
    .recording-text {
      font-size: 26rpx;
      color: #333;
      margin-left: 10rpx;
    }
  }
}
</style>
