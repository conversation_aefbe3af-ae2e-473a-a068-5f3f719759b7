<template>
  <!-- 搜索列表 -->
  <view class="search_list">
    <!-- 顶部 -->
    <view class="header">
      <!-- 搜索 -->
      <view class="search">
        <u-icon name="arrow-left" color="rgba(51, 51, 51, 1)" size="40" style="margin-right: 8px" @click="goBack"></u-icon>
        <view class="cont">
          <uni-icons type="search" size="20" color="#C1C1C1"></uni-icons>
          <view class="cont_text">
            <input
              type="text"
              v-model="search"
              @confirm="getList"
              confirm-type="search"
              maxlength="20"
              class="text"
              placeholder="搜索您想要购买的商品"
            />
            <image
              src="/static/images/search/btn_del.png"
              class="icon_del"
              v-show="search"
              @click="search = ''"
            />
          </view>
          <view class="but" @click="onSearch">搜索</view>
        </view>
      </view>

      <!-- 筛选条件 -->
      <view class="search_screen">
        <!-- 综合 -->
        <view class="item" :class="{ act: isDefault }" @click="setQuery(1)">
          <text>综合</text>
        </view>
        <!-- 销量排序 -->
        <view
          class="item"
          :class="{ act: saleVolume > -1 }"
          @click="setQuery(2)"
        >
          <text>销量排序</text>
          <!-- 未选择 -->
          <image
            mode="aspectFill"
            v-show="saleVolume == -1"
            class="icon"
            src="/static/shop/jt.png"
          />
          <!-- 已选择 -->
          <image
            mode="aspectFill"
            class="icon"
            :class="{ act: saleVolume }"
            v-show="saleVolume > -1"
            src="/static/shop/qh.png"
          />
        </view>
        <!-- 价格排序 -->
        <view class="item" :class="{ act: price > -1 }" @click="setQuery(3)">
          <text>价格排序</text>
          <!-- 未选择 -->
          <image
            mode="aspectFill"
            v-show="price == -1"
            class="icon"
            src="/static/shop/jt.png"
          />
          <!-- 已选择 -->
          <image
            mode="aspectFill"
            class="icon"
            :class="{ act: price }"
            v-show="price > -1"
            src="/static/shop/qh.png"
          />
        </view>
        <!-- 仅有货 -->
        <view class="item" :class="{ act: isOnly }" @click="setQuery(4)">
          <text>仅有货</text>
          <view class="check">
            <uni-icons type="checkmarkempty" size="16" color="#fff"></uni-icons>
          </view>
        </view>
      </view>
    </view>

    <!-- 药品列表 -->
    <view class="list_drugs" v-if="list.length">
      <scroll-view
          class="scroll"
          scroll-with-animation
          scroll-y="true"
          @scrolltolower="loadMoreData"
      >
        <DRUG
            :item="item"
            :index="index"
            v-for="(item, index) in list"
            :key="index"
            @reduce="reduce"
            @add="add"
        />
      </scroll-view>
      <!-- 到底 -->
      <view class="buttom">已经到底了</view>
    </view>

    <!-- 空状态 -->
    <view class="empty" v-else>
      <image
        src="/static/images/index_my/empty-appoint.png"
        mode="aspectFill"
      ></image>
      <text>未能搜索到相关信息</text>
    </view>

    <!-- 悬浮 -->
<!--    <view class="round" :class="{ act: drugNum }" @click="setShowCart">-->
<!--      <image v-show="!drugNum" src="/static/shop/car.png"></image>-->
<!--      <view class="show" v-show="drugNum">-->
<!--        <image src="/static/shop/cart_act.png"></image>-->
<!--        <text>去结算</text>-->
<!--      </view>-->

<!--      <text class="num" v-show="drugNum">{{ drugNum }}</text>-->
<!--    </view>-->

    <CART
      @itemChange="itemChange"
      @clear="getList"
      v-show="showCart"
      ref="cart"
      @close="showCart = false"
    />
    <view class="shopBottom">
<!--      <image  src="/static/shop/car.png"></image>-->
<!--      <div>-->
<!--        <view>我的购物车</view>-->
<!--      </div>-->
      <view class="goToCheckout" @click="setShowCart">
        去结算
      </view>
    </view>
  </view>
</template>

<script>
import { onlineDrugPurchaseQueryByLatLng } from '@/api/shop.js';
import { mapActions, mapGetters } from 'vuex';
import DRUG from '../components/newDrugItem.vue';
import CART from '../components/cart.vue';
export default {
  components: {
    DRUG,
    CART,
  },
  data() {
    return {
      // 显示购物车
      showCart: false,
      search: '',
      // 默认 综合
      isDefault: true,
      // 销量
      saleVolume: -1,
      // 价格
      price: -1,
      // 仅有货
      isOnly: false,
      // 列表
      list: [],
      // 地址
      location: {},
      listquery:{
        page:1,
        limit:50
      },
      totalNum:0,
    };
  },
  onLoad(opt) {
    this.search = opt.str || '';
    this.location = uni.getStorageSync('shop_city') || {};
    this.listquery.page=1
    this.getList();
  },
  onReachBottom() {},
  computed: {
    ...mapGetters(['drugNum', 'shopList',"checkStore"]),
  },
  methods: {
    ...mapActions('shop', ['addDrugItem', 'reduceDrugItem']),
    goBack(){
      uni.navigateBack({
        delta: 1
      });
    },
    // 显示购物车
    setShowCart() {
      if (!this.drugNum) return;
      this.showCart = true;
      this.$refs.cart.showList();
    },
    // 药品减少
    async reduce(item, index) {
      await this.reduceDrugItem(item);
      item.quan--;
      if (item.quan <= 0) item.quan = 0;
      this.$set(this.list, index, item);
    },
    // 药品增加
    async add(item, index) {
      await this.addDrugItem(item);
      item.quan++;
      this.$set(this.list, index, item);
    },
    // 购物车药品变化
    itemChange(item) {
      // 查找当前列表指定药品的下标
      const index = this.list.findIndex((v) => v.yfkcId == item.yfkcId);
      if (index == -1) return;
      let it = this.list[index];
      it.quan = item.quan;
      // 修改当前药品数量
      this.$set(this.list, index, it);
    },
    loadMoreData()  {
      if (this.list.length >= this.totalNum) return;
      this.listquery.page += 1;
      this.getList();
    },
    onSearch(){
      this.listquery.page=1
      this.list=[]
      this.getList()
    },
    // 搜索
    async getList() {
      let str = this.search.trim();
      // if(!str) return;
      let obj = {
        // 条件
        searchCondition: str,
        // 是否仅有货
        inStock: this.isOnly ? 1 : 0,
        ...this.listquery,
        // drugstoreId: this.checkStore.drugstoreId,
      };
      if (this.saleVolume > -1) {
        obj.salesOrder = this.saleVolume;
      }
      if (this.price > -1) {
        obj.priceOrder = this.price;
      }

      const { lat, lng } = this.location;

      obj.latitude = lat;
      obj.longitude = lng;

      let res = await onlineDrugPurchaseQueryByLatLng(obj);
      let total = res.data.total;
      this.totalNum = total;
      if (this.listquery.page > 1) {
        this.list = [...this.list, ...res.data.rows];
      } else {
        this.list = res.data.rows;
      }
      // this.list = res.data;

      uni.pageScrollTo({
        scrollTop: 0,
        duration: 300,
      });
    },
    // 设置筛选条件
    setQuery(n) {
      this.listquery.page=1
      this.list=[]
      switch (n) {
        // 点击综合
        case 1:
          // 如果本身已激活
          // if (this.isDefault) return;
          this.isDefault = true;
          this.saleVolume = -1;
          this.price = -1;
          break;
        // 点击销量
        case 2:
          // 如果未激活
          if (this.saleVolume < 0) {
            this.isDefault = false;
            // 升序
            this.saleVolume = 0;
          } else if (this.saleVolume) {
            this.saleVolume = 0;
          } else {
            this.saleVolume = 1;
          }
          break;
        // 点击价格
        case 3:
          // 如果未激活
          if (this.price < 0) {
            this.isDefault = false;
            // 升序
            this.price = 0;
          } else if (this.price) {
            this.price = 0;
          } else {
            this.price = 1;
          }
          break;
        // 点击有货
        case 4:
          this.isOnly = !this.isOnly;
          break;
        default:
          break;
      }
      this.getList();
    },
  },
};
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}
.shopBottom{
  width: 100%;
  height: 140rpx;
  opacity: 1;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px -2px 10px 2px rgba(0, 0, 0, 0.05);
  position: fixed;
  bottom: 0;
  left: 0;
  display: flex;
  align-items: center;
  padding: 0 20px;
  justify-content: space-between;
}
.goToCheckout{
  //width: 261px;
  width: 100%;
  height: 34px;
  opacity: 1;
  border-radius: 457px;
  background: linear-gradient(145.74deg, rgba(124, 103, 230, 1) 0.14%, rgba(197, 187, 242, 1) 100%);
  text-align: center;
  color: white;
  line-height: 34px;
}
.search_list {
  min-height: 100vh;
  padding-bottom: 140rpx;
  background-color: rgba(243, 243, 246, 1);

  .header {
    position: sticky;
    top: 0;
    z-index: 2;
    background-color: rgba(243, 243, 246, 1);
  }

  .search {
    height: 88rpx;
    padding: 0 24rpx;
    @include flex;
    flex: none;
    justify-content: left !important;
    .cont {
      width: 80%;
      padding: 0 24rpx;
      height: 60rpx;
      border-radius: 30rpx;
      background-color: #ffffff;
      @include flex(lr);

      .cont_text {
        flex: 1;
        padding: 0 20rpx;
        @include flex;

        .text {
          font-size: 28rpx;
          flex: 1;
        }

        .icon_del {
          width: 38upx;
          height: 38upx;
          flex: none;
        }
      }

      .but {
        font-size: 28rpx;
        @include font_theme;
      }
    }
  }

  .search_screen {
    height: 84rpx;
    @include flex;
    border-bottom: 1px solid #eee;

    .item {
      flex: 1;
      @include flex;
      font-size: 28rpx;
      color: #333;

      &.act {
        @include font_theme;

        .check {
          @include bg_theme;
          border: none;
        }
      }

      .icon {
        width: 32rpx;
        height: 32rpx;
        transform: rotate(180deg);

        &.act {
          transform: rotate(0deg);
        }
      }

      .check {
        width: 32rpx;
        height: 32rpx;
        @include flex;
        margin-left: 8rpx;
        border: 1px solid #bbb;
        border-radius: 4rpx;
      }
    }
  }

  .list_drugs {
    padding: 24rpx;
    border-top-left-radius: 32px !important;
    border-top-right-radius: 32px !important;
    background: white;
    height: 700px;
    .buttom {
      font-size: 24rpx;
      color: #999;
      text-align: center;
      padding: 10rpx 0;
    }
  }

  .empty {
    width: 100%;
    @include flex;
    flex-direction: column;
    padding-top: 100rpx;

    text {
      font-size: 28rpx;
      color: #999;
      margin-top: 30rpx;
    }
  }

  .round {
    min-width: 88rpx;
    height: 88rpx;
    position: fixed;
    bottom: 154rpx;
    z-index: 2;
    right: 24rpx;
    border-radius: 44rpx;
    background-color: #fafafa;
    @include flex;
    box-shadow: 0 0 20rpx #ddd;

    &.act {
      background-color: #e2f0f7;
    }

    image {
      width: 64rpx;
      height: 64rpx;
    }

    .num {
      min-width: 30rpx;
      height: 30rpx;
      padding: 0 4rpx;
      background-color: #ff5050;
      @include flex;
      font-size: 24rpx;
      color: #fff;
      border-radius: 16rpx;
      line-height: 24rpx;
      position: absolute;
      right: 0;
      top: 0;
    }

    .show {
      @include flex;
      padding: 0 24rpx;

      image {
        width: 44rpx;
        height: 44rpx;
        margin-right: 8rpx;
      }

      text {
        font-size: 28rpx;
        @include font_theme;
      }
    }
  }
}
.scroll{
  height: 100%;
}
</style>
