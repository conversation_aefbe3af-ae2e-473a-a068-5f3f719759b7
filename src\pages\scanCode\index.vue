<template>
  <!-- 扫码开方 -->
  <view class="scan_code">
    <!-- 选择患者 -->
    <view class="sele_warp">
      <view class="sele_but" @click="toPatient" v-show="!patientInfo">
        <text>请选择就诊人</text>
        <uni-icons type="arrowright" color="#333" size="20"></uni-icons>
      </view>

      <!-- 患者信息 -->
      <view class="user_info" v-show="patientInfo" @click="toPatient">
        <!-- 头像 -->
        <img
          v-img="patientInfo.patientImg"
          v-if="patientInfo.patientImg"
          data-src="/static/images/docHead.png"
          alt=""
          class="user_head"
        />
        <image
          src="/static/images/docHead.png"
          v-else
          alt=""
          class="user_head"
        />
        <!-- 信息 -->
        <view class="user_desc">
          <view class="user_name">{{ patientInfo.patientName }}</view>
          <view class="user_other" v-if="patientInfo.sexCode">
            <image
              v-show="patientInfo.sexCode == 1"
              src="/static/shop/nan.png"
            ></image>
            <image
              v-show="patientInfo.sexCode == 2"
              src="/static/shop/nv.png"
            ></image>
            <text>{{ patientInfo.age }}岁</text>
          </view>
        </view>
        <!-- 箭头 -->
        <uni-icons type="arrowright" color="#333" size="20"></uni-icons>
      </view>

      <view class="sele_info">
        <text>提示</text>
        <view class="info_text">请选择需使用药品的患者</view>
      </view>
    </view>

    <!-- 药品 -->
    <view class="durg_list">
      <!-- 单个药品 -->
      <view class="durg_item" v-for="(drug, d) in list" :key="drug.drugId">
        <img
          v-if="drug.drugImg"
          v-img="drug.drugImg"
          :data-src="errUrl"
          class="left"
        />
        <image v-else class="left" src="/static/shop/drug.png"></image>
        <!-- 内容 -->
        <view class="right">
          <!-- 药品名称 -->
          <view class="drug_name">{{ drug.drugName }}</view>
          <!-- 规格 -->
          <view class="drug_info">规格: {{ drug.gg }}</view>
          <!-- 优惠 -->
          <view class="drug_red" v-if="drug.activeName"
            >单品{{ drug.activeName }}</view
          >
          <!-- 价位数量 -->
          <view class="right_menu">
            <view class="price"
              >￥{{ drug.realMoney || drug.price | toFixed }}
              <text class="del" v-if="drug.realMoney != drug.shouldMoney"
                >￥{{ drug.shouldMoney | toFixed }}</text
              >
            </view>
            <text class="num">x{{ drug.quan }}</text>
          </view>
        </view>
      </view>

      <!-- 统计 -->
      <view class="count">
        <!-- <text class="num">共{{ list.length }}种药品</text> -->
        <view class="count_price">
          合计:<text>￥{{ shouldMoney | toFixed }}</text>
        </view>
        <view class="count_price">
          优惠:<text>￥{{ discount_price | toFixed }}</text>
        </view>
        <view class="count_price">
          实付:<text>￥{{ durg_price | toFixed }}</text>
        </view>
      </view>
    </view>

    <!-- 可选择自提或物流 -->
    <view class="sele_warp">
      <view class="sele_but" @click="seleType" v-show="true">
        <text @click.stop="showTips"
          >请选择配送方式
          <uni-icons type="help" color="#333" size="14"></uni-icons>
        </text>
        <view class="right">
          <text v-if="delivery == 0">统一配送</text>
          <text v-if="delivery == 1">自提</text>
          <uni-icons type="arrowright" color="#333" size="20"></uni-icons>
        </view>
      </view>

      <!-- 物流选择 -->
      <view class="logistics" v-if="delivery == 0 && LogisticsCostList.length">
        <block v-for="(item, index) in LogisticsCostList">
          <view class="item" @click="setLogistType(index)" :key="index">
            <text class="name">{{ item.logisticsName }}</text>
            <view class="right">
              <text>￥{{ item.logisticsCost }}</text>
              <image
                src="/static/shop/sele_act.png"
                v-if="logisticsType == index"
              ></image>
              <image src="/static/shop/sele.png" v-else></image>
            </view>
          </view>
        </block>
      </view>

      <!-- 配送 -->
      <ADDRESS
        v-if="delivery == 0 && address"
        :detail="address"
        @click="seleType"
      />

      <!-- 自提 -->
      <PHARMACY
        showRight
        v-if="delivery == 1 && drugDetail"
        :detail="drugDetail"
        @click="seleType"
      />

      <view class="sele_info">
        <text>提示</text>
        <view class="info_text"
          >不同的配送方式，价格会有差异，请以实际支付为标准</view
        >
      </view>
    </view>

    <!-- 底部 -->
    <view class="footer">
      <view class="count">
        合计：<text>￥{{ total | toFixed }}</text>
      </view>
      <!-- 按钮 -->
      <view class="right">
        <view class="but pay" @click="send(1)">好友付</view>
        <view class="but" @click="send(0)">去支付</view>
      </view>
    </view>

    <!-- 弹框 -->
    <uni-popup ref="popup" type="bottom">
      <view class="pop">
        <view class="pop_title">
          <text>{{ tip.tipsTitle }}</text>
          <uni-icons type="closeempty" @click="hideTip"></uni-icons>
        </view>
        <view class="pop_text">
          {{ tip.tipsContent }}
        </view>
      </view>
    </uni-popup>

    <!-- 支付提示 -->
    <PROPCENTER
      v-if="showPayTip"
      :type="2"
      @cancelPropCenter="showPayTip = false"
      @confirmPropCenter="setParam"
    >
      <view class="pay_tip">
        <view class="title">{{ payTip.tipsTitle }}</view>
        <view class="cont" v-html="payTip.tipsContent"></view>
      </view>
    </PROPCENTER>

    <!-- 引导 -->
    <Tips v-show="showTip" @click="showTip = false" />
  </view>
</template>

<script>
import { queryToPayTips, queryMallOrderStatus } from '@/api/shop.js';

import { getDrugStoragePrice, getLogisticsCostList } from '@/api/cf';

import { getReceiptWay, createScanCodeOrder } from '@/api/order.js';

import {
  findDrugStoreDetail,
  findDoctorByID,
  getDrugPrescriptionPreInfoScanCode,
  queryLogisticsTipsNotPlatformList,
} from '@/api/base';
import PHARMACY from '../shop/components/pharmacy.vue';
import ADDRESS from '../shop/components/address.vue';
import PROPCENTER from '@/components/propCenter/propCenter.vue';
import Login from '@/mixins/login.js';

import { Toast } from '@/common/js/pay.js';

let timer = null;

import payment from '@/mixins/wx';
import {getHelpPayCreateResult} from "../../api/order";

export default {
  mixins: [Login, payment],
  components: {
    PHARMACY,
    ADDRESS,
    PROPCENTER,
  },
  data() {
    return {
      // 显示支付提示
      showPayTip: false,
      // 是否只有一个药店
      isOnly: false,
      // 患者信息
      patientInfo: '',
      // 处方总金额
      totalMoney: 0,
      // 药店信息
      drugDetail: null,
      // 自提地址
      address: null,
      // 快递类型
      logisticsType: 0,
      // 支付提示
      payTip: '',
      // 物流提示
      logisticsTip: '',
      // 物流费用
      logist_count: 0,
      // 要选择的物流价位
      logistPrice: '',
      // 要提示的消息
      tip: '',
      // 配送方式 0 快递 1 自提
      delivery: -1,
      // 查询次数
      num: 3,
      // 参数
      param: '',
      // 药品列表
      list: [],
      // 详情
      detail: '',
      // 药品价位
      durg_price: 0,
      // 医生信息
      docInfo: '',
      // 支付方式列表
      payList: [],
      // 收款方
      callId: '',
      // 失败默认图
      errUrl: require('../../static/shop/drug.png'),
      // 默认药店id
      drugstoreId: '',
      // 是否第一次
      isFrist: true,
      // 优惠金额
      discount_price: 0,
      // 实际金额
      shouldMoney: 0,
      // 物流
      LogisticsCostList: [],
      // 是否代付
      isHelpPay: 0,
      // 是否可继续
      isNext: true,
      //支付生产的订单号 解决代付和自己付生成多个订单
      orderNo:"",
    };
  },
  computed: {
    // 计算价位
    total() {
      let l = Number(this.logist_count) * 100;
      let n = Number(this.durg_price) * 100;
      // 自提 不加运费
      if (this.delivery == 1 || this.delivery == -1) {
        return n / 100;
      } else {
        return (l + n) / 100;
      }
    },
  },
  onLoad(opt) {
    this.param = opt;
    uni.removeStorageSync('shop_patient');
    uni.removeStorageSync('scanAddress');
    uni.removeStorageSync('nowAddress');

    // 不存在用户
    if (!uni.getStorageSync('userId')) {
      this.getDetail();
      return;
    }
    this.init();
    this.isFrist = false;
  },
  onShow() {
    let patientInfo = uni.getStorageSync('shop_patient');
    let nowAddress = uni.getStorageSync('scanAddress');
    if (patientInfo) {
      this.patientInfo = patientInfo;
    }
    if (nowAddress) {
      let address = nowAddress.address;
      this.address = address;
      if (address.deliveryType == 1) {
        this.delivery = 0;
      } else {
        this.delivery = 1;
        this.getDurgDetail(address.drugstoreId);
      }
      // 根据药店 改变药品价位
      this.getDrugPrice(address.drugstoreId);
      this.getPayInfo(address.drugstoreId);
      this.getLogisTip(address.drugstoreId);
    }
    // 如果不存在信息
    if (!this.docInfo && this.isFrist) this.init();
    uni.removeStorageSync('scanAddress');
    uni.removeStorageSync('nowAddress');
  },
  methods: {
    // 初始化
    init() {
      this.getDetail();
      this.getDocInfo();
      this.getPayTip();
    },
    // 获取支付方信息
    async getPayInfo(subjectId) {
      let res = await getReceiptWay({
        subjectId,
      });
      let callId;
      res.data.forEach((v) => {
        if (v.receiptType == 1) {
          callId = v.appid;
        }
      });
      this.payList = res.data;
      this.callId = callId;
    },
    // 查询医生信息
    async getDocInfo() {
      let res = await findDoctorByID({
        docId: this.param.docId,
      });
      this.docInfo = res.data;
    },
    // 获取物流费用
    async getLogisticsPrice(orderCost) {
      const drugstoreId = this.address?.drugstoreId || '';
      let { data } = await getLogisticsCostList({
        orderCost,
        subjectId: drugstoreId,
      });
      this.LogisticsCostList = data;
      // 设置默认
      this.logisticsType = 0;
      this.logist_count = data[0]?.logisticsCost;
    },
    // 获取药品详情
    async getDetail() {
      let res = await getDrugPrescriptionPreInfoScanCode(this.param);
      const { drugList, drugstoreId } = res.data;
      this.drugstoreId = drugstoreId || '';
      let arr = [];
      drugList.forEach((v) => {
        let price = ((Number(v.price) * 100 * Number(v.quan)) / 100).toFixed(2);
        // 实际价位以及应付价位
        v.realMoney = v.shouldMoney = price;
        arr.push({
          drugId: v.drugId,
          quan: v.quan,
        });
      });
      uni.setStorageSync('drugArr', arr);
      this.list = [...drugList];
      delete res.data.drugList;
      this.detail = res.data;
      this.getDurgCount();
    },
    // 根据药店获取药品价位
    async getDrugPrice(drugstoreId) {
      let drug = {
        drugList: uni.getStorageSync('drugArr'),
        drugstoreId,
      };
      let res = await getDrugStoragePrice(drug);
      let arr = res.data;
      let list = [];
      arr.forEach((v) => {
        this.list.forEach((k) => {
          if (v.drugId == k.drugId) {
            let item = { ...k, ...v };
            list.push(item);
          }
        });
      });
      this.list = list;
      // 重新计算药品价位
      this.getDurgCount();
    },
    // 统计药品信息
    getDurgCount() {
      let r = 0;
      let s = 0;
      this.list.forEach((v) => {
        r += Number(v.realMoney) * 100;
        s += Number(v.shouldMoney) * 100;
        v.drugTotal = r / 100;
        v.preferentialAmount = (s - r) / 100 || 0;
      });
      this.durg_price = r / 100;
      this.shouldMoney = s / 100;
      this.discount_price = (s - r) / 100;
      // 根据药品价位查询物流费
      if (uni.getStorageSync('userId')) {
        this.getLogisticsPrice(r / 100);
      }
    },
    // 选择配送物流
    setLogistType(n) {
      if (!this.isNext) return;
      this.logisticsType = n;
      this.logist_count = this.LogisticsCostList[n].logisticsCost;
    },
    // 选择配送方式
    seleType() {
      if (!this.isNext) return;
      if (!this.hasInfo()) return;
      uni.removeStorageSync('scanAddress');
      let url = '/pages/address/hospitalAddress?docId=' + this.param.docId;
      if (this.drugstoreId) {
        url += '&drugstoreId=' + this.drugstoreId;
      }
      uni.navigateTo({
        url,
      });
    },
    // 去选择收货地址
    toAddress() {
      if (!this.isNext) return;
      if (!this.hasInfo()) return;
      // 选择快递地址
      uni.navigateTo({
        url: '/pages/address/index?action=shop',
      });
    },
    // 物流提示
    async getLogisTip(subjectId) {
      let { data } = await queryLogisticsTipsNotPlatformList(subjectId);
      if (!data.length) return;
      let str = '';
      data.forEach((v) => {
        str += `${v.name}：${v.content} `;
      });
      this.logisticsTip = {
        tipsTitle: '运费说明',
        tipsContent: str,
      };
    },
    // 获取支付提示
    async getPayTip() {
      let res = await queryToPayTips();
      this.payTip = res.data;
    },
    // 获取药店详情
    async getDurgDetail(drugstoreId) {
      let res = await findDrugStoreDetail({
        drugstoreId,
      });
      this.drugDetail = res.data;
    },
    // 提示消息
    showTips() {
      this.tip = this.logisticsTip;

      this.$refs.popup.open();
    },
    // 隐藏提示
    hideTip() {
      this.$refs.popup.close();
    },
    // 选择患者
    toPatient() {
      if (!this.isNext) return;
      if (!this.hasInfo()) return;
      uni.navigateTo({
        url: '/pages/personalCenter/patientManage/index?action=shop',
      });
    },
    // 点击去支付
    send(isHelpPay = 0) {
      if (!this.patientInfo) {
        Toast('请选择就诊人');
        return;
      }

      if (this.delivery == -1) {
        Toast('请选择配送方式');
        return;
      }

      if (this.delivery == 0) {
        if (!this.address) {
          Toast('请选择收货地址');
          return;
        }

        if (!this.LogisticsCostList.length) {
          Toast('当前未配置快递费用');
          return;
        }
      }

      if (this.delivery == 1) {
        if (!this.drugDetail) {
          Toast('请选择药店');
          return;
        }
      }

      if (!this.payList.length) {
        Toast('未配置支付方式');
        return;
      }

      // 是否代付
      this.isHelpPay = isHelpPay;

      // 如果代付
      if (isHelpPay == 1) {
        let item = this.payList[0];
        this.callId = item.appid;
        this.showPayTip = true;
        return;
      }

      // 如果无需支付
      if (this.total == 0) {
        // 显示提示
        this.showPayTip = true;
        return;
      }

      // 如果只有一个支付方式（微信）
      if (this.payList.length == 1) {
        // 显示提示
        this.showPayTip = true;
        return;
      }

      let that = this;
      uni.showActionSheet({
        itemList: ['微信支付', '支付宝支付'],
        success(res) {
          if (res.tapIndex == 0) {
            let callId;
            // 显示提示
            that.payList.forEach((v) => {
              if (v.receiptType == 1) {
                callId = v.appid;
              }
            });
            that.callId = callId;
            that.showPayTip = true;
            return;
          } else {
            let callId;
            that.payList.forEach((v) => {
              if (v.receiptType == 2) {
                callId = v.appid;
              }
            });
            that.callId = callId;
            // 显示提示
            that.showPayTip = true;
          }
        },
      });
    },
    // 拼参数
    async setParam() {
      // 关闭弹窗
      this.showPayTip = false;

      uni.showLoading({
        mask: true,
      });

      const isHelpPay = this.isHelpPay;

      // 物流方式 1 快递 2 自提
      let deliveryType = this.delivery + 1;

      let payType = 1;

      // 微信
      if (this.callId.indexOf('wx') > -1) {
        payType = 1;
      } else {
        payType = 2;
      }

      if (isHelpPay == 1) {
        payType = 'd';
      }

      // 无需支付
      if (this.total <= 0) {
        payType = 0;
      }

      // 地址相关
      const {
        deliveryName,
        drugstoreId,
        drugstoreName,
        telNo: deliveryTelNo,
        addressArea,
        addressDetail,
      } = this.address;

      // 地址信息
      let address = {};

      if (deliveryType == 1) {
        address = {
          deliveryAddressDetail: addressArea + '' + addressDetail,
          deliveryTelNo,
          deliveryName,
        };
      }

      // 药店信息
      let durgDetail = {
        subjectId: drugstoreId,
        subjectName: drugstoreName,
      };

      let list = JSON.parse(JSON.stringify(this.list));

      list.forEach((v) => {
        v.drugTotal = v.shouldMoney;
        v.drugTotalReal = v.realMoney;
      });

      // 药品列表
      let ls = [...list];

      // 商家信息
      let shop = {
        deliveryType,
        logisticsCost: deliveryType == 1 ? this.logist_count : 0,
        logisticsCustomName:
          deliveryType == 1
            ? this.LogisticsCostList[this.logisticsType].logisticsName
            : '',
        // 药品价格
        merchantsOrderMoney: this.durg_price,
        // 应付
        merchantsTotalMoney: this.shouldMoney,
        // 优惠金额
        preferentialAmount: this.discount_price,
        ...durgDetail,
        ...address,
        ls,
      };

      // 就诊人相关
      const {
        patientId,
        patientName,
        age,
        birthDate,
        ageUnit,
        sex,
        sexCode,
        telNo,
        idNo,
      } = this.patientInfo;

      // 医生相关
      const { deptId, deptName, docId, docTel, docName } = this.docInfo;

      let info = { ...this.detail };
      delete info.detail;

      // 处方相关
      let proPrescriptionVO = {
        diags: this.detail.diags,
        prescriptions: [
          {
            details: this.list,
            proPrescriptionMaster: {
              ...info,
            },
          },
        ],
        proBusinessInfo: {
          deptId,
          deptName,
          docId,
          docTel,
          docName,
          idNo,
          patientId,
          patientName,
          age,
          birthDate,
          ageUnit,
          sex,
          sexCode,
          telNo,
        },
      };

      // 整合参数
      let obj = {
        appid: uni.getStorageSync('appId'),
        callId: this.callId,
        docId: this.param.docId,
        hosId: uni.getStorageSync('hosId'),
        openid: uni.getStorageSync('wxInfo').openId,
        msgid: this.param.msgid,
        ...address,
        orderMoney: this.total,
        patientId,
        payType,
        patientName,
        deliveryType,
        // 实际金额
        totalMoney:
          (Number(this.total) * 100 + Number(this.discount_price) * 100) / 100,
        oms: [shop],
        proPrescriptionVO,
        isHelpPay,
      };

      console.log('参数', obj);

      try {
        if(this.orderNo){
          if (obj.isHelpPay == 1) {
            // 扫码开方 来源3 商城2
            this.setShare(obj.totalMoney, this.orderNo, 3);
            this.showTip = true;
            this.isNext = false;
            uni.hideLoading();
            return;
          }else{
            getHelpPayCreateResult({
              orderNo: this.orderNo,
              openid: uni.getStorageSync("wxInfo").openId,
              callId: this.callId,
              payType:payType,
            }).then(res=>{
              this.payFun(res);
            })
          }
        }else{
          // 调用接口
          let res = await createScanCodeOrder(obj);
          // 无需支付
          if (payType == 0) {
            const { orderNo, orderStatus } = res.data;
            uni.hideLoading();
            // 调用成功 直接跳转成功页面
            if (orderStatus == 2) {
              uni.reLaunch({
                url: './success',
              });
            }
            return;
          }
          if (obj.isHelpPay == 1) {
            // 扫码开方 来源3 商城2
            this.setShare(obj.totalMoney, res.data.orderNo, 3);
            this.showTip = true;
            this.isNext = false;
            return;
          }
          this.orderNo = res.data.orderNo;
          this.payFun(res);
        }
      } catch (e) {
        console.log('失败', e);
        uni.hideLoading();
      }
    },
    payFun(res){
      // 如果是微信支付
      if (this.callId.indexOf('wx') > -1) {
        this.toPay(res.data);
      } else {
        uni.navigateTo({
          url:
              '/pages/pay/pay?price=' +
              this.total +
              '&orderNo=' +
              res.data.orderNo +
              '&url=' +
              btoa(res.data.url),
        });
      }
      uni.hideLoading();
    },
    // 支付
    toPay(info) {
      const orderNo = info.orderNo;
      delete info.orderNo;
      // 调用微信支付
      let that = this;
      WeixinJSBridge.invoke('getBrandWCPayRequest', info, (res) => {
        if (res.err_msg == 'get_brand_wcpay_request:ok') {
          // 查询状态
          that.getPayStatus(orderNo);
          // 调用三次
          timer = setInterval(() => {
            that.getPayStatus(orderNo);
          }, 2000);
        } else {
          Toast('取消支付');
        }
      });
    },
    // 查询支付状态
    async getPayStatus(orderNo) {
      // 根据订单号查询 目前缺少订单号
      let res = await queryMallOrderStatus(orderNo);
      this.num--;

      if (res.data.orderStatus == 2) {
        clearInterval(timer);
        timer = null;
        this.num = 3;
        uni.reLaunch({
          url: './success',
        });
      }

      // 查询过三次
      if (this.num <= 0) {
        clearInterval(timer);
        // 应该跳转到失败
        if (res.data.orderStatus == 1) {
          uni.navigateTo({
            url: './error',
          });
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}

/deep/ .wrapper .block {
  height: auto;
}

.pay_tip {
  .title {
    font-size: 32rpx;
  }

  .cont {
    padding: 10rpx 0 20rpx;
    font-size: 28rpx;
    text-align: left;
  }
}

.scan_code {
  padding: 24rpx 32rpx 120rpx;
  min-height: 100vh;
  background-color: #f5f5f5;

  .sele_warp {
    width: 100%;
    background-color: #fff;
    border-radius: 8rpx;
    font-size: 28rpx;
    padding: 0 24rpx;
    margin-bottom: 24rpx;

    .sele_but {
      @include flex(lr);
      height: 88rpx;

      .uni-icons {
        margin-left: 10rpx;
      }

      .right {
        @include flex;

        .name {
          color: #666;
        }
      }
    }

    .img_list {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      grid-gap: 20rpx;
      padding-bottom: 20rpx;

      .img_item {
        width: 200rpx;
        height: 200rpx;
        border-radius: 8rpx;
        border: 1px dashed #eee;
        position: relative;
        @include flex;
        flex-direction: column;

        text {
          color: #999;
        }

        .img {
          width: 100%;
          height: 100%;
        }

        .del {
          width: 100%;
          height: 100%;
          background-color: rgba($color: #000000, $alpha: 0.3);
          position: absolute;
          border-radius: 8rpx;
          top: 0;
          left: 0;

          .uni-icons {
            position: absolute;
            right: 5rpx;
            top: 5rpx;
          }
        }
      }
    }

    .sele_info {
      @include flex;
      align-items: flex-start;
      padding-bottom: 20rpx;

      text {
        flex: none;
        width: 110rpx;

        &.red {
          color: red;
          flex: 1;
        }
      }

      .info_text {
        flex: 1;
        color: #999;
      }
    }

    .logistics {
      width: 100%;
      padding: 24rpx 0;

      .item {
        height: 76rpx;
        padding: 0 24rpx;
        @include flex(lr);
        background-color: #f5f5f5;
        border-radius: 8rpx;
        margin-bottom: 24rpx;

        &:last-child {
          margin-bottom: 0;
        }

        .name {
          font-size: 28rpx;
        }

        .right {
          @include flex;

          text {
            font-size: 28rpx;
            color: red;
            padding-right: 30rpx;
          }

          image {
            width: 32rpx;
            height: 32rpx;
          }
        }
      }
    }
  }

  .warp {
    margin-bottom: 24rpx;
  }

  .user_info {
    @include flex(lr);
    padding: 18rpx 0;

    .user_head {
      width: 128rpx;
      height: 128rpx;
      border-radius: 8rpx;
      flex: none;
    }

    .user_desc {
      flex: 1;
      padding-left: 22rpx;

      .user_name {
        font-size: 32rpx;
        font-weight: bold;
      }

      .user_other {
        @include flex(left);
        padding-top: 10rpx;

        image {
          width: 32rpx;
          height: 32rpx;
        }

        text {
          padding-left: 10rpx;
          color: #666;
          font-size: 24rpx;
        }
      }
    }

    .uni-icons {
      flex: none;
    }
  }
}

.durg_list {
  background-color: #fff;
  padding: 24rpx 24rpx 0;
  margin-bottom: 24rpx;

  .durg_item {
    @include flex;
    padding: 16rpx 0;

    &:last-child {
      .right {
        border-bottom: none;
      }
    }

    .left {
      width: 128rpx;
      height: 128rpx;
      margin-right: 24rpx;
      border: 1px solid #eee;
      border-radius: 8rpx;
      flex: none;
    }

    .right {
      flex: 1;
      min-height: 128rpx;
      @include flex(lr);
      flex-direction: column;
      align-items: stretch;
      border-bottom: 1px solid #eee;

      .drug_name {
        flex: 1;
        font-size: 28rpx;
        font-weight: bold;
      }

      .drug_info {
        flex: 1;
        font-size: 24rpx;
        color: #999;
      }

      .drug_red {
        flex: 1;
        font-size: 24rpx;
        color: red;
      }

      .right_menu {
        flex: 2;
        @include flex(lr);
        align-items: flex-end;

        .price {
          font-size: 28rpx;
          color: #ff3b30;
          font-weight: bold;

          .del {
            font-size: 20rpx;
            margin-left: 10rpx;
            color: #999;
            text-decoration: line-through;
          }
        }

        .num {
          font-size: 28rpx;
        }
      }
    }
  }

  .count {
    @include flex(right);
    height: 88rpx;
    font-size: 28rpx;

    .count_num {
      color: #333;
    }

    .count_price {
      margin-left: 24rpx;

      text {
        color: #ff3b30;
        padding-left: 10rpx;
      }
    }
  }
}

// 覆盖样式
.pharmacy_detail {
  padding: 20rpx 0;
}

.address {
  padding: 20rpx 0;
}

.footer {
  width: 100%;
  height: 104rpx;
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 1;
  background-color: #fff;
  padding: 0 32rpx;
  @include flex(lr);
  box-shadow: 0px 4rpx 8rpx 0px rgba(0, 0, 0, 0.5);

  .count {
    font-size: 28rpx;
    color: #333;

    text {
      color: red;
    }
  }

  .right {
    @include flex;

    .but {
      width: 160rpx;
      height: 60rpx;
      color: #fff;
      @include flex;
      @include bg_theme;
      font-size: 28rpx;
      border-radius: 30rpx;

      &.pay {
        background-color: #fff;
        @include font_theme;
        @include border_theme;
        margin-right: 32rpx;
      }
    }
  }
}

.pop {
  padding: 0 32rpx 50rpx;
  background-color: #fff;
  border-radius: 16rpx 16rpx 0px 0px;

  .pop_title {
    height: 88rpx;
    @include flex;
    position: relative;
    font-size: 32rpx;

    .uni-icons {
      position: absolute;
      right: 0;
    }
  }

  .pop_text {
    color: #999;
    font-size: 24rpx;
    line-height: 40rpx;
  }
}
</style>
