<template>
  <view class="rec_item" @click="toDoc(item)">
    <view class="rec-head">
      <view
        style=" margin-right: 32upx;"
      >
        <img
          class="doc_head"
          v-if="item.docImg"
          v-img="item.docImg"
          :data-src="errUrl"
          :class="
          item.isOnline == 0 ? 'docImg_box_unOnline' : 'docImg_box_Online'
        "
        />
        <img src="/static/images/docHead.png" v-else class="doc_head"   :class="
          item.isOnline == 0 ? 'docImg_box_unOnline' : 'docImg_box_Online'
        "/>
        <div
            :style="{
                          color: item.isOnline == 0 ? '#ffffff' : '#ffffff',
                          textAlign: 'center',
                          marginTop: '-15px',
                          zIndex: 0,
                          position: 'relative',
                          fontSize: '10px',
                        }"
        >
          <div v-if="item.isOnline == 0" style=" width: 37px;height: 15px;border-radius: 15px;background: #c7c7c7;margin: auto;display: flex;align-items: center;justify-content: space-evenly;color: #efefef">
            <span style="font-size: 8px">离线</span>
          </div>
          <div v-else style=" width: 37px;height: 15px;border-radius: 15px;background: #0AC2B2;margin: auto;display: flex;align-items: center;justify-content: space-evenly;color: #ffffff">
            <span style="font-size: 8px">在线</span>
          </div>
        </div>
      </view>
      <view>
        <view class="docName">{{ item.docName }}</view>
        <view class="docName_dept" style="margin-top: 4px">{{
          item.deptName
        }}</view>
        <view class="docName_dept">{{ item.docProf }}</view>
      </view>
    </view>
    <view class="docName_desps">
      <view class="docName_desp">
        <view class="icons">
          <image class="com_icon" src="/static/doc/路径 1.png"></image>
        </view>
        <text>{{ isShowWorkHosName ? item.workHosName : item.hosName }}</text>
      </view>
      <view class="docName_desp">
        <image class="docName_desp_image" src="/static/doc/5.png"></image>
        <text>接诊量:{{ item.consultationCount }}</text>
      </view>
      <view class="docName_desp">
        <image class="docName_desp_image" src="/static/doc/6.png"></image>
        <text>好评率:{{ item.percentage }}%</text>
      </view>
    </view>
    <!--    &lt;!&ndash; 右侧 &ndash;&gt;-->
    <!--    <view class="doc_desc">-->
    <!--      &lt;!&ndash; 名字 &ndash;&gt;-->
    <!--      <view class="desc_top">-->
    <!--        <view class="left">-->
    <!--          <text>{{ item.docName }}</text>-->
    <!--          &lt;!&ndash; 科室 &ndash;&gt;-->
    <!--          <text>{{ item.docProf }}</text>-->
    <!--        </view>-->
    <!--        &lt;!&ndash; 右侧 &ndash;&gt;-->
    <!--        <view class="right">-->
    <!--          <block v-for="v in item.visitType">-->
    <!--            <text class="audio" v-if="v.visitTypeCode == 2">语音</text>-->
    <!--            <text class="tw" v-if="v.visitTypeCode == 1">图文</text>-->
    <!--          </block>-->
    <!--        </view>-->
    <!--      </view>-->
    <!--      &lt;!&ndash; 科室 医院 &ndash;&gt;-->
    <!--      <view class="desc_two">-->
    <!--        <text>{{ item.deptName }}</text>-->
    <!--        <text class="host">{{-->
    <!--          isShowWorkHosName ? item.workHosName : item.hosName-->
    <!--        }}</text>-->
    <!--      </view>-->
    <!--      &lt;!&ndash; 信息 &ndash;&gt;-->
    <!--      <view class="desc_num">-->
    <!--        <text>好评率:{{ item.percentage }}%</text> |-->
    <!--        <text>接诊量:{{ item.consultationCount }}</text>-->
    <!--&lt;!&ndash;        |&ndash;&gt;-->
    <!--&lt;!&ndash;        <text>平均响应:{{ item.responseTime }}分钟</text>&ndash;&gt;-->
    <!--      </view>-->
    <!--      &lt;!&ndash; 标签 &ndash;&gt;-->
    <!--      <view class="desc_tag">-->
    <!--        <block v-for="(l, lk) in item.docLable">-->
    <!--          <text :key="lk">{{ l.lableName }}</text>-->
    <!--        </block>-->
    <!--      </view>-->
    <!--    </view>-->
  </view>
</template>

<script>
export default {
  props: {
    item: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      errUrl: require("../../../static/images/docHead.png"),
      isShowWorkHosName: uni.getStorageSync("isShowWorkHosName") || false,
    };
  },
  methods: {
    // 去医生主页
    toDoc(item) {
      // 医院id
      uni.setStorageSync("hosId", item.hosId);
      uni.navigateTo({
        url: "/pages/register/docHomePage/index?docId=" + item.docId,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.docImg_box_unOnline {
  border: 1px solid #CCCCCC;

}
.docImg_box_Online {
  border: 1px solid #0AC2B2;

}
.icons {
  width: 28rpx;
  height: 28rpx;
  border-radius: 50%;
  background: rgba(0, 186, 173, 1);
  display: flex;
  justify-content: center;
  align-items: center;
  .com_icon {
    width: 12rpx;
    height: 16rpx;
  }
  margin-right: 10rpx;
}
.docName_desp_image {
  width: 28rpx;
  height: 28rpx;
  margin-right: 10rpx;
}

.rec_item {
  width: 335rpx;
  padding: 20rpx;
  border-radius: 16rpx;
  background: rgba(247, 248, 252, 1);
  box-sizing: border-box;
  border-bottom: 0px !important;
  .rec-head {
    display: flex;
    align-items: center;
  }
  .docName {
    font-size: 28rpx;
    font-weight: bold;
    color: black;
  }
  .docName_desps {
    margin-top: 20rpx;
  }
  .docName_desp {
    color: rgba(153, 153, 153, 1);
    font-size: 24rpx;
    line-height: 36rpx;
    display: flex;
    align-items: center;
  }
  .docName_dept {
    font-size: 28rpx;
    color: rgba(102, 102, 102, 1);
  }
  .doc_head {
    width: 120rpx;
    height: 120rpx;
    border-radius: 8rpx;
    flex: none;
    object-fit: cover;
  }

  .doc_desc {
    flex: 1;
    overflow: hidden;

    .desc_top {
      @include flex(lr);

      .left {
        font-size: 32rpx;
        color: #333;
        font-weight: bold;

        text:last-child {
          margin-left: 24rpx;
        }
      }

      .right {
        @include flex;
        gap: 20rpx;

        text {
          width: 88rpx;
          height: 36rpx;
          color: #fff;
          font-size: 26rpx;
          border-radius: 8rpx;
          text-align: center;

          &.audio {
            @include bg_theme;
          }

          &.tw {
            background-color: #ffb541;
          }
        }
      }
    }

    .desc_two {
      margin-top: 8rpx;
      font-size: 24rpx;
      color: #333;

      .host {
        margin-left: 24rpx;
      }
    }

    .desc_num {
      margin-top: 8rpx;
      @include flex(lr);
      font-size: 24rpx;
      color: #a6aab2;
    }

    .desc_tag {
      margin-top: 16rpx;
      @include flex(left);
      gap: 16rpx;
      flex-wrap: wrap;

      text {
        padding: 0 12rpx;
        height: 36rpx;
        border-radius: 18rpx;
        background-color: #e8f6fd;
        @include font_theme;
        font-size: 22rpx;
        @include flex;
      }
    }
  }
}
</style>
