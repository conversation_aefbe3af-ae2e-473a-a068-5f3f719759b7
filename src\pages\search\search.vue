<template>
  <view class="search">
    <!-- 顶部 -->
    <view class="search_cont">
      <!-- 左侧图标 -->
      <image src="/static/images/search/search.png" class="icon_search" />
      <!-- 输入框 -->
      <view class="cont_input">
        <input
          class="input"
          type="text"
          v-model="text"
          :focus="true"
          @confirm="search"
          placeholder="搜索科室、医生"
        />
        <image
          src="/static/images/search/btn_del.png"
          class="icon_del"
          v-show="text"
          @click="text = ''"
        />
      </view>
      <!-- 取消按钮 -->
      <button class="cont_but" @click="search">搜索</button>
      <button class="cont_but" @click="back">取消</button>
    </view>

    <!-- 搜索历史 -->
    <view class="search_list" v-if="list.length">
      <!-- 标题 -->
      <view class="list_title">
        <view class="left">搜索历史</view>
        <!-- 清空按钮 -->
        <view class="right" @click="showRemove = true">
          <image src="/static/images/search/del.png" />
          <text>清空</text>
        </view>
      </view>
      <!-- 历史 -->
      <view class="list_items">
        <text
          class="item"
          v-for="item in list"
          :key="item"
          @click="toDocList(item)"
          >{{ item }}</text
        >
      </view>
    </view>

    <!-- 确认弹窗 -->
    <propCenter
      v-if="showRemove"
      @cancelPropCenter="showRemove = false"
      @confirmPropCenter="removeList"
      :type="2"
    >
      确定要清空所有记录吗？
    </propCenter>
  </view>
</template>

<script>
import propCenter from '@/components/propCenter/propCenter.vue';
export default {
  data() {
    return {
      text: '',
      list: [],
      showRemove: false,
    };
  },
  methods: {
    // 搜索
    search(e) {
      let str = this.text;
      this.toDocList(str);
      this.setList(str);
    },
    // 去医生列表
    toDocList(str) {
      uni.navigateTo({
        url: '/pages/register/docList/index?search=' + str,
      });
    },
    // 设置搜索历史
    setList(str) {
      // 不为空
      if (this.list.length) {
        let arr = [str, ...this.list];
        this.list = [...new Set(arr)];
      } else {
        this.list = [str];
      }
      uni.setStorageSync('search_list', JSON.stringify(this.list));
    },
    // 清空历史
    removeList() {
      this.list = [];
      uni.removeStorageSync('search_list');
      this.showRemove = false;
    },
    // 取消
    back() {
      // 获取当前页面栈数量
      let length = getCurrentPages().length;
      // 可以后退
      if (length > 1) {
        uni.navigateBack({
          delta: 1,
        });
      } else {
        // 刷新页面导致页面栈丢失
        history.back();
      }
    },
  },
  onShow() {
    let list = uni.getStorageSync('search_list') || false;
    if (list) {
      this.list = JSON.parse(list);
    }
  },
  components: {
    propCenter,
  },
};
</script>

<style lang="scss" scoped>
.search {
  height: 100vh;
  background-color: #fff;

  * {
    box-sizing: border-box;
  }

  .search_cont {
    width: 100%;
    height: 88upx;
    @include flex(lr);
    padding-left: 24upx;
    background-color: #fff;
    border-bottom: 1px solid $k-hr-color;

    .icon_search {
      width: 44upx;
      height: 44upx;
      margin-left: 8upx;
    }

    .cont_input {
      flex: 1;
      @include flex(lr);
      padding: 0 0 0 18upx;

      .input {
        flex: 1;
        font-size: 28upx;
        color: $k-title;
      }

      .icon_del {
        width: 38upx;
        height: 38upx;
        flex: none;
      }
    }

    .cont_but {
      padding-left: 20upx;
      flex: none;
      font-size: 28upx;
      @include font_theme;
      text-align: right;
    }
  }

  .search_list {
    width: 100%;
    padding: 0 32upx;

    .list_title {
      @include flex(lr);
      height: 90upx;

      .left {
        font-size: 32upx;
        color: $k-title;
        font-weight: bold;
      }

      .right {
        @include flex(center);
        font-size: 22upx;
        color: $k-info-title;
        image {
          width: 32upx;
          height: 32upx;
        }
      }
    }

    .list_items {
      @include flex(left);
      flex-wrap: wrap;

      text {
        font-size: 28upx;
        color: $k-sub-title;
        height: 64upx;
        @include flex;
        padding: 0 32upx;
        border-radius: 32upx;
        background-color: $k-page-bg-color;
        margin-right: 16upx;
        margin-bottom: 16upx;
      }
    }
  }
}
</style>
