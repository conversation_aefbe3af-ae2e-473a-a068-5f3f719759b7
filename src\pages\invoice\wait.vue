<template>
  <div class="wait">
    <div class="head">
      <HMfilterDropdown
        :menuTop="80"
        :filterData="filterData"
        :defaultSelected="defaultSelected"
        :updateMenuName="true"
        @confirm="confirm"
        dataFormat="Object"
      ></HMfilterDropdown>
    </div>

    <div class="list" v-if="list.length">
      <block v-for="item in list">
        <PACS
          @click="toAdd(item)"
          :item="item"
          v-if="item.orderType == 6"
          :key="item.orderNo"
        />

        <LIS
          @click="toAdd(item)"
          :item="item"
          v-if="item.orderType == 5"
          :key="item.orderNo"
        />

        <REGISTER
          :item="item"
          v-if="item.orderType == 1"
          :key="item.orderNo"
          @click="toAdd(item)"
        />

        <PRESCRIPTION
          v-if="item.orderType == 3"
          :item="item"
          :key="item.orderNo"
          @click="toAdd(item)"
        />

        <SERVE
          @click="toAdd(item)"
          :item="item"
          v-if="item.orderType == 4"
          :key="item.orderNo"
        />
      </block>
    </div>

    <div class="empty" v-if="!list.length">
      <image src="/static/images/index/box_empty.png" />
      <span>暂无订单信息</span>
    </div>
  </div>
</template>

<script>
import { getSysCodeByType } from '@/api/shop';
import { getCanGiveInvoiceOrderPage } from '@/api/invoice';
import HMfilterDropdown from '@/components/HM-filterDropdown/HM-filterDropdown.vue';
import { findIsEnabledPatientByUserId } from '@/api/user.js';
import myJsTools from '@/common/js/myJsTools.js';
import PACS from './com/pacs.vue';
import LIS from './com/lis.vue';
import REGISTER from './com/register.vue';
import SERVE from './com/serve.vue';
import PRESCRIPTION from './com/prescription.vue';

export default {
  name: 'WaitInvoice',
  components: {
    HMfilterDropdown,
    PACS,
    LIS,
    REGISTER,
    SERVE,
    PRESCRIPTION,
  },
  data() {
    return {
      filterData: [
        {
          name: '订单类型',
          type: 'hierarchy',
          submenu: [],
        },
        {
          name: '时间',
          type: 'hierarchy',
          submenu: [
            {
              name: '七天内',
              value: '0',
            },
            {
              name: '一月内',
              value: '1',
            },
            {
              name: '三月内',
              value: '2',
            },
            {
              name: '半年内',
              value: '3',
            },
          ],
        },
        {
          name: '就诊人',
          type: 'hierarchy',
          submenu: [],
        },
      ],
      defaultSelected: [],
      query: {
        startTime: '',
        endTime: '',
        selectPatientId: '',
        orderType: '',
        page: 1,
        limit: 10,
        patientIdList: uni.getStorageSync('patientIdList') || [],
      },
      list: [],
      total: 0,
    };
  },
  onLoad() {
    this.init();
    this.getList();
  },
  methods: {
    // 初始化下拉选数据
    async init() {
      let { data } = await findIsEnabledPatientByUserId({
        userId: uni.getStorageSync('userId'),
      });
      if (!data) return;
      let submenu = [];
      for (let i = 0; i < data.length; i++) {
        submenu.push({
          value: data[i].patientId,
          name: data[i].patientName,
        });
      }
      this.filterData[2].submenu = submenu;

      this.getOrderType();
    },
    // 订单类型
    async getOrderType() {
      let { data } = await getSysCodeByType('075');
      let arr = data.map((v) => {
        return {
          name: v.meaning,
          value: v.code,
        };
      });
      this.filterData[0].submenu = arr;
    },
    // 筛选
    confirm(e) {
      let valueArr = e.value;
      this.query.page = 1;
      let orderType = valueArr[0];
      if (orderType[0]) {
        this.query.orderType = orderType[0];
      }
      let time = valueArr[1];
      if (time[0]) {
        this.query.endTime = myJsTools.getDate(new Date()) + ' 23:59:59';
        let timeValue = time[0];

        let startTime;
        if (timeValue == 0) {
          startTime = myJsTools.getDate('day', -7);
        } else if (timeValue == 1) {
          startTime = myJsTools.getDate('month', -1);
        } else if (timeValue == 2) {
          startTime = myJsTools.getDate('month', -3);
        } else if (timeValue == 3) {
          startTime = myJsTools.getDate('month', -6);
        }
        this.query.startTime = startTime + ' 00:00:00';
      }

      let patients = valueArr[2];
      if (patients[0]) {
        this.query.selectPatientId = patients[0];
      }
      this.getList();
    },
    async getList() {
      let { data } = await getCanGiveInvoiceOrderPage(this.query);
      const { total, rows } = data;
      this.total = total;
      if (this.query.page == 1) {
        this.list = rows;
      } else {
        this.list = [...this.list, ...rows];
      }
      uni.stopPullDownRefresh();
    },
    toAdd(item) {
      const { orderNo, orderType, orderMoney } = item;
      uni.redirectTo({
        url:
          '/pages/invoice/input?orderNo=' +
          orderNo +
          '&orderMoney=' +
          orderMoney,
      });
    },
  },
  onPullDownRefresh() {
    this.query.page = 1;
    this.getList();
  },
  onReachBottom() {
    if (this.list.length >= this.total) return;
    this.query.page++;
    this.getList();
  },
};
</script>

<style lang="scss" scoped>
page {
  background: #f5f5f5;
}
.wait {
  .head {
    position: sticky;
    top: 0;
    z-index: 1;
  }

  .list {
    padding: 24rpx 32rpx;
  }

  .empty {
    padding-top: 100rpx;
    @include flex;
    flex-direction: column;

    image {
      width: 600rpx;
    }

    span {
      font-size: 28rpx;
      color: #666;
      margin-top: 24rpx;
    }
  }
}
</style>
