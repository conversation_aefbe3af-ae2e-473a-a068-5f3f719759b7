<template>
  <div class="drug_list">
    <!-- 药店名称 -->
    <div class="drug_title">
      <p>
        <span></span>
        {{ name }}
      </p>

      <span class="r" v-if="showTelNo" @click="call">联系药店</span>
    </div>

    <!-- 药品 -->
    <div class="drug">
      <!-- 单个药品 -->
      <div class="item" v-for="(item, index) in list" :key="index">
        <img
          v-if="item.drugImg"
          v-img="item.drugImg"
          :data-src="errUrl"
          class="img"
          alt=""
        />
        <!-- 商品图片 -->
        <image
          src="/static/shop/drug.png"
          v-else
          mode="aspectFill"
          class="img"
        ></image>

        <div class="right">
          <p class="title">{{ item.drugname || item.drugName }}</p>
          <p class="info">规格: {{ item.gg }}</p>
          <p class="active" v-if="item.activeName || item.activename">
            单品{{ item.activename || item.activeName }}
          </p>
          <p class="price">
            <span class="red"
              >￥{{
                (item.drugtotalreal || item.drugTotalReal || 0) | toFixed
              }}
              <span
                class="del"
                v-if="
                  (item.drugTotal || item.drugtotal) !=
                    (item.drugtotalreal || item.drugTotalReal)
                "
                >￥{{ item.drugTotal || item.drugtotal | toFixed }}</span
              ></span
            >

            <span>x{{ item.quan }}</span>
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DrugList',
  props: {
    name: String,
    list: {
      type: Array,
      default: () => [],
    },
    // 显示联系药店
    showTelNo: {
      type: Boolean,
      default: false,
    },
    // 药店联系方式
    telNo: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      errUrl: require('../../../static/images/Pharmacy-default.png'),
    };
  },
  methods: {
    call() {
      uni.makePhoneCall({
        phoneNumber: this.telNo,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.drug_list {
  font-size: 28rpx;

  .drug_title {
    @include flex(lr);
    height: 88rpx;

    p {
      @include flex(left);

      span {
        width: 6rpx;
        height: 28rpx;
        @include bg_theme;
        margin-right: 14rpx;
      }
    }

    .r {
      width: 140rpx;
      height: 50rpx;
      @include flex;
      border-radius: 30rpx;
      @include border_theme;
      @include font_theme;
      font-size: 24rpx;
    }
  }

  .drug {
    border-top: 1px solid #f5f5f5;
    border-bottom: 1px solid #f5f5f5;

    .item {
      @include flex(left);
      padding: 12rpx 0;

      &:last-child {
        .right {
          border-bottom: none;
        }
      }

      .img {
        width: 128rpx;
        height: 128rpx;
        border-radius: 8rpx;
        flex: none;
        margin-right: 24rpx;
        border: 1px solid #f5f5f5;
      }

      .right {
        flex: 1;
        min-height: 128rpx;
        @include flex(lr);
        align-items: stretch;
        flex-direction: column;
        border-bottom: 1px solid #f5f5f5;
        font-size: 24rpx;
        padding-bottom: 12rpx;

        .title {
          font-size: 28rpx;
          font-weight: bold;
        }

        .info {
          color: #999;
        }

        .active {
          color: red;
        }

        .price {
          @include flex(lr);

          .red {
            font-weight: bold;
            color: red;

            .del {
              font-weight: normal;
              font-size: 20rpx;
              color: #666;
              text-decoration: line-through;
              padding-left: 10rpx;
            }
          }
        }
      }
    }
  }
}
</style>
