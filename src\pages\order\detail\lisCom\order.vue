<template>
  <!-- 订单信息 -->
  <view class="order">
    <view class="order_item">
      <text class="title"> 订单信息 </text>
    </view>
    <view v-if="isConcat!=2" class="concat" @click="toCustomerService">
          联系客服
        </view>
    <view class="order_item">
      <text> 订单信息 </text>
      <text> {{ info.orderNo }} </text>
    </view>

    <view class="order_item">
      <text> 创建时间 </text>
      <text> {{ info.addTime }} </text>
    </view>

    <view class="order_item" v-if="info.payTime">
      <text> 支付时间 </text>
      <text> {{ info.payTime }} </text>
    </view>

    <view class="order_item" v-if="false">
      <text> 成交时间 </text>
      <text> 2020-02-22 10:00:34 </text>
    </view>

    <view class="order_item" v-if="info.returnTime">
      <text> 退费时间 </text>
      <text> {{ info.returnTime }} </text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'Order',
  props: ['info','isConcat'],
  methods: {
    toCustomerService() {
      uni.navigateTo({
        url: '/pages/personalCenter/casuggestions/index?orderNo=' + this.info.orderNo + '&source=gh',
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.order {
  position: relative;
  background: #fff;
  padding: 10rpx 32rpx;
  width: 90%;
  margin: auto;
  margin-top: -32rpx;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px 1px 4px  rgba(121, 72, 234, 0.15);
  border-radius: 10px;
  .order_item {
    @include flex(left);
    font-size: 28rpx;
    color: #333;
    height: 60rpx;

    .title {
      font-weight: bold;
    }

    text {
      margin-right: 32rpx;
    }
  }
}
.concat {
  position: absolute;
  right: 32rpx;
  top: 20rpx;
  font-size: 24rpx;
  color: #fff;
  background: linear-gradient(135deg, #516afb 0%, #859bff 99.97%);
  padding: 10rpx 20rpx;
  border-radius: 10rpx;
}
</style>
