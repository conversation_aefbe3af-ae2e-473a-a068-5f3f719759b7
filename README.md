# 患者端公众号项目

 项目整理 使用 uniapp 框架编写（只做 h5 端，无需考虑其他）

## 用到的一些 js 相关语法

- 对象解构赋值 let {a} = obj;
- 函数默认值 function test(n = 0) {return n}
- 箭头函数 =>
- async await
- 展开运算符 ...
- 常量 作用域变量 const let
- class 类
- 对象赋值简写 {name}
- Promise

## 下载依赖

```sh
npm install
```

## 本地启动

```sh
# 启动

npm run serve
```

## 打包编译

```sh
# 打包
npm run build
```

## 打包并部署（开发或测试环境）

需在根目录 **upload.sh** 文件内填写相关配置，打包完成后会提示输入密码，输入密码回车，即可自动上传

```sh
# 打包并上传开发环境目录
npm run upload-dev

# 打包并上传测试环境目录
npm run upload-test
```

## 地图选点

 使用腾讯地图

流程：

1，获取定位，如果失败则使用ip定位，ip定位失败则使用默认定位（天安门坐标）

2，根据定位，初始化地图，并已当前定位坐标搜索周边位置

拖动地图，中心点变化，则重新根据选定定位搜索地址

## 录音相关流程

 区分安卓与 ios

### 安卓录音

 使用 **Recorder** 网页录音插件 录制 **amr** 格式 转为 **blob** 直接上传环信

### ios 录音

 1，使用微信 sdk 录音

 2，上传到微信素材库

 3，调用接口，从服务器下载 amr 音频

 4，将下载的录音文件 blob 上传环信

### 播放录音

 使用 **BenzAMRRecorder** 插件进行播放 amr 格式音频

## 支付相关

微信支付为 公众号网页SDK发起支付 部分微信支付封装在 **src/modules/pay.js**

支付宝支付 则使用后台生成的支付链接 跳转到统一页面 **src/pages/pay/pay.vue** 此页面判断环境，使用支付宝必须在浏览器中打开

## SRC 主目录相关

| 目录       | 描述                 |
| :--------- | :------------------- |
| api        | 接口相关             |
| common     | 一部分 js 文件       |
| components | 全局组件             |
| db         | indexedDB 数据库封装 |
| mixins     | 公用混入             |
| modules    | 封装的微信支付       |
| pages      | 具体页面             |
| sdk        | 环信 sdk             |
| static     | 静态资源图片         |
| store      | vuex 相关            |
| utils      | 一部分 js 文件       |

## 配置相关

 接口配置，环信配置，SCSS 配置

### 接口配置

> common/request/config.js

| 字段           | 描述                        |
| -------------- | --------------------------- |
| serverUrl      | 接口域名                    |
| ossUrl         | 阿里云 oss 域名             |
| lisUrl         | 暂时无用                    |
| shareLogoUrl   | 公众号网页分享 logo 地址    |
| firmId         | 来源 根据环境 例如 YiXiaoLu |
| version        | 版本                        |
| clientType     | 类型 G                      |
| downLoadAppUrl | 医生端 app 下载地址         |
| rootUrl        | 患者端完整路径              |

### 环信配置

> utils/WebIMConfig.js

| 字段    | 描述           |
| ------- | -------------- |
| xmppURL | websocket 地址 |
| apiURL  | 环信提供       |
| appKey  | 环信 key       |
| Host    | 环信提供       |

### SCSS 配置

> src 目录下 uni.scss 文件

定义一些主题色，以及混入函数，例如 flex 布局， 文本溢出隐藏等

## 注意事项

> 项目中很多后台图片 使用 oss 服务，展示图片需拿 文件名称调用接口 处理返回的 url 地址 才可展示

现已封装为 自定义指令 **v-img** 但必须使用原生 `<img />` 标签，如需预览 **v-img:click** ，uniapp 内置的 image 组件在 h5 端不是单一 dom

```html
<!-- 不可预览 没有默认图 -->
<img v-img="src" />

<!-- 下载图片失败 使用默认图 -->
<img v-img="src" :data-src="errUrl" />

<!-- 下载图片失败 使用默认图 点击预览 -->
<img v-img:click="src" :data-src="errUrl" />
```

### 移动端调试工具

 用于移动端网页查看请求，本地存储，以及打印的数据

```html
<script type="text/javascript" src="//cdn.jsdelivr.net/npm/eruda"></script>
```

初始化工具

```js
eruda.init();
```

可在 init 初始页面时，连续点击启动图标 3 下 即可初始化

## 页面相关

> 所有页面均在 **pages** 目录下

### Tabbar 页面

- 首页 index
- 沟通 chatList
- 购药 shop
- 我的 personalCenter

### 其他页面

> 大体分类

| 目录                  | 描述                                   |
| --------------------- | -------------------------------------- |
| address               | 地址相关                               |
| chatCardDetail        | 聊天卡片相关                           |
| continuedPrescription | 聊天中慢病续方                         |
| disease               | 首页慢病续方                           |
| docSign               | 医生注册                               |
| ewmCard               | 医生端分享的处方二维码页面             |
| guidance              | 首页智能导诊                           |
| init                  | 初始化页面（执行跳转，获取用户信息等） |
| inspect               | 检查检验单相关                         |
| invoice               | 发票服务相关页面                       |
| login                 | 用户注册登录页                         |
| order                 | 订单相关                               |
| patientRecord         | 关于挂号相关                           |
| pay                   | 支付宝支付                             |
| payment               | 代付页面                               |
| prescription          | 处方                                   |
| protocol              | 协议                                   |
| quick                 | 快捷开方                               |
| register              | 挂号相关流程                           |
| scanCode              | 扫码开方                               |
| search                | 首页搜索                               |
| shareDocCard          | 医生分享页面                           |
| shopOrder             | 商城订单相关                           |

### 开发环境跳过 code 换取 openid 以及用户信息的方式

> src/pages/init/index.vue

onLoad 方法内 找到以下代码

```javascript
// 公众号appid
this.appid = '';

// 默认医院id
this.hosId = 1;

this.$store.commit('setAppId', this.appid);

// 模拟微信信息
this.wxInfo = {
  city: '天津',
  country: '天津',
  headimgurl: 'xxx.jpg',
  nickname: '王可',
  openId: '',
  sex: '1',
};
this.$store.commit('setWxInfo', this.wxInfo);
```

填写 this.wxInfo 以及 appid 等信息 即可
