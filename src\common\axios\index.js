import axios from 'axios';

import urlConfig from '../request/config.js';
import Base64 from '../request/base64.js';
import md5 from '../request/md5.js';
let baseUrl = urlConfig.serverUrl;

const http = axios.create({
  baseURL: baseUrl,
});

// 添加请求拦截器
http.interceptors.request.use(
  function(config) {
    // 数据转换

    config.data = reSetData(config.data);
    config.headers = {
      Authorization: uni.getStorageSync('proPfInfo').token || '',
      hosId: uni.getStorageSync('hosId') || '',
      firmId: urlConfig.firmId,
      version: urlConfig.version,
      clientType: urlConfig.clientType,
    };
    // 在发送请求之前做些什么
    return config;
  },
  function(error) {
    // 对请求错误做些什么
    return Promise.reject(error);
  }
);

// 添加响应拦截器
http.interceptors.response.use(
  function(response) {
    // 对响应数据做点什么
    return response.data;
  },
  function(error) {
    // 对响应错误做点什么
    return Promise.reject(error);
  }
);

const reSetData = (requestData) => {
  let timestamp = new Date().getTime() + '';
  let nonce = guid();
  let token_info = uni.getStorageSync('proPfInfo').token;

  let base64Data = '';
  if (requestData) {
    base64Data = Base64.encode(JSON.stringify(requestData));
  } else {
    let map = {};
    base64Data = Base64.encode(JSON.stringify(map));
  }
  let data = {
    data:JSON.stringify(requestData) //base64Data,
  };
  data.timestamp = timestamp;
  data.nonce = nonce;
  if (token_info) {
    data.token = token_info;
  }
  let s = '';
  Object.keys(data)
    .sort()
    .forEach((k) => {
      if (data[k] && data[k].length > 0) {
        if (s.length > 0) {
          s += '&';
        }
        s += k + '=' + data[k];
      }
    });
  let md5Data = md5(s).toLocaleUpperCase();
  data.sign = md5Data;

  return data;
};

const guid = () => {
  return (
    S4() +
    S4() +
    '-' +
    S4() +
    '-' +
    S4() +
    '-' +
    S4() +
    '-' +
    S4() +
    S4() +
    S4()
  );
};

const S4 = () => {
  return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
};

export default http;
