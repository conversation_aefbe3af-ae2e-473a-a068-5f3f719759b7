<template>
  <view class="page-container">
    <view class="tab_title">
      <view class="tab_title_box" @click="setNav(0)">
        <view
          class="tab_title_font"
          :class="query.isOffLine == 0 ? 'active' : ''"
          >线上问诊
          <span v-if="query.isOffLine == 0">{{ total }}</span>
        </view>
      </view>
      <view class="tab_title_box" @click="setNav(1)">
        <view
          class="tab_title_font"
          :class="query.isOffLine == 1 ? 'active' : ''"
          >线下问诊
          <span v-if="query.isOffLine == 1">{{ total }}</span>
        </view>
      </view>
    </view>

    <view class="list">
      <!-- 内容 -->
      <block v-for="(item, index) in list">
        <CARD :item="item" :key="index" @click="toDetail" />
      </block>
    </view>

    <!-- 空白 -->
    <view class="empt" v-show="!list.length">
      <image src="/static/images/index/box_empty.png"></image>
      <text>暂无预约</text>
    </view>
  </view>
</template>

<script>
import { selectAppointListPage } from "@/api/appoint.js";
import DateJs from "@/utils/date.js";
import CARD from "./com/card.vue";

export default {
  components: {
    CARD,
  },
  data() {
    return {
      tab: ["线上问诊", "线下问诊"],
      // 当前日期
      nowDate: DateJs.getFormatDate("-"),
      query: {
        limit: 10,
        page: 1,
        // 1 线下 0 线上
        isOffLine: 0,
        patientIds: uni.getStorageSync("patientIdList") || [],
      },
      list: [],
      total: 0,
    };
  },
  onLoad() {
    this.getList();
  },
  onPullDownRefresh() {
    this.query.page = 1;
    this.getList();
    setTimeout(() => {
      uni.stopPullDownRefresh();
    }, 1000);
  },
  methods: {
    // 获取我的预约
    async getList() {
      let {
        data: { rows, total },
      } = await selectAppointListPage(this.query);
      if (!rows) return;
      if (this.query.page > 1) {
        this.list = [...this.list, ...rows];
      } else {
        this.list = rows;
      }
      this.total = total;
    },
    // 设置列表
    setNav(n) {
      if (this.query.isOffLine == n) return;
      this.query.page = 1;
      this.query.isOffLine = n;
      this.getList();
    },
    // 去签到
    goSign(item) {
      if (item.extendInfo.ywStatus != 1) return;
      uni.setStorageSync("hosId", item.hosId);
      uni.navigateTo({
        url:
          "/pages/personalCenter/myAppoint/signInfo/index?appointSignInfo=" +
          JSON.stringify(item),
      });
    },
    // 去详情
    toDetail(v) {
      uni.navigateTo({
        url: "/pages/personalCenter/diagnosisRecord/detail?id=" + v.regId,
      });
    },

    // 跳转预约成功页面 （取消预约）
    toPath(item) {
      let { extendInfo: obj } = item;
      // 状态不为 待签到 不可跳转
      if (obj.ywStatus != 1) return;
      uni.navigateTo({
        url:
          "/pages/chatCardDetail/outpatientDepartment?docId=" +
          item.docId +
          "&patientId=" +
          item.patientId +
          "&date=" +
          obj.appointTime +
          "&poaId=" +
          item.poaId +
          "&status=1",
      });
    },
  },
};
</script>

<style scoped lang="scss">
.page-container {
  box-sizing: border-box;
}

page {
  background-color: #f0f2fc;
}

/* tab标签 */
.tab_title {
  display: flex;
  align-items: center;
  font-size: 34rpx;
  text-align: center;
  height: 88rpx;
  padding: 0 24rpx;
  position: sticky;
  top: 0;
  z-index: 3;
  box-sizing: border-box;
  background: #ffffff;
}

.tab_title .tab_title_box {
  flex: 1;
}

.tab_title .tab_title_font {
  color: #333333;
  display: inline-block;
  border-bottom: 6rpx solid transparent;
  line-height: 88rpx;
  padding-top: 6rpx;
  font-size: 32rpx;
  box-sizing: border-box;
  position: relative;
  span {
    width: 19px;
    height: 19px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    font-size: 12px;
    position: absolute;
    right: -15px;
    top: 10px;
    background-color: #ff6253;
    border-radius: 50%;
  }
}

.tab_title .active {
  padding-top: 6rpx;
  line-height: 88rpx;
  @include font_theme;
  display: inline-block;
  @include border_theme(6rpx, bottom);
  box-sizing: border-box;
  font-size: 36rpx;
  color: #333333 !important;
}

.list {
  padding: 32rpx;
}

/* 空白 */
.empt {
  margin: 80rpx 0;
  text-align: center;
  font-size: 28rpx;
  color: #666666;
}

.empt image {
  display: block;
  margin: 0 auto;
}
</style>
