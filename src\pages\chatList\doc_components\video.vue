<template>
  <!-- 视频消息 -->
  <view class="chat_video">
    <!-- 头像 -->
    <image
      @click="head"
      :src="imgUrl || '/static/images/docHead.png'"
      mode="aspectFill"
      class="chat_user_img"
    />
    <!-- 气泡 -->
    <view class="video_content" :id="'k' + id" @click.stop="click">
      <!-- 视频 -->
      <!-- <video
        class="cont_video"
        :id="'v_' + id"
        :poster="poster"
        :key="id"
        :src="videoSrc"
        @loadedmetadata="videoLoad"
        x-webkit-airplay="allow"
        x5-video-player-type="h5"
        x5-video-player-fullscreen="true"
        x5-video-orientation="portraint"
        webkit-playsinline="true"
        playsinline="true"
        preload="Metadata"
        initial-time="1"
        controls
      >
        <source :src="videoSrc" type="video/mp4" />
      </video> -->
      <iframe
        class="cont_video"
        ref="mapIframe"
        scrolling="no"
        :src="iframeSrc"
      ></iframe>
      <!-- 遮罩 -->
      <!-- <div class="zhe" v-show="!playing"></div> -->
      <!-- 时长 -->
      <view class="video_duration" v-if="duration && false">{{
        duration
      }}</view>
    </view>
  </view>
</template>

<script>
let u = navigator.userAgent;
let isIOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);
export default {
  name: "ChatVideo",
  props: {
    // 头像
    imgUrl: {
      type: String,
      default: "",
    },
    // 地址
    videoSrc: {
      type: String,
      default: "",
    },
    // id
    id: {
      type: String,
      default: "",
    },
    // 封面
    poster: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      duration: 0,
      v: "",
      src: "",
      playing: false,
      //'https://dev.llootong.net/cloud/cloudHosPatient/index.html',
      iframeSrc: "",
    };
  },
  created() {
    let src = "videoCompent/video.html";
    this.iframeSrc = src;
    this.iframeSrc += "?add=" + this.videoSrc;
  },
  mounted() {},
  methods: {
    // 设置时长
    setNum(str) {
      let n = Number(str).toFixed(0);
      let m = parseInt(n / 60);
      let s = n % 60;
      if (n < 10) {
        m = "0" + m;
      }
      if (s < 10) {
        s = "0" + s;
      }
      return m + ":" + s;
    },
    head() {
      this.$emit("head");
    },
    async click() {},
    videoLoad(e) {
      let id = "v_" + this.id;
      let v = uni.createVideoContext(id, this);
      v.play();

      v.seek(2);

      setTimeout(() => {
        v.pause();
        v = null;
      }, 200);
    },
  },
};
</script>

<style lang="scss" scoped>
.chat_video {
  max-width: 600upx;
  box-sizing: border-box;
  @include flex(left);
  padding-bottom: 30upx;
  align-items: flex-start;

  // 头像
  .chat_user_img {
    width: 64upx;
    height: 64upx;
    border-radius: 4upx;
    margin-right: 16upx;
    flex: none;
  }

  // 内容
  .video_content {
    width: 280upx;
    position: relative;

    .video_duration {
      position: absolute;
      bottom: 20upx;
      right: 20upx;
      font-size: 28upx;
      color: #fff;
      z-index: 9;
    }

    .zhe {
      position: absolute;
      width: 100%;
      height: 100%;
      z-index: 1;
      top: 0;
      left: 0;
    }

    /deep/.cont_video {
      width: 100%;
      border-radius: 16upx;
    }
  }
}
</style>
