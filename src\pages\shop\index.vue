<template>
  <!-- 在线购药首页 -->
  <view class="shop_index">
    <!-- 搜索 -->
    <view class="search">
      <view class="address" @click="toAddress">
        <!--        <image src="/static/doc/zaixian.png"></image>-->
        <uni-icons type="location-filled" size="14" color="#999"></uni-icons>
        <!--        <text class="city">{{ location.city }}</text>-->
        <text class="city">{{ checkStore.drugstoreName }}</text>
      </view>
      <view class="cont" @click="toSearch">
        <uni-icons type="search" size="20" color="#C1C1C1"></uni-icons>
        <view class="text">搜索要购买的商品</view>
        <view class="but">搜索</view>
      </view>
      <view class="order" v-if="isYnw" @click="toOrder">我的订单</view>
    </view>
    <!-- 主体 -->
    <view class="shop_cont">
      <!-- 左侧导航 -->
      <view class="menu">
        <view class="menu_item" :class="{ act: act == -1 }" @click="setMenu(-1)"
          >全部</view
        >
        <!-- 一级菜单 -->
        <view
          class="menu_item"
          :class="{ act: act == index }"
          v-for="(item, index) in menu"
          :key="item.dstId"
          @click="setMenu(index)"
          >{{ item.classificationName }}</view
        >
      </view>
      <!-- 右侧内容 -->
      <view class="cont_list">
        <!-- 二级分类 -->
        <view class="list_nav" v-if="sub_menu.length">
          <text
            class="span"
            :class="{ act: act > -1 ? dstId == menu[act].dstId : true }"
            @click="setSubMenu('')"
            >全部</text
          >

          <text
            class="span"
            :class="{ act: dstId == item.dstId }"
            v-for="item in sub_menu"
            :key="item.dstId"
            @click="setSubMenu(item.dstId)"
            >{{ item.classificationName }}</text
          >
        </view>
        <!-- 药品列表 -->
        <view class="drug_list">
          <scroll-view
            class="scroll"
            :scroll-top="scrollTop"
            scroll-with-animation
            scroll-y="true"
            @scroll="scroll"
            @scrolltolower="loadMoreData"
          >
            <view
              class="drug_item"
              v-for="(item, index) in list"
              :key="item.yfkcId"
            >
              <DRUG :item="item" :index="index" @reduce="reduce" @add="add" />
            </view>
            <!-- 空列表 -->
            <view class="empty" v-if="!list.length">
              <image
                src="/static/images/index/box_empty.png"
                mode="widthFix"
                class="img"
              ></image>
              <text>暂无数据</text>
            </view>

            <!-- 到底了 -->
            <view class="list_footer" v-if="list.length">已经到底了</view>
          </scroll-view>

          <!-- 悬浮 -->
          <view class="round" :class="{ act: drugNum }" @click="setShowCart">
            <image v-show="!drugNum" src="/static/shop/car.png"></image>
            <view class="show" v-show="drugNum">
              <image src="/static/shop/cart_act.png"></image>
              <text>去结算</text>
            </view>

            <text class="num" v-show="drugNum">{{ drugNum }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 购物车 -->
    <CART
      v-show="showCart"
      ref="cart"
      @itemChange="itemChange"
      @clear="initList"
      @close="showCart = false"
    />
  </view>
</template>

<script>
import LOCATION from "@/mixins/location.js";
import {
  getDrugStoreTypeList,
  onlineDrugPurchaseQueryByLatLng,
} from "@/api/shop.js";
import { getSysPlatformConfigByKeyList } from "@/api/base.js";
import DRUG from "./components/drugItem.vue";
import CART from "./components/cart.vue";
import { mapGetters, mapActions } from "vuex";
import { getCityById } from "./map";
import Login from "@/mixins/login.js";
import { getDrugStoreInfoPage } from "../../api/base";
import drugRulesMixin from '@/mixins/drugRules.js'
// 是否第一次
let isFrist = true;

export default {
  name: "ShopIndex",
  mixins: [Login, LOCATION,drugRulesMixin],
  components: {
    DRUG,
    CART,
  },
  data() {
    return {
      scrollTop: 0,
      // 分类
      menu: [],
      // 当前选中
      act: -1,
      // 二级分类
      sub_menu: [],
      // 列表
      list: [],
      // 搜索条件
      dstId: "",
      // 显示购物车
      showCart: false,
      // 位置
      location: {},
      // 是否ynw
      isYnw: false,
      totalNum: 0,
      listquery: {
        page: 1,
        limit: 10,
      },
      source:1
    };
  },
  computed: {
    ...mapGetters(["drugNum", "shopList", "checkStore"]),
  },
  async onLoad() {
    await this.getConfig();
    let isHide = uni.getStorageSync("hideTab");
    if (isHide && isHide == 1) {
      uni.hideTabBar();
    }
    
  },
  created() {
    console.log(uni.getStorageSync('sourceCode'),'sourceCode');
    // source 618 (618商城活动卖药)
    this.source= uni.getStorageSync('sourceCode')||0
  },
  onLoad(options){
    this.source=options.source
  },
  async onShow() {
    await this.initDrugRules()
    console.log(this.source,'sourceCode this.source' );
    this.getCartList()
    let cityInfo = uni.getStorageSync("shop_city") || "";
    if (cityInfo) {
      this.location = cityInfo;
    }
    // this.$store.commit("shop/SETCHECKSTORE", {});
    if(this.source=='618'){
      if (!this.checkStore.drugstoreName) {
        await this.getDrugStoreInfoPage();
      }
    }else{
      if (!this.checkStore.drugstoreName) {
        await this.getDrugStoreInfoPage();
      }
    }
    // await this.getCity();
    await this.getMenu();
    this.initList()
  },
  methods: {
    ...mapActions("shop", ["addDrugItem", "reduceDrugItem","getCartList"]),
    // 获取配置
    loadMoreData() {
      if (this.list.length >= this.totalNum) return;
      this.listquery.page += 1;
      this.getList();
    },
    initList(){
      this.list=[]
      this.listquery.page = 1;
      this.getList();
    },
    async getConfig() {
      let { data } = await getSysPlatformConfigByKeyList([
        "shoppingOnlineFastBuyDrug",
      ]);
      if (data.length) {
        // 1 为新流程 不可多选
        this.isYnw = data[0].configValue == 1 ? true : false;
      }
    },
    async getDrugStoreInfoPage() {
      const res = await getDrugStoreInfoPage({});
      let storeList = res.data;
      if(this.source=='618'){
        const store= storeList.find(item=>item.drugstoreId=='574a40ea01424f7a8b62762b37ff58e2')||{}
        this.$store.commit("shop/SETCHECKSTORE", store);
        return
      }
      storeList=storeList.filter(item=>item.drugstoreId!='574a40ea01424f7a8b62762b37ff58e2')
      this.$store.commit("shop/SETCHECKSTORE", storeList[0]);
    },
    // 去订单
    toOrder() {
      if (!this.hasInfo()) return;
      uni.navigateTo({
        url: "/pages/shopOrder/index",
      });
    },
    // 获取当前城市
    async getCity() {
      let infos = {
        city: "正在获取中",
      };
      this.location = infos;
      let info;
      try {
        info = await this.wxsdkInit();
      } catch (error) {
        info = await getCityById();
      }
      this.location = info;
      uni.setStorageSync("shop_city", info);
      isFrist = false;
    },
    // 跳转选择地址
    toAddress() {
      if (!this.hasInfo()) return;
      // uni.navigateTo({
      //   url: "/pages/shop/address/location?city=" + this.location.city,
      // });
      uni.navigateTo({
        url: "/pages/shop/store?city=" + JSON.stringify(this.location),
      });
    },
    // 购物车药品变化
    itemChange(item) {
      // 查找当前列表指定药品的下标
      const index = this.list.findIndex((v) => v.yfkcId == item.yfkcId);
      if (index == -1) return;
      let it = this.list[index];
      it.quan = item.quan;
      console.log(it,'it',item);
      
      // 修改当前药品数量
      this.$set(this.list, index, it);
    },
    // 药品减少
    async reduce(item, index,amount = 1) {
      console.log(item,'item',amount);
      await this.reduceDrugItem({ ...item, amount });
      if(item.drugId === '72d263bd0ebd48e49e9063320efa0ff7'&&amount>=60){
        item.quan = 60
        this.$set(this.list, index, item);
        console.log(this.list,'this.list');
        return
      }
      item.quan -= amount;
      item.inputQuan = item.quan
      if (item.quan <= 0) item.quan = 0;
      this.$set(this.list, index, item);
    },
    // 药品增加
    async add(item, index,amount = 1) {
      console.log(item,'item',amount);
      await this.addDrugItem({ ...item, amount });
      if(item.drugId === '72d263bd0ebd48e49e9063320efa0ff7'&&amount>=60){
        item.quan = 60
        this.$set(this.list, index, item);
        return
      }
      item.quan += amount;
      item.inputQuan = item.quan
      this.$set(this.list, index, item);
    },
    // 显示购物车
    setShowCart() {
      if (!this.drugNum) return;
      this.showCart = true;
      this.$refs.cart.showList();
    },
    // 点击一级菜单
    setMenu(n) {
      this.listquery.page = 1;
      this.listquery.limit = 10;
      this.list = [];
      if (this.act == n) return;
      this.act = n;
      if (n == -1) {
        this.dstId = "";
        this.sub_menu = [];
      }
      if (n > -1) {
        // 改变二级菜单
        this.sub_menu = this.menu[n].children;
        this.dstId = this.menu[n].dstId;
      }
      this.scrollTop = 0;
      this.getList();
    },
    // 点击二级菜单
    setSubMenu(id) {
      this.listquery.page = 1;
      this.listquery.limit = 10;
      if (!id) {
        if (this.act == -1) {
          this.dstId = "";
        } else {
          this.dstId = this.menu[this.act].dstId;
        }
      } else {
        if (this.dstId == id) return;
        this.dstId = id;
      }

      this.scrollTop = 0;
      this.getList();
    },
    // 滚动到底
    scrollFooter() {},
    // 监听滚动
    
    scroll(e) {
      const { scrollTop } = e.detail;
      this.scrollTop = scrollTop;
    },
    // 去搜索
    toSearch() {
      uni.navigateTo({
        url: "./search/search",
      });
    },
    // 获取分类
    getMenu() {
      console.log("this.checkStore", this.checkStore);
      getDrugStoreTypeList({ drugStoreId: this.checkStore.drugstoreId }).then(
        (res) => {
          let menu = [];
          let arr = [];
          res.data.forEach((item) => {
            if (item.grade == 1) {
              menu.push(item);
            } else if (item.grade == 2) {
              arr.push(item);
            }
          });
          menu.forEach((v) => {
            v.children = arr.filter((item) => item.pdstId == v.dstId);
          });
          // 分类
          this.menu = menu;
        }
      );
    },
    // 获取药品列表
    async getList() {
      const { lat, lng, province } = this.location;

      const drugId = uni.getStorageSync("ynw_drugId");

      let res = await onlineDrugPurchaseQueryByLatLng({
        ...this.listquery,
        dstId: this.dstId,
        latitude: lat,
        longitude: lng,
        provinceName: this.isYnw ? province : "",
        drugIdList: drugId ? [drugId] : [],
        drugstoreId: this.checkStore.drugstoreId,
      });
      let total = res.data.total;
      this.totalNum = total;
      if (this.listquery.page > 1) {
        this.list = [...this.list, ...res.data.rows];
      } else {
        this.list = res.data.rows;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
uni-page-body {
  height: 100%;
  touch-action: none;

  * {
    box-sizing: border-box;
  }
}

.shop_index {
  background-color: #fff;
  height: 100%;
  @include flex;
  flex-direction: column;
  align-items: stretch;

  .search {
    height: 88rpx;
    padding: 0 24rpx 0 0;
    @include flex;
    flex: none;
    .address {
      flex: none;
      @include flex;
      font-size: 28rpx;
      width: 40%;

      .city {
        @include hide;
      }
      image {
        width: 120rpx;
        height: 40rpx;
      }
    }

    .cont {
      width: 100%;
      padding: 0 24rpx;
      height: 60rpx;
      border-radius: 30rpx;
      background-color: #f5f5f5;
      @include flex(lr);

      .text {
        font-size: 28rpx;
        color: #c1c1c1;
        flex: 1;
        padding-left: 20rpx;
      }

      .but {
        font-size: 28rpx;
        @include font_theme;
      }
    }

    .order {
      flex: none;
      margin-left: 14rpx;
      width: 148rpx;
      height: 60rpx;
      @include bg_theme;
      color: #fff;
      font-size: 24rpx;
      @include flex;
      border-radius: 30rpx;
    }
  }

  .shop_cont {
    flex: 1;
    @include flex;
    align-items: stretch;
    overflow: hidden;

    .menu {
      width: 186rpx;
      overflow-y: scroll;
      background-color: #fafafa;
      flex: none;

      .menu_item {
        height: 88rpx;
        width: 100%;
        @include flex;
        position: relative;
        font-size: 26rpx;
        color: #333;
        text-align: center;
        padding: 0 14rpx;

        &.act {
          @include font_theme;
          background-color: #fff;
          font-weight: bold;

          &::before {
            content: "";
            display: block;
            position: absolute;
            width: 6rpx;
            height: 24rpx;
            @include bg_theme;
            left: 8rpx;
            top: calc(50% - 12rpx);
            border-radius: 3rpx;
          }
        }
      }
    }

    .cont_list {
      width: calc(100% - 186rpx);
      padding: 0 20rpx;
      @include flex;
      flex-direction: column;
      align-items: stretch;
      justify-items: flex-start;

      .list_nav {
        height: 56rpx;
        overflow-x: scroll;
        white-space: nowrap;
        overflow-y: hidden;
        flex: none;

        .span {
          padding: 0 24rpx;
          height: 100%;
          @include flex;
          display: inline-flex;
          font-size: 24rpx;
          background-color: #fafafa;
          color: #333;
          margin-right: 12rpx;
          border-radius: 6rpx;

          &.act {
            @include font_theme;
            background-color: #e7f5fc;
          }
        }
      }

      .drug_list {
        width: 100%;
        // max-height: calc(100% - 56rpx);
        height: 100%;
        padding: 15rpx 0;
        overflow-y: scroll;

        .scroll {
          height: 100%;

          .empty {
            width: 100%;
            height: 80%;
            @include flex;
            flex-direction: column;

            .img {
              width: 100%;
            }

            text {
              font-size: 24rpx;
              color: #999;
              padding-top: 30rpx;
            }
          }
        }

        .list_footer {
          text-align: center;
          padding: 20rpx 0 100rpx;
          font-size: 24rpx;
          color: #999;
        }
      }
    }

    .round {
      min-width: 88rpx;
      height: 88rpx;
      position: fixed;
      bottom: 154rpx;
      z-index: 2;
      right: 24rpx;
      border-radius: 44rpx;
      background-color: #fafafa;
      @include flex;
      box-shadow: 0 0 20rpx #ddd;

      &.act {
        background-color: #e2f0f7;
      }

      image {
        width: 64rpx;
        height: 64rpx;
      }

      .num {
        min-width: 30rpx;
        height: 30rpx;
        padding: 0 4rpx;
        background-color: #ff5050;
        @include flex;
        font-size: 24rpx;
        color: #fff;
        border-radius: 16rpx;
        line-height: 24rpx;
        position: absolute;
        right: 0;
        top: 0;
      }

      .show {
        @include flex;
        padding: 0 24rpx;

        image {
          width: 44rpx;
          height: 44rpx;
          margin-right: 8rpx;
        }

        text {
          font-size: 28rpx;
          @include font_theme;
        }
      }
    }
  }
}
</style>
