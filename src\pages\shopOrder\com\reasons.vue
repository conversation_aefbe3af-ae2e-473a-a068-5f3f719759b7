<template>
  <div class="refuse">
    <div class="warp" v-for="item in list" :key="item.auditTime">
      <p class="label">
        <block v-if="item.auditStatus == 4">药店审核未通过</block>
        <block v-if="item.auditStatus == 5">药店拒绝您的订单</block>
      </p>

      <!-- 拒绝时间 -->
      <p class="date">
        <span>{{ item.auditTime }}</span>
        <span></span>
      </p>

      <!-- 具体 -->
      <div class="cont">
        <p class="text" v-if="item.reasonsForFailure">
          {{ item.reasonsForFailure }}
        </p>
        <p class="text" v-if="item.auditDescription">
          {{ item.auditDescription }}
        </p>

        <div class="img_list" v-if="item.pictureList">
          <img
            v-for="img in item.pictureList.split('|')"
            v-img:click="img"
            :key="img"
            alt=""
            class="img"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Reasons',
  props: {
    // 列表
    list: {
      type: Array,
      default: () => [],
    },
  },
};
</script>

<style lang="scss" scoped>
.refuse {
  padding-bottom: 24rpx;
  background-color: #fff;
  font-size: 28rpx;
  margin-bottom: 24rpx;

  .warp {
    padding: 0 32rpx 24rpx;
    border-bottom: 1px dashed #f5f5f5;

    &:last-child {
      border-bottom: none;
    }
  }

  .label {
    color: red;
    line-height: 80rpx;
  }

  .date {
    @include flex(lr);
    color: #333;
    padding-bottom: 24rpx;
    border-bottom: 1px solid #f5f5f5;
  }

  .cont {
    background-color: #f5f5f5;
    padding: 24rpx;

    .text {
      line-height: 40rpx;
    }

    .img_list {
      margin-top: 24rpx;
      @include flex(left);
      flex-wrap: wrap;
      gap: 28rpx;

      .img {
        width: 194rpx;
        height: 194rpx;
        border-radius: 8rpx;
        object-fit: cover;
      }
    }
  }
}
</style>
