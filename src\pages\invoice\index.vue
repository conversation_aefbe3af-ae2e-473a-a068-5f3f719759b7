<template>
  <div class="index">
    <!-- 选项 -->
    <div class="nav">
      <p :class="{ act: type == 0 }" @click="setType(0)">申请中发票</p>
      <p :class="{ act: type == 1 }" @click="setType(1)">已开发票</p>
    </div>

    <!-- 列表 -->
    <div class="list" v-if="list.length">
      <div
        class="item"
        v-for="item in list"
        :key="item.irId"
        @click="toDetail(item)"
      >
        <p class="title">
          {{ item.personalOrCompanyName }}
        </p>
        <p class="info">
          <span>{{ item.invoiceTypeName }}</span>
          <span>￥{{ item.invoiceMoney | toFixed }}</span>
        </p>
        <div class="last">
          <span :class="{ red: item.irStatus == 3 }">{{
            item.irStatusName
          }}</span>
          <p>
            <span>查看详情</span>
            <uni-icons type="arrowright" size="22" color="#666" />
          </p>
        </div>
      </div>
    </div>

    <div class="empty" v-if="!list.length">
      <image src="/static/images/index/box_empty.png" />
      <span>暂无发票信息</span>
    </div>

    <FOOTER @click="toWait">申请开票</FOOTER>
  </div>
</template>

<script>
import { getMyInvoiceRecordPage } from "@/api/invoice";
import FOOTER from "@/components/footer_button/button.vue";

export default {
  name: "Invoice",
  components: {
    FOOTER,
  },
  data() {
    return {
      type: 0,
      query: {
        // 1 申请中 2 已开发票
        fpStatus: "1",
        page: 1,
        limit: 10,
        patientUserId: uni.getStorageSync("userId"),
      },
      list: [],
      total: 0,
    };
  },
  onLoad() {
    this.getList();
  },
  methods: {
    // 选择类别
    setType(n) {
      if (n == this.type) return;
      this.type = n;
      this.query.fpStatus = n + 1;
      this.query.page = 1;
      this.getList();
      uni.pageScrollTo({
        duration: 100,
        scrollTop: 0,
      });
    },
    // 去开票
    toWait() {
      uni.navigateTo({
        url: "/pages/invoice/wait",
      });
    },
    // 去详情
    toDetail(item) {
      uni.navigateTo({
        url: "/pages/invoice/detail?irId=" + item.irId,
      });
    },
    async getList() {
      let { data } = await getMyInvoiceRecordPage(this.query);
      const { total, rows } = data;
      if (this.query.page == 1) {
        this.list = rows;
      } else {
        this.list = [...this.list, ...rows];
      }
      uni.stopPullDownRefresh();

      this.total = total;
    },
  },
  onPullDownRefresh() {
    this.query.page = 1;
    this.getList();
  },
  onReachBottom() {
    if (this.total <= this.list.length) return;
    this.query.page++;
    this.getList();
  },
};
</script>

<style lang="scss" scoped>
page {
  background: #f5f5f5;
}
.index {
  padding-bottom: 124rpx;

  .nav {
    height: 84rpx;
    background: #fff;
    position: sticky;
    top: 0;
    @include flex(lr);
    padding: 0 88rpx;
    box-shadow: 0 1px 0 0 #f5f5f5;

    p {
      font-size: 34rpx;
      height: 84rpx;
      position: relative;
      @include flex;

      &.act {
        @include font_theme;
        font-weight: bold;

        &::after {
          content: "";
          display: block;
          position: absolute;
          left: 50%;
          width: 80%;
          height: 4rpx;
          bottom: 0;
          @include bg_theme;
          transform: translateX(-50%);
        }
      }
    }
  }

  .list {
    padding: 24rpx 32rpx 0;

    .item {
      background: #fff;
      border-radius: 8rpx;
      font-size: 28rpx;
      padding-top: 24rpx;
      margin-bottom: 24rpx;

      .title {
        font-weight: bold;
        padding-left: 32rpx;
      }

      .info {
        @include flex(lr);
        margin-top: 24rpx;
        padding: 0 32rpx 24rpx;

        span {
          color: #666;

          &:last-child {
            color: red;
          }
        }
      }

      .last {
        border-top: 1px solid #f5f5f5;
        @include flex(lr);
        padding: 24rpx 32rpx;
        color: #666;

        .red {
          color: red;
        }

        p {
          @include flex;
        }
      }
    }
  }

  .empty {
    padding-top: 100rpx;
    @include flex;
    flex-direction: column;

    image {
      width: 600rpx;
    }

    span {
      font-size: 28rpx;
      color: #666;
      margin-top: 24rpx;
    }
  }
}
</style>
