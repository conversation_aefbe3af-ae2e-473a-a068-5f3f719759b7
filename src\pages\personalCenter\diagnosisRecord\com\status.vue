<template>
  <view :class="status == 3 ? 'tuiStatus' : 'status'">
    <img v-if="status != 3" class="n2" src="/static/doc/n2.png" alt="" />
    <img
      v-if="status == 3"
      style="width: 24px; height: 24px"
      src="/static/pre/路径 <EMAIL>"
      alt=""
    />
    <block v-if="status == 0">
      <view class="h2">已失效</view>
      <view class="status_info">您的挂号已失效</view>
    </block>

    <block v-if="status == 1">
      <view class="h2">待支付</view>
      <view class="status_info">请尽快支付，超时未支付将自动取消挂号</view>
    </block>

    <block v-if="status == 2">
      <view class="h2">待签到</view>
      <view class="status_info">请于{{ time }},进行签到</view>
    </block>

    <view v-if="status == 3" style="display: flex; flex-direction: column">
      <view class="h2" style="margin-left: 14rpx">已退费</view>
      <view class="status_info_a">已为您退费，请注意查收</view>
    </view>

    <block v-if="status == 4&&detail.bussType !=7">
      <view class="h2">待接诊</view>
      <view class="status_info">
        您当前为第<text>{{ queueNumber }}</text
        >位，前面还有{{ frontNum }}位，请耐心等待
      </view>
    </block>
    <block v-if="status == 4&&detail.bussType ==7">
      <view class="h2">待接诊</view>
    </block>
    <block v-if="status == 5">
      <view class="h2">接诊中</view>
      <view class="status_info">正在问诊中…</view>
    </block>

    <block v-if="status == 6">
      <view class="h2">已结束</view>
      <view class="status_info">本次服务已完成</view>
    </block>

    <image v-if="status != 3" class="icon" src="/static/doc/n3.png" />
    <image v-if="status == 3" class="icon" src="/static/pre/路径 <EMAIL>" />
  </view>
</template>

<script>
export default {
  name: "Status",
  props: {
    status: {
      type: Number | String,
      default: 0,
    },
    time: {
      type: String,
      default: "",
    },
    // 排队
    queueNumber: {
      type: String | Number,
      default: 0,
    },
    // 前面
    frontNum: {
      type: String | Number,
      default: 0,
    },
    detail:{
      type: Object,
      default: () => {},
    }
  },
};
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}
.status_info {
  margin-left: 60rpx;
  font-size: 12px !important;
}
.status_info_a {
  margin-left: 14rpx;
  margin-top: 14rpx;
  font-size: 12px !important;
}
.tuiStatus {
  width: 100%;
  height: 180rpx;
  background: linear-gradient(90.8deg, #3b60ef 60%, #2cc793 130%);
  color: #fff;
  padding: 32rpx;
  border-radius: 0 0 8rpx 8rpx;
  display: flex;
  .n2 {
    float: left;
    margin-right: 20rpx;
  }
  .h2 {
    font-size: 32rpx;
    font-weight: bold;
  }

  &_info {
    font-size: 28rpx;
    margin-top: 24rpx;

    text {
      font-size: 36rpx;
      padding: 0 10rpx;
      font-weight: bold;
    }
  }

  .icon {
    width: 100rpx;
    height: 100rpx;
    position: absolute;
    top: 15px;
    right: 32rpx;
  }
}
.status {
  width: 100%;
  height: 180rpx;
  background: linear-gradient(
    162.8deg,
    rgba(44, 199, 147, 1) 40%,
    rgba(44, 199, 147, 0) 100%
  );
  color: #fff;
  padding: 32rpx;
  border-radius: 0 0 8rpx 8rpx;
  .n2 {
    float: left;
    margin-right: 20rpx;
  }
  .h2 {
    font-size: 32rpx;
    font-weight: bold;
  }

  &_info {
    font-size: 28rpx;
    margin-top: 24rpx;

    text {
      font-size: 36rpx;
      padding: 0 10rpx;
      font-weight: bold;
    }
  }

  .icon {
    width: 100rpx;
    height: 100rpx;
    position: absolute;
    top: 15px;
    right: 32rpx;
  }
}
</style>
