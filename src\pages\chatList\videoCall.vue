<template>
  <!-- 视频通话 -->
  <view class="video_call">
    <!-- 医生头像 -->
    <view class="doc_info" v-if="!isVideo">
      <image class="doc_head" :src="docImg" mode="aspectFill"></image>
      <view class="doc_name">{{ docName }}</view>
    </view>

    <!-- 对方视频 -->
    <video
      id="docter"
      v-show="isVideo"
      :class="isMeMax ? 'docter_video' : 'me_video'"
      :controls="false"
      :show-progress="false"
      @click="onSwitch"
      :show-fullscreen-btn="false"
      :show-play-btn="false"
      :show-center-play-btn="false"
      :enable-progress-gesture="false"
      x-webkit-airplay="allow"
      x5-video-player-type="h5"
      x5-video-player-fullscreen="true"
      x5-video-orientation="portraint"
      webkit-playsinline="true"
      playsinline="true"
    ></video>

    <!-- 自己视频 -->
    <video
      id="me"
      v-show="isVideo"
      :class="isMeMax ? 'me_video' : 'docter_video'"
      :controls="false"
      :show-progress="false"
      object-fit="cover"
      @click="onSwitch"
      :show-fullscreen-btn="false"
      :show-play-btn="false"
      :show-center-play-btn="false"
      :enable-progress-gesture="false"
      x-webkit-airplay="allow"
      x5-video-player-type="h5"
      x5-video-player-fullscreen="true"
      x5-video-orientation="portraint"
      webkit-playsinline="true"
      playsinline="true"
    ></video>

    <!-- 控制按钮 -->
    <view class="footer" v-if="!showTip">
      <!-- 倒计时 -->
      <view class="footer_time" v-if="isPrepool==0">{{ timeStr }}</view>
      <!-- 静音 -->
      <view class="menu_item" v-if="false">
        <image src="/static/images/video/mute.png" class="item_icon" />
        <button>静音</button>
      </view>

      <!-- 挂断 -->
      <view class="menu_item" @click="hangUp">
        <image src="/static/images/video/hangUp.png" class="item_icon" />
        <button>挂断</button>
      </view>

      <!-- 切换 -->
      <view class="menu_item" @click="setCamera" v-if="false">
        <image src="/static/images/video/switch.png" class="item_icon" />
        <button>切换</button>
      </view>
    </view>

    <TIP v-if="showTip" />

    <!-- 遮罩 -->
    <view class="zhe" v-if="showZhe">
      <view class="title">
        <image src="/static/images/video/waing.png" class="tip_icon"></image>
        提示
      </view>
      <view class="content">
        <text>抱歉！当前通话已失效</text>
        <text>如需继续沟通</text>
        <text>请回到微信公众号联系该医生</text>
      </view>
    </view>
  </view>
</template>

<script>
import { Toast } from '@/common/js/pay.js';
let emedia = require('@/utils/WebIM.js')['emedia'];

import { findDoctorByID } from '@/api/base';

import myJsTools from '../../common/js/myJsTools';

import {
  // 挂断
  hangUpVideo,
  // 接听
  videoPhone,
} from '@/api/pv.js';

let timer;

function getTime(n) {
  let h = parseInt(n / 60 / 60);
  let m = parseInt((n / 60) % 60);
  let s = parseInt(n % 60);
  if (h < 10) h = '0' + h;
  if (m < 10) m = '0' + m;
  if (s < 10) s = '0' + s;
  return h + ':' + m + ':' + s;
}

import TIP from '@/components/tips/tips.vue';

export default {
  name: 'VideoCall',
  components: {
    TIP,
  },
  data() {
    return {
      // 是否显示遮罩
      showZhe: false,
      // 是否微信内
      isWx: false,
      // 是否自己最大化
      isMeMax: false,
      // 服务id
      regId: '',
      // 用户 id
      userId: '',
      // token
      token: '',
      // 会议id
      confrId: '',
      // 是否挂断
      isHangUp: false,
      // 倒计时
      time: 10,
      // 循环
      timer: '',
      // 显示
      timeStr: '00:00:00',
      // 显示提示
      showTip: false,
      // 医生信息
      docImg: '',
      // 医生id
      docId: '',
      // 科室id
      deptId: '',
      // 医生姓名
      docName: '',
      // 是否视频
      isVideo: false,
      //是否流转处方
      isPrepool:''
    };
  },
  created() {},
  onLoad(obj) {
    // 是否ios
    let isIos = false;
    let { system } = uni.getSystemInfoSync();
    if (system.indexOf('iOS') != -1) {
      isIos = true;
    }

    // 是否微信
    let isWx;
    const ua = window.navigator.userAgent.toLowerCase();
    if (
      ua.match(/MicroMessenger/i) == 'micromessenger' ||
      ua.match(/_SQ_/i) == '_sq_'
    ) {
      isWx = true;
    } else {
      isWx = false;
    }
    this.isWx = isWx;

    // 赋值
    this.regId = obj.regId;
    this.confrId = obj.conferenceId;
    this.userId = obj.userId;
    this.token = obj.token;
    this.docId = obj.docId;
    this.deptId = obj.deptId;
    this.isVideo = obj.isVideo == 0 ? false : true;
    this.isPrepool=obj.isPrepool;
    let hosId = obj.hosId;
    // 如果存在hosId
    if (hosId) {
      uni.setStorageSync('hosId', hosId);
    }

    // 初始化
    this.init();

    // 获取头像
    this.getDocInfo();

    // 如果在微信环境 并且是ios 则提示打开浏览器
    if (isWx && isIos) {
      this.showTip = true;
      return;
    }

    this.showTip = false;

    // 如果是ios 需要先登录 并且不是微信内
    if (!isWx && isIos) {
      // 设置token
      uni.setStorageSync('proPfInfo', {
        token: this.token,
      });

      // 环信登录
      this.login().then((res) => {
        // 加入会议
        this.joinRoom();
      });
      return;
    }

    // 安卓
    this.joinRoom();
  },
  methods: {
    // 初始化监听
    init() {
      let that = this;
      //有人加入会议
      emedia.mgr.onMemberJoined = (member) => {
        // member: 加入会议成员信息
        console.log('加入会议', member);
      };
      // 监听流加入
      emedia.mgr.onStreamAdded = (member, stream) => {
        if (!stream.located()) {
          let docter = document.querySelector('#docter');
          let video = docter.querySelector('.uni-video-video');
          // 订阅流
          emedia.mgr.subscribe(member, stream, true, true, video);
        }
      };
      // 监听有人退出会议
      emedia.mgr.onMemberExited = (member) => {
        console.log('>>>>>', member.name, ' 退出会议');
        // 主动挂断并返回
        that.hangUp();
      };
      // 监听媒体流移除
      emedia.mgr.onStreamRemoved = (member, stream) => {
        console.log('>>>>>', member.name, ' 媒体流移除');
        // 自己移除流
        if (stream.located()) return;
        // 主动挂断并返回
        that.hangUp();
      };
      //监听弱网状态
      emedia.mgr.onNetworkWeak = (streamId) => {
        //streamId 会议中的流 ID
        console.log('弱网', streamId);
      };
      //监听断网状态
      emedia.mgr.onNetworkDisconnect = (streamId) => {
        //streamId 会议中的流 ID
        console.log('断网', streamId);
      };
      // 会议退出
      emedia.mgr.onConferenceExit = (reason, failed) => {
        reason = reason || 0;
        switch (reason) {
          case 0:
            reason = '正常挂断';
            break;
          case 1:
            reason = '没响应';
            break;
          case 2:
            reason = '服务器拒绝';
            break;
          case 3:
            reason = '对方忙';
            break;
          case 4:
            reason = '失败,可能是网络或服务器拒绝';
            if (failed === -9527) {
              reason = '失败,网络原因';
            }
            if (failed === -500) {
              reason = 'Ticket失效';
            }
            if (failed === -502) {
              reason = 'Ticket过期';
            }
            if (failed === -504) {
              reason = '链接已失效';
            }
            if (failed === -508) {
              reason = '会议无效';
            }
            if (failed === -510) {
              reason = '服务端限制';
            }
            break;
          case 5:
            reason = '不支持';
            break;
          case 10:
            reason = '其他设备登录';
            break;
          case 11:
            reason = '会议关闭';
            break;
        }
        Toast(reason);
        console.log('会议异常', reason);
        // this.hangUp();
      };
    },
    // 加入会议
    async joinRoom() {
      try {
        await emedia.mgr.joinConference(this.confrId, '123', '会议');

        // 加入成功 调用推流
        this.pushLive();

        // 调用接视频接口
        let data={//isShoppingRegId是否为商城挂号业务信息(0.否 1.是)
          regId:this.regId,
          isShoppingRegId:this.isPrepool
        }
        let res = await videoPhone(data);
        
        this.time = res.data.surplusDuration;
        if (!this.isWx) {
          uni.showModal({
            title: '提示',
            content: '点击确定开始通话',
            showCancel: false,
            success() {
              let doc = document
                .querySelector('#docter')
                .querySelector('.uni-video-video');
              let me = document
                .querySelector('#me')
                .querySelector('.uni-video-video');

              doc.play();
              me.play();
            },
          });
        }

        // 倒计时（正常挂号显示）
        if(this.isPrepool==0){
        this.setTime();  
        }
        
      } catch (e) {
        console.log('加入会议失败', e);

        Toast('当前通话已失效' + JSON.stringify(e));
        setTimeout(() => {
          this.hangUp();
        }, 1500);
      }
    },
    // 开始推流
    async pushLive() {
      let constaints;
      // 视频通话
      if (this.isVideo) {
        constaints = {
          audio: true,
          video: true,
        };
      } else {
        constaints = {
          audio: true,
        };
      }

      try {
        //发布视频流
        let pushedStream = await emedia.mgr.publish(constaints, '会议');

        let me = document.querySelector('#me');
        let video = me.querySelector('.uni-video-video');

        emedia.mgr.streamBindVideo(pushedStream, video);
        console.log('发布推流成功');
      } catch (e) {
        console.log('发布推流失败', e);
        Toast(JSON.stringify(e));
      }
    },
    // 挂断
    hangUp() {
      // 要挂断
      this.isHangUp = true;
      clearTimeout(timer);
      timer = setTimeout(() => {
        emedia.mgr.exitConference();
        this.sendServe();
        let length = getCurrentPages().length;
        // 如果有上一页 返回上一页
        if (length > 1) {
          uni.navigateBack();
        } else {
          // 如果不是微信环境
          if (!this.isWx) {
            this.showZhe = true;
            return;
          }
          uni.switchTab({
            url: '/pages/index/index',
          });
        }
      }, 500);
    },
    // 切换视频(自己跟医生谁大)
    onSwitch() {
      this.isMeMax = !this.isMeMax;
    },
    // 告诉后台挂断
    sendServe() {
      let obj = {
        confrId: this.confrId,
        regId: this.regId,
      };
      hangUpVideo(obj);
    },
    // 切换摄像头
    async setCamera() {
      // 切换手机前后摄像头
      await emedia.mgr.switchMobileCamera(this.confrId);
    },
    // 倒计时
    setTime() {
      this.timer = setInterval(() => {
        this.time--;
        this.timeStr = getTime(this.time);
        if (this.time <= 0) {
          clearInterval(this.timer);
          this.hangUp();
        }
      }, 1000);
    },
    // 环信登录
    login() {
      let _this = this;
      return new Promise((resolve, reject) => {
        let options = {
          apiUrl: _this.$im.config.apiURL,
          user: _this.userId,
          pwd: _this.userId,
          grant_type: _this.userId,
          appKey: _this.$im.config.appkey,
          success(res) {
            let memName = _this.$im.config.appkey + '_' + _this.userId;
            // 进入会议前 必须调用
            emedia.mgr.setIdentity(memName, res.access_token);
            setTimeout(resolve, 1000);
          },
          error(err) {
            reject(err);
          },
        };
        _this.$im.conn.open(options);
      });
    },
    // 获取医生头像信息
    async getDocInfo(id) {
      let res = await findDoctorByID({
        docId: this.docId,
        deptId: this.deptId,
      });
      this.docName = res.data.docName;
      if (res.data.docImg) {
        myJsTools.downAndSaveImg(res.data.docImg, (url) => {
          this.docImg = url;
        });
      }
    },
  },
  // 监听页面卸载
  onUnload() {
    // 已调用挂断 则不执行
    if (this.isHangUp) return;
    // 非正常挂断 直接退出 则执行
    emedia.mgr.exitConference();
    this.sendServe();
  },
  mounted() {
    window.onbeforeunload = function(e) {};
  },
};
</script>

<style lang="scss" scoped>
.video_call {
  height: 100vh;
  overflow: hidden;
  position: relative;
  background: #000000;
}

.zhe {
  width: 100%;
  height: 100vh;
  background: #f5f5f5;
  padding: 200upx 30upx 30upx;
  text-align: center;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 99;
  box-sizing: border-box;

  .title {
    margin-top: 60upx;
    font-size: 32upx;
    font-weight: bold;
    @include flex;

    .tip_icon {
      width: 40upx;
      height: 40upx;
      margin-right: 10upx;
    }
  }

  .content {
    margin-top: 50upx;
    padding: 0 30upx;
    font-size: 28upx;
    color: #666;
    text {
      display: block;
      padding-bottom: 20upx;
    }
  }
}

// 医生信息
.doc_info {
  width: 100%;
  height: 70vh;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;

  .doc_head {
    width: 200upx;
    height: 200upx;
    border-radius: 8upx;
  }

  .doc_name {
    color: #fff;
    font-size: 72upx;
    margin-top: 20upx;
  }
}

// 医生视频
.docter_video {
  height: 100vh;
  width: 100vw;
  object-fit: contain;
  z-index: 0;
  transform: rotateY(180deg);
}

// 患者视频
.me_video {
  width: 172upx;
  height: 305upx;
  object-fit: cover;
  border-radius: 8upx;
  overflow: hidden;
  position: absolute;
  top: 30upx;
  right: 30upx;
  z-index: 2;
  background-color: #fff;
  transform: rotateY(180deg);
}

.footer {
  width: 100%;
  height: 350upx;
  padding: 44upx 56upx;
  @include flex(center);
  flex-wrap: wrap;
  box-sizing: border-box;
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 2;

  .footer_time {
    width: 100%;
    color: #fff;
    padding-bottom: 30upx;
    text-align: center;
    font-size: 36upx;
  }

  .menu_item {
    width: 160upx;

    .item_icon {
      width: 128upx;
      height: 128upx;
      display: block;
      margin: 0 auto;
    }

    button {
      color: #fff;
      font-size: 32upx;
    }
  }
}
</style>
