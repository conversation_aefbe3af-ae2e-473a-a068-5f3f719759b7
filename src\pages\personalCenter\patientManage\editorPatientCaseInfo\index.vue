<template>
  <!-- 完善就诊人健康信息 -->
  <view>
    <view class="page-container">
      <view class="header_but">
        <button class="right" @click="addCasePopup">添加</button>
      </view>
      <view
        class="patient-info"
        v-for="item in patientInfoList"
        :key="item.pkId"
      >
        <view>
          <view class="title_format" style="font-size: 30upx">
            <view>{{ item.addTime }}</view>
            <view style="color: #239bf3" @click="addCasePopup(2, item)">
              编辑
            </view>
            <view
              style="color: red"
              @click="(itemCurrent = item), (showConfirm = true)"
            >
              删除资料
              <uni-icons type="trash" color="red" size="20"></uni-icons>
            </view>
          </view>
          <view class="content">
            <view
              style="font-size: 30upx; color: #999999; padding-bottom: 20upx"
              >描述:{{ item.description }}</view
            >

            <!-- 图片 -->
            <view class="img_list">
              <view
                v-if="index < 3"
                class="img_item"
                v-for="(imgUrl, index) in item.showFileJson"
                :key="index"
              >
                <image
                  :src="imgUrl.url"
                  @click="previewShow(item.showFileJson)"
                  mode="aspectFit"
                  class="img"
                ></image>
              </view>
            </view>
            <view v-if="item.showFileJson && item.showFileJson.length > 3">
              <uni-collapse>
                <uni-collapse-item title="查看更多" :show-animation="true">
                  <view class="content">
                    <!-- 图片 -->
                    <view class="img_list">
                      <view
                        v-if="index >= 3"
                        class="img_item"
                        v-for="(imgUrl, index) in item.showFileJson"
                        :key="index"
                      >
                        <image
                          :src="imgUrl.url"
                          @click="previewShow(item.showFileJson)"
                          mode="aspectFit"
                          class="img"
                        ></image>
                      </view>
                    </view>
                  </view>
                </uni-collapse-item>
              </uni-collapse>
            </view>
          </view>
        </view>
      </view>
      <!-- 弹框 -->
      <uni-popup ref="popup" type="bottom">
        <view class="pop">
          <view class="pop_title">
            <button @click="clearDate">取消</button>
            <button @click="saveDate">确定</button>
          </view>
          <view class="pop_text">
            <view class="patient-info">
              <view class="title_format">添加描述</view>
              <view class="content">
                <textarea
                  placeholder="请输入描述"
                  maxlength="100"
                  show-word-limit
                  :value="description"
                  v-model="description"
                  class="other-content"
                  @input="textareaValue($event)"
                />
                
              </view>
            </view>
            <!-- 上传照片 -->
            <view class="sele_warp">
              <view
                class="sele_but"
                :class="{ act: fileList.length }"
                @click="cloosImgTop"
              >
                <text>请上传处方及病历照片(最多9张)</text>
                <uni-icons type="arrowright" color="#333" size="20"></uni-icons>
              </view>

              <!-- 图片 -->
              <view class="img_list" v-show="fileList.length">
                <view
                  class="img_item"
                  v-for="(item, index) in fileList"
                  :key="index"
                >
                  <image
                    :src="item.url"
                    @click="preview(item)"
                    mode="aspectFit"
                    class="img"
                  ></image>
                  <!-- 删除按钮 -->
                  <view class="del" @click="delFile(index)">
                    <uni-icons type="close" color="#fff" size="24"></uni-icons>
                  </view>
                </view>
                <!-- 上传 -->
                <view
                  class="img_item"
                  @click="cloosImg"
                  v-if="fileList.length < 9"
                >
                  <text>+</text>
                  <text>上传图片</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </uni-popup>
    </view>
    <!-- 询问框 -->
    <Confirm
      @confirmPropCenter="deleteCase"
      :type="2"
      @cancelPropCenter="showConfirm = false"
      v-if="showConfirm"
    >
      是否要删除已上传的资料？
    </Confirm>
    <Confirm
      v-if="showReadAndAgreeConfirm"
      @confirmPropCenter="readAndAgree"
      :type="100"
      @cancelPropCenter="showConfirm = false"
    >
      <view class="test2">
        <view v-html="agreementContent"></view>
      </view>
    </Confirm>
  </view>
</template>

<script>
import {
  savePatientCase,
  getPatientCase,
  deletePatientCase,
  selectInformConsent,
  empowerConsent,
  findVisitAgreement,
  updatePatientCase,
} from "@/api/user.js";
import Confirm from "@/components/propCenter/propCenter.vue";
import { uploadImg, downPdf } from "@/api/oss.js";
import myJsTools from "@/common/js/myJsTools.js";
import { Toast } from "@/common/js/pay.js";
export default {
  components: { Confirm },
  data() {
    return {
      patientInfoList: [],
      patientId: "",
      fileList: [], // 上传的图片列表
      description: "", // 描述
      showConfirm: false, // 删除弹框
      itemCurrent: {}, // 要删除得对象
      showReadAndAgreeConfirm: false,
      agreementContent: "",
      editorFlag: false,
      editorInfo: {},
    };
  },
  filters: {},
  onLoad(option) {
    this.patientId = option.patientId;
    this.findVisitAgreement();
    this.selectInformConsent();
    this.getPatientCaseList();
    console.log("查看当前传过来的患者ID", this.patientId);
  },
  methods: {
    async selectInformConsent() {
      const data = {
        userId: uni.getStorageSync("userId"),
      };
      const res = await selectInformConsent(data);
      if (res.data) {
        this.showReadAndAgreeConfirm = res.data.isEmpower != 1;
      }
    },
    async readAndAgree() {
      const data = {
        userId: uni.getStorageSync("userId"),
      };
      const res = await empowerConsent(data);
      this.showReadAndAgreeConfirm = false;
      Toast("保存成功");
    },
    async findVisitAgreement() {
      const res = await findVisitAgreement({ agreementType: "11" });
      if (res.data) {
        this.agreementContent = res.data.agreementContent || "";
      }
    },
    // 获取病历记录
    getPatientCaseList() {
      getPatientCase({ patientId: this.patientId }).then((res) => {
        this.patientInfoList = res.data;
        this.patientInfoList.map(async (item) => {
          if (item.fileJson.length > 0) {
            const imUrlArr = item.fileJson.map((items) => {
              return {
                fileName: items.url,
              };
            });
            let arr = [];
            await downPdf(imUrlArr).then((res) => {
              arr = res.data.map((w) => {
                return {
                  ...w,
                  name: item.fileJson.find((q) => q.url == w.fileName).fileName,
                };
              });
              console.log("downPdf", arr);
            });
            this.$set(item, "showFileJson", arr);
            return item;
          }
        });
        console.log("查看处理之后得数据", this.patientInfoList);
      });
    },
    // 取消清空
    clearDate() {
      this.fileList = [];
      this.description = "";
      this.$refs.popup.close();
    },
    // 输入框文本输入
    textareaValue(val) {
      if (val.detail.value.replace(/\s*/g, "").length >= 100) {
        return Toast("最多填写100汉字！");
      }
      this.description = val.detail.value.replace(/\s*/g, "");
    },
    // 删除病历信息
    deleteCase() {
      deletePatientCase({ pkId: this.itemCurrent.pkId }).then((res) => {
        this.getPatientCaseList();
        this.showConfirm = false;
      });
    },
    // 确认保存
    async saveDate() {
      let images = [];
      console.log(this.fileList);
      // 如果存在图片
      if (this.fileList.length) {
        // 循环上传图片
        for (let i = 0; i < this.fileList.length; i++) {
          try {
            if (!this.fileList[i].url.includes("http")) {
              let url = await this.upImg(this.fileList[i]);
              let fileName =
                Number(
                  Math.random().toString().substr(3, 18) + Date.now()
                ).toString(36) + ".png";
              images.push({ url, fileName });
            } else {
              images.push({
                url: this.fileList[i].fileName,
                fileName: this.fileList[i].name,
              });
            }
          } catch (e) {
            console.log("图片上传失败", e);
          }
        }
      }
      console.log(images);
      console.log(this.description);
      if (images.length === 0 && this.description === "") {
        Toast("请填写描述、病历图片信息!");
        return;
      }
      let dataJson = {
        fileJson: images,
        description: this.description,
        patientId: this.patientId,
      };
      if (this.editorFlag) {
        dataJson.pkId = this.editorInfo.pkId;
        updatePatientCase(dataJson).then((res) => {
          console.log(res);
          this.$refs.popup.close();
          this.getPatientCaseList();
        });
        return;
      }
      savePatientCase(dataJson).then((res) => {
        console.log(res);
        this.fileList = [];
        this.description = "";
        this.$refs.popup.close();
        this.getPatientCaseList();
      });
    },
    // 添加弹出框
    addCasePopup(type, item) {
      console.log(item);
      this.editorFlag = false;
      if (type == 2) {
        this.fileList = [...(item.showFileJson || [])];
        this.description = item.description;
        this.editorFlag = true;
        this.editorInfo = { ...item };
      } else {
        this.fileList = [];
        this.description = "";
      }
      this.$refs.popup.open();
    },
    // 上传图片
    async upImg(obj) {
      let para = {
        folderType: 11,
        imgBody: obj.base64,
        // 患者id
        otherId: this.patientId,
      };
      let res = await uploadImg(para);
      // 图片名称
      return res.data.url;
    },
    // 移除图片
    delFile(n) {
      this.fileList.splice(n, 1);
    },
    // 初次选择
    cloosImgTop() {
      if (!this.fileList.length) {
        this.cloosImg();
      }
    },
    // 选择图片
    cloosImg() {
      const that = this;
      uni.chooseImage({
        count: 1,
        success: function (res) {
          // 读取图片
          const file = res.tempFiles[0];
          myJsTools.setImgZip(file, (dataUrl) => {
            that.fileList.push({
              base64: dataUrl.split(",")[1],
              url: dataUrl,
              name: "",
            });
          });
        },
      });
    },
    // 图片预览
    previewShow(item) {
      console.log("11111", item);
      const showImg = item.map((ele) => {
        return ele.url;
      });
      uni.previewImage({
        urls: showImg,
      });
    },
    // 图片预览
    preview(item) {
      uni.previewImage({
        urls: [item.url],
      });
    },
    // 保存
    async editorPatient() {
      let para = {
        ...this.patientInfo,
        isDefault: 0,
        patientId: this.patientId,
        userId: uni.getStorageSync("userId"),
      };

      // 提交
      await editorPatient(para);
      uni.navigateBack({
        delta: 1,
      });
    },
  },
};
</script>
<style scoped>
.content /deep/ .uni-collapse-cell--open {
  background-color: #ffffff !important;
}
</style>

<style scoped lang="scss">
page {
  background-color: #f5f5f5;
}
.page-container {
  padding: 24rpx 32rpx 140rpx;
  box-sizing: border-box;
  overflow-y: auto;
}

.patient-info {
  border-radius: 8rpx;
  background: #ffffff;
  padding: 24upx 32upx;
}

.title_format {
  font-size: 32rpx;
  color: rgba(51, 51, 51, 1);
  line-height: 92rpx;
  display: flex;
  justify-content: space-between;
}

.content {
  .img_list {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-gap: 20rpx;
    padding-bottom: 20rpx;

    .img_item {
      width: 200rpx;
      height: 200rpx;
      border-radius: 8rpx;
      border: 1px dashed #eee;
      position: relative;
      @include flex;
      flex-direction: column;

      text {
        color: #999;
      }

      .img {
        width: 100%;
        height: 100%;
      }

      .del {
        background-color: rgba($color: #000000, $alpha: 0.3);
        position: absolute;
        border-radius: 50%;
        top: 0;
        right: 0;
      }
    }
  }
}

.other-content {
  box-sizing: border-box;
  width: 100%;
  border-radius: 8rpx;
  border: 1px solid rgba(229, 229, 229, 1);
  font-size: 28rpx;
  line-height: 40rpx;
  padding: 24rpx 16rpx;
  height: 180rpx;
}

.footer_but {
  width: 100%;
  height: 108upx;
  @include flex(lr);
  position: fixed;
  bottom: 0;
  left: 0;
  background-color: #fff;
  box-shadow: 0px 0px 2upx 0px rgba(0, 0, 0, 0.12);
  border-radius: 16upx 16upx 0 0;
  box-sizing: border-box;

  button {
    width: 334upx;
    height: 84upx;
    border-radius: 42upx;
    @include flex;
    font-size: 32upx;
    box-sizing: border-box;

    &.left {
      @include font_theme;
      @include border_theme;
    }

    &.right {
      color: #fff;
      @include bg_theme;
    }
  }
}
.header_but {
  width: 100%;
  height: 108upx;
  @include flex(lr);
  background-color: #fff;
  box-shadow: 0px 0px 2upx 0px rgba(0, 0, 0, 0.12);
  border-radius: 16upx 16upx 0 0;
  box-sizing: border-box;

  button {
    width: 234upx;
    height: 84upx;
    @include flex;
    font-size: 32upx;
    box-sizing: border-box;

    &.left {
      @include font_theme;
      @include border_theme;
    }

    &.right {
      color: #fff;
      @include bg_theme;
    }
  }
}
.pop {
  width: 100%;
  background-color: #ffffff;
  border-radius: 20upx 20upx 0 0;
  padding: 40upx;
  box-sizing: border-box;
  .pop_title {
    display: flex;
    button {
      width: 100upx;
      height: 50upx;
      font-size: 24upx;
      @include flex;
      color: #fff;
      @include bg_theme;
    }
  }
}
.sele_warp {
  width: 100%;
  background-color: #fff;
  border-radius: 8rpx;
  font-size: 28rpx;
  padding: 0 24rpx;
  margin-bottom: 24rpx;

  .sele_but {
    @include flex(lr);
    height: 88rpx;

    &.act {
      .uni-icons {
        transform: rotate(90deg);
      }
    }

    .uni-icons {
      transition: all 0.3s;
      margin-left: 10rpx;
    }

    .right {
      @include flex;

      .name {
        color: #666;
      }
    }
  }

  .img_list {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-gap: 20rpx;
    padding-bottom: 20rpx;

    .img_item {
      width: 200rpx;
      height: 200rpx;
      border-radius: 8rpx;
      border: 1px dashed #eee;
      position: relative;
      @include flex;
      flex-direction: column;

      text {
        color: #999;
      }

      .img {
        width: 100%;
        height: 100%;
      }

      .del {
        background-color: rgba($color: #000000, $alpha: 0.3);
        position: absolute;
        border-radius: 50%;
        top: 0;
        right: 0;
      }
    }
  }

  .sele_info {
    @include flex;
    align-items: flex-start;
    padding-bottom: 20rpx;

    text {
      flex: none;
      width: 110rpx;

      &.red {
        color: red;
        flex: 1;
      }
    }

    .info_text {
      flex: 1;
      color: #999;
    }
  }

  .logistics {
    width: 100%;
    padding-bottom: 24rpx;

    .item {
      height: 76rpx;
      padding: 0 24rpx;
      @include flex(lr);
      background-color: #f5f5f5;
      border-radius: 8rpx;

      &:last-child {
        margin-top: 24rpx;
      }

      .name {
        font-size: 28rpx;
      }

      .right {
        @include flex;

        text {
          font-size: 28rpx;
          color: red;
          padding-right: 30rpx;
        }

        image {
          width: 32rpx;
          height: 32rpx;
        }
      }
    }
  }
}
.test {
}
.test2 {
  width: 100%;
  overflow: hidden;
  word-wrap: break-word;
  overflow-wrap: break-word; /* 标准属性 */
  max-height: 600px;
  overflow: auto;
}
</style>
