<template>
  <div class="login" id="login">
    <header>
      <image class="icon" :src="icon" mode="widthFix"></image>
    </header>
    <div class="loginBody">
      <view class="input_item">
        <input
          placeholder-class="place"
          type="number"
          maxlength="11"
          v-model="telNo"
          placeholder="请输入手机号"
        />
      </view>

      <view class="input_item">
        <input
          type="text"
          placeholder-class="place"
          v-model="captcha"
          placeholder="请输入验证码"
          maxlength="6"
        />
        <button :class="{ act: codeinfo.auth_time > 0 }" @click="sendCode">
          {{ codeinfo.btnText }}
        </button>
      </view>
      <button class="publicBtn loginBtn" @click="login">登录</button>
    </div>
  </div>
</template>

<script>
// 引入视频通话w
import { hisPatientLoginByTelPhoneAndCaptcha } from "../../api/base";

const emedia = require("@/utils/WebIM.js")["emedia"];
import myJsTools from "../../utils/myJsTools";
import Head from "../register/docHomePage/com/head";
import regex from "../../common/js/regex";
import { sendCaptcha, updateHxidIsregistStatus } from "../../api/user";

export default {
  name: "initlogin",
  components: { Head },
  data() {
    return {
      telNo: "",
      captcha: "",
      // 图标展示
      icon: CONFIG_ENV.VUE_APP_SHARE,
      codeinfo: {
        sendAuthCode: true,
        auth_time: 0,
        btnText: "发送验证码",
      }, //验证码时间及是否可点击
    };
  },
  // 如果有token信息.则直接进主页
  onShow() {},
  methods: {
    //验证码字段验证
    codeBlur() {
      var re = /^\d{6}$/;
      if (!this.captcha) {
        Toast("验证码不能为空");
        return;
      } else if (!re.test(this.captcha)) {
        Toast("请输入正确的验证码");
        return false;
      } else {
        return true;
      }
    },
    //手机号正则表达式
    telBlur() {
      var re = /^1\d{10}$/;
      if (!this.telNo) {
        Toast("手机号不能为空");
        return false;
      } else if (!re.test(this.telNo)) {
        Toast("请输入正确的手机号");
        return false;
      } else {
        return true;
      }
    },
    //登录
    login() {
      if (this.codeBlur() && this.telBlur()) {
        var capParam = {
          captcha: this.captcha,
          telNo: this.telNo,
          // appid:"wxa44359c5ebfba07f",
          // openid:"opL5Fw22wX6jucu1JIcNEM6sMaDM"
        };
        hisPatientLoginByTelPhoneAndCaptcha(capParam).then((res) => {
          console.log(res, "用户登录返回信息");
          if (res.data && res.data.userId) {
            let proPfInfo = res.data;
            // 存储用户信息
            this.$store.commit("setProPfInfo", proPfInfo);
            // let  wxInfo = {
            //    city: "",
            //    country: "天津",
            //    headimgurl: "/static/logo.png",
            //    nickname: "**",
            //    openId:proPfInfo.openid,
            //    sex: 0,
            //  };
            //  this.$store.commit("setWxInfo", wxInfo);

            // userId
            uni.setStorageSync("userId", proPfInfo.userId);
            // 用以注册环信(全部小写)
            let myUsername = proPfInfo.userId.toLowerCase();
            uni.setStorageSync("myUsername", myUsername);
            uni.setStorageSync("tel", res.data.telNo || "");
            // 该用户下所有的患者id
            uni.setStorageSync("patientIdList", proPfInfo.patientIdList || []);
            myJsTools.setItem("proPfInfo", proPfInfo);
            // // 暂时跳过环信登录
            // this.toPath()
            // return
            // 是否注册过
            let hxidIsregist = proPfInfo.hxidIsregist;
            if (hxidIsregist == "1") {
              console.log("去登录");
              this.WebIMLogin();
            } else {
              this.WebIMRegister();
            }
          }
        });
      }
    },

    // 环信登录
    WebIMLogin() {
      let _this = this;
      let userId = uni.getStorageSync("userId");
      let options = {
        apiUrl: _this.$im.config.apiURL,
        user: userId,
        pwd: userId,
        grant_type: userId,
        appKey: _this.$im.config.appkey,
        success: function (res) {
          console.log("登陆成功", res);
          let memName = _this.$im.config.appkey + "_" + userId;
          // 进入会议前 必须调用
          emedia.mgr.setIdentity(memName, res.access_token);
          _this.toPath();
        },
        error: function (err) {
          _this.isModelToastShow = true;
        },
      };
      _this.$im.conn.open(options);
    },
    toPath() {
      uni.reLaunch({
        url: "/pages/index/index",
      });
      return;
    },
    // 环信注册
    WebIMRegister() {
      let _this = this;
      let userId = uni.getStorageSync("userId");
      let options = {
        username: userId,
        password: userId,
        nickname: userId,
        appKey: _this.$im.config.appkey,
        success: function () {
          _this.updateHxidIsregistStatusFun();

          // _this.WebIMLogin();
        },
        error: function (err) {
          if (err.statusCode == 400) {
            _this.updateHxidIsregistStatusFun();
            return;
          }
          _this.isModelToastShow = true;
        },
        apiUrl: _this.$im.config.apiURL,
      };
      _this.$im.conn.registerUser(options);
    },
    // 更改用户环信状态
    async updateHxidIsregistStatusFun() {
      let userId = uni.getStorageSync("userId");
      await updateHxidIsregistStatus({
        userId,
      });
      // 更改用户环信状态成功
      this.WebIMLogin();
    },
    // 获取验证码
    async sendCode() {
      let telNo = this.telNo;
      if (regex.telBlur(telNo)) {
        // 如果正在倒计时 防止点击
        if (this.codeinfo.auth_time > 0) return;
        await sendCaptcha({
          telNo,
        });
        // 发送验证码,倒数读秒
        this.codeinfo.auth_time = 60;
        var auth_timetimer = setInterval(() => {
          this.codeinfo.auth_time--;
          this.codeinfo.btnText = this.codeinfo.auth_time + "s后重新发送";
          if (this.codeinfo.auth_time <= 0) {
            this.codeinfo.sendAuthCode = true;
            // 重置为0
            this.codeinfo.auth_time = 0;
            this.codeinfo.btnText = "重新发送";
            clearInterval(auth_timetimer);
          }
        }, 1000);
      }
    },
  },
};
</script>
<style scoped lang="scss">
header {
  text-align: center;
}
.icon {
  width: 100px;
  height: 100px;
  display: inline-block;
  margin: 30% auto 48px;
}

.loginBody {
  padding: 0 38px;
}

.login {
  background: #ffffff;
  height: 100vh;
}
.publicBtn {
  box-sizing: border-box;
  background: #14a0e6;
  line-height: 44rpx;
  color: #ffffff;
  font-weight: 500;
  font-size: 32rpx;
  width: 100%;
  border-radius: 66rpx;
  padding: 20rpx 0;
  margin-top: 108px;
}
.input_item {
  background: #f5f5f5;
  @include flex(lr);
  font-size: 28rpx;
  color: #333;
  height: 88rpx;
  padding: 0 30rpx;
  margin-bottom: 32rpx;
  border-radius: 23px;

  input {
    flex: 1;
  }

  /deep/ .uni-input-input {
    font-size: 14px !important;
  }

  .uni-input-placeholder {
    color: #c1c1c1;
    font-size: 28rpx;
  }

  button {
    min-width: 180rpx;
    flex: none;
    height: 80rpx;
    @include flex;
    @include font_theme;
    font-size: 30rpx;
    padding: 0;

    &.act {
      color: #999;
    }
  }
}

.positionBtn {
  position: fixed;
  bottom: 0.32rem;
  left: 0px;
  background: #fff;
  padding: 10px 0px;
  text-align: center;
  width: 90%;

  span {
    margin-top: 12px;
    font-size: 12px;
    padding-left: 12px;
    display: inline-block;
  }
}
</style>
