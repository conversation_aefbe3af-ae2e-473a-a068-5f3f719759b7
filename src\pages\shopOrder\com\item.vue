<template>
  <div class="order_item">
    <div @click="toDetail">
      <!-- 订单编号 状态 -->
      <div class="no">
        <span class="num">订单编号：{{ item.orderNo }}</span>

        <!-- 线上支付 -->
        <block
          v-if="item.payType < 6 && item.payType != 0 && item.orderStatus < 4"
        >
          <!-- 支付方式 1微信 2支付宝 6到店 7货到付款 -->
          <span class="status red">
            <block v-if="item.orderStatus == 1">待支付</block>
            <block v-if="item.orderStatus == 2">
              <block v-if="item.auditStatus < 3">待审核</block>
              <!-- 审核完成 待发货 待取药 -->
              <block v-if="item.auditStatus == 3">
                <block v-if="item.deliverytype == 1 || item.deliverytype == 3"
                  >待发货</block
                >
                <block v-else>待取药</block>
              </block>
              <block v-if="item.auditStatus == 4">未通过</block>
              <block v-if="item.auditStatus == 5">拒单</block>
            </block>
            <block v-if="item.orderStatus == 3">
              <block v-if="item.deliverytype == 1 || item.deliverytype == 3"
                >待收货</block
              >
              <block v-else>待取药</block>
            </block>
          </span>
        </block>

        <block
          v-else-if="
            (item.payType > 5 || item.payType == 0) && item.orderStatus < 4
          "
        >
          <span class="status red">
            <!-- 线下支付没有待支付 -->
            <block v-if="item.orderStatus == 1">
              <block v-if="item.auditStatus < 3">待审核</block>
              <!-- 审核完成 待发货 待取药 -->
              <block v-if="item.auditStatus == 3">
                <block v-if="item.deliverytype == 1 || item.deliverytype == 3"
                  >待发货</block
                >
                <block v-else>待取药</block>
              </block>
              <block v-if="item.auditStatus == 4">未通过</block>
              <block v-if="item.auditStatus == 5">拒单</block>
            </block>

            <block v-if="item.orderStatus == 2">
              <block v-if="item.auditStatus < 3">待审核</block>
              <!-- 审核完成 待发货 待取药 -->
              <block v-if="item.auditStatus == 3">
                <block v-if="item.deliverytype == 1 || item.deliverytype == 3"
                  >待发货</block
                >
                <block v-else>待取药</block>
              </block>
              <block v-if="item.auditStatus == 4">未通过</block>
              <block v-if="item.auditStatus == 5">拒单</block>
            </block>

            <block v-if="item.orderStatus == 3">
              <block v-if="item.deliverytype == 1 || item.deliverytype == 3"
                >待收货</block
              >
              <block v-else>待取药</block>
            </block>
          </span>
        </block>

        <!-- 其他不用判断的状态 -->
        <span class="status" v-else>
          <block v-if="item.orderStatus == 4">交易完成</block>
          <block v-if="item.orderStatus == 6">已退费</block>
          <block v-if="item.orderStatus == 7">交易关闭</block>
        </span>
      </div>

      <!-- 药品相关 -->
      <DRUGLIST :list="item.drugList" :name="item.subjectname" />

      <!-- 价位 -->
      <div class="drug_price">
        <p>
          总价:<span>￥{{ item.totalmoney | toFixed }}</span>
        </p>
        <p>
          优惠:<span>￥{{ item.ordermoneydis || 0 | toFixed }}</span>
        </p>
        <p>
          实付:<span>￥{{ item.totalmoneyreal | toFixed }}</span>
        </p>
      </div>
    </div>
    <!-- 按钮组 -->
    <div class="footer">
      <!-- 退费，交易关闭，审核被拒 等情况 -->

      <button v-if="showInvoice(item)" @click="updateOrderInvoice">
        补开发票
      </button>

      <button v-if="showEdit(item)" @click="toEdit">修改订单</button>

      <button class="act" v-if="showRefund(item)" @click="toRefund">
        <block v-if="item.refundAuditList && item.refundAuditList.length"
          >取消详情</block
        >
        <block v-else>取消订单</block>
      </button>

      <button class="act" @click="toRepeat" v-if="showRepeat(item)">
        再次购买
      </button>

      <button
        class="act"
        v-if="item.deliverytype == 1 && showLogistics(item)"
        @click="lockLogistics"
      >
        查看物流
      </button>

      <button
        class="act"
        v-if="item.deliverytype == 3 && showLogistics(item)"
        @click="lock"
      >
        配送查看
      </button>

      <button
        class="act"
        v-if="
          item.deliverytype != 2 &&
            item.logisticsstatus == 1 &&
            item.orderStatus < 6
        "
        @click="confirmGoods"
      >
        确认收货
      </button>

      <button class="act" v-if="showDrugCode(item)" @click="showCode">
        取药码
      </button>

      <button class="act" v-if="showPay(item)" @click="toDetail">去支付</button>
    </div>
  </div>
</template>

<script>
import DRUGLIST from './drugList.vue';
import { confirmGoods } from '@/api/shop';
import { Toast } from '@/common/js/pay';
import {
  tradeRefundFastMallOrder,
  queryFastMallOrderStatusIschecking,
  secondAddShoppingCart,
} from '@/api/shop';
import {
  isShowInvoice,
  isShowRepeat,
  isShowRefund,
  isShowDrugCode,
  isShowEdit,
  isShowLogistics,
  isShowPay,
} from './status';

/**
 * 订单状态
 * 1待付款 2待发货/发药 3待收货 4已收货 5申请退费 6已退费 7交易关闭
 *
 * 审核状态
 * 0待开始 1待审核,2审核中,3已通过,4 驳回,5拒单
 *
 * 物流状态
 * 0待发货 1已发货 2已收货
 *
 * 配送方式
 * 1统一配送 2自提 3.店员配送
 */

export default {
  name: 'OrderItem',
  components: {
    DRUGLIST,
  },
  props: {
    item: {
      type: Object,
      default: () => {},
    },
    index: String | Number,
  },
  methods: {
    // 去详情
    toDetail() {
      uni.navigateTo({
        url: '/pages/shopOrder/detail?orderNo=' + this.item.orderNo,
      });
    },
    // 展示取药码
    showCode() {
      this.$emit('showCode', this.item.drugCode);
    },
    // 补开发票
    updateOrderInvoice() {
      let url = '/pages/shop/invoice/index?orderNo=' + this.item.orderNo;
      uni.navigateTo({ url });
    },
    // 修改订单
    async toEdit() {
      const { orderNo } = this.item;
      const {
        data: { status },
      } = await queryFastMallOrderStatusIschecking(orderNo);
      if (status > 0) {
        Toast('订单正在审核中，请稍后再试！');
        return;
      }
      await secondAddShoppingCart(orderNo);
      this.$store.dispatch('shop/getCartList');
      let url = '/pages/shop/submit/edit?orderNo=' + orderNo;
      uni.navigateTo({ url });
    },
    // 跳转取消申请
    async toRefund() {
      const { orderNo, refundAuditList } = this.item;
      const index = this.index;
      if (refundAuditList && refundAuditList.length) {
        uni.navigateTo({
          url: '/pages/shopOrder/cancel?orderNo=' + orderNo,
        });
        return;
      }
      const {
        data: { status },
      } = await queryFastMallOrderStatusIschecking(orderNo);
      // 已申请退款
      if (status == 5) {
        Toast('您已提交取消申请，请耐心等待');
        return;
      }
      // 订单关闭
      if (status == 6) {
        Toast('订单已关闭');
        return;
      }
      // 可直接退费
      if (status == 0) {
        await tradeRefundFastMallOrder(orderNo);
        Toast('成功取消');
        this.$emit('change', { orderNo, index });
        return;
      }
      // 需要提交申请
      let url = '/pages/shopOrder/refund?orderNo=' + orderNo;
      uni.navigateTo({ url });
    },
    // 再次购买
    async toRepeat() {
      const { orderNo } = this.item;
      await secondAddShoppingCart(orderNo);
      this.$store.dispatch('shop/getCartList');
      let url = '/pages/shop/submit/repeat?orderNo=' + orderNo;
      uni.navigateTo({ url });
    },
    // 确认收货
    async confirmGoods() {
      const { orderNo } = this.item;
      const index = this.index;
      await confirmGoods(this.item.orderNo);
      Toast('收货成功');
      this.$emit('change', { orderNo, index });
    },
    // 控制发票按钮
    showInvoice(item) {
      return isShowInvoice(item);
    },
    // 控制再次购买
    showRepeat(item) {
      return isShowRepeat(item);
    },
    // 控制退费申请
    showRefund(item) {
      return isShowRefund(item);
    },
    // 配送查看
    lock() {
      const { orderNo } = this.item;
      let url = '/pages/shopOrder/logistic?orderNo=' + orderNo;
      uni.navigateTo({ url });
    },
    // 取药码
    showDrugCode(item) {
      return isShowDrugCode(item);
    },
    // 查看物流
    lockLogistics() {
      let {
        logisticsCode: code,
        deliveryTelNo,
        logisticsName: name,
      } = this.item;
      if (!code) {
        Toast('未查询到物流单号');
        return;
      }
      let tel = deliveryTelNo.slice(-4);
      uni.navigateTo({
        url:
          '/pages/personalCenter/myOrder/allOrder/prescriptionOrder/logistics?code=' +
          code +
          '&tel=' +
          tel +
          '&name=' +
          name,
      });
    },
    // 修改订单按钮
    showEdit(item) {
      return isShowEdit(item);
    },
    // 查看物流
    showLogistics(item) {
      return isShowLogistics(item);
    },
    // 显示支付
    showPay(item) {
      return isShowPay(item);
    },
  },
};
</script>

<style lang="scss" scoped>
page {
  background: #f5f5f5;
}

.order_item {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  padding: 0 32rpx;
  font-size: 28rpx;

  .no {
    @include flex(lr);
    height: 88rpx;
    border-bottom: 1px solid #f5f5f5;

    .num {
      font-weight: bold;
      font-size: 26rpx;
    }

    .status {
      color: #666;
      font-size: 26rpx;

      &.red {
        color: red;
      }
    }
  }

  .drug_price {
    @include flex(right);
    height: 88rpx;

    p {
      padding-left: 16rpx;
      font-size: 22rpx;

      span {
        font-size: 28rpx;
        color: red;
        font-weight: bold;
        padding-left: 4rpx;
      }
    }
  }

  .footer {
    @include flex(right);
    padding-bottom: 24rpx;
    flex-wrap: wrap;

    button {
      width: 158rpx;
      height: 56rpx;
      border-radius: 28rpx;
      @include flex;
      font-size: 26rpx;
      @include font_theme;
      margin-left: 24rpx;
      padding: 0;
      @include border_theme;
      margin-right: inherit;

      &.act {
        @include bg_theme;
        color: #fff;
      }
    }
  }
}
</style>
