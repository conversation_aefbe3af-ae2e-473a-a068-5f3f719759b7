import { Toast } from '@/common/js/pay.js';
import urlConfig from './config.js';
import Base64 from './base64.js';
import md5 from './md5.js';
let baseUrl = urlConfig.ossUrl;
const http = ({ url = '', param = {}, ...other } = {}) => {
  return new Promise((resolve, reject) => {
    if (other.method == 'post') {
      param = reSetData(param);
    }
    uni.request({
      url: getUrl(url),
      data: param,
      header: {
        Authorization: uni.getStorageSync('proPfInfo').token || '',
        firmId: urlConfig.firmId,
        version: urlConfig.version,
        clientType: urlConfig.clientType,
      },
      ...other,
      success: (res) => {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          if (res.data.code == 20000 || res.data.code == 20011) {
            resolve(res.data);
          } else {
            Toast(res.data.message);
            reject(res);
          }
        } else {
          reject(res);
        }
      },
      fail: (err) => {
        reject(err);
      },
    });
  });
};
const getUrl = (url) => {
  if (url.indexOf('://') == -1) {
    url = baseUrl + url;
  }
  return url;
};
const reSetData = (requestData) => {
  let timestamp = new Date().getTime() + '';
  let nonce = guid();
  let token_info = uni.getStorageSync('proPfInfo').token;

  let base64Data = '';
  if (requestData) {
    base64Data = Base64.encode(JSON.stringify(requestData));
  } else {
    let map = {};
    base64Data = Base64.encode(JSON.stringify(map));
  }
  // let data = {
  //   data: base64Data,
  // };
  let data = {
    data: JSON.stringify(requestData),
  };
  data.timestamp = timestamp;
  data.nonce = nonce;
  if (token_info) {
    data.token = token_info;
  }
  let s = '';
  Object.keys(data)
    .sort()
    .forEach((k) => {
      if (data[k] && data[k].length > 0) {
        if (s.length > 0) {
          s += '&';
        }
        s += k + '=' + data[k];
      }
    });
  let md5Data = md5(s).toLocaleUpperCase();
  data.sign = md5Data;
  return data;
};

const guid = () => {
  return (
    S4() +
    S4() +
    '-' +
    S4() +
    '-' +
    S4() +
    '-' +
    S4() +
    '-' +
    S4() +
    S4() +
    S4()
  );
};

const S4 = () => {
  return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
};
export default http;
