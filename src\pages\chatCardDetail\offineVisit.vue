<template>
  <!-- 线下预约 -->
  <view class="offine_visit">
    <!-- 医生 -->
    <DocInfo :doc-id="docId" pageType="false" />

    <!-- 线下 -->
    <Offine
      v-if="showOffine"
      :pageParam="{
        docId: docId,
        patientId: patientId,
        vrTempType: vrTempType,
        dntId: dntId,
      }"
    />
  </view>
</template>

<script>
import DocInfo from '@/components/docInfo/docInfo';
import Offine from './components/Offline.vue';
export default {
  components: {
    DocInfo,
    Offine,
  },
  data() {
    return {
      // 医生id
      docId: '',
      // 患者id
      patientId: '',
      // 显示组件
      showOffine: false,
      // 1 医院排班 2 个人
      vrTempType: '',
      // 号别id
      dntId: '',
    };
  },
  onLoad(e) {
    this.docId = e.docId;
    this.patientId = e.patientId;
    this.vrTempType = e.vrTempType || '';
    this.dntId = e.dntId || '';
  },
  onShow() {
    this.showOffine = false;
    this.$nextTick(function() {
      this.showOffine = true;
    });
  },
};
</script>
