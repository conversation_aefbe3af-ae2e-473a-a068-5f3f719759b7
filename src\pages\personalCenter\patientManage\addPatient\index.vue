<template>
  <!-- 添加就诊人 -->
  <view class="body">
    <view class="page-container" v-if="simpName == '0'">
      <view class="patient-info">
        <view class="input-box">
          <text>头像</text>
          <input
            type="text"
            disabled=""
            placeholder="点击上传头像(选填)"
            @click="uploadTx"
            v-if="!patientImg"
          />
          <image
            :src="patientImg.url"
            mode="aspectFill"
            v-else
            class="txClass"
            @click="uploadTx"
          ></image>
        </view>
        <view class="input-box">
          <text>姓名</text>
          <input
            type="text"
            style="text-align: right"
            maxlength="10"
            :value="patientInfo.patientName"
            v-model="patientInfo.patientName"
            placeholder="请输入姓名"
          />
        </view>
        <view class="input-box">
          <text>身份证号</text>
          <input
            type="idcard"
            :value="patientInfo.idNo"
            v-model="patientInfo.idNo"
            :placeholder="
              isRealName ? '请输入身份证号' : '请输入身份证号(选填)'
            "
            @blur="cardIdBlur($event)"
          />
        </view>
        <view class="input-box">
          <text>性别</text>
          <input
            type="text"
            :value="patientInfo.sex"
            v-model="patientInfo.sex"
            placeholder="-"
            disabled
          />
        </view>
        <view class="input-box">
          <text>年龄</text>
          <input
            type="text"
            :value="patientInfo.age"
            v-model="patientInfo.age"
            placeholder="-"
            disabled
          />
        </view>
        <view class="input-box">
          <text>民族</text>
          <view @click="openNation">
            <input
              type="text"
              :placeholder="isRealName ? '点击选择民族' : '点击选择民族(选填)'"
              disabled
              class="disabled"
              v-model="patientInfo.nation"
            />
          </view>
        </view>
        <view class="input-box" v-if="underAge">
          <text>监护人</text>
          <input
            type="idcard"
            :value="patientInfo.guardianIdNo"
            v-model="patientInfo.guardianIdNo"
            placeholder="请输入监护人身份证号"
          />
        </view>
        <view class="input-box">
          <text>手机号</text>
          <input
            type="number"
            :value="patientInfo.telNo"
            maxlength="11"
            v-model="patientInfo.telNo"
            placeholder="请输入手机号"
          />
        </view>
        <view class="input-box" v-if="isRealName">
          <text>验证码</text>
          <input
            type="number"
            :value="telCode"
            v-model="telCode"
            placeholder="请输入验证码"
            maxlength="6"
          />
          <view class="sendBtn" @click="sendCode">
            {{ codeinfo.btnText }}
          </view>
        </view>
        <view class="input-box" @click="selectYbCardFun" v-if="isRealName">
          <text>医保卡</text>
          <input
            type="text"
            class="disabled"
            :value="medicareTypeName"
            v-model="medicareTypeName"
            placeholder="请选择医保卡类型"
            disabled
          />
          <uni-icons class="icon" type="arrowright" size="20"></uni-icons>
        </view>
        <view v-if="ybCardShow" class="input-box">
          <text></text>
          <input
            type="text"
            :value="patientInfo.medicareNum"
            v-model="patientInfo.medicareNum"
            placeholder="请填写医保卡卡号"
          />
        </view>
        <view class="input-box">
          <text>家人关系</text>
          <view @click="openLbPicker" style="flex: 1">
            <input
              type="text"
              placeholder="点击选择家人关系"
              disabled
              class="disabled"
              style="width: 100%"
              v-model="patientInfo.spuRelation"
            />
          </view>
        </view>
        <!--        二次开发需求-->
        <view class="input-box">
          <text>首次复诊时间</text>
          <view class="displayFlex">
            <uni-datetime-picker
              type="date"
              :value="patientInfo.firstFollowupDate"
              start="2021-3-20"
              end=""
              @change="change"
            />
          </view>
        </view>
      </view>
    </view>
    <view class="page-container" v-if="simpName == '1'">
      <view class="input-box">
        <text>姓名</text>
        <input
          type="text"
          maxlength="10"
          :value="patientInfo.patientName"
          v-model="patientInfo.patientName"
          placeholder="请输入姓名"
        />
      </view>
      <view class="input-box">
        <text>家人关系</text>
        <view @click="openLbPicker" style="flex: 1">
          <input
            type="text"
            placeholder="点击选择家人关系"
            disabled
            class="disabled"
            v-model="patientInfo.spuRelation"
          />
        </view>
      </view>
      <view class="input-box">
        <text>身份证号</text>
        <input
          type="idcard"
          :value="patientInfo.idNo"
          v-model="patientInfo.idNo"
          placeholder="请输入身份证号"
          @blur="cardIdBlur($event)"
        />
      </view>
      <view class="input-box">
        <text>手机号</text>
        <input
          type="number"
          :value="patientInfo.telNo"
          maxlength="11"
          v-model="patientInfo.telNo"
          placeholder="请输入手机号"
        />
      </view>
      <view class="input-box">
        <text>验证码</text>
        <input
          type="number"
          :value="telCode"
          v-model="telCode"
          placeholder="请输入验证码"
          maxlength="6"
        />
        <view class="sendBtn" @click="sendCode">
          {{ codeinfo.btnText }}
        </view>
      </view>
    </view>
    <propBottom
      v-if="selectYbType"
      :actions="actions"
      @propConfirm="propConfirm"
      @propCancel="propCancel"
    ></propBottom>

    <!-- 验证码弹窗 -->
    <view class="old-patient" v-show="addPatientToast">
      <view class="wrapper">
        <view class="block">
          <view class="title">提示</view>
          <view class="content">
            <view
              >该就诊人之前绑定过，已向{{
                oldTelNo
              }}发送验证码，请填写验证码，核实您的身份</view
            >
            <view class="tel-code">
              <view class="code-title">验证码</view>
              <view class="ipt_box">
                <input
                  type="number"
                  maxlength="1"
                  :value="
                    oldTelCode.length >= index + 1 ? oldTelCode[index] : ''
                  "
                  v-for="(item, index) in 6"
                  :disabled="false"
                  @click="onFocus"
                  :key="index"
                />
              </view>
              <input
                type="number"
                class="hidden_ipt"
                maxlength="6"
                @blur="isFocus = false"
                :focus="isFocus"
                v-model="oldTelCode"
              />
            </view>
          </view>
          <view class="out-btn">
            <button class="btn cancel" @click="cancelAdd">取消</button>
            <button class="btn confirm" @click="addOldPatient">确定</button>
          </view>
        </view>
      </view>
    </view>
    <lb-picker
      :list="relationship"
      ref="lbpicker"
      v-model="patientInfo.spuRelation"
      mode="multiSelector"
      :level="2"
      @confirm="handleConfirm"
    ></lb-picker>
    <lb-picker
      :list="nationList"
      ref="nation"
      v-model="patientInfo.nation"
      mode="multiSelector"
      :level="1"
      @confirm="handleConfirmnation"
    ></lb-picker>
    <view class="wrapper1" v-if="showTips">
      <view class="hx-block">
        <view class="title"> {{ patient_age_limit_tip }} </view>
        <view class="btn_footer">
          <view @click="showTips = false">确定</view>
        </view>
      </view>
    </view>
    <!-- 底部按钮 -->
    <!--    <FooterButton @click="addPatientFun">保存</FooterButton>-->
    <view style="margin-top: 31px; padding: 0 19px">
      <view class="add-patient-btn" @click="addPatientFun">
        <img src="../../../../static/路径 <EMAIL>" alt="" />
        <span>保存</span>
      </view>
    </view>
  </view>
</template>

<script>
import { Toast } from "@/common/js/pay.js";
import myJsTools from "@/common/js/myJsTools.js";
import regex from "@/common/js/regex.js";
import FooterButton from "@/components/footer_button/button.vue";
import { getSysPlatformConfigByKeyList } from "@/api/base";
import {
  sendCaptcha,
  cationCode,
  addPatient,
  addOldPatient,
  findPatientRelationDict,
} from "@/api/user.js";
import { getSysCodeByType } from "@/api/shop";
import propBottom from "@/components/propBottom/propBottom.vue";
import LbPicker from "@/components/lb-picker";
import { uploadImg } from "@/api/oss.js";
export default {
  components: {
    propBottom,
    LbPicker,
    FooterButton,
  },
  data() {
    return {
      // 是否实名
      isRealName: true,
      showTips: false,
      patientInfo: {
        patientImg: "",
        patientName: "",
        idNo: "",
        sex: "",
        sexCode: "",
        age: "",
        guardianIdNo: "", // 监护人身份证号
        telNo: "",
        isMedicareType: NaN, // 医保卡类型
        medicareNum: "", // 医保卡号
        userId: "",
        weight: "",
        height: "",
        isDefault: 0, // 是否为默认就诊人
        drugAllergyHistory: "", // 药物过敏史
        familyMedicalHistory: "", // 家族病史
        geneticHistory: "", // 遗传病史
        previousHistory: "", // 既往病史
        spuRelation: "",
        nation: "", //民族
      },
      nationList: [],
      underAge: false, // 是否显示监护人身份证号填写栏
      codeinfo: {
        sendAuthCode: true,
        auth_time: 0,
        btnText: "获取验证码",
      },
      telCode: "",
      medicareTypeName: "",
      selectYbType: false,
      ybCardShow: false,
      actions: [
        {
          name: "就诊人无本市医保卡",
          ybCardType: 0,
        },
        {
          name: "就诊人持有本市医保卡",
          ybCardType: 1,
        },
        {
          name: "就诊人持有外地医保卡",
          ybCardType: 2,
        },
      ],
      oldTelNo: "",
      oldTelCode: "",
      addPatientToast: false,
      isFocus: false,
      relationship: [],
      patientImg: "",
      //简化版显示
      simpName: "0",
      //年龄限制提示
      patient_age_limit: "",
      patient_age_limit_tip: "",
    };
  },
  onLoad() {
    this.getConfig();
  },
  onShow() {
    this.getOptions();
    this.findPatientRelationDict();
    this.getSysCodeByType();
  },
  methods: {
    change(e) {
      this.patientInfo.firstFollowupDate = e;
      console.log("-change事件:", e);
    },
    //获取配置项
    async getOptions() {
      this.simpName = 0;
      let { data } = await getSysPlatformConfigByKeyList([
        "simplify_patient_real_name",
        "patient_age_limit",
        "patient_age_limit_tip",
      ]);
      data.map((el) => {
        if (el.configKey == "simplify_patient_real_name") {
          this.simpName = el.configValue;
        } else if (el.configKey == "patient_age_limit") {
          this.patient_age_limit = el.configValue;
        } else if (el.configKey == "patient_age_limit_tip") {
          this.patient_age_limit_tip = el.configValue;
        }
      });
    },
    // 获取配置 是否实名
    async getConfig() {
      let { data } = await getSysPlatformConfigByKeyList(["patient_real_name"]);
      // 0 不实名 1 实名
      if (data[0].configValue == 0) this.isRealName = false;
    },
    // 校验身份证号,并获取用户性别,年龄
    cardIdBlur(e) {
      let cardId = e.detail.value;
      let patientInfo = this.patientInfo;
      if (cardId) {
        if (!regex.idNoBlur(cardId)) {
          Toast("请确认您的身份证号是否正确");
          if (patientInfo.sex) {
            patientInfo.sex = "";
          }
          if (patientInfo.age) {
            patientInfo.age = "";
          }
          return;
        }
        if (regex.disCriCard(cardId, "sex") == 2) {
          patientInfo.sex = "女";
        } else {
          patientInfo.sex = "男";
        }
        patientInfo.sexCode = regex.disCriCard(cardId, "sex");
        patientInfo.age = regex.disCriCard(cardId, "age");
        if (patientInfo.age < 18) {
          this.underAge = true;
        }
      } else {
        if (patientInfo.sex) {
          patientInfo.sex = "";
        }
        if (patientInfo.age) {
          patientInfo.age = "";
        }
      }
    },
    // 获取验证码
    async sendCode() {
      // 防止多次点击触发多次请求
      if (this.codeinfo.auth_time > 0) return;
      let telNo = this.patientInfo.telNo;
      if (regex.telBlur(telNo)) {
        // 发送验证码,倒数读秒 发送请求之前 防止点击后反应慢
        this.codeinfo.auth_time = 60;
        await sendCaptcha({
          telNo: telNo,
        });
        // 倒计时
        var auth_timetimer = setInterval(() => {
          this.codeinfo.auth_time--;
          this.codeinfo.btnText = this.codeinfo.auth_time + "s后重新发送";
          if (this.codeinfo.auth_time <= 0) {
            this.codeinfo.sendAuthCode = true;
            this.codeinfo.btnText = "重新发送";
            // 重置为0
            this.codeinfo.auth_time = 0;
            clearInterval(auth_timetimer);
          }
        }, 1000);
      }
    },
    // 选择医保类型
    selectYbCardFun() {
      this.selectYbType = true;
    },
    // 接收prop-bottom的返回值
    propConfirm(data) {
      this.selectYbType = false;
      var patientInfo = this.patientInfo;
      var isMedicareType = patientInfo.isMedicareType;
      let ybCardType = data.ybCardType;

      if (ybCardType != isMedicareType) {
        // 只有在该次选择与上次选择的结果不一致的情况下才会赋值
        this.patientInfo.isMedicareType = ybCardType;
        this.patientInfo.medicareNum = "";
        this.medicareTypeName = data.name;
        if (ybCardType == "1" || ybCardType == "2") {
          this.ybCardShow = true;
        } else {
          this.ybCardShow = false;
        }
      }
    },
    propCancel() {
      this.selectYbType = false;
    },

    // 添加就诊人
    async addPatientFun() {
      if (!this.patientInfo.spuRelation) {
        Toast("请选择就诊人关系");
        return;
      }
      let patientInfo = this.patientInfo;
      console.log("查看添加参数", patientInfo);
      // 如果无需实名
      if (!this.isRealName && this.simpName == "0") {
        if (
          regex.nameBlur(patientInfo.patientName) &&
          regex.telBlur(patientInfo.telNo)
        ) {
          // 调用接口
          this.noRealNameSave();
        }
        return;
      }

      let age = patientInfo.age;
      if (this.patient_age_limit && age <= this.patient_age_limit) {
        this.showTips = true;
        return;
      }
      if (this.simpName == "0" && age < 18) {
        if (
          regex.nameBlur(patientInfo.patientName) &&
          regex.idNoBlur(patientInfo.idNo) &&
          regex.guardianIdNoBlur(patientInfo.guardianIdNo) &&
          regex.telBlur(patientInfo.telNo) &&
          regex.codeBlur(this.telCode)
        ) {
          this.cationTelCode();
        }
      } else {
        if (
          regex.nameBlur(patientInfo.patientName) &&
          regex.idNoBlur(patientInfo.idNo) &&
          regex.telBlur(patientInfo.telNo) &&
          regex.codeBlur(this.telCode)
        ) {
          this.cationTelCode();
        }
      }
    },
    // 无需验证
    async noRealNameSave() {
      let patientInfo = this.patientInfo;
      if (patientInfo.idNo && regex.idNoBlur(patientInfo.idNo)) {
        if (
          this.patient_age_limit &&
          patientInfo.age <= this.patient_age_limit
        ) {
          this.showTips = true;
          return;
        }
      }
      if (patientInfo.age && patientInfo.age < 18 && this.simpName == "0") {
        if (!regex.guardianIdNoBlur(patientInfo.guardianIdNo)) {
          return;
        }
      }
      let userId = uni.getStorageSync("userId");
      patientInfo.userId = userId;

      if (this.patientImg) {
        let uploadImg = await this.upoladImgFun(this.patientImg);
        console.log(uploadImg);
        patientInfo.patientImg = uploadImg.data.url;
      }
      console.log(patientInfo);
      // return
      let res = await addPatient(patientInfo);
      uni.setStorageSync("addPatientFlag", 1);
      // if(res.code == 20020) {
      //   this.isRealName = true;
      //   return;
      // }
      console.log(res, "接口返回结果");
      if (res.code == 20011 || res.code == 20020) {
        let oldTelNo = res.data.oldTelNo;
        this.oldTelNo = oldTelNo;
        this.addPatientToast = true;
        await sendCaptcha({
          telNo: oldTelNo,
        });
      } else {
        // debugger;
        uni.navigateBack();
      }
    },
    // 校验手机验证码
    async cationTelCode() {
      let patientInfo = this.patientInfo;
      var para = {
        captcha: this.telCode,
        telNo: this.patientInfo.telNo,
      };
      if (!this.patientInfo.spuRelation && this.simpName == "0") {
        Toast("请选择家人关系");
        return false;
      }
      if (!this.patientInfo.nation && this.simpName == "0") {
        Toast("请选择民族");
        return false;
      }
      if (this.patientImg) {
        let uploadImg = await this.upoladImgFun(this.patientImg);
        patientInfo.patientImg = uploadImg.data.url;
      }

      await cationCode(para);
      let userId = uni.getStorageSync("userId");
      patientInfo.userId = userId;
      // patientInfo.birthDate = myJsTools.getBirthdayFromIdCard(patientInfo.idNo);

      let res = await addPatient(patientInfo);
      uni.setStorageSync("addPatientFlag", 1);
      if (res.code == 20011) {
        let oldTelNo = res.data.oldTelNo;
        this.oldTelNo = oldTelNo;
        this.addPatientToast = true;
        if (regex.telBlur(oldTelNo)) {
          await sendCaptcha({
            telNo: oldTelNo,
          });
        }
      } else {
        var patientId = res.data.patientId;
        if (this.simpName == "0") {
          uni.redirectTo({
            url:
              "/pages/personalCenter/patientManage/addPatientOtherInfo/index?patientInfo=" +
              JSON.stringify(patientInfo) +
              "&patientId=" +
              patientId,
          });
        } else {
          uni.navigateBack();
        }
      }
    },
    onFocus() {
      this.isFocus = true;
    },
    // 取消添加就诊人
    cancelAdd() {
      this.addPatientToast = false;
      uni.navigateBack({
        delta: 1,
      });
    },
    async addOldPatient() {
      if (regex.telBlur(this.oldTelNo) && regex.codeBlur(this.oldTelCode)) {
        var para = {
          captcha: this.oldTelCode,
          telNo: this.oldTelNo,
        };
        await cationCode(para);
        let param = {
          userId: uni.getStorageSync("userId"),
          telNo: this.patientInfo.telNo,
          idNo: this.patientInfo.idNo,
        };

        await addOldPatient(param);
        this.addPatientToast = false;
        wx.navigateBack({
          delta: 1,
        });
      }
    },
    // 选择患者头像
    uploadTx() {
      let _this = this;
      uni.chooseImage({
        count: 1,
        sourceType: ["album", "camera"],
        success: (res) => {
          let tempFiles = res.tempFiles[0];
          myJsTools.setImgZip(tempFiles, (dataUrl) => {
            _this.patientImg = {
              base64: dataUrl.split(",")[1],
              url: dataUrl,
              name: "",
            };
            _this.patientInfo.patientImg = dataUrl;
          });
        },
      });
    },
    upoladImgFun(obj) {
      let para = {
        folderType: 1,
        imgBody: obj.base64,
      };
      return uploadImg(para);
    },
    async findPatientRelationDict() {
      let { data } = await findPatientRelationDict();
      console.log(data);
      this.relationship = data;
    },
    async getSysCodeByType() {
      let { data } = await getSysCodeByType("085");
      //  console.log(data)
      let list = [];
      data.forEach((item) => {
        list.push({ label: item.meaning, value: item.code, children: [] });
      });
      // console.log(list);
      this.nationList = list;
    },
    openLbPicker() {
      this.$refs.lbpicker.show();
    },
    openNation() {
      console.log("22222");
      this.$refs.nation.show();
    },
    handleConfirm(e) {
      this.patientInfo.spuRelation = e.item[1].label;
    },
    handleConfirmnation(e) {
      // console.log(e.item[0]);
      this.patientInfo.nation = e.item[0].label;
    },
  },
};
</script>

<style scoped>
.displayFlex /deep/ .uni-date__x-input {
  font-size: 28rpx !important;
  padding-left: 30rpx !important;
}
.displayFlex /deep/ .uni-date-x--border {
  border: none !important;
}
</style>

<style scoped lang="scss">
.body {
  background-color: #f0f2fc !important;
  height: 100vh;
}

.add-patient-btn {
  border-radius: 100rpx;
  height: 36px;
  background: linear-gradient(135deg, #516afb 0%, #859bff 99.97%);
  display: flex;
  align-items: center;
  justify-content: center;
  span {
    color: #fff;
    font-size: 14px;
  }
  img {
    width: 12px;
    height: 12px;
    margin-right: 5px;
  }
}
.disabled {
  pointer-events: none;
}
.page-container {
  padding: 34rpx 40rpx;
  box-sizing: border-box;
  overflow-y: auto;
  background-color: #ffffff;
}

.patient-info {
  background: #ffffff;
  padding-left: 32rpx;
  border-radius: 8rpx;
}

.input-box {
  width: 100%;
  display: flex;
  height: 92rpx;
  border-radius: 4rpx;
  box-sizing: border-box;
  line-height: 92rpx;
  padding-right: 32upx;
  border-bottom: 1rpx solid #ebebeb;
}

.patient-info .input-box:last-child {
  border-bottom: none;
}

.input-box text {
  display: inline-block;
  width: 150rpx;
  flex: none;
  font-size: 28rpx;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
}

.input-box .icon {
  width: 40rpx;
}

.input-box input {
  flex: 1;
  height: 100%;
  line-height: 100%;
  padding-left: 30rpx;
  font-size: 24rpx;
  font-weight: 400;
  text-align: right;
  color: #999999 !important;
}

.sendBtn {
  flex: none;
  min-width: 188rpx;
  height: 92rpx;
  line-height: 92rpx;
  font-size: 28rpx;
  font-weight: 400;
  @include font_theme;
  padding: 0;
  text-align: right;
}

/* 添加就诊人时,如果为之前已经被添加的就诊人且手机号不一致,验证手机号 */
.wrapper {
  position: fixed;
  width: 100%;
  height: 100vh;
  left: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}

.wrapper .block {
  width: 606rpx;
  box-sizing: border-box;
  background: rgba(255, 255, 255, 1);
  border-radius: 16rpx;
  overflow: hidden;
}

.wrapper .title {
  text-align: center;
  padding-top: 40rpx;
  font-size: 36rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: rgba(51, 51, 51, 1);
  line-height: 50rpx;
}

.wrapper .content {
  padding: 20rpx 24rpx 52rpx 24rpx;
  font-size: 30rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  line-height: 42rpx;
  min-height: 100rpx;
}

.tel-code {
  display: flex;
  width: 100%;
  padding: 38rpx 42rpx;
  box-sizing: border-box;
  position: relative;
}

.code-title {
  font-size: 28rpx;
  font-family: PingFangSC-Semibold, PingFang SC;
  font-weight: 600;
  color: rgba(51, 51, 51, 1);
  line-height: 40rpx;
  padding-right: 20rpx;
  flex: none;
}

.ipt_box {
  flex: 1;
  display: flex;
  justify-content: space-around;
}

.ipt_box input {
  height: 44rpx;
  width: 36rpx;
  border: 1px solid #cdcdcd;
  border-radius: 4rpx;
  display: block;
  line-height: 44rpx;
  text-align: center;
  font-size: 32rpx;
  font-family: PingFangSC-Semibold, PingFang SC;
  font-weight: 600;
  color: rgba(51, 51, 51, 1);
  background: rgba(236, 236, 236, 1);
}

.tel-code .hidden_ipt {
  height: 0rpx;
  border: none;
  position: absolute;
  top: -80rpx;
  opacity: 0;
}

.wrapper .out-btn {
  width: 100%;
  padding: 0 24rpx 48rpx;
  box-sizing: border-box;
  display: flex;
  justify-content: space-around;
}

.out-btn .btn {
  width: 244rpx;
  height: 68rpx;
  line-height: 68rpx;
  border-radius: 46rpx;
  font-size: 36rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
}

.out-btn .cancel {
  @include border_theme(2rpx);
  @include font_theme;
  background: #fff;
}

.out-btn .confirm {
  @include bg_theme;
  color: #fff;
}

.txClass {
  width: 70upx;
  height: 70upx;
  border-radius: 16upx;
  margin-left: 40upx;
  margin-top: 6upx;
}

.wrapper1 {
  position: fixed;
  width: 100%;
  height: 100vh;
  left: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.5);
  @include flex(center);
  z-index: 999;

  // 容器
  .block_box {
    width: 600upx;
    border-radius: 16upx;
    background-color: #fff;
    padding: 0 24upx;
    box-sizing: border-box;

    // 头像
    .avatar {
      width: 170upx;
      height: 170upx;
      display: block;
      margin: -85upx auto 0;
    }

    // 内容
    .info {
      .text {
        color: $k-title;
        font-weight: 500;
        font-size: 28upx;
        text-align: center;
      }
    }
  }
}
/* 环信弹框 */
.wrapper1 .hx-block {
  width: 606rpx;
  box-sizing: border-box;
  background: #fff;
  border-radius: 16upx;
  overflow: hidden;
  padding: 94rpx 40rpx 40rpx 40rpx;
}

.hx-block .title {
  padding-bottom: 64rpx;
  color: $k-title;
  font-size: 32upx;
  font-weight: 600;
  white-space: normal;
  text-align: center;
}

.hx-block .btn_footer {
  width: 100%;
  @include flex(center);
  justify-content: space-around;
}

.hx-block .btn_footer view {
  width: 242rpx;
  height: 68rpx;
  border-radius: 46rpx;
  color: #ffffff;
  font-weight: 600;
  @include bg_theme;
  font-size: 28rpx;
  text-align: center;
  line-height: 68rpx;
  box-sizing: border-box;
}

.hx-block .btn_footer .cancel {
  @include border_theme;
  @include font_theme;
  background: transparent;
  margin-right: 36rpx;
}

.displayFlex {
  display: flex;
  width: 80%;
  align-items: center;
  justify-content: space-between;
  input {
    width: 80%;
  }
}
</style>
