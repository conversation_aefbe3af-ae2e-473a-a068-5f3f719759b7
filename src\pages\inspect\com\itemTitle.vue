<template>
  <!-- 标题 -->
  <view class="itemTitle" @click="click">
    <text class="title">{{ title }}</text>
    <uni-icons type="help" v-if="showIcon" color="#999" />
  </view>
</template>

<script>
export default {
  name: 'ItemTitle',
  props: {
    title: String,
    showIcon: {
      type: Boolean,
      default: false,
    },
  },

  methods: {
    click() {

      if (!this.showIcon) return;
      this.$emit('click');
    },
  },
};
</script>

<style lang="scss" scoped>
.itemTitle {
  font-size: 32rpx;
  font-weight: bold;
  height: 44rpx;
  @include flex(left);

  .title {
    padding-left: 18rpx;
    position: relative;
    line-height: 30rpx;
    margin-right: 12rpx;

    &::before {
      content: '';
      display: block;
      width: 6rpx;
      height: 100%;
      @include bg_theme;
      position: absolute;
      border-radius: 4rpx;
      left: 0;
      top: 0;
    }
  }
}
</style>
