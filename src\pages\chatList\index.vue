<template>
  <view class="page">
    <!-- tab页面 -->
    <view class="tab_title">
      <view class="tab_title_box" @click="setNav(0)">
        <view
          class="tab_title_font"
          :class="query.isOffLine == 0 ? 'active' : ''"
          >我的问诊</view
        >
        <uni-badge
          class="tab_title_abdge1"
          :text="0"
          absolute="rightTop"
          size="small"
          :is-dot="true"
        />
      </view>
      <view class="tab_title_box" @click="setNav(3)">
        <view
          class="tab_title_font"
          :class="query.isOffLine == 3 ? 'active' : ''"
          >AI医生助手
        </view>
      </view>
      <view class="tab_title_box" @click="setNav(1)">
        <view
          class="tab_title_font"
          :class="query.isOffLine == 1 ? 'active' : ''"
          >其他消息</view
        >
        <uni-badge
          v-if="sysNum > 0"
          class="tab_title_abdge1"
          :text="0"
          absolute="rightTop"
          size="small"
          :is-dot="true"
        />
      </view>
    </view>

    <!-- 群聊 -->
    <view
      class="home-page"
      style="height: calc(100% - 65px)"
      v-if="query.isOffLine == 0"
    >
      <view class="bg_top" style="height: 100%">
        <view class="search">
          <uni-search-bar
            v-model="searchValue"
            :radius="100"
            cancelButton="auto"
            bgColor="#FFF"
            @cancel="reset"
            maxlength="20"
            placeholder="输入医生姓名进行查找"
            @confirm="search"
          ></uni-search-bar>
          <view style="color: rgba(135, 79, 240, 1)" @click="search">
            <text style="margin-right: 12px">|</text>
            搜索
          </view>
        </view>

        <view class="empty" v-if="groupChatList.length == 0">
          <image
            src="../../static/images/index/box_empty.png"
            class="emptyImg"
          ></image>
          <view>暂无沟通信息</view>
        </view>
        <scroll-view scroll-y="true" class="scroll-Y" @scrolltolower="lower">
          <view v-for="(item, index) in groupChatList" :key="index">
            <view
              v-if="item.id != 'admin,admin'"
              class="chatList"
              @click="openChatRoom(item, item.id)"
            >
              <view class="header_img">
                <img
                  :src="
                    item.docImgUrl
                      ? item.docImgUrl
                      : '../../static/images/docHead.png'
                  "
                  class="userImg"
                />
              </view>
              <view class="info">
                <view class="info_title">
                  <u class="patientInfo">
                    <text class="docName">{{ item.platformDocName || item.docName }}</text>
                    <text class="deptName">{{ item.deptName }}</text>
                  </u>
                  <u>
                    <text
                      v-if="item.ywStatus == '0'"
                      class="status"
                      style="
                        color: rgba(234, 115, 47, 1);
                        background: rgba(253, 241, 233, 1);
                      "
                      >待签到</text
                    >
                    <text
                      v-if="item.ywStatus == '1'"
                      class="status"
                      style="
                        color: rgba(165, 129, 77, 1);
                        background: rgba(255, 239, 215, 1);
                      "
                      >待接诊</text
                    >
                    <text
                      v-if="item.ywStatus == '2'"
                      class="status"
                      style="
                        color: rgba(44, 199, 147, 1);
                        background: rgba(232, 250, 244, 1);
                      "
                      >接诊中</text
                    >
                    <text
                      v-if="item.ywStatus == '3'"
                      class="status"
                      style="
                        color: rgba(211, 65, 63, 1);
                        background: rgba(253, 236, 237, 1);
                      "
                      >就诊结束</text
                    >
                    <text
                      v-if="item.ywStatus == '4'"
                      class="status"
                      style="
                        color: rgba(59, 96, 239, 1);
                        background: rgba(235, 240, 254, 1);
                      "
                      >已退诊</text
                    >
                    <text
                      v-if="item.ywStatus == '5'"
                      class="status"
                      style="
                        color: rgba(134, 138, 141, 1);
                        background: rgba(244, 244, 244, 1);
                      "
                      >已失效</text
                    >
                  </u>
                </view>
                <view
                  style="
                    display: flex;
                    justify-content: space-between;
                    border-bottom: 1px solid #eaeaea;
                    padding-bottom: 5px;
                  "
                >
                  <view class="description">
                    就诊人姓名：{{ item.patientName }}
                  </view>
                  <view>
                    <text class="visitType" v-if="item.visitTypeCode == 1"
                      >图文</text
                    >
                    <text class="visitType" v-if="item.visitTypeCode == 2"
                      >语音</text
                    >
                    <text class="visitType" v-if="item.visitTypeCode == 4"
                      >视频</text
                    >
                    <text class="visitType" v-if="item.projectId">患者管理</text>
                  </view>
                </view>
                <view class="contentInfo">
                  <u class="fitrstChild">
                    <view
                      class="lastContent"
                      v-html="renderTxt(item.latInfo)"
                    ></view>
                  </u>
                  <text class="hours">{{ item.timeDiff }}</text>
                </view>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>
    <!-- AI医生小助手 -->
    <view v-if="query.isOffLine == 3">
      <view class="ai-assistant-page">
        <!-- 添加就诊人筛选器 -->
        <scroll-view scroll-x="true" class="patient-filter-scroll">
          <view class="patient-filter">
            <view 
              class="patient-item" 
              :class="{ active: selectedPatientId === 'all' }"
              @click="selectPatient('all')"
            >全部</view>
            <view 
              v-for="(patient, index) in patientList" 
              :key="index" 
              class="patient-item"
              :class="{ active: selectedPatientId === patient.patientId }"
              @click="selectPatient(patient.patientId)"
            >{{patient.patientName}}</view>
          </view>
        </scroll-view>

        <view class="ai-message-list">
          <view class="empty" v-if="aiMessages.length === 0">
            <image
              src="../../static/images/index/box_empty.png"
              class="emptyImg"
            ></image>
            <view>暂无消息</view>
          </view>

          <scroll-view
            scroll-y="true"
            class="ai-scroll-view"
            @scrolltolower="loadMoreAiMessages"
          >
            <view
              v-for="(item, index) in aiMessages"
              :key="index"
              class="ai-message-item"
              @click="handleAiMessageClick(item)"
            >
              <view class="ai-message-content">
                <img src="/static/doc/log.png" class="tips" />
                <view class="ai-message-info">
                  <view class="ai-message-title">{{
                    item.wxTitle || '您的健康报告已生成，请查看'
                  }}</view>
                  <!-- 添加就诊人姓名和发送时间 -->
                  <view class="ai-message-details">
                    <text class="ai-message-patient">就诊人：{{item.extJson.patientName || ''}}</text>
                    <text class="ai-message-time">{{item.pushTime}}</text>
                  </view>
                </view>
              </view>
              <view class="ai-message-status" v-if="item.status === 0">
                <view class="unread-dot"></view>
              </view>
            </view>
          </scroll-view>
        </view>
      </view>
    </view>
    <!-- 其他消息 -->
    <view v-if="query.isOffLine == 1">
      <SysTemList />
    </view>
    <!-- 透明遮罩 -->
    <view class="k_page" v-show="showTips" @click="showTips = false"></view>

    <!-- 气泡弹层 -->
    <view
      class="k_tips"
      :style="{ top: pageY + 'px', left: pageX + 'px' }"
      v-show="showTips"
    >
      <view class="tips_item" v-if="false">
        <image src="/static/images/tips/read.png" class="item_icon" />
        <text class="item_text">标为已读</text>
      </view>
      <view class="tips_item">
        <image src="/static/images/tips/del.png" class="item_icon" />
        <text class="item_text" @click="del">删除</text>
      </view>
      <view class="tips_item" @click="setTop" v-if="false">
        <image src="/static/images/tips/read.png" class="item_icon" />
        <text class="item_text">置顶</text>
      </view>
    </view>
  </view>
</template>

<script>
import SysTemList from './sysTemList.vue'
import { basicgetPatientChatList, updateChatList } from '@/api/chat'
import date from '@/utils/date'
import { uniSwipeAction, uniSwipeActionItem } from '@dcloudio/uni-ui'
import { getSysPushResultRecord } from '@/api/qcAi'
import { getPatientList } from '@/api/user'
import myJsTools from '@/common/js/myJsTools.js'
import indexedDB from '@/db/main.js'
import { getPatientChatListSM } from '../../api/order'

// 获取屏幕宽高
let { screenHeight, screenWidth } = uni.getSystemInfoSync()

let timer
export default {
  components: {
    uniSwipeAction,
    uniSwipeActionItem,
    SysTemList,
  },
  data() {
    return {
      chatNum: '0',
      sysNum: '0',
      query: {
        isOffLine: 0,
      },
      docList: [],
      list: [],
      listQuery: {
        page: 1,
        limit: 10,
        sopIds: '',
        pinyinName: '',
      },
      userId: '',
      // 要操作的消息
      item: '',
      nowChatList: [],
      // 定时器
      timer: '',
      // left
      pageX: 0,
      // top
      pageY: 0,
      // 显示弹窗
      showTips: false,
      // 系统图标
      sys_icon: CONFIG_ENV.VUE_APP_SHARE,
      searchValue: '',
      groupChatList: [],
      total: 0,
      patientList: [],
      selectedPatientId: 'all',
      aiMessages: [],
      aiQueryParams: {
        page: 1,
        limit: 20,
        total: 0,
      },
    }
  },
  computed: {
    // 监听接诊退诊
    jzStatus() {
      return this.$store.getters.getJzStatus
    },
    chatAll() {
      console.log('update---chat----', this.$store.getters.getGroupChatAllList)
      return this.$store.getters.getGroupChatAllList
    },
  },
  watch: {
    list(n, o) {
      if (n.length > o.length) {
        this.getDocList()
      }
    },
    jzStatus(n, o) {
      if (!n) return
      let path = this.$route.path
      if (path != '/pages/chatList/index') return
      this.getDocList()
    },
    chatAll() {
      if (!this.chatAll.length) return
      console.log('update---chat----', this.chatAll)
      this.groupChatList.forEach((v) => {
        if (v.groupId == this.chatAll[this.chatAll.length - 1].to) {
          v.newMsg = this.chatAll[this.chatAll.length - 1].data
        }
      })
    },
  },
  async onShow() {
    this.$store.commit('SET_CHATLIST', [])
    this.listQuery.sopIds = uni.getStorageSync('patientIdList')
    this.userId = uni.getStorageSync('userId')
    this.listQuery.page = 1
    this.groupChatList = []
    this.getChatListSM()
    this.chatId = 'admin,admin'
    let obj = {
      chatId: this.chatId,
    }
    await this.$store.dispatch('getChatListId', obj)
    const chatList = this.$store.getters.getChatList || { chatRecordList: [] }
    const unReadNum = chatList?.chatRecordList?.filter(
      (v) => v.status != 'read'
    ).length
    this.sysNum = unReadNum > 99 ? '99' : String(unReadNum || 0)

    // 如果当前显示AI消息页面，加载AI消息和就诊人列表
    if (this.query.isOffLine === 3) {
      this.aiQueryParams.page = 1
      this.getAiMessages()
      this.getPatientList()
    }
  },
  onPullDownRefresh() {
    this.listQuery.page = 1
    this.listQuery.pinyinName = ''
    this.docList = []
    this.getChatListSM()

    // 如果当前是AI消息页面，刷新AI消息
    if (this.query.isOffLine === 3) {
      this.aiQueryParams.page = 1
      this.getAiMessages().then(() => {
        uni.stopPullDownRefresh()
      })
    }
  },
  methods: {
    goToAiChat() {
      uni.navigateTo({
        url: '/pages/aiAssistant/chat',
      })
    },
    async getChatListSM() {
      const res = await getPatientChatListSM({
        page: this.listQuery.page,
        limit: this.listQuery.limit,
        docName: this.searchValue?.value,
        userId: uni.getStorageSync('userId'),
      })
      this.total = res.data.total
      if (this.listQuery.page > 1) {
        this.groupChatList = [...this.groupChatList, ...res.data.rows]
      } else {
        this.groupChatList = res.data.rows
      }
    },
    // 滚动组件触底
    lower() {
      console.log('触底加载')
      if (this.groupChatList.length >= this.total) return
      this.listQuery.page += 1
      this.getChatListSM()
    },
    setNav(n) {
      if (this.query.isOffLine == n) return
      this.query.isOffLine = n

      // 如果切换到AI医生小助手页面
      if (n === 3) {
        this.aiMessages = []
        this.aiQueryParams.page = 1
        this.getAiMessages()
        this.getPatientList()
      }
    },
    async getAiMessages() {
      try {
        const userId = uni.getStorageSync('userId')
        const params = {
          docId: userId,
          page: this.aiQueryParams.page,
          limit: this.aiQueryParams.limit,
          patientId:''
        }
        
        // 添加就诊人ID筛选条件
        if (this.selectedPatientId !== 'all') {
          params.patientId = this.selectedPatientId;
        }

        const res = await getSysPushResultRecord(params)

        if (res.data) {
          this.aiQueryParams.total = res.data.total || 0

          // 处理数据
          const messages = (res.data.rows || []).map(item => {
            return {
              ...item,
              extJson: JSON.parse(item.ext||'{}')
            }
          })

          // 如果是加载更多，则追加数据
          if (this.aiQueryParams.page > 1) {
            this.aiMessages = [...this.aiMessages, ...messages]
          } else {
            this.aiMessages = messages
          }
        }
      } catch (error) {
        console.error('获取AI消息列表失败', error)
        uni.showToast({
          title: '获取消息失败',
          icon: 'none',
        })
      }
    },
    async loadMoreAiMessages() {
      if (this.aiMessages.length >= this.aiQueryParams.total) {
        return // 已加载全部数据
      }

      this.aiQueryParams.page += 1
      await this.getAiMessages() // 已包含就诊人筛选逻辑
    },
    handleAiMessageClick(item) {
      if (item.hxMessage) {
        uni.navigateTo({
          url: `/pages/qcAi/report?id=${item.hxMessage}`,
        })
      }
    },
    setImg() {
      this.list.forEach((item) => {
        if (item.docImg && !item.docImgCopy) {
          myJsTools.downAndSaveImg(item.docImg, (url) => {
            this.$set(item, 'docImgCopy', url)
          })
        }
      })
    },
    touchStart(e, index) {
      this.item = index
      this.showTips = false
      let { touches } = e
      if (touches.length > 1) return
      this.timer = setTimeout(() => {
        if (touches.length > 1) return
        let { pageX, pageY } = touches[0]
        if (screenHeight - pageY < 200) {
          pageY = screenHeight - 200
        }
        if (screenWidth - pageX < 120) {
          pageX = screenWidth - 120
        }
        this.pageX = pageX
        this.pageY = pageY
        this.showTips = true
      }, 500)
    },
    touchMove() {
      clearTimeout(this.timer)
      this.timer = ''
      this.showTips = false
    },
    touchEnd() {},
    bindClick(e, item) {
      let _this = this

      if (e.index == 0) {
        if (e.content.text == '置顶') {
          this.setTop(item)
        } else {
          this.cancelTop(item)
        }
      } else if (e.index == 1) {
        this.delete(item)
      }
    },
    async setTop() {
      let list = this.chatList

      list[this.item].isTop = true

      let item = list[this.item]

      this.$store.commit('setAllChatList', list)

      let para = {}
      para.docId = item.dataVal.docId
      para.isTopping = 1
      para.patientId = item.dataVal.patientId
      para.pcoType = 1
      para.userId = uni.getStorageSync('userId')

      await updateChatList(para)
      this.getDocList()
    },
    async cancelTop(item) {
      let para = {
        docId: item.docId,
        isTopping: 0,
        patientId: item.patientId,
        pcoType: 1,
        userId: uni.getStorageSync('userId'),
      }
      await updateChatList(para)
      this.getDocList()
    },
    async del() {
      let item = this.list[this.item]
      await indexedDB.del(item.id)
      this.showTips = false
      this.$store.dispatch('getChatListDoc')
    },
    getUnreadNum(item) {
      var userId = item.id.split(',')[0]
      var docId = item.id.split(',')[1]
      let chatList
      this.chatList.map((item) => {
        if (userId + ',' + docId == item.id) {
          chatList = item
        }
      })
      let unReadNum = 0
      if (chatList.chatRecordList && chatList.chatRecordList.length > 0) {
        chatList.chatRecordList.forEach((msg) => {
          if (msg.status == 'unread' && msg.type == 'receive') {
            unReadNum++
          }
        })
      }
      return unReadNum
    },
    async getDocList() {
      let paramLists = []
      this.chatList.map((item) => {
        let ids = item.id.split(',')
        paramLists.push({
          patientId: ids[0],
          docId: ids[1],
        })
      })
      if (paramLists.length == 0) {
        uni.stopPullDownRefresh()
        return
      }

      let res = await basicgetPatientChatList({
        paramLists,
        page: this.listQuery.page,
        limit: this.listQuery.limit,
      })
      let data = res.data.rows

      let arr = JSON.parse(JSON.stringify(this.chatList))

      arr.map((item) => {
        data.map((el) => {
          let id = el.patientId.toLowerCase() + ',' + el.docId.toLowerCase()
          if (item.id == id) {
            item.dataVal = el
          }
        })
      })
      if (arr.length > 0) {
        this.$store.commit('setAllChatList', arr)
      }
      uni.stopPullDownRefresh()
    },
    async openChatRoom(item, id) {
      uni.setStorageSync('patientId', item.patientId)
      clearTimeout(this.timer)
      uni.setStorageSync('chatItem', item)
      uni.setStorageSync('hosId', item.hosId)
      let param = {
        docId: item.docId,
      }
      let obj = {
        chatId: id,
      }
      if (item.groupId) {
        let isPatientAdminChat = item.projectId ? '&isPatientAdminChat=1' : ''
        uni.navigateTo({
          url:
            '/pages/chatList/scanChatDetail?param=' +
            JSON.stringify(param) +
            isPatientAdminChat,
        })
        return
      }
      await this.$store.dispatch('getChatListId', obj)
      uni.navigateTo({
        url: '/pages/chatList/chatDetail?param=' + JSON.stringify(param),
      })
    },
    openAdminMsg(item) {
      item.patientId = 'admin'
      item.docId = 'admin'
      uni.setStorageSync('chatItem', item)
      uni.navigateTo({
        url: '/pages/chatList/sysTemList',
      })
    },
    customEmoji(value) {
      return `<image src="http://llootong.cn/cloud/hisImg/static/faces/${value}" style="width:15px;height:15px"></image>`
    },
    renderTxt(txt = '') {
      let rnTxt = []
      let match = null
      const regex = /(\[.*?\])/g
      let start = 0
      let index = 0
      while ((match = regex.exec(txt))) {
        index = match.index
        if (index > start) {
          rnTxt.push(txt.substring(start, index))
        }
        if (match[1] in this.$im.Emoji.map) {
          const v = this.$im.Emoji.map[match[1]]
          rnTxt.push(this.customEmoji(v))
        } else {
          rnTxt.push(match[1])
        }
        start = index + match[1].length
      }
      rnTxt.push(txt.substring(start, txt.length))
      return rnTxt.toString().replace(/,/g, '')
    },
    search(e) {
      this.listQuery.docName = this.searchValue
      this.listQuery.page = 1
      this.getChatListSM()
    },
    async reset() {
    },
    selectPatient(patientId) {
      if (this.selectedPatientId === patientId) return;
      
      this.selectedPatientId = patientId;
      this.aiQueryParams.page = 1; // 重置页码
      this.aiMessages = []; // 清空现有消息
      this.getAiMessages(); // 重新加载消息
    },
    formatTime(timestamp) {
      if (!timestamp) return '';
      return date.getDateDiff(timestamp);
    },
    async getPatientList() {
      try {
        const res = await getPatientList({
          userId: uni.getStorageSync('userId')
        });
        if (res.data) {
          this.patientList = res.data;
        }
      } catch (error) {
        console.error('获取就诊人列表失败', error);
      }
    },
  },
}
</script>

<style scoped lang="scss">
.scroll-Y {
  width: 100%;
  height: 100%;
  height: calc(100% - 40px);
}

.ai-assistant-page {
  background-color: #f8f8f8;
  min-height: calc(100vh - 150px);
  padding-bottom: 20px;
}

.ai-assistant-header {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #fff;
  margin-bottom: 20rpx;

  .ai-logo {
    width: 80rpx;
    height: 80rpx;
    margin-right: 20rpx;
  }

  .ai-title {
    font-size: 34rpx;
    font-weight: bold;
    color: #333;
  }
}

.ai-message-list {
  padding: 0 30rpx;

}

.ai-scroll-view {
  height: calc(100vh - 200px);
}

.ai-message-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fff;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.ai-message-content {
  flex: 1;
  display: flex;
  align-items: flex-start;
}

.ai-message-info {
  flex: 1;
  margin-left: 20rpx;
}

.ai-message-title {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 16rpx;
}

.ai-message-details {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: #999;
}

.ai-message-patient {
  color: #666;
}

.ai-message-time {
  margin-left: 20rpx;
}

.ai-message-status {
  width: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.unread-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background-color: #ff3b30;
}

.tips {
  width: 70rpx;
  height: 70rpx;
}

.search {
  width: 706rpx;
  height: 60rpx;
  opacity: 1;
  border-radius: 30rpx;
  background: rgba(242, 245, 255, 1);
  margin: auto;
  margin-top: 20rpx;
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  .uni-searchbar {
    border: none !important;
    background: none;
    width: 80%;
    /deep/ .uni-searchbar__box {
      border: none !important;
      background: none !important;
      justify-content: left !important;
    }
  }
}
.tab_title {
  display: flex;
  align-items: center;
  font-size: 34rpx;
  text-align: center;
  height: 88rpx;
  padding: 0 24rpx;
  position: sticky;
  top: 0;
  z-index: 3;
  box-sizing: border-box;
  background: #ffffff;
}

.tab_title .tab_title_box {
  flex: 1;
  position: relative;
  .tab_title_abdge {
    position: absolute;
    top: 8rpx;
    right: 60rpx;
  }
}
.tab_title .tab_title_box {
  flex: 1;
  position: relative;
  .tab_title_abdge1 {
    position: absolute;
    top: 30rpx;
    right: 32rpx;
    background: #dd524d;
    border-radius: 50%;
    height: 10px;
    width: 10px;
  }
}
.tab_title .tab_title_font {
  color: #666666;
  display: inline-block;
  border-bottom: 6rpx solid transparent;
  line-height: 88rpx;
  padding-top: 6rpx;
  box-sizing: border-box;
}

.tab_title .active {
  padding-top: 6rpx;
  line-height: 88rpx;
  @include font_theme;
  display: inline-block;
  @include border_theme(6rpx, bottom);
  box-sizing: border-box;
  font-weight: 700;
}
.page {
  background-color: #fff;
  height: calc(100vh - 70px);
}

.empty {
  text-align: center;
  color: $k-info-title;
  font-size: 28rpx;
  margin-top: 250rpx;
}

.emptyImg {
  width: 286rpx;
  height: 226rpx;
  margin-bottom: 40rpx;
}

.chatList {
  padding-left: 30rpx;
  @include flex;
  width: 90%;
  height: 192upx;
  margin: auto;
  border-radius: 5px;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px 1px 4px rgba(121, 72, 234, 0.15);
  margin-bottom: 30rpx;
  .header_img {
    display: flex;
    position: relative;
    margin-right: 24rpx;
    flex: none;

    .userImg {
      width: 104upx;
      height: 104upx;
      border-radius: 50%;
    }
  }
}

u {
  text-decoration: none;
  display: inline-block;
}

.docName,
.deptName {
  color: $k-title;
  font-size: 30rpx;
  font-weight: 600;
}

.deptName {
  font-weight: 500;
  margin-left: 24rpx;
}

.status {
  @include font_theme;
  height: 44rpx;
  font-size: 24rpx;
  font-weight: 600;
  border-radius: 30rpx;
  background: rgba(232, 250, 244, 1);
  padding: 4rpx 16rpx;
}

.visitType {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: rgba(135, 79, 240, 1);
  font-size: 20upx;
  height: 36upx;
  line-height: 36upx;
  padding: 0 18rpx;
  background: rgba(242, 245, 255, 1);
  border-radius: 8upx;
  margin-left: 24rpx;
  margin-top: 16rpx;
}

.info {
  flex: 1;
  @include flex;
  align-items: stretch;
  flex-direction: column;
  height: 100%;
  padding-right: 32upx;
  border-bottom: 1px solid #ebebeb;
  overflow: hidden;

  .info_title {
    @include flex(lr);

    .patientInfo {
      max-width: 63%;
    }
  }
}

.lastContent {
  color: #999999;
  font-size: 13px;
  line-height: 18px;
}

.patientName {
  color: #ffffff;
  font-size: 18rpx;
  background-color: #676767;
  border-radius: 14upx;
  height: 28upx;
  padding: 0 10rpx;
  @include flex;
}

.hours {
  color: #999999;
  font-size: 24upx;
  margin-left: 20rpx;
  text-align: right;
  display: inline-block;
  flex: none;
}

.sysTime {
  margin-left: 50upx;
}

.contentInfo {
  margin-top: 10upx;
  overflow: hidden;
  @include flex(lr);
}

.fitrstChild {
  flex: 1;
  width: 55%;

  .lastContent {
    @include hide;
  }
}

.chatListTop {
  background: #f5f5f5;
}

/deep/.uni-badge--error {
  color: #fff;
  background-color: #dd524d;
  position: absolute;
  @include flex;
  right: 0px;
  top: 0px;
}

.k_page {
  width: 100%;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9;
}

.k_tips {
  width: 210upx;
  max-height: 276upx;
  background-color: #fff;
  border-radius: 8upx;
  border: 1px solid #e5e5e5;
  box-sizing: border-box;
  padding: 10upx 20upx;
  @include flex;
  align-items: stretch;
  flex-direction: column;
  box-shadow: 0 0 10upx #bbb;
  position: absolute;
  z-index: 10;

  .tips_item {
    flex: 1;
    @include flex(left);
    border-bottom: 1px solid #e5e5e5;

    &:last-child {
      border-bottom: none;
    }

    .item_icon {
      width: 44upx;
      height: 44upx;
      margin-right: 10upx;
      flex: none;
    }

    .item_text {
      font-size: 28upx;
      color: #333;
      flex: 1;
    }
  }
}
.ai-assistant-btn {
  position: fixed;
  right: 20rpx;
  bottom: 200rpx;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.15);
  background: #fff;
  border-radius: 46rpx;
  width: 200rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #000000;
  font-size: 26rpx;
  z-index: 999;

  .ai-icon {
    width: 60rpx;
    height: 60rpx;
    margin-right: 10rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    image {
      width: 100%;
      height: 100%;
    }
  }
}

.patient-filter-scroll {
  background: #fff;
  padding: 24rpx 0;
  margin-bottom: 20rpx;
  white-space: nowrap;
}

.patient-filter {
  display: inline-flex;
  padding: 0 30rpx;
}

.patient-item {
  padding: 12rpx 30rpx;
  margin-right: 20rpx;
  font-size: 28rpx;
  color: #666;
  background: #f5f5f5;
  border-radius: 32rpx;
  display: inline-block;
}

.patient-item.active {
  color: #fff;
  background: #836aff;
}
</style>
