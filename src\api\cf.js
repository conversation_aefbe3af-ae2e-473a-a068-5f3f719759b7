import http from '../common/request/request.js';
// 处方列表(post请求)
export function getPatientPrescriptionOrderInfo(param = {}) {
  return http({
    url: 'business/proPrescriptionController/getPatientPrescriptionOrderInfo',
    param,
    method: 'post',
  });
}

// 获取选项配置信息
export function getSysOptionConfig(param = {}) {
  return http({
    url: 'basic/sysoptionconfig/getSysOptionConfig',
    param,
    method: 'post',
  });
}

// 保存订单业务
export function saveProPayOrder(param = {}) {
  return http({
    url: 'business/proPayOrderController/saveProPayOrder',
    param,
    method: 'post',
  });
}

// 根据ID查询挂号信息
export function getRegInfoByID(param = {}) {
  return http({
    url: 'business/proregister/getRegInfoByID',
    param,
    method: 'post',
  });
}
//获取药店
export function getDrugStorageStore(param = {}) {
  return http({
    url: 'basic/dicDrugController/getDrugStorageStore',
    param,
    method: 'post',
  });
}

//获取药店药品价格
export function getDrugStoragePrice(param = {}) {
  return http({
    url: 'basic/dicDrugController/getDrugStoragePrice',
    param,
    method: 'post',
  });
}

// 获取药店药品价格(新接口 返回 id_quan)
export function getDrugStoragePriceSameDrug(param = {}) {
  return http({
    url: 'basic/dicDrugController/getDrugStoragePriceSameDrug',
    param,
    method: 'post',
  });
}

// 获取快递费用
export function getLogisticsCost(param = {}) {
  return http({
    url: 'business/DicLogisticsCost/getLogisticsCost',
    param,
    method: 'post',
  });
}

// 查询代煎费用
export function getCureJYF() {
  return http({
    url: 'dictionary/diccure/getCureJYF',
    method: 'post',
  });
}

// 慢病续费 首页入口列表
export function getDocPatientPreListHomePage(param) {
  return http({
    url: 'business/proPrescriptionController/getDocPatientPreListHomePage',
    param,
    method: 'post',
  });
}

// 获取患者处方业务订单信息
export function getPatientPrescriptionBusinessOrderInfo(param) {
  return http({
    url:
      'business/proPrescriptionController/getPatientPrescriptionBusinessOrderInfo',
    param,
    method: 'post',
  });
}

// 新处方缴费
export function saveReceiptAndToPay(param) {
  return http({
    url: 'business/paymentBusiness/saveReceiptAndToPay',
    param,
    method: 'post',
  });
}

// 查询代煎费
export function getSubjectCure(subjectId) {
  return http({
    url: 'dictionary/diccure/getSubjectCure',
    param: { subjectId },
    method: 'post',
  });
}

// 根据价格获取物流金额信息
export function getLogisticsCostList(param) {
  return http({
    url: 'business/DicLogisticsCost/getLogisticsCostList',
    param,
    method: 'post',
  });
}

// 订单待缴费分割缴费
export function savePendingReceiptAndToPay(param) {
  return http({
    url: 'business/paymentBusiness/savePendingReceiptAndToPay',
    param,
    method: 'post',
  });
}

// 获取药店其他信息
export function getSomePriceSameByDrugStorage(param) {
  return http({
    url: 'basic/dicDrugController/getSomePriceSameByDrugStorage',
    param,
    method: 'post',
  });
}
