/*
 * @Descripttion:
 * @version:
 * @Author: zhengyangyang
 * @Date: 2023-05-22 10:54:43
 * @LastEditors: zhengyangyang
 * @LastEditTime: 2023-05-22 17:29:26
 */
import date from "./utils/date";

export const initRouter = () => {
    uni.addInterceptor('navigateTo', {
        //监听跳转
        success(e) {
            init(e)
        }
    })
    uni.addInterceptor('redirectTo', {
        //监听关闭本页面跳转
        success(e) {
            init(e)
        }
    })
    uni.addInterceptor('switchTab', {
        //监听tabBar跳转
        success(e) {
            init(e)
        }
    })
    uni.addInterceptor('navigateBack', {
        //监听返回
        success(e) {
            init(e)
        }
    })
    uni.addInterceptor('reLaunch', {
        //监听返回
        success(e) {
            init(e)
        }
    })
}

export function init(e) {
    // pages/promotion/index 这个页面不需要水印
    if (window.location.href.includes('pages/promotion/index')) {
        return
    }
    const oDiv = document.createElement('view')
    oDiv.className = 'waterMark'
    let content = `
      <view class="waterMark-text"> 
    `
    const o = document.getElementsByClassName('waterMark')
    if (o.length) {
        return
    }
    const info = uni.getStorageSync('wxInfo') || {}
    if (info && info.nickname) {
        for (let i = 0; i < 100; i++) {
            content += `<text class="text">${info.nickname}-${date.getNowDate(new Date())}</text>`
        }
        content += `</view>`
        oDiv.innerHTML = content
        document.body.appendChild(oDiv)
    }
    // if (!uni.getStorageSync('token') && window.location.pathname !== '/pages/login/index') {
    //     uni.reLaunch({
    //         url: '/pages/login/index'
    //     })
    // }
}