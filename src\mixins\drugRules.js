/**
 * 药品加减规则 Mixin
 * 提供统一的药品规则处理方法
 */
import { mapGetters, mapActions } from 'vuex'

export default {
  computed: {
    ...mapGetters(['drugRules'])
  },
  
  methods: {
    ...mapActions(['getDrugRules']),
    
    /**
     * 根据药品ID获取对应的规则
     * @param {string} drugId 药品ID
     * @returns {object|null} 规则对象或null
     */
    getDrugRule(drugId) {
      return this.drugRules.find(rule => rule.drugId === drugId) || null
    },
    
    /**
     * 根据规则计算加购数量
     * @param {string} drugId 药品ID
     * @param {number} currentQuan 当前数量
     * @returns {number} 加购数量
     */
    getAddQuantity(drugId, currentQuan) {
      const rule = this.getDrugRule(drugId)
      if (!rule) {
        return 1 // 默认加1
      }
      
      const currentQuantity = currentQuan || 0
      
      // 首次加购（数量为0时）
      if (currentQuantity == 0) {
        return parseInt(rule.firstAddJudge) || 1
      }
      
      // 如果没有numJudge规则，使用eachAdd
      if (!rule.numJudge) {
        return parseInt(rule.eachAdd) || 1
      }
      
      // 有numJudge规则时，根据当前数量判断
      const judgeNum = parseInt(rule.numJudge)
      if (currentQuantity >= judgeNum) {
        return parseInt(rule.eachOverJudgeAdd) || 1
      } else {
        return parseInt(rule.eachLowJudgeAdd) || 1
      }
    },
    
    /**
     * 根据规则计算减购数量
     * @param {string} drugId 药品ID
     * @param {number} currentQuan 当前数量
     * @returns {number} 减购数量
     */
    getReduceQuantity(drugId, currentQuan) {
      const rule = this.getDrugRule(drugId)
      if (!rule) {
        return 1 // 默认减1
      }
      
      const currentQuantity = currentQuan || 0
      
      // 如果没有numJudge规则，使用eachReduce
      if (!rule.numJudge) {
        return parseInt(rule.eachReduce) || 1
      }
      
      // 有numJudge规则时，根据当前数量判断
      const judgeNum = parseInt(rule.numJudge)
      if (currentQuantity > judgeNum) {
        return parseInt(rule.eachOverJudgeReduce) || 1
      } else {
        return parseInt(rule.eachLowJudgeReduce) || currentQuantity
      }
    },
    
    /**
     * 根据规则获取初始添加数量
     * @param {string} drugId 药品ID
     * @returns {number} 初始数量
     */
    getInitialQuantity(drugId) {
      const rule = this.getDrugRule(drugId)
      if (!rule) {
        return 1 // 默认数量为1
      }
      
      // 首次添加使用 firstAddJudge
      return parseInt(rule.firstAddJudge) || 1
    },
    
    /**
     * 初始化药品规则（在组件创建时调用）
     */
    async initDrugRules() {
      await this.getDrugRules()
    }
  }
}
