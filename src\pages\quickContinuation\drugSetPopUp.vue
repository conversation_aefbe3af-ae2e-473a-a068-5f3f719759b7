<template>
  <!-- 设置用法用量 -->
  <u-popup v-model="setDrugPopVisable" height="88%" mode="bottom" @open="openPopUp">
    <view class="drug-num-content">
      <view class="drug-num-label">请设置用法用量</view>
      <view class="drug-name"> {{ patent.drugName }}</view>
      <view class="drug-list">
        <view class="drug-item">
          <view class="drug-item-label">规格</view>
          <view class="drug-item-value"> {{ patent.gg }}</view>
        </view>
        <view class="drug-item" style="display: flex; flex-direction: column; align-items: flex-start" v-if="patent.memo">
          <view class="drug-item-label mb-[20rpx]">药品主治</view>
          <view class="drug-item-value">{{ patent.memo }}</view>
        </view>
        <view class="drug-item">
          <view class="drug-item-label">用药天数</view>
          <view class="drug-item-value flex">
            <u-number-box class="flex-auto tnNumber" v-model="patent.days"></u-number-box>
            <view class="pl-[10rpx]">天</view>
          </view>
        </view>
        <view class="drug-item">
          <view class="drug-item-label">用药频次 </view>
          <view class="drug-item-value flex items-center">
            <u-input :border="false" style="width: 100%" @click="openPCPicker" type="select"
              v-model="patent.ddufName"></u-input>
            <image src="/static/image/cf/plus.png" class="w-[30rpx] h-[30rpx] ml-[10rpx]"></image>
            <u-picker v-model="patent.ddufName" v-model:open="pcPickerOpen" :data="pcpickerData" @confirm="pcConfirm"
              label-key="ddufName" value-key="ddufName" />
          </view>
        </view>
        <view class="drug-item">
          <view class="drug-item-label">每次剂量</view>
          <view class="drug-item-value flex items-center">
            <u-number-box class="flex-auto" v-model="patent.eachQuan"></u-number-box>
            <view class="pl-[10rpx]">{{ patent.jldw }}</view>
          </view>
        </view>
        <view class="drug-item">
          <view class="drug-item-label">发药数量 </view>
          <view class="drug-item-value flex items-center">
            <u-number-box class="flex-auto" v-model="patent.quan"></u-number-box>
            <view class="pl-[10rpx]">{{ patent.unit }}</view>
          </view>
        </view>
        <view class="drug-item">
          <view class="drug-item-label">用药方法 </view>
          <view class="drug-item-value flex items-center">
            <u-input :border="false" style="width: 100%" @click="openFnPicker" type="select"
              v-model="patent.dduName"></u-input>
            <image src="/static/image/cf/plus.png" class="w-[30rpx] h-[30rpx] ml-[10rpx]"></image>
            <u-picker v-model="patent.dduName" v-model:open="fnPickerOpen" :data="fnpickerData" label-key="dduName"
              value-key="dduName" @confirm="fnConfirm" />
          </view>
        </view>
      </view>
      <view class="flex justify-end">
        <view class="btn-style mr-[32rpx] h-[60rpx] w-[160rpx]" hover-class="none" @click="btnCancleOperate">
          <span>取消</span>
        </view>
        <view class="btn-style btn-add h-[60rpx] w-[160rpx]" @click="btnSumbitOperate">
          <span>确定</span>
        </view>
      </view>
    </view>
  </u-popup>
</template>
<script>
import { dicdrugusage, dicdrugfrequency } from "@/api/user.js";
export  default {
  props:{
    value: false,
    editOrAddDrug: { //新增或者编辑
      type: String,
      default: "add",
    },
    cfIndex: {//处方索引
      type: Number,
      default: 0
    },
    drugIndex: {//药的索引
      type: Number,
      default: 0
    },
    patent: { //当前详情
      type: Object,
      default: () => {
        return {
          drugId: "",
          drugName: "", //药名
          gg: "", //规则
          memo: "", //主治
          days: "", //天数
          ddufName: "", //频次
          ddufCode: "", //频次
          eachQuan: "", //每次剂量
          quan: "", //发药数量this.patent
          dduName: "", //用药方法
          dduCode: "", //用药方法
        };
      },
    },
  },
  data(){
    return {
      pcpickerData:'',
      fnpickerData: '',
      pcPickerOpen: false,
      fnPickerOpen: false,
    }
  },
  computed:{
    setDrugPopVisable:{
      get(){
        return this.value
      },
      set(newVal){
        this.$emit('input', newVal)
      }
    }
  },
  methods:{
    openPopUp(){
      this.getDicdrugusage();
      this.getDicdrugfrequency();
    },
    btnSumbitOperate(){
      if (!this.patent.days) {
        uni.showToast({
          title: '请填写用药天数',
          icon:'none'
        })
        return;
      }
      if (!this.patent.ddufName) {
        uni.showToast({
          title: '请选择用药频次',
          icon:'none'
        })
        return;
      }
      if (!this.patent.eachQuan) {
        uni.showToast({
          title: '请填写每次剂量',
          icon:'none'
        })
        return;
      }
      if (!this.patent.quan) {
        uni.showToast({
          title: '请填写发药数量',
          icon:'none'
        })
        return;
      }
      console.log("34", this.editOrAddDrug, this.patent, this.cfIndex, this.drugIndex);
      if (this.editOrAddDrug == "add") { //添加药
        this.$emit("closeDrugPopUp");
      } else {//编辑药
      }
      this.$emit("input", false);
    },
    btnCancleOperate(){
      this.$emit("input", false);
      this.$emit("closeDrugPopUp");
    },
    openPCPicker(){
      this.pcPickerOpen = true;
    },
    getDicdrugfrequency(){
      dicdrugfrequency().then((res) => {
        let data = res.data;
        this.pcpickerData = res.data;
      });
    },
    pcConfirm(){
      let { ddufName, ddufCode } = this.pcpickerData.find((cur) => cur.ddufName == item)
      this.patent.ddufName = ddufName
      this.patent.ddufCode = ddufCode
    },
    openFnPicker(){
      this.fnPickerOpen = true;
    },
    getDicdrugusage(){
      dicdrugusage().then((res) => {
        let data = res.data;
        this.fnpickerData = res.data;
      });
    },
    fnConfirm(){
      let { dduName, dduCode } = this.fnpickerData.find((cur) => cur.dduName == item);
      this.patent.dduName = dduName
      this.patent.dduCode = dduCode
    }
  }
}
</script>
<style scoped lang="scss">
.drug-num-content {
  padding: 0 54rpx;

  .drug-num-label {
    font-weight: 800;
    font-size: 36rpx;
    color: #333333;
    line-height: 50rpx;
    text-align: center;
    margin: 32rpx 0;
  }

  .drug-name {
    font-weight: 500;
    font-size: 32rpx;
    color: #14a0e6;
    line-height: 32rpx;
    text-align: left;
    font-style: normal;
  }

  .drug-list {
    .drug-item {
      display: flex;
      align-items: center;
      margin: 30rpx 0;

      .drug-item-label {
        font-weight: 800;
        font-size: 32rpx;
        color: #333333;
        padding-right: 32rpx;
      }

      .drug-item-value {
        font-weight: 400;
        font-size: 28rpx;
        color: #333333;
        flex: 1;
        align-items: center;

        .flex-auto {
          display: flex;
        }
      }
    }
  }
}
</style>
