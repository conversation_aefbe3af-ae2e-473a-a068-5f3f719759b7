{"name": "cloudHosUni", "version": "1.0.0", "description": "", "main": "main.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build:h5": "cross-env VUE_APP_ENV=production UNI_PLATFORM=h5 vue-cli-service uni-build"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@dcloudio/uni-ui": "^1.2.5", "cross-env": "^7.0.2", "js-audio-recorder": "^1.0.6", "jweixin-module": "^1.6.0", "vue-jsonp": "^0.1.8"}}