// indexedDB.js，浏览器本地数据库操作
var db;
var n = 1;
export default {
  // indexedDB兼容
  indexedDB:
    window.indexedDB ||
    window.webkitindexedDB ||
    window.msIndexedDB ||
    mozIndexedDB,
  // 打开数据库
  // 新对象储存空间newStore参数：newStore.name、newStore.key
  // 新增对象存储空间要更改数据库版本
  openDB: function(dbname, version) {
    n--;
    var version = version || 1;
    var request = this.indexedDB.open(dbname, version);
    request.onerror = function(event) {};
    request.onsuccess = function(event) {
      db = event.target.result;
    };
    // onupgradeneeded，调用创建新的储存空间
    request.onupgradeneeded = function(event) {
      db = event.target.result;
      var objectStore;
      if (!db.objectStoreNames.contains('chatTable')) {
        objectStore = db.createObjectStore('chatTable', {
          keyPath: 'id',
        });
        objectStore.createIndex('docName', 'docName', {
          unique: false,
        });
      }
    };
    if (n < 0) return;
    setTimeout(() => {
      if (!db) this.openDB(dbname, version);
    }, 2000);
  },
  // 清空聊天记录
  clearDB() {
    db.transaction(['chatTable'], 'readwrite')
      .objectStore('chatTable')
      .clear();
  },
  // 删除数据库
  deleteDB: function(dbname, callback) {
    var deleteQuest = this.indexedDB.deleteDatabase(dbname);
    deleteQuest.onerror = function() {};
    deleteQuest.onsuccess = function() {
      if (callback && typeof callback === 'function') {
        callback();
      }
    };
  },
  // 关闭数据库
  closeDB: function(dbname) {
    dbname.close();
  },
  // 删除指定聊天记录
  del(key) {
    let res = db
      .transaction(['chatTable'], 'readwrite')
      .objectStore('chatTable')
      .delete(key);

    return new Promise((resolve, reject) => {
      res.onsuccess = () => {
        resolve();
      };
      res.onerror = () => {
        reject();
      };
    });
  },
  // 增加
  putData: function(obj) {
    let objData = {
      id: obj.id || '',
      userId: uni.getStorageSync('userId'),
      chatRecordList: obj.chatRecordList || [],
      dataVal: obj.dataVal || {},
      latInfo: obj.latInfo || '',
      time: obj.time || new Date().getTime(),
      timeDiff: obj.timeDiff || '',
      docId: obj.docId || '',
      docImg: obj.docImg || '',
      docName: obj.docName || '',
    };
    var request = db
      .transaction(['chatTable'], 'readwrite')
      .objectStore('chatTable')
      .add(objData);
    return new Promise((res, rej) => {
      request.onsuccess = function(event) {
        res('数据写入成功');
      };

      request.onerror = function(event) {
        rej('数据写入失败或该数据已存在');
      };
    });
  },
  // 根据ID获取聊天记录
  reaData: function(id) {
    var transaction = db.transaction(['chatTable']);
    var objectStore = transaction.objectStore('chatTable');
    var request = objectStore.get(id);
    return new Promise((res, rej) => {
      request.onerror = function(event) {
        rej('事务失败');
      };
      request.onsuccess = function(event) {
        if (request.result) {
          if (!request.result.userId) {
            request.result.userId = uni.getStorageSync('userId');
          }
          res(request.result);
        } else {
          res([]);
        }
      };
    });
  },
  //遍历
  readAll: function() {
    var objectStore = db.transaction('chatTable').objectStore('chatTable');
    var customers = [];
    let userId = uni.getStorageSync('userId');
    return new Promise((resolve, reject) => {
      objectStore.openCursor().onsuccess = function(event) {
        var cursor = event.target.result;
        if (cursor) {
          // 如果不存在userId 则赋值
          if (!cursor.value.userId) {
            cursor.value.userId = userId;
          }
          // 如果是当前用户的userId 则push
          if (cursor.value.userId == userId) {
            customers.push(cursor.value);
          }
          // 继续循环
          cursor.continue();
        } else {
          resolve(customers);
        }
      };
    });
  },
  //遍历查询
  readAllSearch: function() {
    var objectStore = db.transaction('chatTable').objectStore('chatTable');
    var customers = [];
    return new Promise((res, rej) => {
      objectStore.openCursor().onsuccess = function(event) {
        var cursor = event.target.result;
        if (cursor) {
          customers.push(cursor.value);
          cursor.continue();
        } else {
          res(customers);
        }
      };
    });
  },
  //更新聊天记录
  updateChatList: function(objData) {
    let _that = this;
    return new Promise((res, rej) => {
      let id = objData.id;
      let data = objData.data;
      var objectStore = db
        .transaction(['chatTable'], 'readwrite')
        .objectStore('chatTable');
      var request = objectStore.get(id);
      request.onerror = function(event) {
        rej('查找失败');
        return false;
      };
      request.onsuccess = function(event) {
        var data = event.target.result;
        if (data) {
          if (!data.chatRecordList) {
            data.chatRecordList = [];
            data.chatRecordList.push(objData.data);
          } else {
            data.chatRecordList.push(objData.data);
          }
          data.time = objData.data.time;
          data.userId = objData.data.userId;
          data.latInfo = objData.data.content;
          var requestUpdate = objectStore.put(data);
          requestUpdate.onerror = function(event) {
            rej('数据更失败');
          };
          requestUpdate.onsuccess = function(event) {
            res('数据已更新');
          };
        } else {
          let obj = {
            id: objData.id,
            chatRecordList: [objData.data],
          };
          // 调用更新
          _that
            .putData(obj)
            .then(res)
            .catch(rej);
        }
      };
    });
  },
  //更新单个聊天记录
  updateChatDataValList: function(objData) {
    let id = objData.id;
    let data = objData.data;
    return new Promise((res, rej) => {
      var objectStore = db
        .transaction(['chatTable'], 'readwrite')
        .objectStore('chatTable');
      var request = objectStore.get(id);
      request.onerror = function(event) {
        rej('查找失败');
      };
      request.onsuccess = function(event) {
        var data = event.target.result;
        data.dataVal = objData.data.dataVal;
        data.docName = objData.data.dataVal.docName;
        data.docImg = objData.data.dataVal.docImg;
        data.userId = objData.data.userId;
        var requestUpdate = objectStore.put(data);
        requestUpdate.onerror = function(event) {
          rej('数据更失败');
        };
        requestUpdate.onsuccess = function(event) {
          res('数据已更新');
        };
      };
    });
  },
  //更新聊天记录状态
  updateChatStatus: function(obj) {
    let id = obj.id;
    let message = obj.message;

    return new Promise((res, rej) => {
      var objectStore = db
        .transaction(['chatTable'], 'readwrite')
        .objectStore('chatTable');
      var request = objectStore.get(id);
      request.onerror = function(event) {
        rej('查找失败');
      };
      request.onsuccess = function(event) {
        var data = event.target.result;
        data.chatRecordList.map((el, elIndex) => {
          if (el.id == message.id) {
            if (el.status != 'recall') {
              el.status = message.status;
            }
          }
        });
        var requestUpdate = objectStore.put(data);
        requestUpdate.onerror = function(event) {
          rej('数据更失败');
        };
        requestUpdate.onsuccess = function(event) {
          res(data);
        };
      };
    });
  },
  // 根据 id 查询并修改 视频邀请失效状态
  upVideoInvitaionStatus(obj) {
    // 聊天记录id 环信消息id
    let { id, msgId } = obj;
    // 数据库
    let objectStore = db
      .transaction(['chatTable'], 'readwrite')
      .objectStore('chatTable');
    // promise
    return new Promise((resolve, reject) => {
      let res = objectStore.get(id);
      // 查询失败
      res.onerror = reject;
      // 查询成功
      res.onsuccess = (e) => {
        // 查询结果
        let data = e.target.result;
        let arr = data.chatRecordList;
        arr.forEach((v) => {
          // 找到要更新的数据
          if (v.id == msgId) {
            // 设置为false 失效
            v.ext.isNext = false;
          }
        });
        var upData = objectStore.put(data);
        upData.onerror = reject;
        // 释放
        upData.onsuccess = resolve;
      };
    });
  },
  //更新全部聊天记录
  updateCardStatus: function(obj) {
    let id = obj.id;
    let chatList = obj.chatList;
    var objectStore = db
      .transaction(['chatTable'], 'readwrite')
      .objectStore('chatTable');
    var request = objectStore.get(id);
    return new Promise((res, rej) => {
      request.onerror = function(event) {
        rej('查找失败');
      };
      request.onsuccess = function(event) {
        var data = event.target.result;
        // 如果存在值
        if (data) {
          data.chatRecordList = chatList;
        } else {
          data = {
            id,
            chatRecordList: chatList,
          };
        }
        // 更新数据
        var requestUpdate = objectStore.put(data);
        requestUpdate.onerror = function(event) {
          rej('数据更失败');
        };
        requestUpdate.onsuccess = function(event) {
          res('数据已更新');
        };
      };
    });
  },
  //通过name获取
  searchName(name) {
    var transaction = db.transaction(['chatTable']);
    var objectStore = transaction.objectStore('chatTable');
    var indexs = objectStore.index('docName');
    var request = indexs.openCursor(IDBKeyRange.lowerBound(name));
    let arr = [];
    return new Promise((resolve, reject) => {
      request.onsuccess = function(e) {
        var cursor = e.target.result;
        if (cursor) {
          if (cursor.value.docName.indexOf(name) != -1) {
            arr.push(cursor.value);
          }
          cursor.continue();
        } else {
          resolve(arr);
        }
      };
    });
  },
};
