<template>
  <!-- 单个 -->
  <view class="card" @click="click(item)">
    <!-- 编号 -->
    <view class="item_title">
      <view class="item_title-i">
        <img src="/static/doc/no.png" alt="">
        <text>挂号编号：{{ item.regCode }}</text>
      </view>
      <text
        class="status"
        :class="{
          wait: item.status == 1 || item.status == 2 || item.status == 4,
          act: item.status == 5,
        }"
        :style="getStyle(item.statusName)"
        >{{ item.statusName }}</text
      >
    </view>

    <!-- 相关信息 -->
    <view class="item_cont">
      <view class="left_info" style="justify-content: left">
        <text style="font-size: 14px;">{{ item.platformDocName || item.docName }}</text>
        <text style="font-size: 12px;font-weight: 400;color: rgba(102, 102, 102, 1);margin-left: 9px;margin-top: 5px">{{ item.deptName }} </text>
      </view>
      <view class="left_info"> 医院：{{ item.hosName }} </view>
      <view v-if="item.receiveType&&!item.receiveType.includes('快速续方')" class="left_info">
        号别：{{ item.dntName }}
        <!-- 右箭头 -->
        <uni-icons type="arrowright" color="rgba(166, 166, 166, 1)" size="16"></uni-icons>
      </view>
      <view class="left_info"> 问诊类型：{{ item.receiveType }} </view>
      <view class="left_info">
        <text class="cus">就诊人：{{ item.patientName }}</text> </view>
    </view>

    <!-- 时间 -->
    <view class="item_time"> 问诊时间：{{ item.receiveTime }} </view>
  </view>
</template>

<script>
export default {
  props: {
    item: {
      type: Object,
      default: () => {},
    },
  },
  methods: {
    click(item) {
      this.$emit('click', item);
    },
    getStyle(name){
      if(name==='接诊中'){
        return {
          background: 'rgba(232, 250, 244, 1)',
          color: 'rgba(44, 199, 147, 1)',
        }
      }
      if(name==='已结束'){
        return {
          background: 'rgba(253, 236, 237, 1)',
          color: 'rgba(211, 65, 63, 1)',
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.cus{
  display: flex;
  border-radius: 2px;
  background: rgba(247, 248, 252, 1);
  border: 0.5px solid rgba(204, 176, 255, 1);
  width: auto;
  padding: 0 10rpx;
  font-size: 10px !important;
}
.item_title-i{
  display: flex;
  align-items: center;
  /** 文本1 */
  font-size: 12px;
  font-weight: 400;
  letter-spacing: 0px;
  line-height: 18px;
  color: rgba(51, 51, 51, 1);
  img{
    margin-right: 7px;
  }
}
.card {
  padding: 0 32rpx;
  border-radius: 8rpx;
  background: #fff;
  margin-bottom: 24rpx;
  border-radius: 7.62px;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px 1px 4px  rgba(121, 72, 234, 0.15);
  &:last-child {
    margin-bottom: 0;
  }

  .item_title {
    height: 88rpx;
    @include flex(lr);
    font-size: 28rpx;
    color: #333;
    font-weight: bold;
    border-bottom: 1px solid #ebebeb;

    .status {
      font-weight: normal;
      color: #333;
      width: 54px;
      height: 22px;
      opacity: 1;
      border-radius: 212px;
      background: rgba(232, 250, 244, 1);
      font-size: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      color: rgba(44, 199, 147, 1);
      &.wait {
        color: #ff3b30;
      }

      &.act {
        @include font_theme;
      }
    }
  }

  .item_cont {
    padding: 10rpx 0;
    border-bottom: 1px solid #ebebeb;

    .left_info {
      @include flex(lr);
      color: #666666;
      font-size: 12px;
      font-weight: 400;
      line-height: 24px;
      vertical-align: top;
      color: rgba(153, 153, 153, 1);
      &:first-child {
        font-weight: bold;
        color: #333;
      }
    }
  }

  .item_time {
    height: 88rpx;
    @include flex(lr);
    font-size: 28rpx;
    color: #333;
  }
}
</style>
