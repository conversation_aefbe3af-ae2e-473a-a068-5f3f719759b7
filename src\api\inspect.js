import http from "../common/request/request.js";

//从卡片进去支付
export function getProPacsInfoByID(param = {}) {
  return http({
    url: "business/proPacsInfoController/getProPacsInfoByID",
    param,
    method: "post",
  });
}

// 根据openid获取用户是否存在
export function getOpenIdUserInfo(param = {}) {
  return http({
    url: "basic/enterpriseWeChatRegister/judgeOpenIdIfExist",
    param,
    method: "post",
  });
}

/*
    检查相关
*/

// 根据ID查询检查单支付信息
export function getProPacsPayInfoByID(ppiId) {
  return http({
    url: "basic/proPacsInfoController/getProPacsPayInfoByID",
    param: { ppiId },
    method: "post",
  });
}

// 判断检查单预约状态
export function checkPacsAppointStatus(ppiId) {
  return http({
    url: "basic/proPacsInfoController/checkPacsAppointStatus",
    param: { ppiId },
    method: "post",
  });
}

// 获取预约检查服务订单支付信息
export function getAppointPayInfo(param) {
  return http({
    url: "basic/proPacsInfoController/getAppointPayInfo",
    param,
    method: "post",
  });
}

// 根据ID查询检查单信息
export function getProPacsAllInfoByID(ppiId) {
  return http({
    url: "basic/proPacsInfoController/getProPacsAllInfoByID",
    param: { ppiId },
    method: "post",
  });
}

// 获取检查预约时段
export function checkPacsAppointNum(param) {
  return http({
    url: "basic/proPacsInfoController/checkAppointNum",
    param,
    method: "post",
  });
}

// 获取检查预约机构
export function getPacsListToOrgan(param) {
  console.log(param);
  return http({
    url: "basic/proPacsInfoController/getProPayPacsListToOrgan",
    param,
    method: "post",
  });
}

// 查询检查机构项目价格
export function getPaceItemPrice(param) {
  return http({
    url: "basic/pacsLinkItemController/getPaceItemPrice",
    param,
    method: "post",
  });
}

// 查询支付状态
export function queryPacsRegisterPayStatus(ppiId) {
  return http({
    url: "basic/proPacsInfoController/queryRegisterPayStatus",
    param: {
      ppiId,
    },
    method: "post",
  });
}

// 我的检查单排队
export function myPacsQueueNum(ppiId) {
  return http({
    url: "basic/proPacsInfoController/myPacsQueueNum",
    param: {
      ppiId,
    },
    method: "post",
  });
}

/*
    检验相关
*/

// 我的检验单排队
export function myLisQueueNum(pliId) {
  return http({
    url: "basic/proLisInfoController/myLisQueueNum",
    param: {
      pliId,
    },
    method: "post",
  });
}

// 判断检验单预约状态
export function checkLisAppointStatus(pliId) {
  return http({
    url: "basic/proLisInfoController/checkLisAppointStatus",
    param: { pliId },
    method: "post",
  });
}

// 根据ID查询检验单支付信息
export function getProLisPayInfoByID(pliId) {
  return http({
    url: "basic/proLisInfoController/getProLisPayInfoByID",
    param: { pliId },
    method: "post",
  });
}

// 获取预约检验服务订单支付信息
export function getLisAppointPayInfo(param) {
  return http({
    url: "basic/proLisInfoController/getAppointPayInfo",
    param,
    method: "post",
  });
}

// 根据ID查询检验单信息
export function getProLisInfoByID(pliId) {
  return http({
    url: "basic/proLisInfoController/getProLisInfoByID",
    param: { pliId },
    method: "post",
  });
}

// 获取检验预约时段
export function checkLisAppointNum(param) {
  return http({
    url: "basic/proLisInfoController/checkAppointNum",
    param,
    method: "post",
  });
}

// 获取检验预约机构
export function getLisListToOrgan(param) {
  return http({
    url: "basic/proLisInfoController/getProPayPacsListToOrgan",
    param,
    method: "post",
  });
}

// 根据id 查询检验机构详情
export function queryDicLisOrganizationbyId(dloId) {
  return http({
    url: "basic/dicLisOrganization/queryDicLisOrganizationbyId",
    param: {
      isDelete: 1,
      dloId,
    },
    method: "post",
  });
}

// 查询检验机构项目价格
export function getLisItemPrice(param) {
  return http({
    url: "basic/lisItemInfoController/getLisItemPrice",
    param,
    method: "post",
  });
}

// 查询支付状态
export function queryLisRegisterPayStatus(pliId) {
  return http({
    url: "basic/proLisInfoController/queryRegisterPayStatus",
    param: {
      pliId,
    },
    method: "post",
  });
}

/*
  订单相关
*/

// 我的检验单
export function myLisList(param) {
  return http({
    url: "basic/proLisInfoController/myLisList",
    param,
    method: "post",
  });
}

// 我的检查单
export function myPacsList(param) {
  return http({
    url: "basic/proPacsInfoController/myPacsList",
    param,
    method: "post",
  });
}

// 检验检查订单详情
export function pacsLisOrderDetailNew(param) {
  return http({
    url: "order/payOrderPatient/pacsLisOrderDetailNew",
    param,
    method: "post",
  });
}
