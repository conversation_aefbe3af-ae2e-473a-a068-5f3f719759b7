@import 'uview-ui/theme.scss';
/* 行为相关颜色 */
$uni-color-primary: #007aff;
$uni-color-success: #4cd964;
$uni-color-warning: #f0ad4e;
$uni-color-error: #dd524d;

/* 文字基本颜色 */
$uni-text-color: #333; //基本色
$uni-text-color-inverse: #fff; //反色
$uni-text-color-grey: #999; //辅助灰色，如加载更多的提示信息
$uni-text-color-placeholder: #808080;
$uni-text-color-disable: #c0c0c0;

/* 背景颜色 */
$uni-bg-color: #ffffff;
$uni-bg-color-grey: #f8f8f8;
$uni-bg-color-hover: #f1f1f1; //点击状态颜色
$uni-bg-color-mask: rgba(0, 0, 0, 0.4); //遮罩颜色

/* 边框颜色 */
$uni-border-color: #e5e5e5;

/* 尺寸变量 */

/* 文字尺寸 */
$uni-font-size-sm: 24rpx;
$uni-font-size-base: 28rpx;
$uni-font-size-lg: 32rpx;

/* 图片尺寸 */
$uni-img-size-sm: 40rpx;
$uni-img-size-base: 52rpx;
$uni-img-size-lg: 80rpx;

/* Border Radius */
$uni-border-radius-sm: 4rpx;
$uni-border-radius-base: 6rpx;
$uni-border-radius-lg: 12rpx;
$uni-border-radius-circle: 50%;

/* 水平间距 */
$uni-spacing-row-sm: 10px;
$uni-spacing-row-base: 20rpx;
$uni-spacing-row-lg: 30rpx;

/* 垂直间距 */
$uni-spacing-col-sm: 8rpx;
$uni-spacing-col-base: 16rpx;
$uni-spacing-col-lg: 24rpx;

/* 透明度 */
$uni-opacity-disabled: 0.3; // 组件禁用态的透明度

/* 文章场景相关 */
$uni-color-title: #2c405a; // 文章标题颜色
$uni-font-size-title: 40rpx;
$uni-color-subtitle: #555555; // 二级标题颜色
$uni-font-size-subtitle: 36rpx;
$uni-color-paragraph: #3f536e; // 文章段落颜色
$uni-font-size-paragraph: 30rpx;

/*
	字体颜色相关
*/

// 一级标题颜色
$k-title: #333;

// 二级标题颜色
$k-sub-title: #666;

// 辅助标题颜色
$k-info-title: #999;

// 警告，价格醒目颜色
$k-price-coloe: #ff5050;

/*
	背景色 分割线色
*/

// 分割线
$k-hr-color: #ebebeb;

// 页面底层背景
$k-page-bg-color: #f5f5f5;

// 聊天界面背景色
$k-chat-bg-color: #fafafa;

// 聊天底部输入
$k-chat-bottom-color: #f0f0f0;

/*
	主题色
*/

// 医小鹿
$k-theme-color:  rgba(131, 106, 255, 1);

// 诺信
$nx-theme: #107dff;

// 测试
$test: #2db99d;

// 强调色 红色
$k-red-color: #ff5050;

// 黄色
$k-yellow-color: #ffb541;

// 绿色
$k-green-color: #23b067;

// 背景色
@mixin bg_theme() {
  background-color: $k-theme-color;

  [data-theme='nx'] & {
    background-color: $nx-theme;
  }

  [data-theme='test'] & {
    background-color: $test;
  }
}

// 字体颜色
@mixin font_theme() {
  color: $k-theme-color;

  [data-theme='nx'] & {
    color: $nx-theme;
  }

  [data-theme='test'] & {
    color: $test;
  }
}

// 边框颜色
@mixin border_theme($px: 1px, $bottom: none, $style: solid) {
  // 默认
  @if $bottom == bottom {
    border-bottom: $px $style $k-theme-color;
  }
  @if $bottom == none {
    border: $px $style $k-theme-color;
  }

  // 诺信
  [data-theme='nx'] & {
    @if $bottom == bottom {
      border-bottom: $px $style $nx-theme;
    }
    @if $bottom == none {
      border: $px $style $nx-theme;
    }
  }

  // 测试
  [data-theme='test'] & {
    @if $bottom == bottom {
      border-bottom: $px $style $test;
    }
    @if $bottom == none {
      border: $px $style $test;
    }
  }
}

/*
		渐变可调角度
*/
@mixin linear($deg: 90deg) {
  background: linear-gradient($deg, $k-theme-color 0%, #4699fc 100%);

  [data-theme='nx'] & {
    background: linear-gradient($deg, $nx-theme 0%, #4699fc 100%);
  }

  [data-theme='test'] & {
    background: linear-gradient($deg, $test 0%, #737373 100%);
  }
}

/*
	定义弹性布局
 */
@mixin flex($s: center) {
  display: flex;
  align-items: center;
  // 默认居中
  @if $s == center {
    justify-content: center;
  }
  // 左右
  @if $s == lr {
    justify-content: space-between;
  }
  // 左侧
  @if $s == left {
    justify-content: flex-start;
  }
  // 右侧
  @if $s == right {
    justify-content: flex-end;
  }
}

/*
	单行文本溢出隐藏
*/
@mixin hide() {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 单行 多行文本超出隐藏
@mixin text($n: 1) {
  @if $n == 1 {
    text-overflow: ellipsis;
    white-space: nowrap;
  } @else {
    display: -webkit-box;
    -webkit-line-clamp: $n;
    -webkit-box-orient: vertical;
  }
  overflow: hidden;
}
