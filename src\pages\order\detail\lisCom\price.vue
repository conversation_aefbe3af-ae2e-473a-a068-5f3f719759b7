<template>
  <!-- 金额摘要 -->
  <view class="price_info" @click="click">
    <!-- 顶部 -->
    <view class="title">
      <text>{{ info.itemName }}</text>
      <image src="/static/inspection/lis.png" class="icon" />
    </view>

    <!-- 文案 -->
    <view class="info_text">
      <view class="item_info"> 医院：{{ info.hosName }} </view>

      <view class="item_info">
        就诊人：{{ info.remark && info.remark.patientName }}
        <uni-icons type="arrowright" color="#666" />
      </view>

      <view class="item_info"> 医生姓名：{{ info.docName }} </view>

      <view class="item_info"> 医生诊断：{{ info.diagName }} </view>
    </view>

    <!-- 金额 -->
    <view class="info_price">
      <!-- 单个 -->
      <view class="item_info">
        <text class="sub">合计金额</text>
        <text>￥{{ info.totalPay }}</text>
      </view>

      <view class="item_info">
        <text class="sub">支付方式</text>
        <text>在线支付</text>
      </view>

      <view class="item_info">
        <text class="sub">医保类型</text>
        <text>自费</text>
      </view>

      <view class="item_info">
        <text class="bold">实付款</text>
        <text class="num">￥{{ info.orderMoney }}</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    info: {
      type: Object,
      default: () => {},
    },
  },
  methods: {
    click() {
      this.$emit('click', this.info);
    },
  },
};
</script>

<style lang="scss" scoped>
.price_info {
  background: #fff;
  border-radius: 32rpx 32rpx 0px 0px;
  padding: 0 32rpx 32rpx;

  .title {
    @include flex(lr);
    height: 88rpx;
    font-size: 28rpx;
    font-weight: bold;
    border-bottom: 1px solid #ebebeb;

    .icon {
      width: 64rpx;
      height: 64rpx;
    }
  }

  .info_text {
    padding: 10rpx 0;
    border-bottom: 1px solid #ebebeb;

    .item_info {
      @include flex(lr);
      font-size: 28rpx;
      color: #333;
      height: 60rpx;

      .uni-icons {
        position: relative;
        top: 25rpx;
      }
    }
  }

  .info_price {
    padding-top: 10rpx;

    .item_info {
      @include flex(lr);
      font-size: 28rpx;
      color: #333;
      height: 60rpx;

      .sub {
        color: #666;
      }

      .bold {
        font-weight: bold;
      }

      .num {
        color: #ff5050;
        font-weight: bold;
      }
    }
  }
}
</style>
