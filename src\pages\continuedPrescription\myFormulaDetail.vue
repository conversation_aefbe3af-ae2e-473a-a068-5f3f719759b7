<template>
  <!-- 慢病续方 -->
  <view class="prescriptionDetail">
    <view class="cfDetailView" v-if="prescriptionDetail">
      <!-- 头部 -->
      <view class="pre_head" v-if="prescriptionDetail">
        <view class="head_item">
          <text class="bold"
            >医生名称：{{ prescriptionDetail.proBusinessInfo.docName }}</text
          >
          <text class="bold"
            >医生科室：{{ prescriptionDetail.proBusinessInfo.deptName }}</text
          >
        </view>

        <view class="head_item">
          <text class="bold"
            >医院名称：{{ prescriptionDetail.proBusinessInfo.hosName }}</text
          >
        </view>

        <view class="head_item">
          <text class="bold"
            >药师姓名：{{
              prescriptionDetail.proBusinessInfo.checkUserName
            }}</text
          >
        </view>

        <view class="head_item">
          <text>处方诊断：{{ prescriptionDetail.proBusinessInfo.diags }}</text>
        </view>

        <view class="head_item">
          <text
            >开方时间：{{ prescriptionDetail.proBusinessInfo.addTime }}</text
          >
        </view>

        <view class="head_item">
          <text
            >处方编号：{{
              prescriptionDetail.proBusinessInfo.businessCode
            }}</text
          >
        </view>
      </view>

      <!-- 药品 -->
      <DRUGDETAIL
        :list="prescriptionDetail.prescriptions"
        :preferentialAmount="
          prescriptionDetail.proBusinessInfo.preferentialAmount || 0
        "
        :totalMoney="prescriptionDetail.proBusinessInfo.totalMoney || 0"
        :orderMoney="prescriptionDetail.proBusinessInfo.orderMoney || 0"
      />
    </view>

    <FOOTER @click="sendContinue" v-if="!look">立即续方</FOOTER>
  </view>
</template>

<script>
import { Toast } from '@/common/js/pay.js';
import { getDocPatientBlackList } from '@/api/register';
import { getPatientPrescriptionBusinessOrderInfo as getPatientPrescriptionOrderInfo } from '@/api/cf';
import DRUGDETAIL from './com/drugDetail.vue';
import FOOTER from '@/components/footer_button/button.vue';

import * as myJsTools from '@/common/js/myJsTools.js';
export default {
  components: {
    DRUGDETAIL,
    FOOTER,
  },
  data() {
    return {
      businessId: '',
      total: '',
      prescriptionDetail: '',
      selectVal: [],
      checked: true,
      hintList: [],
      userId: uni.getStorageSync('userId'),
      docId: '',
      id: '',
      look: '',
    };
  },
  onLoad(option) {
    this.businessId = option.businessId;
    this.look = option.look;
    this.docId = option.docId;
    this.id = option.patientId;
    this.getDetail();
  },

  methods: {
    addNum(a, b) {
      return (a * 100 + b * 100) / 100;
    },

    // 获取处方详情
    async getDetail() {
      let res = await getPatientPrescriptionOrderInfo({
        businessId: this.businessId,
      });
      if (res.code != 20000 || !res.data.length) return;
      let obj = res.data[0];
      let dias = [];

      obj.diags.forEach((v) => {
        dias.push(v.diagName);
      });
      obj.proBusinessInfo.diags = dias.toString();
      this.total = obj.proBusinessInfo.cost;
      this.prescriptionDetail = obj;
    },

    async sendContinue() {
      await getDocPatientBlackList({
        patientId: this.id,
        docId: this.docId,
      });
      let _this = this;
      let id = this.$im.conn.getUniqueId();
      let msg = new this.$im.message('txt', id);
      let strText = '患者给你发了一张慢病续方，请点击查看';
      msg.set({
        msg: strText,
        from: _this.userId,
        to: _this.docId,
        roomType: false,
        ext: {
          type: 'mbxf',
          businessId: _this.businessId,
          diagName: _this.prescriptionDetail.diags[0].diagName,
          hzStstus: '0',
          patientId: _this.id,
        },
        success(id, serverMsgId) {
          //
          let msg = {
            type: 'send',
            from: _this.userId,
            ext: {
              type: 'mbxf',
              businessId: _this.businessId,
              diagName: _this.prescriptionDetail.diags[0].diagName,
              hzStstus: '0',
              patientId: _this.id,
            },
            time: new Date().getTime(),
            to: _this.docId,
            messType: 'text',
            content: strText,
            mid: serverMsgId,
          };
          _this.$store.dispatch('setChatList', msg);
          myJsTools.msgReadFun(msg);
        },
        fail(e) {},
      });
      try {
        this.$im.conn.send(msg.body);
        Toast('已向医生申请续方，请耐心等待医生响应');
        setTimeout(() => {
          uni.navigateBack({
            delta: 2,
          });
        }, 1500);
      } catch (e) {}
    },
  },
};
</script>

<style lang="scss" scoped>
page {
  background: #f5f5f5;
}

.cfDetailView {
  padding: 32rpx 32rpx 140rpx;
}

.pre_head {
  background-color: #fff;
  border-radius: 8rpx;
  padding: 24rpx 32rpx;

  .head_item {
    @include flex(lr);
    font-size: 28rpx;
    color: #666;
    line-height: 50rpx;

    .bold {
      color: #333;
    }
  }
}
</style>
