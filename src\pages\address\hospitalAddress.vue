<template>
  <view>
    <!-- 有收货地址，查询药店 -->
    <template v-if="addressStatus == 1">
      <view class="address-only" @click="switchAddress">
        <view class="address-only-left">
          <view class="address-only-left-top">
            <text>{{ address.deliveryName }}</text>
            <text>{{ address.telNo }}</text>
            <view class="address-only-left-icon" v-if="address.isDefault == '1'"
              >默认</view
            >
            <view class="address-only-left-icon" v-if="address.lableName">{{
              address.lableName
            }}</view>
          </view>
          <view class="address-only-left-bot">
            {{ address.addressArea }}{{ address.addressDetail }}
          </view>
        </view>
        <view class="address-only-right">
          <uni-icons type="arrowright" color="#333" size="22"></uni-icons>
        </view>
      </view>
      <view class="title"> 选择供药机构 </view>
      <view class="address-hospital">
        <view
          class="address-hospital-item"
          @click="selfGive(1, item)"
          v-for="(item, index) in hospitalListFirst"
          :key="'a' + index"
        >
          <img
            class="address-hospital-item-left"
            v-img="item.drugstoreImg"
            data-src="../../static/images/Pharmacy-default.png"
            v-if="item.drugstoreImg"
            alt=""
          />
          <image
            v-else
            src="../../static/images/Pharmacy-default.png"
            mode="aspectFill"
            class="address-hospital-item-left"
          ></image>
          <view class="address-hospital-item-right">
            <view class="address-hospital-item-text">
              <text>{{ item.drugstoreName }}</text>
              <view class="address-hospital-icon"> 快递 </view>
            </view>
            <view class="address-hospital-item-text">
              <text>{{ item.drugstoreAddress }}</text>
              <view class="">
                <uni-icons type="location" color="#666" size="16"></uni-icons>
                <template v-if="item.distance > 1">
                  {{ item.distance }}km
                </template>
                <template v-else> {{ item.distance * 2 }}m </template>
              </view>
            </view>
          </view>
        </view>

        <view
          class="address-hospital-item"
          v-for="(item, index) in hospitalList"
          :key="index"
          @click="selfGive(2, item)"
        >
          <img
            class="address-hospital-item-left"
            v-img="item.drugstoreImg"
            data-src="../../static/images/Pharmacy-default.png"
            v-if="item.drugstoreImg"
            alt=""
          />
          <image
            src="../../static/images/Pharmacy-default.png"
            mode="aspectFill"
            v-else
            class="address-hospital-item-left"
          ></image>

          <view class="address-hospital-item-right">
            <view class="address-hospital-item-text">
              <text>{{ item.drugstoreName }}</text>
              <view class="address-hospital-icon"> 自提 </view>
            </view>
            <view class="address-hospital-item-text">
              <text>{{ item.drugstoreAddress }}</text>
              <view class="">
                <uni-icons type="location" color="#666" size="16"></uni-icons>
                <template v-if="item.distance > 1">
                  {{ item.distance }}km
                </template>
                <template v-else> {{ item.distance * 2 }}m </template>
              </view>
            </view>
          </view>
        </view>
        <view class="address-hospital-item" v-for="(item, index) in sameCityDrugStore" :key="index" @click="selfGive(3, item)">
          <img
            class="address-hospital-item-left"
            v-img="item.drugstoreImg"
            data-src="../../static/images/Pharmacy-default.png"
            v-if="item.drugstoreImg"
            alt=""
          />
          <image
            src="../../static/images/Pharmacy-default.png"
            mode="aspectFill"
            v-else
            class="address-hospital-item-left"
          ></image>

          <view class="address-hospital-item-right">
            <view class="address-hospital-item-text">
              <text>{{ item.drugstoreName }}</text>
              <view class="address-hospital-icon"> 同城配送 </view>
            </view>
            <view class="address-hospital-item-text">
              <text>{{ item.drugstoreAddress }}</text>
              <view class="">
                <uni-icons type="location" color="#666" size="16"></uni-icons>
                <template v-if="item.distance > 1">
                  {{ item.distance }}km
                </template>
                <template v-else> {{ item.distance * 2 }}m </template>
              </view>
            </view>
          </view>
        </view>
      </view>
      <!-- 空状态 -->
      <view class="empty" v-if="!hospitalListFirst.length">
        <image src="@/static/images/index/address_emity.png" mode="w"></image>
        <view>没有满足库存的药店</view>
      </view>
    </template>

    <!-- 没有收货地址，去新增收货地址 -->
    <template v-if="addressStatus == 0">
      <view class="empty">
        <image src="@/static/images/index/address_emity.png" mode="w"></image>
        <view>您还没有收货地址，赶快添加一个吧</view>
      </view>
      <!-- 按钮 -->
      <FooterButton @click="newAddress"> 新建地址 </FooterButton>
    </template>
  </view>
</template>

<script>
import { getDrugStorageStore } from "@/api/cf.js";
import { findAddressByUserId } from "@/api/address.js";
const mapKey = require("@/common/request/config.js").mapKey;

import FooterButton from "@/components/footer_button/button.vue";
export default {
  components: {
    FooterButton,
  },
  data() {
    return {
      address: {},
      list: {
        docId: "",
        drugList: [],
        drugNum: "",
        latitude: "",
        longitude: "",
        limit: 10,//无用，根据系统配置条数显示
        page: 1,
        drugstoreId: "",
      },
      hospitalListFirst: [],
      hospitalList: [],
      sameCityDrugStore: [],
      businessId: "",
      action: "", // 标识从哪个页面进入
      addressStatus: "0", // 是否有地址
    };
  },
  onLoad(option) {
    if (option.drugstoreId) this.list.drugstoreId = option.drugstoreId || "";
    this.businessId = option.businessId; // 处方ID
    this.list.docId = option.docId || "";
    let drugArr = uni.getStorageSync("drugArr");
    this.list.drugList = drugArr;
    this.list.drugNum = drugArr.length; // 处方明细
    this.initData();
  },
  onShow() {
    let action = uni.getStorageSync("action");
    this.action = action;
    uni.removeStorageSync("action");
    if (this.action == "switchAddress") {
      this.initData();
    }
  },
  methods: {
    async initData() {
      /*
				判断是否有选中的地址，
					如果没有，则可能是第一次进入，判断是否有系统默认的收货地址，如果有，按该地址进行药店查询，如果没有，提示去新增地址
					如果有选中的地址，则按照选中的地址查询药店
				*/
      let nowAddress = uni.getStorageSync("nowAddress");
      if (nowAddress.address) {
        this.address = nowAddress.address;
        this.addressStatus = "1";
        // 查询药店
        let { fromlatitude, fromlongitude } = this.address;
        if (fromlatitude) {
          this.list.latitude = fromlatitude;
          this.list.longitude = fromlongitude;
        } else {
          this.list.latitude = this.address.latitude;
          this.list.longitude = this.address.longitude;
        }

        this.hospitalList = [];
        this.getDrugStorageStore(this.list, 1);
      } else {
        let res = await findAddressByUserId({
          userId: uni.getStorageSync("userId"),
          isDefaul: "",
        });
        if (res.code == 20000) {
          if (res.data.length > 0) {
            let obj = {};
            obj.address = res.data[0];
            obj.address.giveStatus = 1;
            this.address = obj.address;

            uni.setStorageSync("nowAddress", obj);
            this.addressStatus = "1"; // 有收货地址
            // 查询药店
            this.list.latitude = this.address.latitude;
            this.list.longitude = this.address.longitude;
            this.hospitalList = [];
            this.getDrugStorageStore(this.list, 1);
          } else {
            this.addressStatus = "0"; // 没有收货地址
          }
        }
      }
    },
    async getDrugStorageStore(data) {
      let obj = await this.getLocation(data);
      data = { ...data, ...obj };
      let res = await getDrugStorageStore(data);
      // 新增同城配送
      let { expressDeliveryDrugStore, selfDeliveryDrugStore, sameCityDrugStore } = res.data;
      if (expressDeliveryDrugStore.length) {
        this.hospitalListFirst = expressDeliveryDrugStore;
      }
      this.hospitalList = selfDeliveryDrugStore;
      this.sameCityDrugStore = sameCityDrugStore;
    },
    // 地址逆向解析
    async getLocation({ latitude, longitude }) {
      let res = await this.$jsonp("https://apis.map.qq.com/ws/geocoder/v1/", {
        location: latitude + "," + longitude,
        get_poi: 1,
        key: mapKey,
        output: "jsonp",
      });
      if (res.status) {
        return {
          provinceName: "",
          cityName: "",
        };
      }
      let {
        result: { address_component: data },
      } = res;

      return {
        provinceName: data.province,
        cityName: data.city,
      };
    },
    // 切换地址
    switchAddress() {
      uni.navigateTo({
        url:
          "/pages/address/index?action=" +
          "switchAddress" +
          "&businessId=" +
          this.businessId,
      });
    },
    // 新建收货地址
    newAddress() {
      uni.navigateTo({
        url:
          "/pages/address/newAddress?action=" +
          "emptyAddress" +
          "&businessId=" +
          this.businessId,
      });
    },
    selfGive(type, item) {
      let address = "";
      item.telNoSh = item.telNo;
      if (type == 1) {
        // 配送
        this.address.deliveryType = type;
        this.address.drugstoreId = item.drugstoreId;
        this.address.drugstoreName = item.drugstoreName;
        this.address.fromlatitude = this.address.latitude;
        this.address.fromlongitude = this.address.longitude;
        address = this.address;
      } else if (type == 3) {
        // 同城配送暂时不支持
        uni.showToast({
          title: '同城配送暂未开放',
          icon: 'none',
        });
        return
        // 同城配送
        this.address.deliveryType = type;
        this.address.drugstoreId = item.drugstoreId;
        this.address.drugstoreName = item.drugstoreName;
        this.address.fromlatitude = this.address.latitude;
        this.address.fromlongitude = this.address.longitude;
        address = this.address;
      } else {
        // 自提
        item.deliveryType = type;
        item.addressArea = this.address.addressArea;
        item.addressDetail = this.address.addressDetail;
        item.deliveryName = this.address.deliveryName;
        item.fromlatitude = this.address.latitude;
        item.fromlongitude = this.address.longitude;
        address = item;
      }
      address.isCandj = item.isCandj;
      address.telNo = this.address.telNo;
      address.telNoSh = item.telNoSh;
      address.isDeposit = item.isDeposit; //是否支付定金
      address.depositProportion = item.depositProportion; //支付比例
      let obj = uni.getStorageSync("nowAddress");
      obj.address = address;

      uni.setStorageSync("nowAddress", obj); // 选中的地址信息，以及选择的药店信息
      uni.setStorageSync("scanAddress", obj); // 扫码开方用
      uni.navigateBack({
        delta: 1,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.address-only {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 24upx 0;
  background-color: #fff;
  padding: 32upx;

  .editAddress {
    width: 32rpx;
    height: 32rpx;
  }

  .address-only-left {
    flex: 1;

    .address-only-left-top {
      display: flex;
      align-items: center;
      margin-bottom: 16upx;

      text {
        font-size: 28upx;
        font-weight: 600;
        color: #333333;
        padding-right: 15upx;
      }

      .address-only-left-icon {
        line-height: 32upx;
        background-color: #666;
        border-radius: 8upx;
        font-size: 22upx;
        padding: 0 10upx;
        color: #fff;
        margin-left: 12upx;
      }
    }

    .address-only-left-bot {
      font-size: 24upx;
      font-weight: 400;
      color: #666666;
      line-height: 40upx;
    }
  }

  .defaultImg {
    position: absolute;
    right: 0;
    top: 0;
    width: 48upx;
    height: 48upx;
  }
}

.title {
  font-size: 22upx;
  font-weight: 400;
  color: #666666;
  line-height: 16upx;
  padding: 0 32upx;
}

.address-hospital {
  padding: 0 32upx;
  background-color: #fff;
  margin-top: 24upx;

  .address-hospital-item {
    display: flex;
    padding: 14upx 0;
    border-bottom: 1upx solid #ebebeb;

    &:last-child {
      border-bottom: none;
    }

    .address-hospital-item-left {
      width: 140upx;
      height: 140upx;
      border-radius: 8upx;
      margin-right: 26upx;
    }

    .address-hospital-item-right {
      flex: 1;

      .address-hospital-item-text {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 14upx;
        font-size: 26upx;
        font-weight: 400;
        color: #666;

        &:nth-child(1) {
          font-size: 28upx;
          font-weight: 400;
          color: #333;

          .address-hospital-icon {
            line-height: 32upx;
            background-color: #666;
            border-radius: 8upx;
            font-size: 22upx;
            padding: 0 10upx;
            color: #fff;
          }
        }

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

// 没有收货地址的提示
.empty {
  text-align: center;
  margin-top: 100px;

  image {
    width: 386rpx;
    height: 324upx;
    margin-bottom: 40rpx;
    margin: 0 auto;
  }

  view {
    text-align: center;
    color: #999;
    font-size: 28rpx;
  }
}

.addBtn {
  @include bg_theme;
  color: #ffffff;
  font-size: 36rpx;
  width: 100%;
  position: fixed;
  bottom: 0;
  height: 98rpx;
  line-height: 98rpx;
  text-align: center;
}
</style>
