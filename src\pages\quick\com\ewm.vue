<template>
  <div class="pay_code">
    <p class="title">代付码</p>
    <div class="ewm">
      <QRCODE
        :val="'orderNo=' + code"
        :size="300"
        background="#ffffff"
        foreground="#000000"
        pdground="#000000"
        icon="/static/images/logo-img.png"
        :iconSize="45"
        onval
        loadMake
      />
    </div>
    <div class="label">
      <p>无法付款可以联系工作人员</p>
      <p>扫码代付</p>
    </div>
  </div>
</template>

<script>
import QRCODE from '@/components/tki-qrcode/tki-qrcode.vue';
export default {
  name: 'PayCode',
  props: ['code'],
  components: {
    QRCODE,
  },
};
</script>

<style lang="scss" scoped>
.pay_code {
  background-color: #fff;
  border-radius: 16rpx;
  padding-bottom: 32rpx;
  overflow: hidden;
  width: 544rpx;

  .title {
    height: 116rpx;
    @include bg_theme;
    @include flex;
    font-size: 36rpx;
    color: #fff;
  }

  .ewm {
    @include flex;
    padding: 38rpx;
  }

  .label {
    font-size: 32rpx;
    text-align: center;
    color: #333;
    line-height: 44rpx;
  }
}
</style>
