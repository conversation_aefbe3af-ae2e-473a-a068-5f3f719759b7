<template>
  <u-popup
    v-model="popupVisable1"
    mode="bottom" close-btn height="70%"
    @close="popClose"
    bg-color="#F5F5F5"
    close-btn-position="left-top"
  >
    <view class="drug-detail">
      <view class="drug-detail-label">药品详情</view>
      <view class="drugCart">
        <view style="position: relative">
          <image
              :src="durginfo.drugImageUrl || `/static/drug1.png`"
              class="drugImage"
          />
          <view v-if="durginfo.drugType=='025.9'" class="drug-coverUp cfCover">
            处方药 <br>
            依规定不展示包装
          </view>
        </view>
        <view class="info">
          <view class="name">
            {{ durginfo.drugName }}
          </view>
          <view>
            <tn-button round v-if="durginfo.otc == 0" color="#14A0E6">处方药 </tn-button>
            <tn-button round v-else color="#14A0E6">非处方药</tn-button>
          </view>
        </view>
      </view>
      <scroll-view scroll-y class="drug-detail-content-wrapper" :style="`height:600rpx`">
        <view class="drug-detail-content" style="position: relative">
          <view v-if="durginfo.drugType=='025.9'" class="cfCover">
            <view style="flex: 1;z-index: 9;font-size: 16px">
              处方药 <br>
              依法规不展示药品详情
            </view>
          </view>
          <view class="drugInfo">
            <view class="druginfo-title">药品信息</view>
            <view class="infoCard" v-if="durginfo.memo">
              <view class="effect-title">【功能主治】</view>
              <view class="effect-detail">{{ durginfo.memo }}</view>
            </view>
            <view class="infoCard" v-if="durginfo.dduName">
              <view class="effect-title">【用法用量】</view>
              <view class="effect-detail" v-if="drugType != 3">
                {{ durginfo.dduName }}，每次{{ durginfo.eachQuan }}{{ durginfo.jldw }}，{{
                  durginfo.ddufName
                }}
              </view>
              <view class="effect-detail" v-else>
                单包{{ durginfo.eachQuan }}{{ durginfo.jldw }}
              </view>
            </view>
            <view class="infoCard">
              <view class="effect-title" v-if="durginfo.genericName">【通用名称】</view>
              <view class="effect-detail" v-if="durginfo.genericName">
                {{ durginfo.genericName }}
              </view>
              <view class="effect-title" v-if="durginfo.chemistryName">【化学名称】</view>
              <view class="effect-detail" v-if="durginfo.chemistryName">
                {{ durginfo.chemistryName }}
              </view>
              <view class="effect-title" v-if="durginfo.component">【成分】</view>
              <view class="effect-detail" v-if="durginfo.component">
                {{ durginfo.component }}
              </view>
              <view class="effect-title" v-if="durginfo.adverseReaction">【不良反应】</view>
              <view class="effect-detail" v-if="durginfo.adverseReaction">
                {{ durginfo.adverseReaction }}
              </view>
              <view class="effect-title" v-if="durginfo.taboo">【禁忌】</view>
              <view class="effect-detail" v-if="durginfo.taboo">{{ durginfo.taboo }}</view>
              <view class="effect-title" v-if="durginfo.storage">【贮藏】</view>
              <view class="effect-detail" v-if="durginfo.storage">
                {{ durginfo.storage }}
              </view>
              <view class="effect-title" v-if="durginfo.gg">【规格】</view>
              <view class="effect-detail" v-if="durginfo.gg">{{ durginfo.gg }}</view>
              <view class="effect-title" v-if="durginfo.jx">【剂型】</view>
              <view class="effect-detail" v-if="durginfo.jx">{{ durginfo.jx }}</view>
              <view class="effect-title" v-if="durginfo.termOfValidity">【有效期】</view>
              <view class="effect-detail" v-if="durginfo.termOfValidity">
                {{ durginfo.termOfValidity }}
              </view>
              <view class="effect-title" v-if="durginfo.pzwh">【批准文号】</view>
              <view class="effect-detail" v-if="durginfo.pzwh">{{ durginfo.pzwh }}</view>
              <view class="effect-title" v-if="durginfo.scqy">【生产企业】</view>
              <view class="effect-detail" v-if="durginfo.scqy">{{ durginfo.scqy }}</view>
              <view class="effect-title" v-if="durginfo.reminder">【温馨提示】</view>
              <view class="effect-detail" v-if="durginfo.reminder">
                {{ durginfo.reminder }}
              </view>
              <view class="effect-title" v-if="durginfo.drugDescriptionPicture">【药品说明书】</view>
              <view class="effect-detail" v-if="durginfo.drugDescriptionPicture">
                <image
                    v-for="(item, index) in durginfo.drugDescriptionPictureImg"
                    :src="item"
                    :key="item"
                    class="w-[120rpx] h-[120rpx]"
                    style="margin-right: 5px"
                    @click="previews(durginfo.drugDescriptionPictureImg, index)"
                ></image>
              </view>
            </view>
          </view>
        </view>
      </scroll-view>

    </view>
  </u-popup>
</template>

<script>
export default {
  components: {},
  props: {
    value:{
      type: Boolean,
      default: false
    },
    durginfo: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  computed:{

  },
  watch:{
    popupVisable1(newVal){
      this.$emit('input', newVal)
    },
    value(){
      this.popupVisable1 = this.value
    }
  },
  data() {
    return {
      drugType:"1",
      popupVisable1:false,
      drugTypeList: [],
      ddtId: "",
      act: -1,
      sub_menu: [],
    }
  },
  methods:{
    popClose(){
      this.$emit("update:popupVisable", false);
    },
    previews(urls, index){
      uni.previewImage({
        urls: urls,
        current: index,
      });
    },
  }
}
</script>

<style scoped lang="scss">
  .btn-style {
    width: 140rpx;
    height: 52rpx;
    line-height: 52rpx;
    border: 2rpx solid #999999;
    border-radius: 40rpx;
    min-width: 60px;
    font-size: 24rpx;
    color: #999999;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;

    &.btn-add {
      background: #14a0e6;
      color: #fff;
      border: none;
    }
    &.shoucang {
      border: 1px solid #14a0e6;
      color: #14a0e6;
    }
  }

  .drug-detail {
    height: 100%;
    overflow: auto;
    .drug-detail-label {
      font-size: 36rpx;
      color: #333333;
      height: 100rpx;
      line-height: 100rpx;
      text-align: center;
      font-weight: 600;
      background: #fff;
    }

    .drugCart {
      display: flex;
      height: 75px;
      background: white;
      padding: 12px;
      border-radius: 0rpx 0rpx 16rpx 16rpx;

      .info {
        padding-left: 12px;
        flex: 2;

        view {
          text-align: left;
          height: 35px;
        }

        .name {
          color: #333;
          font-size: 32rpx;
          font-weight: 600;
        }

        .specification {
          color: #999;
          font-size: 28rpx;
          margin-top: 2px;
        }

        .price {
          color: red;
          font-size: 26rpx;
          margin-top: 4px;
          font-weight: 600;
        }
      }

      .save-btn {
        flex: 1;
        padding: 30px 30px 10px;
      }
    }

    .drug-detail-content {
      overflow: auto;

      .drugInfo {
        background: #fff;
        width: 92%;
        margin: 4%;
        border-radius: 5px;
        padding-bottom: 5px;

        .druginfo-title {
          font-size: 28rpx;
          height: 22px;
          line-height: 22px;
          padding: 12px 12px 0;
          font-weight: 600;
        }

        .infoCard {
          margin: 10px;
          background: #fff;
          padding: 5px;
          border-radius: 5px;
          background: #f5f5f5;
          border-radius: 16rpx;
        }

        .effect-title {
          font-weight: 600;
          font-size: 14px;
          font-size: 28rpx;
          color: #333333;
          font-weight: 600;
          line-height: 40rpx;
        }

        .effect-detail {
          font-size: 14px;
          color: #666666;
          padding: 8px;
        }
      }

      .save-btn .grayBtn {
        color: #ddd !important;
        background: #fff;
        border: 1px solid#ddd;
      }
    }
  }
  .drugImage{
    width: 100rpx;
    height: 100rpx;
  }
  .cfCover{
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    text-align: center;
    font-size: 10px;
    background: rgba(255,255,255,0.7) !important;
    display: flex;
    align-items: center;
    flex: 1;
    z-index: 2;
  }
  .gx{
    filter: blur(2px);
  }
  .drug-coverUp{
    width: 128rpx;height: 128rpx;
    background: rgba(255, 255, 255, 0.5);
    position: absolute;
    top: 0;
    left: 0;
  }
</style>
<style scoped lang="scss"></style>
