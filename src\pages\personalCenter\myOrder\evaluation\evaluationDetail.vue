<template>
  <view class="warp">
    <view :class="evaluaSuccess == 1 ? 'page-container' : 'page-box'">
      <TIP :obj="tips" />

      <view class="oreder-direc" @click="toRegisterDetail">
        <view class="title item">
          <text>挂号</text>
          <text class="status">交易成功</text>
        </view>
        <view class="con item">
          <text class="slowColor">科室</text>
          <text>{{ infoDetail.deptName }}</text>
        </view>
        <view class="con item">
          <text class="slowColor">医生</text>
          <text>{{ infoDetail.docName }}</text>
        </view>
        <view class="lastcon item">
          <view>
            <text class="slowColor">问诊方式</text>
            <text>{{ infoDetail.visitTypeName }}问诊</text>
          </view>
          <uni-icons type="arrowright" color="#333" size="22"></uni-icons>
        </view>
      </view>
      <view class="pay-details">
        <view class="total flex-row">
          <view class="blod-title"> 费用合计 </view>
          <view> 合计：{{ infoDetail.totalPay }} </view>
        </view>
<!--        <view class="item blod-title"> 医保担负明细 </view>-->
<!--        <view class="flex-row item">-->
<!--          <view> 医保统筹支付 </view>-->
<!--          <view>-->
<!--            {{ infoDetail.medicareAccountPay }}-->
<!--          </view>-->
<!--        </view>-->
<!--        <view class="flex-row item">-->
<!--          <view> 医保账户支付 </view>-->
<!--          <view>-->
<!--            {{ infoDetail.selfAccountPay }}-->
<!--          </view>-->
<!--        </view>-->
        <view class="item blod-title"> 优惠券折扣明细 </view>
        <view class="flex-row item">
          <view> 新用户立减 </view>
          <view> 0.00 </view>
        </view>
        <view class="item self">
          实际支付：<text>¥{{ infoDetail.selfPay }}</text>
        </view>
      </view>
      <view class="other-info">
        <view class="list">
          <view class="left-title"> 订单编号 </view>
          <view>
            {{ infoDetail.regCode }}
          </view>
        </view>
        <view class="list">
          <view class="left-title"> 创建时间 </view>
          <view>
            {{ infoDetail.addTime }}
          </view>
        </view>
        <view class="list">
          <view class="left-title"> 付款时间 </view>
          <view>
            {{ infoDetail.payTime }}
          </view>
        </view>
      </view>
    </view>
    <view class="footer" v-if="evaluaSuccess == 1">
      <view class="btn" @click="toEvaluation"> 评价 </view>
    </view>
  </view>
</template>

<script>
import { allRegisterOrder } from '@/api/order.js';
import TIP from '@/pages/personalCenter/myOrder/components/tip.vue';
export default {
  components: {
    TIP,
  },
  data() {
    return {
      infoDetail: {},
      info: {},
      evaluaSuccess: 1,
      tips: {
        title: '交易成功',
        toast: '交易成功，快去评价吧',
      },
    };
  },
  onLoad(v) {
    this.info = v;
    this.getRegisterOrder();
  },

  onShow() {
    let action = uni.getStorageSync('evaluaAction') || '';
    // 评价成功返回,去掉评价按钮
    if (action == 'evaluaSuccess') {
      uni.removeStorageSync('evaluaAction'); // 移除标识
      this.evaluaSuccess = 0;
    }
  },

  methods: {
    async getRegisterOrder() {
      let res = await allRegisterOrder({
        regId: this.info.regId,
      });
      this.infoDetail = res.data;
    },
    toRegisterDetail() {
      uni.navigateTo({
        url:
          '/pages/personalCenter/diagnosisRecord/detail?id=' + this.info.regId,
      });
    },
    toEvaluation() {
      const { regId, docId, orderNo } = this.info;
      uni.navigateTo({
        url:
          '/pages/personalCenter/myOrder/evaluation/evaluation?regId=' +
          regId +
          '&docId=' +
          docId +
          '&orderNo=' +
          orderNo,
      });
    },
  },
};
</script>

<style scoped lang="scss">
.warp {
  height: 100vh;
  overflow-y: auto;
}
.page-container {
  min-height: calc(100vh - 100rpx);
}

.page-box {
  width: 100%;
  overflow-y: auto;
}

/* oreder-direc */
.oreder-direc {
  margin-top: 24rpx;
  background: #ffffff;
  padding: 24rpx 32rpx;
  /* 常规字体 */
  font-size: 28rpx;
  font-weight: 400;
  color: #333333;
  line-height: 28rpx;
}

.oreder-direc .item:not(:first-child) {
  margin-top: 24rpx;
}

.oreder-direc .title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  line-height: 28rpx;
}

.oreder-direc .title .status {
  @include font_theme;
}

.oreder-direc .slowColor {
  display: inline-block;
  width: 144rpx;
}

.oreder-direc .lastcon {
  @include flex(lr);
}

/* pay-details */
.pay-details {
  width: 100%;
  background: #ffffff;
  padding: 26rpx 32rpx;
  box-sizing: border-box;
  margin-top: 24rpx;

  /* 常规字体 */
  font-size: 28rpx;
  font-weight: 400;
  color: #999999;
  line-height: 28rpx;
}

.pay-details .blod-title {
  font-weight: 600;
  color: #333333;
}

.pay-details .flex-row {
  @include flex(lr);
}

.pay-details .total {
  border-bottom: 2rpx dashed #979797;
  padding-bottom: 40rpx;
}

.pay-details .item {
  margin-top: 40rpx;
}

.pay-details .self {
  color: #333333;
  line-height: 20px;
  text-align: right;
}

.pay-details .self text {
  color: #ff0000;
}

/* other-info */
.other-info {
  width: 100%;
  background: #ffffff;
  margin-top: 24rpx;
  padding: 24rpx 32rpx;
  box-sizing: border-box;
  margin-bottom: 24rpx;
}

.other-info .list {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  font-weight: 400;
  color: #333333;
  line-height: 40rpx;
}

.other-info .list:not(:first-child) {
  margin-top: 24rpx;
}

.other-info .left-title {
  margin-right: 32rpx;
}

.footer {
  height: 104rpx;
  background: #ffffff;
  box-shadow: 0px 2rpx 4rpx 0px rgba(0, 0, 0, 0.5);
  padding: 0 30rpx;
  box-sizing: border-box;
  @include flex(right);
  position: sticky;
  bottom: 0;
  z-index: 3;
}

.footer .btn {
  width: 160rpx;
  height: 60rpx;
  @include bg_theme;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #ffffff;
  @include flex;
}
</style>
