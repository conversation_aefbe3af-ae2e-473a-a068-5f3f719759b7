<template>
  <!-- 药品状态详情 -->
  <view class="detail">
    <!-- 浮动图标 -->
    <GOHOME />

    <!-- 顶部状态 -->
    <STATUS
      :status="detail.payStatus"
      :deliveryType="detail.deliveryType"
      :time="time"
    />

    <!--    处方头部显示区域-->
    <view class="szCfInfo">
      <view class="infoCon">
        <view class="infoCon-head">
          <text>医院：神州（天津）互联网医院-test</text>
          <h4>处方笺</h4>
          <view class="infoCon-tips">普通处方</view>
        </view>
        <view class="infoCon-foot">
          <view>
            <h3>白玉</h3>
            <span style="padding-right: 10px">
              <img
                style="width: 14px; height: 14px; margin-right: 3px"
                src="../../../static/cf-age.png"
                alt=""
              />
              女
            </span>
            <span>
              <img
                style="width: 13px; height: 12px; margin-right: 3px"
                src="../../../static/cf-sex.png"
                alt=""
              />
              28岁
            </span>
          </view>
          <view>就诊科室：肿瘤大内科</view>
          <view>开方时间：2024-12-03 12:22:33</view>
          <view>
            <span>处方ID：2132123213123122311 </span>
            <img
              style="width: 12px; height: 13px; margin-left: 10px"
              src="../../../static/cf-copy.png"
              alt=""
            />
          </view>
        </view>
      </view>
    </view>

    <!--诊断展示区域-->
    <view class="szCfInfo">
      <view class="infoCon" style="padding-left: 10px; padding-right: 10px">
        <view class="infoCon-head">
          <h3>医院：神州（天津）互联网医院-test</h3>
        </view>
        <view class="infoCon-foot"> 支气管炎 </view>
      </view>
    </view>

    <!--    处方信息展示区域-->
    <view class="szCfInfo">
      <view class="infoCon" style="padding-left: 10px; padding-right: 10px">
        <view
          class="infoCon-head"
          style="
            display: flex;
            justify-content: space-between;
            align-items: center;
          "
        >
          <h3>处方信息</h3>
          <view
            style="
              display: flex;
              align-items: center;
              color: rgba(131, 106, 255, 1);
            "
            >原始处方
            <img
              style="width: 7.5px; height: 14px; margin-left: 6px"
              src="../../../static/rightJt.png"
              alt=""
          /></view>
        </view>
        <view class="infoCon-foot">
          <view style="font-size: 12px">
            <h4 style="color: #333333; font-size: 14px; padding-right: 10px">
              头孢分散片
            </h4>
            <span style="padding-right: 10px">100mg/100ml</span>
            <span style="padding-right: 10px">2板/盒</span>
            <span>x1</span>
          </view>
          <view>口服：一日2次 每次100mg</view>
        </view>
      </view>
    </view>

    <!-- 引导 -->
    <Tips v-show="showTip" @click="showTip = false" />
  </view>
</template>

<script>
import STATUS from "./components/status.vue";
import PHARMACY from "@/pages/shop/components/pharmacy.vue";
import FOOTER from "@/components/footer_button/button.vue";
import SHOP from "./components/shop.vue";
import PRESCRIPTION from "./components/prescription.vue";
import GOHOME from "@/components/home/<USER>";
import { findDrugStoreDetail } from "@/api/base";
import {
  orderDetailNew,
  findPayOrderInfo,
  confirmGoods,
  getPresPayStatue,
  queryGetMedicine,
} from "@/api/order.js";

import { queryMallOrderStatus, refundAndAddYf } from "@/api/shop.js";

import myJsTools from "@/common/js/myJsTools.js";

import { wxPay } from "@/common/js/pay";

import tkiQrcode from "@/components/tki-qrcode/tki-qrcode.vue";

import { Toast } from "@/common/js/pay.js";

import payment from "@/mixins/wx";
import Pay from "@/modules/pay";
import { getSysPlatformConfigByKeyList } from "@/api/base";
import { getHelpPayCreateResult } from "@/api/order";
let timer = null;
let inter = null;
let PAY;
export default {
  name: "Detail",
  mixins: [payment],
  components: {
    STATUS,
    PHARMACY,
    FOOTER,
    SHOP,
    PRESCRIPTION,
    GOHOME,
    tkiQrcode,
  },
  data() {
    return {
      // 展开地址
      show: false,
      // 参数
      param: {
        // 订单号
        orderNo: "",
        // 来源
        source: "",
      },
      detail: "",
      // 处方列表
      list: [],
      // 药店信息
      pharmacy: {},
      // 计数
      num: 3,
      // 计时器
      time: 0,
      // 挂号id
      ghId: "",
      // 展示取药码
      isShowEwm: false,
      // 取药码
      codeUrl: "",
      // 文案
      code_text: "",
      // 收货地址
      address: {},
    };
  },
  onLoad(opt) {
    let { orderNo, source } = opt;
    this.param = {
      orderNo,
      source,
    };
    this.getDetail();
  },
  onUnload() {
    if (timer) clearInterval(timer);
    if (inter) clearInterval(inter);
  },
  methods: {
    // 好友付
    async payment() {
      const { orderMoney } = this.detail;
      let ghId = this.detail.ghId;
      const { orderNo, source } = this.param;
      if (source != 1) {
        ghId = "";
      }
      await this.setShare(orderMoney, orderNo, source, ghId);
      this.showTip = true;
    },
    // 获取取药码
    async getCode() {
      let { businessId } = this.detail;
      let res = await queryGetMedicine(businessId);
      // 如果不为空
      if (res.data.drugCode) {
        this.codeUrl = res.data.drugCode;
        this.isShowEwm = true;
        return;
      }
      Toast("未能获取取药码");
    },
    // 去处方详情
    toPreDetail() {
      const { prescriptionBusinessId, businessId, hosId, payStatus } =
        this.detail;
      if (!prescriptionBusinessId || !businessId) {
        Toast("未能查询到处方详情");
        return;
      }
      uni.setStorageSync("hosId", hosId);
      let url;
      if (payStatus == 1) {
        url =
          "/pages/prescription/prescriptionDetail?businessId=" +
          prescriptionBusinessId +
          "&type=2";
      } else {
        url =
          "/pages/prescription/preDetail?businessId=" + prescriptionBusinessId;
      }
      uni.navigateTo({
        url,
      });
    },
    // 查看物流
    lockLogist(code, name) {
      console.log("this.detail", this.detail);
      const { deliveryTelNo, businessId, orderNo } = this.detail;
      let tel = deliveryTelNo.slice(-4);
      uni.navigateTo({
        url:
          "/pages/personalCenter/myOrder/allOrder/prescriptionOrder/logistics?code=" +
          code +
          "&tel=" +
          tel +
          "&name=" +
          name +
          "&businessId=" +
          businessId +
          "&orderNo=" +
          orderNo,
      });
    },
    // 查询详情
    async getDetail() {
      let res = await orderDetailNew(this.param);
      console.log(res);
      this.detail = res.data;
      const { orderDrugStoreListNewVOList: list, source } = res.data;
      if (res.data.payStatus == 1) {
        this.time = res.data.forRestOf;
        // 正常处方 只会存在一个药店
        if (source == 1 || source == 3) {
          const { drugstoreId } = list[0];
          PAY = new Pay(drugstoreId);
        } else {
          PAY = new Pay();
          this.getPriceList();
        }
        this.setTimes();
      }
      if (res.data.deliveryType == 1) {
        let { deliveryName, deliveryTelNo, deliveryAddressDetail } = res.data;
        this.address = {
          deliveryName,
          deliveryTelNo,
          deliveryAddressDetail,
        };
      }
      this.list = res.data.orderDrugStoreListNewVOList;
      // 查询药店信息
      if (res.data.deliveryType == 2) {
        this.show = true;
        this.getDurgDetail(res.data.drugstoreId);
      }
    },
    // 待支付倒计时
    setTimes() {
      if (this.time <= 0) {
        this.time = 0;
        return;
      }
      inter = setInterval(() => {
        this.time--;
        if (this.time <= 0) {
          this.getDetail();
          clearInterval(inter);
        }
      }, 1000);
    },
    // 获取药店详情
    async getDurgDetail(drugstoreId) {
      let res = await findDrugStoreDetail({
        drugstoreId,
      });
      this.pharmacy = res.data;
    },
    // 手机号显示处理
    phone(str) {
      return myJsTools.phone(str);
    },
    // 获取平台支付方式 商城用
    async getPriceList() {
      let res = await getSysPlatformConfigByKeyList(["onlineMallPayId"]);
      let callId = res.data[0].configValue;
      // 收款方
      let list = callId.split(",") || [];
      let arr = list.map((v) => {
        let obj = {
          appid: v,
        };
        if (v.indexOf("wx") > -1) {
          obj.receiptType = 1;
        } else {
          obj.receiptType = 2;
        }
        return obj;
      });
      PAY.payList = arr;
    },
    // 点击支付
    async goPay() {
      const { index, item } = await PAY.selePay(this.detail.orderRealMoney);
      console.log(index, item);
      let res = await getHelpPayCreateResult({
        callId: item.appid,
        payType: index,
        orderNo: this.param.orderNo,
        openid: uni.getStorageSync("wxInfo").openId,
      });
      let info = res.data;
      // 用于处方
      // if(res.data.orderStatus == 2){
      //   Toast("该订单已支付成功，请勿重新支付");
      //   this.getDetail()
      //   return;
      // }if(res.data.orderStatus == 3){
      //   Toast("该订单已退款，请重新进入订单页面");
      //   this.getDetail()
      //   return;
      // }else if(res.data.orderStatus == 1 || res.data.orderStatus == null){
      if (index == 1) {
        this.wxPay(info);
      } else if (index == 2) {
        uni.navigateTo({
          url:
            "/pages/pay/pay?price=" +
            res.data.orderMoney +
            "&orderNo=" +
            info.orderNo +
            "&url=" +
            btoa(info.url),
        });
      }
      // }
    },
    async wxPay(info) {
      // 调用微信支付
      let source = this.param.source;
      try {
        await wxPay(info);
        if (source == 1) {
          this.preStatus();
          timer = setInterval(() => {
            this.preStatus();
          }, 2000);
          return;
        }
        this.getPayStatus();
        timer = setInterval(() => {
          this.getPayStatus();
        }, 2000);
      } catch (error) {
        Toast("取消支付");
      }
    },
    // 商城支付状态
    async getPayStatus() {
      const { orderNo } = this.param;
      // 根据订单号查询
      let res = await queryMallOrderStatus(orderNo);
      this.getStatus(res);
    },
    // 处方查询状态
    async preStatus() {
      let res = await getPresPayStatue({
        ghId: this.detail.ghId,
      });
      this.getStatus(res);
    },
    // 轮询查状态
    getStatus(res) {
      this.num--;

      if (res.data.orderStatus == 2 || res.data.regStatus == 2) {
        clearInterval(timer);
        timer = null;
        this.num = 3;

        // 支付成功 重新获取状态
        this.getDetail();
      }

      // 查询过三次
      if (this.num <= 0) {
        clearInterval(timer);
        // 应该跳转到失败
        if (res.data.orderStatus == 1 || res.data.regStatus == 1) {
          Toast("支付状态异常");
        }
      }
    },
    // 确认收货
    async confirmGood(merchantsOrderNo) {
      let { businessId, orderNo } = this.detail;
      let item = {
        merchantsOrderNo,
        businessId,
        orderNo,
      };
      let res = await confirmGoods(item);
      uni.showToast({
        title: "收货成功",
        icon: "none",
      });
      this.getDetail();
    },
    // 申请退费
    applyRefund() {
      console.log("111111", this.item);
      refundAndAddYf({ orderNo: this.item.orderNo }).then((res) => {
        uni.showToast({
          title: "申请退款成功！",
          icon: "none",
        });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}

.detail {
  min-height: 100vh;
  background-color: #f0f2fc;
  padding-bottom: 120rpx;

  .szCfInfo {
    padding: 0 40rpx;
    margin-bottom: 10px;
    .infoCon {
      border-radius: 4px;
      background: rgba(255, 255, 255, 1);
      box-shadow: 0px 1px 4px rgba(121, 72, 234, 0.15);
      padding: 24rpx 60rpx;
      position: relative;
      .infoCon-head {
        border-bottom: 1px solid rgba(229, 229, 229, 1);
        padding-bottom: 8px;
        .infoCon-tips {
          background-color: rgba(242, 245, 255, 1);
          padding: 2px 5px;
          color: rgba(135, 79, 240, 1);
          font-size: 10px;
          position: absolute;
          right: 28rpx;
          top: 20rpx;
        }
      }
      .infoCon-foot {
        padding-top: 8px;
        view {
          padding-bottom: 3px;
          color: rgba(153, 153, 153, 1);
          font-size: 12px;
          display: flex;
          align-items: center;
          h3 {
            color: #333333;
            width: 50px;
          }
        }
      }
    }
  }
  .address {
    margin-top: -32rpx;
    background-color: #fff;
    border-radius: 32rpx;
    padding: 0 32rpx;

    .title {
      height: 88rpx;
      @include flex(left);
      font-size: 28rpx;
      font-weight: bold;
      border-bottom: 1px solid #f5f5f5;
    }

    .address_detail {
      font-size: 28rpx;
      padding: 24rpx 0;
      color: #333;
      line-height: 40rpx;
    }

    .address_info {
      font-size: 28rpx;
      color: #666;
      padding-bottom: 24rpx;

      text {
        margin-right: 12rpx;
      }
    }
  }

  .pre {
    margin-top: 24rpx;
  }

  .mt {
    margin-top: -32rpx;
  }

  .pay_list {
    padding: 0rpx 32rpx;
    background-color: #fff;

    .pay_info {
      @include flex(lr);
      color: #999;
      font-size: 28rpx;
      height: 68rpx;

      &.red {
        text {
          color: red;
        }
      }

      text {
        color: #333;
      }
    }

    .pay_price {
      @include flex(lr);
      font-size: 28rpx;
      color: #333;
      height: 68rpx;

      text {
        color: #ff3b30;
        font-weight: bold;
      }
    }
  }

  .logist {
    background-color: #fff;
    padding: 0 32rpx;
    margin-top: 24rpx;

    &.act {
      .label {
        .type {
          .uni-icons {
            transform: rotate(90deg);
          }
        }
      }

      .logist_cont {
        border-top: 1px solid #ebebeb;
        max-height: 500rpx;
      }
    }

    .label {
      height: 88rpx;
      @include flex(lr);
      font-size: 28rpx;

      .name {
        font-weight: bold;
      }

      .type {
        @include flex;

        .text {
          margin-right: 20rpx;
        }

        .uni-icons {
          transition: all 0.3s;
        }
      }
    }

    .logist_cont {
      max-height: 0;
      transition: all 0.3s;
      overflow: hidden;
      border-top: 1px solid transparent;

      .zt {
        padding: 24rpx 0;
        font-size: 28rpx;
        line-height: 40rpx;

        .address {
          color: #333;
        }

        .user {
          color: #666;
          margin-top: 10rpx;

          text {
            margin-left: 20rpx;
          }
        }
      }
    }
  }

  .pharmacy_detail {
    padding: 24rpx 0;

    .qym {
      width: 160rpx;
      height: 60rpx;
      border-radius: 30rpx;
      @include flex;
      font-size: 28rpx;
      color: #fff;
      @include bg_theme;
    }
  }

  .order_info {
    margin-top: 24rpx;
    background-color: #fff;
    padding: 0 32rpx;

    .info_title {
      font-size: 28rpx;
      font-weight: bold;
      line-height: 68rpx;
    }

    .info_item {
      font-size: 28rpx;
      line-height: 68rpx;

      text {
        display: inline-block;
        width: 140rpx;
      }
    }
  }
}

/* 取药号二维码  */
.qrcode-container {
  position: fixed;
  width: 100%;
  height: 100vh;
  left: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;

  .qrcode-content {
    width: 544rpx;
    height: 588rpx;
    background: #ffffff;
    border-radius: 16rpx;
    overflow: hidden;
    text-align: center;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);

    .title {
      width: 100%;
      height: 116rpx;
      @include bg_theme;
      line-height: 116rpx;
      font-size: 36rpx;
      font-weight: 500;
      color: #ffffff;
      display: flex;
      justify-content: center;
    }
  }
}

.qrcode {
  margin-top: 70rpx;
}

// 底部
.footer {
  width: 100%;
  height: 108rpx;
  background: #ffffff;
  box-shadow: 0 0 2rpx rgba(0, 0, 0, 0.2);
  border-radius: 16rpx 16rpx 0px 0px;
  position: fixed;
  left: 0;
  bottom: 0;
  z-index: 2;
  @include flex(lr);
  padding: 0 32rpx;

  text {
    width: 334rpx;
    height: 84rpx;
    border-radius: 42rpx;
    @include border_theme;
    font-size: 32rpx;
    @include flex;

    &.a {
      @include font_theme;
    }

    &.b {
      @include bg_theme;
      color: #fff;
    }
  }
}
</style>
