<template>
  <!-- 检查中心-->
  <view class="check_detail">
    <!-- 药店图标 -->
    <img
      v-img="detail.organImg"
      v-if="detail.organImg"
      :data-src="err"
      alt=""
      class="poster"
    />
    <image
      class="poster"
      v-else
      src="/static/images/Pharmacy-default.png"
    ></image>
    <!-- 右侧 -->
    <view class="detail">
      <view class="pharmacy_name">
        {{ detail.organName || detail.lisOrgName }}
      </view>
      <view class="pharmacy_area">
        {{ detail.provinceName }} {{ detail.cityName }}
      </view>
      <view class="pharmacy_address" @click="click">
        <text> {{ detail.area }} {{ detail.address }} </text>
        <uni-icons
          type="arrowright"
          color="#666"
          size="16"
          v-if="showRight"
        ></uni-icons>
      </view>
    </view>
    <!-- 底部按钮 -->
    <view class="pharmacy_buts">
      <text @click.stop="callTel">联系中心</text>
      <text @click.stop="toAddress">去中心</text>
    </view>
  </view>
</template>

<script>
export default {
  name: "CheckAddress",
  props: {
    detail: {
      type: Object,
      default: () => {
        return {
          drugstoreName: "",
          provinceName: "",
          cityName: "",
          area: "",
          address: "",
        };
      },
    },
    showRight: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      err: require("../../../static/images/Pharmacy-default.png"),
    };
  },
  methods: {
    callTel() {
      uni.makePhoneCall({
        phoneNumber: this.detail.telNo,
      });
    },
    click() {
      if (this.showRight) {
        this.$emit("click");
      }
    },
    toAddress() {
      uni.navigateTo({
        url: `../address/goAddress?address=${JSON.stringify(this.detail)}`,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}

.check_detail {
  width: 100%;
  @include flex(lr);
  flex-wrap: wrap;
  padding: 24rpx 0;
  background-color: #fff;
  overflow: hidden;

  .poster {
    width: 140rpx;
    height: 140rpx;
    border-radius: 8rpx;
    flex: none;
  }

  .detail {
    flex: 1;
    padding-left: 26rpx;
    // height: 140rpx;
    min-height: 140rpx;
    @include flex(lr);
    flex-direction: column;
    align-items: stretch;
    overflow: auto;

    .pharmacy_name {
      @include text;
      font-size: 28rpx;
    }

    .pharmacy_area {
      @include flex(lr);
      font-size: 26rpx;
      color: #666;
    }

    .pharmacy_address {
      @include flex(lr);
      font-size: 26rpx;
      color: #666;
    }
  }

  .pharmacy_buts {
    width: 100%;
    padding-top: 24rpx;
    @include flex(right);

    text {
      width: 160rpx;
      height: 60rpx;
      border-radius: 30rpx;
      @include flex;
      font-size: 28rpx;
      @include font_theme;
      @include border_theme;
      margin-left: 16rpx;
    }
  }
}
</style>
