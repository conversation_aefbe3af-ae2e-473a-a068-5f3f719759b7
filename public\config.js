/*
 * @Descripttion:
 * @version:
 * @Author: zhengyangyang
 * @Date: 2025-06-14 09:44:17
 * @LastEditors: zhengyangyang
 * @LastEditTime: 2025-07-09 17:10:16
 */
const CONFIG_ENV = {
  // 来源
  VUE_APP_FIRMID: 'prod',
  // 标题
  VUE_APP_TITLE: '互联网医院',
  // 环信
  // VUE_APP_HXKEY: '1151240618150120#sz-hospital',
  VUE_APP_HXKEY: '1151240618150120#demo',
  // 声网
  VUE_APP_AGORA: '********************************',
  // 请求域名
  // VUE_APP_SERVE: 'https://dev.imizan.cn/his/',
  VUE_APP_SERVE: 'https://szhy.dchealthcloud.com/his/',
  // VUE_APP_AISERVE: 'https://szhy.dchealthcloud.com',
  // VUE_APP_SERVE: "http://*************:8101/",
  // VUE_APP_SERVE: 'https://sz.dhchospital.com/his/',
  // VUE_APP_SERVE: 'http://*************:8101/',
  // oss请求
  // VUE_APP_OSS: "https://sz.dhchospital.com/oss/",
  VUE_APP_OSS: 'https://szhy.dchealthcloud.com/oss/',
  // VUE_APP_OSS: 'http://**************:8300',
  // VUE_APP_OSS: 'http://*************:8300/',
  // 项目目录
  VUE_APP_ROOTURL: 'https://test.imizan.cn/cloud/cloudHosPatient/index.html',
  // logo图标
  VUE_APP_SHARE: 'https://szhy.dchealthcloud.com/user/logo.png',
  // 腾讯地图key
  VUE_APP_MAPKEY: 'HYRBZ-LHUC7-O3PXV-PNPAN-7VEOS-K6FQ5',
  //公众号appid
  APPID: 'wx54529fae788f1776',
}

// var domain = "https://his.qtykhos.com";
// const CONFIG_ENV = {
//     // 来源
//     VUE_APP_FIRMID: 'qtyk',
//     // 标题
//     VUE_APP_TITLE: '启泰元康互联网医院',
//     // 环信
//     VUE_APP_HXKEY: '1111230129172590#his',
//     // 声网
//     VUE_APP_AGORA: '********************************',
//     // 请求域名
//     VUE_APP_SERVE: domain+'/his/',
//     // oss请求
//     VUE_APP_OSS: domain+'/oss/',
//     // 项目目录
//     VUE_APP_ROOTURL: domain+'/cloud/cloudHosPatient/index.html',
//     // logo图标
//     VUE_APP_SHARE: domain+'/cloud/logo_pat.png',
//     // 腾讯地图key
//     VUE_APP_MAPKEY: '6NCBZ-ZLZL3-73Q3T-YNECD-7VMSO-LTFBE',
// };
