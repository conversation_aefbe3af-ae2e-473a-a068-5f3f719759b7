<template>
  <!-- 本次处方 -->
  <view class="details">
    <view class="medRecordCard">
      <!-- 头部 -->
      <Docter :infoDetail="docInfo" />

      <!-- 简单信息 -->
      <view class="patientInfo">
        <view class="name">
          <view class="label"> 就诊人 </view>
          <view class="content">
            {{ infoDetail.patientName }}
          </view>
        </view>
        <view class="name">
          <view class="label"> 问诊时间 </view>
          <view class="content">
            {{ infoDetail.signTime }}
          </view>
        </view>
        <view class="name">
          <view class="label"> 主诉 </view>
          <view class="content">
            {{ infoDetail.recordsTitle }}
          </view>
        </view>
      </view>

      <!-- 处方简要 -->
      <view
        class="list_detail"
        v-for="(item, index) in prescriptionList"
        :key="index"
        @click="openDetail(item)"
      >
        <!-- 未支付 且不可见 -->
        <block
          v-if="
            hideDetail.includes(item.proBusinessInfo.status) &&
              item.beforePayVisible == 0
          "
        >
          <!-- 业务编号 -->
          <view class="detail_title">
            <text class="title_label">处方编号</text>
            <text class="title_num">{{
              item.proBusinessInfo.businessCode
            }}</text>
            <!-- 待支付 -->
            <text class="title_status">{{
              item.proBusinessInfo.statusFont
            }}</text>
          </view>
          <!-- 处方诊断 -->
          <view class="detail_diag">
            <text class="diag_label">处方诊断</text>
            <text class="diag_value">
              <!-- 诊断数组 -->
              <block v-for="(it, i) in item.diags">
                {{ it.diagNamePrefix }}{{ it.diagName }}{{ it.diagNameSuffix }}
              </block>
            </text>
          </view>
          <!-- 费用明细 -->
          <view class="detail_price">
            <block v-for="(it, i) in item.prescriptions" :key="i">
              <!-- 西药 -->
              <view
                class="detail_item"
                v-if="
                  it.proPrescriptionMasterVO.prescriptionType == '1' ||
                    it.proPrescriptionMasterVO.prescriptionType == '2' ||
                    it.proPrescriptionMasterVO.prescriptionType == '4'
                "
              >
                <!-- 西药 -->
                <view class="item_label">{{
                  it.proPrescriptionMasterVO.prescriptionTypeName
                }}</view>
                <!-- 数量 -->
                <view class="item_num">共{{ it.details.length }}种药品</view>
                <!-- 总价 -->
                <view class="item_price"
                  >￥{{ it.proPrescriptionMasterVO.cost | toFixed }}</view
                >
              </view>
              <!-- 中药 -->
              <block v-if="it.proPrescriptionMasterVO.prescriptionType == '3'">
                <view class="detail_item">
                  <view class="item_label">{{
                    it.proPrescriptionMasterVO.prescriptionTypeName
                  }}</view>
                  <view class="item_num">共{{ it.details.length }}种药品</view>
                  <view class="item_price">
                    <text
                      >￥{{ it.proPrescriptionMasterVO.cost | toFixed }}</text
                    >
                  </view>

                  <!-- 用法 -->
                  <view class="detail_item_use" v-if="false">
                    <view class="use_label">用法：</view>
                    <view class="use_cont"
                      >{{
                        it.proPrescriptionMasterVO.dduName || '口服'
                      }}，每日{{ it.proPrescriptionMasterVO.rc }}次，每次{{
                        it.proPrescriptionMasterVO.rj
                      }}剂，用药{{ it.proPrescriptionMasterVO.days }}天</view
                    >
                  </view>
                </view>
              </block>
            </block>
          </view>
          <!-- 药品数量 -->
          <view class="detail_number" v-if="false"
            >共{{ getLength(item.prescriptions) }}种药品</view
          >
          <!-- 价位总计 -->
          <view class="totalPrice">
            <view class="price"
              >总价:
              <text style="color: #ff0000"
                >￥{{ item.proBusinessInfo.totalMoney | toFixed }}</text
              ></view
            >
            <view class="price"
              >优惠:
              <text style="color: #ff0000"
                >￥{{ item.proBusinessInfo.preferentialAmount | toFixed }}</text
              ></view
            >
            <view class="price"
              >实付款:
              <text style="color: #ff0000"
                >￥{{ item.proBusinessInfo.orderMoney | toFixed }}</text
              ></view
            >
          </view>
        </block>
      </view>

      <!-- 详细列表 -->
      <view v-if="prescriptionList.length">
        <view
          v-for="(cfItem, cfIndex) in prescriptionList"
          :key="cfIndex"
          class="cfItem"
        >
          <!-- 支付状态 或支付前可见 -->
          <block
            v-if="
              !hideDetail.includes(cfItem.proBusinessInfo.status) ||
                (hideDetail.includes(cfItem.proBusinessInfo.status) &&
                  cfItem.beforePayVisible == 1)
            "
          >
            <view @click="openDetail(cfItem)">
              <view class="orderTitle">
                <view class="orderNum">
                  <img src="/static/doc/n6.png" alt="" style="margin-right: 7px">
                  <text>处方编号</text>
                  {{ cfItem.proBusinessInfo.businessCode }}
                </view>
                <view class="orderStatus">{{
                  cfItem.proBusinessInfo.statusFont
                }}</view>
              </view>
              <!-- 处方诊断 -->
              <view class="detail_diag">
                <text class="diag_label">处方诊断</text>
                <text class="diag_value">
                  <!-- 诊断数组 -->
                  <block v-for="(it, i) in cfItem.diags">
                    {{ it.diagNamePrefix }}{{ it.diagName
                    }}{{ it.diagNameSuffix }}
                  </block>
                </text>
              </view>
              <view
                class="cfDetail"
                v-for="(item, index) in cfItem.prescriptions"
                :key="index"
              >
                <view
                  class="xyCf"
                  v-if="
                    item.proPrescriptionMasterVO.prescriptionType == '1' ||
                      item.proPrescriptionMasterVO.prescriptionType == '2' ||
                      item.proPrescriptionMasterVO.prescriptionType == '4'
                  "
                >
                  <view class="title"
                    >Rp
                    <text>{{
                      item.proPrescriptionMasterVO.prescriptionTypeName
                    }}</text></view
                  >

                  <!-- 西药列表 -->
                  <view
                    v-for="(xyItem, xyIndex) in item.details"
                    :key="xyIndex"
                  >
                    <view
                      style="
                        display: flex;
                        align-items: start;
                        margin-top: 24rpx;
                      "
                    >
                      <view style="width: 100%">
                        <view class="drugName">
                          <view
                            >{{ xyIndex + 1 }}.{{ xyItem.drugName }}({{
                              xyItem.gg
                            }})</view
                          >
                        </view>
                        <view class="drugInfo">
                          <view
                            >用法：{{ xyItem.dduName || '口服' }}，每次{{
                              xyItem.eachQuan
                            }}{{ xyItem.eachUnit }}，{{
                              xyItem.ddufName
                            }}，用药{{ xyItem.days }}天</view
                          >
                          <view class="price"
                            >价格：<text style="color: rgba(255, 87, 51, 1)">￥{{ xyItem.price }}</text>
                            <text class="n8">x {{ xyItem.quan }}</text>
                          </view>
                          <view v-if="xyItem.memo"
                            >说明：{{ xyItem.memo }}</view
                          >
                        </view>
                      </view>
                    </view>
                  </view>
<!--                  <view class="xy_total">-->
<!--                    合计：<text-->
<!--                      >￥{{-->
<!--                        item.proPrescriptionMasterVO.totalMoney | toFixed-->
<!--                      }}</text-->
<!--                    >-->
<!--                  </view>-->
                </view>

                <view
                  class="cyCf"
                  v-if="item.proPrescriptionMasterVO.prescriptionType == '3'"
                >
                  <view class="title"
                    >Rp
                    <text>{{
                      item.proPrescriptionMasterVO.prescriptionTypeName
                    }}</text></view
                  >
                  <label
                    class="uni-list-cell uni-list-cell-pd"
                    v-for="(xyItem, xyIndex) in item.details"
                    :key="xyIndex"
                  >
                    <view class="drugName">
                      <view
                        >{{ xyIndex + 1 }}.{{ xyItem.drugName }}({{
                          xyItem.gg
                        }})</view
                      >
                    </view>
                    <view class="drugInfo">
                      <view class="price"
                        >价格：￥{{ xyItem.price }}
                        <text>x{{ xyItem.eachQuan }}</text></view
                      >
                      <view v-if="xyItem.memo">说明：{{ xyItem.memo }}</view>
                    </view>
                  </label>
                  <view class="useMethods"
                    >用法：{{
                      item.proPrescriptionMasterVO.dduName || '口服'
                    }}，每日{{ item.proPrescriptionMasterVO.rc }}次，每次{{
                      item.proPrescriptionMasterVO.rj
                    }}剂，用药{{ item.proPrescriptionMasterVO.days }}天</view
                  >

                  <view class="djCf">
                    共{{ item.proPrescriptionMasterVO.herbalNum }}付
                  </view>

                  <view class="djCf" v-if="cfItem.proBusinessInfo.isDj == '1'">
                    代煎费：￥{{
                      item.proPrescriptionMasterVO.djCost | toFixed
                    }}
                  </view>
                  <view class="total">
                    合计：<text
                      >￥{{
                        item.proPrescriptionMasterVO.totalMoney | toFixed
                      }}</text
                    >
                  </view>
                </view>
              </view>
            </view>

            <view class="totalPrice">
              <view class="price"
                >合计:
                <text style="color: #ff0000"
                  >￥{{ cfItem.proBusinessInfo.totalMoney | toFixed }}</text
                ></view
              >
              <view class="price"
                >优惠:
                <text style="color: rgba(255, 195, 0, 1)"
                  >￥{{
                    cfItem.proBusinessInfo.preferentialAmount | toFixed
                  }}</text
                ></view
              >
              <view class="price"
                >实付:
                <text style="color: #ff0000"
                  >￥{{ cfItem.proBusinessInfo.orderMoney | toFixed }}</text
                ></view
              >

              <!-- 按钮显示 -->
              <view class="btns" v-if="false">
                <template v-for="element in cfItem.proBusinessInfo.btns">
                  <view class="btn" @click="btnClick(cfItem, element)">{{
                    element
                  }}</view>
                </template>
              </view>
            </view>
          </block>
        </view>
      </view>

      <!-- 空白 -->
      <view v-if="!prescriptionList.length" class="empty">
        <image
          src="../../static/images/index/recipe_empty.png"
          mode=""
          class="emptyIcon"
        ></image>
        <view>医生暂未给您开方，快联系医生开方</view>
      </view>
    </view>

    <!-- 取药码 -->
    <view class="qrcode-container" v-if="isShowEwm" @click="closeEwm">
      <view class="qrcode-content">
        <view class="title"> 取药码 </view>
        <view class="qrcode">
          <view class="qrimg">
            <tkiQrcode
              ref="qrcode"
              :val="codeUrl"
              :size="size"
              background="#ffffff"
              foreground="#000000"
              pdground="#000000"
              icon="/static/images/logo-img.png"
              :iconSize="iconsize"
              :onval="onval"
              :loadMake="loadMake"
            ></tkiQrcode>
          </view>
          <view class="qrcode-toast" v-if="false">{{ codeUrl }}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { Toast } from '@/common/js/pay.js';
import Docter from '@/components/doctor_header/doctor_header.vue';
import { getDocInfoById } from '@/api/chatCardDetail';
import {
  getPatientPrescriptionBusinessOrderInfo,
  getRegInfoByID,
} from '@/api/cf';
import { confirmGoods, getLogisticsInfo, queryGetMedicine } from '@/api/order';
import tkiQrcode from '@/components/tki-qrcode/tki-qrcode.vue';
import myJsTools from '@/common/js/myJsTools.js';
export default {
  components: {
    tkiQrcode,
    Docter,
  },
  data() {
    return {
      // 不可显示详细药品的状态
      hideDetail: ['0', '6', '7', '8', '9', '10'],
      regId: '',
      docInfo: {},
      infoDetail: {},
      prescriptionList: [],
      docId: '',
      // 取药码
      isShowEwm: false,
      codeUrl: '',
      size: 300,
      iconsize: 45,
      onval: true,
      loadMake: true,
    };
  },
  onLoad(options) {
    this.regId = options.regId;
    this.docId = options.docId;
    this.getDocInfo();
    this.getInfo();
  },
  onShow() {
    // 移除地址缓存
    uni.removeStorageSync('nowAddress');
    // 获取处方列表
    this.getList();
  },
  methods: {
    // 加法
    addNum(i, n) {
      return (i * 100 + n * 100) / 100;
    },
    // 获取药品总数量
    getLength(arr) {
      let n = 0;
      arr.forEach((v) => {
        n += v.details.length;
      });

      return n;
    },

    // 获取医生信息
    async getDocInfo() {
      let { data: obj } = await getDocInfoById({
        docId: this.docId,
      });
      // this.docInfo.docProf = .docProf
      if (obj.lableName) {
        obj.docLable = obj.lableName.split(',');
      }
      if (obj.docImg) {
        myJsTools.downAndSaveImg(obj.docImg, (url) => {
          obj.docImg = url;
        });
      }
      this.docInfo = obj;
    },

    // 获取挂号信息
    getInfo() {
      getRegInfoByID({
        regId: this.regId,
      }).then((res) => {
        this.infoDetail = res.data;
      });
    },

    // 获取处方列表
    async getList() {
      let { data } = await getPatientPrescriptionBusinessOrderInfo({
        regId: this.regId,
      });
      data.forEach((v) => {
        let oStatus = v.proBusinessInfo.status;
        let arr = ['0', '7', '8'];
        let status = v.proBusinessOrderInfo.orderStatus;
        let deliveryType = v.proBusinessOrderInfo.deliveryType;

        if (arr.includes(oStatus)) {
          status = 0;
        }
        switch (Number(status)) {
          case 0:
            v.proBusinessInfo.statusFont = '待支付';
            v.proBusinessInfo.btns = ['去支付'];
            break;
          case 1:
            v.proBusinessInfo.statusFont =
              deliveryType == 2 ? '待取药' : '待发货';
            v.proBusinessInfo.btns = [];
            break;
          case 3:
            v.proBusinessInfo.statusFont = '待收货';
            v.proBusinessInfo.btns = [];
            break;
          case 5:
            v.proBusinessInfo.statusFont =
              deliveryType == 2 ? '已取药' : '已收货';
            v.proBusinessInfo.btns = [];
            break;
          case 9:
            v.proBusinessInfo.statusFont = '已失效';
            v.proBusinessInfo.btns = [];
            break;
          case 10:
            v.proBusinessInfo.statusFont = '已退费';
            v.proBusinessInfo.btns = [];
            break;
        }
      });
      this.prescriptionList = data;
    },
    // 去详情
    openDetail(item) {
      uni.removeStorageSync('nowAddress');
      let { businessId, status } = item.proBusinessInfo;
      let arr = ['0', '7', '8'];
      // 未支付
      if (arr.includes(status)) {
        wx.navigateTo({
          url:
            '/pages/prescription/prescriptionDetail?businessId=' + businessId,
        });
      } else {
        wx.navigateTo({
          url: '/pages/prescription/preDetail?businessId=' + businessId,
        });
      }
    },

    // 按钮点击
    async btnClick(evt, val) {
      let cfItem = evt;
      let btnValue = val;
      let { orderBussId, status, businessId } = cfItem.proBusinessInfo;
      if (status == 0 || status == 9) {
        uni.navigateTo({
          url:
            '/pages/prescription/prescriptionDetail?businessId=' + businessId,
        });
        return;
      }

      if (status == 1) {
        if (btnValue == '催物流') {
          Toast('已催物流');
        }
        return;
      }

      if (status == 2) {
        if (btnValue == '取药码') {
          this.getQrcode(cfItem);
        }
        return;
      }

      if (status == 3) {
        if (btnValue == '确认收货') {
          await confirmGoods({
            businessId: orderBussId,
          });
          Toast('收货成功');
          this.getList();
          return;
        }
        if (btnValue == '查看物流') {
          // 查询物流单号
          let res = await getLogisticsInfo({
            businessId: orderBussId,
          });
          let logisticsCode = res.data.logisticsCode;
          // 患者手机号
          let tel = res.data.deliveryTelNo || '1234';
          tel = tel.slice(-4);
          // 物流名称
          let name = res.data.logisticsName;

          if (!logisticsCode) {
            Toast('暂无物流信息');
            return;
          }

          uni.navigateTo({
            url:
              '/pages/personalCenter/myOrder/allOrder/prescriptionOrder/logistics?code=' +
              logisticsCode +
              '&tel=' +
              tel +
              '&name=' +
              name,
          });
        }
        return;
      }

      if (status == 7 || status == 8) {
        uni.navigateTo({
          url:
            '/pages/prescription/prescriptionDetail?businessId=' + businessId,
        });
        return;
      }
    },

    // 取药码
    async getQrcode(item) {
      let id = item.proBusinessInfo.orderBussId;
      let res = await queryGetMedicine(id);
      if (!res.data) {
        Toast('未能获取取药码');
        return;
      }
      // 如果不为空
      if (res.data.drugCode) {
        this.codeUrl = res.data.drugCode;
        this.isShowEwm = true;
        return;
      }
    },

    // 点击关闭取药码
    closeEwm() {
      this.isShowEwm = false;
    },
  },
};
</script>

<style lang="scss" scoped>
page {
  background-color: rgba(240, 242, 252, 1);
}
.doctor_box_top{
  border-bottom-left-radius: 20px !important;
  border-bottom-right-radius: 20px !important;
  box-shadow: 0px 1px 4px  rgba(121, 72, 234, 0.15);
  padding-left: 40px !important;
}
.medRecordCard {
  margin: 0 0rpx;
  padding-bottom: 24upx;

  .doctor_box_top {
    padding: 24upx 28upx;
    border-radius: 16upx;
  }
}

.patientInfo {
  width: 90%;
  margin: auto;
  box-shadow: 0px 1px 4px  rgba(121, 72, 234, 0.15);
  color: rgba(102, 102, 102, 1) !important;
  .name {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0 32rpx;
    padding: 20rpx 0;
    font-size: 12px !important;
    .label {
      width: 150upx;
      flex: none;
    }
  }

  .name:last-child {
    border: none;
  }
}

.patientInfo {
  background: #ffffff;
  border-radius: 16rpx;
  color: #333333;
  font-size: 28rpx;
  margin-top: 24rpx;
}

.empty {
  text-align: center;
  color: #333333;
  font-size: 28rpx;
  line-height: 40rpx;

  image {
    width: 386rpx;
    height: 324rpx;
    margin: 40rpx 0;
  }
}

.cfItem {
  border-radius: 12rpx;
  background: #ffffff;
  box-shadow: 0px 1px 4px  rgba(121, 72, 234, 0.15);
  width: 90%;
  margin: auto;
  margin-top: 24rpx;

}

.cfDetail {
  padding: 0 32rpx;
  background: #ffffff;
  .title {
    color: rgba(102, 102, 102, 1);
    font-size: 12px;
    position: relative;
    padding: 20rpx 0rpx 20rpx 0;
    @include flex(lr);
    border-bottom:1px solid rgba(229, 229, 229, 1);
  }

  .drugName {
    display: flex;
    align-items: center;
    color: rgba(131, 106, 255, 1) !important;
    font-size: 12px;
    font-weight: 600;
    margin-top: 10rpx;

    uni-checkbox {
      margin-right: 20rpx;
    }
  }
  .n8{
    width: 28px;
    height: 20px;
    opacity: 1;
    border-radius: 4px;
    background: rgba(240, 240, 240, 1);
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 12px;
  }
  .drugInfo {
    color: rgba(51, 51, 51, 1);
    font-size: 12px;

    view {
      margin-top: 16rpx;
      @include flex(lr);
    }

    .total {
      padding-bottom: 24rpx;

      text {
        color: #ff0000;
      }

      text-align: right;
    }
  }

  &:last-child {
    .xyCf {
      border-bottom: none;
    }
  }

  .xyCf {
    border-bottom: 1rpx dashed #ebebeb;
  }

  .cyCf {
    color: #333333;
    font-size: 28rpx;
    border-bottom: 1rpx solid #ebebeb;

    .drugName {
      margin-top: 18rpx;
    }

    .total {
      font-size: 28rpx;
      padding-bottom: 24rpx;

      text {
        color: #ff0000;
      }

      text-align: right;
    }

    .price {
      position: relative;
      @include flex(lr);
    }

    .useMethods,
    .djCf {
      margin: 16rpx 0;
    }
  }
}

.xy_total {
  text-align: right;
  font-size: 28upx;
  color: #333;
  padding: 20upx 0;
  text {
    color: red;
  }
}

.orderTitle {
  font-size: 28rpx;
  color: #333333;
  height: 80rpx;
  margin: 0 32rpx;
  @include flex(lr);
  background: #ffffff;
  border-bottom: 1px solid  rgba(229, 229, 229, 1);;
  .orderNum {
    // width: 80%;
    @include flex;
    font-weight: bold;
    font-size: 12px;
    text {
      width: 150upx;
    }
  }

  .orderStatus {
    text-align: right;
    width: 20%;
    color: red;
    font-weight: bold;
  }
}

.totalPrice {
  font-size: 12px;
  color: rgba(102, 102, 102, 1);
  @include flex(lr);
  padding: 26rpx 0;
  margin: 0 32rpx;
  border-top: 1px solid #f5f5f5;
  margin-top: 10rpx;

  view {
    margin-left: 24rpx;

    text {
      color: red;
      font-weight: bold;
      padding-left: 10rpx;
      font-size: 16px;
    }
  }

  .btns {
    display: flex;
    flex-direction: row-reverse;
    align-items: center;
  }

  .btns .btn {
    width: 158rpx;
    height: 56rpx;
    @include bg_theme;
    border-radius: 28rpx;
    font-size: 26rpx;
    font-weight: 500;
    color: #ffffff;
    line-height: 56rpx;
    text-align: center;
  }

  .btns .btn:first-child {
    margin-left: 24rpx;
  }

  .btns .btn:nth-child(2) {
    background-color: #ffffff;
    @include border_theme;
    @include font_theme;
  }
}

/* 取药号二维码  S*/
.qrcode-container {
  position: fixed;
  width: 100%;
  height: 100vh;
  left: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
}

.qrcode-container .qrcode-content {
  width: 544rpx;
  height: 588rpx;
  background: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  text-align: center;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.qrcode-container .qrcode-content .title {
  width: 100%;
  height: 116rpx;
  @include bg_theme;
  line-height: 116rpx;
  font-size: 36rpx;
  font-weight: 500;
  color: #ffffff;
  display: flex;
  justify-content: center;
}

.qrcode {
  margin-top: 70rpx;
}

.qrcode-toast {
  margin-top: 50rpx;
  font-size: 36rpx;
  font-weight: 400;
  color: #333333;
  line-height: 50rpx;
}

/* 取药号二维码  E*/

// 处方简要信息
.list_detail {
  background-color: #fff;
  border-radius: 16upx;
  padding: 0 32upx;

  margin-top: 24upx;

  .totalPrice {
    margin: 0;
  }

  .detail_diag {
    padding-left: 0;
    padding-right: 0;
    border-bottom: 1px solid  rgba(229, 229, 229, 1);
  }

  // 订单编号
  .detail_title {
    @include flex(lr);
    font-size: 28upx;
    height: 92upx;
    border-bottom: 1px solid $k-hr-color;

    .title_label {
      width: 150upx;
      flex: none;
      color: $k-title;
    }

    .title_num {
      flex: 1;
      text-align: left;
    }

    .title_status {
      flex: none;
      color: red;
      font-weight: bold;
    }
  }

  // 费用明细
  .detail_price {
    .price_title {
      height: 92upx;
      @include flex(left);
      font-size: 28upx;
      color: $k-title;
    }

    // 药品
    .detail_item {
      padding: 24upx;
      background-color: #f5f5f5;
      font-size: 28upx;
      color: $k-title;
      @include flex(lr);
      flex-wrap: wrap;
      margin-bottom: 24upx;
      border-radius: 8upx;

      &:last-child {
        margin-bottom: 0;
      }

      .item_label {
        width: 126upx;
        flex: none;
      }

      .item_num {
        flex: 1;
      }

      .item_price {
        color: red;
      }

      // 使用方法
      .detail_item_use {
        width: 100%;
        @include flex(lr);
        padding-top: 10upx;

        .use_label {
          width: 126upx;
        }

        .use_cont {
          flex: 1;
        }
      }
    }
  }

  // 药品数量
  .detail_number {
    @include flex(right);
    height: 92upx;
    font-size: 28upx;
    color: $k-title;
    border-bottom: 1px solid $k-hr-color;
  }

  // 底部
  .detail_footer {
    @include flex(lr);
    height: 92upx;

    .footer_left {
      color: $k-title;
      font-size: 28upx;

      .left_red {
        color: $k-red-color;
      }
    }

    .footer_right {
      margin: 0;
      padding: 0;
      width: 160upx;
      height: 60upx;
      border-radius: 30upx;
      font-size: 28upx;
      color: #fff;
      @include flex;
      @include bg_theme;

      &.cancel {
        background-color: #d8d8d8;
      }
    }
  }
}

// 诊断
.detail_diag {
  min-height: 80upx;
  @include flex(left);
  font-size: 12px;
  color: rgba(102, 102, 102, 1);
  padding: 10upx 32rpx;
  box-sizing: border-box;
  border-bottom: 1px solid  rgba(229, 229, 229, 1);
  .diag_label {
    width: 150upx;
    flex: none;
  }

  .diag_value {
    flex: 1;
  }
}
</style>
