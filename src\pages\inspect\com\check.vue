<template>
  <!-- 检查中心 -->
  <view class="check">
    <!-- 标题 -->
    <TITLE :title="isLis ? '检验中心' : '检查中心'" />

    <!-- 选择检查中心 -->
    <view class="sele" @click="click" v-if="!check.address">
      <text>选择{{ isLis ? '检验' : '检查' }}中心</text>
      <uni-icons type="arrowright" color="#666" />
    </view>

    <!-- 地址 -->
    <ADDRESS
      :showRight="showRight"
      v-if="check.organName || check.lisOrgName"
      @click="click"
      :detail="check"
    />

    <!-- 提示 -->
    <view class="tip">
      <text class="label">提示</text>
      <view class="tip_cont">
        <view class="text"
          >请选择您想要进行{{
            isLis ? '检验的检验中心' : '检查的检查中心'
          }}</view
        >
        <view class="text"
          >不同的{{
            isLis ? '检验中心的检验' : '检查中心的检查'
          }}项目价格会有差异，请以实际支付价格为准</view
        >
      </view>
    </view>
  </view>
</template>

<script>
import TITLE from './itemTitle.vue';
import ADDRESS from './address.vue';

export default {
  name: 'Check',
  props: {
    // 是否检验
    isLis: {
      type: Boolean,
      default: false,
    },
    check: {
      type: Object,
      default: () => {},
    },
    showRight: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    TITLE,
    ADDRESS,
  },
  methods: {
    click() {
      this.$emit('click');
    },
  },
};
</script>

<style lang="scss" scoped>
.check {
  padding: 24rpx 32rpx 32rpx;
  background: #fff;
  border-radius: 8rpx;
  margin-top: 16rpx;

  .sele {
    height: 80rpx;
    @include flex(lr);
    font-size: 28rpx;
    color: #333;
  }

  .tip {
    @include flex(lr);
    align-items: flex-start;

    .label {
      width: 120rpx;
      flex: none;
      font-size: 24rpx;
      color: #333;
    }

    .tip_cont {
      flex: 1;
      font-size: 24rpx;
      line-height: 38rpx;

      .text {
        color: #999;
      }
    }
  }
}
</style>
