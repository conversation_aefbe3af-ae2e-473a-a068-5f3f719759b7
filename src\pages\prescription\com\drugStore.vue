<template>
  <!-- 药店信息-->
  <div class="drug_address">
    <div class="address">
      <!-- 左侧 -->
      <view class="left">
        <view class="left_title">{{ detail.drugstoreName }}</view>
        <view class="info" v-if="detail.drugstoreProvince"
          >{{ detail.drugstoreProvince }} {{ detail.drugstoreCity }}
        </view>
        <view class="info" v-if="detail.provinceName"
          >{{ detail.provinceName }} {{ detail.area }}
          {{ detail.address }}</view
        >
        <!-- <view class="info">{{ detail.addressDetail }}</view> -->
        <view class="info">{{ detail.drugstoreAddress }}</view>
      </view>
      <!-- 右侧 -->
      <view class="right">
        <view class="item" @click="call">
          <image class="icon" src="/static/inspection/call.png" />
          <text>打电话</text>
        </view>
        <view class="item" @click="toAddress">
          <image class="icon" src="/static/inspection/lx.png" />
          <text>去这里</text>
        </view>
      </view>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DrugAddress',
  props: {
    detail: {
      type: Object,
      default: () => {
        return {
          drugstoreName: '',
          drugstoreProvince: '',
          drugstoreCity: '',
          drugstoreArea: '',
          //addressDetail: '',
          drugstoreAddress:''
        };
      },
    },
    showRight: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    call() {
      uni.makePhoneCall({
        phoneNumber: this.detail.drugstoreTelNo || this.detail.telNoSh,
      });
    },
    toAddress() {
      console.log('qqqqq')
      // const { latitude, longitude } = this.detail;
      // uni.openLocation({
      //   latitude: Number(latitude),
      //   longitude: Number(longitude),
      //   success: function() {},
      //   fail(err) {},
      // });
      uni.navigateTo({
        url: `../address/goAddress?address=${JSON.stringify(this.detail)}`,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}

.drug_address {
  width: 100%;
  margin-top: 24rpx;
  border-radius: 8rpx;
  padding: 32rpx;
  background-color: #fff;
  overflow: hidden;

  .address {
    @include flex(lr);
    padding-top: 10rpx;

    .left {
      flex: 1;
      padding-right: 30rpx;

      .left_title {
        color: #333;
        font-size: 28rpx;
        line-height: 54rpx;
      }

      .info {
        font-size: 26rpx;
        color: #666;
        line-height: 40rpx;
      }
    }

    .right {
      flex: none;
      @include flex(lr);
      position: relative;
      padding-left: 30rpx;

      &::after {
        content: '';
        display: block;
        position: absolute;
        width: 4rpx;
        height: 100%;
        background: #d8d8d8;
        left: 0;
        top: 0;
        border-radius: 2rpx;
      }

      .item {
        @include flex;
        flex-direction: column;

        &:last-child {
          margin-left: 16rpx;
        }

        .icon {
          width: 44rpx;
          height: 44rpx;
        }

        text {
          @include font_theme;
          padding-top: 10rpx;
          font-size: 24rpx;
        }
      }
    }
  }
}
</style>
