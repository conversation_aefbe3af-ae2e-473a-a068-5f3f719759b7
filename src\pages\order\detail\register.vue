<template>
  <!-- 挂号订单详情 -->
  <view class="register">
    <!-- 状态 -->
    <STATUS
      :time="detail.appointSignTime"
      :queueNumber="detail.queueNumber"
      :num="num"
      :addTime="timeStr"
      :status="status"
      :detail="detail"
    />

    <!-- 价格相关 -->
    <view class="price_info" @click="toDetail">
      <!-- 顶部 -->
      <view class="title">
        <text>{{ detail.docName }}({{ detail.deptName }})</text>
        <image src="/static/register/gh_act.png" class="icon" />
      </view>

      <!-- 文案 -->
      <view class="info_text">
        <view class="item_info"> 医院：{{ detail.hosName }} </view>

        <view class="item_info">
          号别：{{ detail.dntName }}
          <uni-icons type="arrowright" color="#666" />
        </view>

        <view class="item_info">
          问诊类型：{{ detail.isOffLine == 0 ? '在线' : '线下'
          }}{{ detail.receiveType }}
        </view>

        <view class="item_info"> 就诊人：{{ detail.patientName }} </view>
      </view>

      <!-- 金额 -->
      <view class="info_price">
        <!-- 单个 -->
        <view class="item_info">
          <text class="sub">合计金额</text>
          <text>￥{{ detail.totalPay }}</text>
        </view>

        <view class="item_info">
          <text class="sub">医保类型</text>
          <text>自费</text>
        </view>

        <view class="item_info">
          <text class="bold">实付款</text>
          <text class="num">￥{{ detail.orderMoney }}</text>
        </view>
      </view>
    </view>

    <!-- 订单信息 -->
    <ORDER :info="detail" />

    <FOOTER @click="toPay" v-if="status == 1">去支付</FOOTER>
  </view>
</template>

<script>
import STATUS from './register/status.vue';
import ORDER from './lisCom/order.vue';
import FOOTER from '@/components/footer_button/button.vue';
import { queryRegisterPayStatus, myRegQueue } from '@/api/base';
import { pregOrderDetailNew } from '@/api/order';
import { wxPay, Toast } from '@/common/js/pay';
let timer;
let num = 3;

function setTimeStr(val) {
  if (!val) return 0;
  val = val.replace(/-/g, '/');
  let start = new Date(val).getTime();
  let end = new Date().getTime();
  let c = 1800 - (end - start) / 1000;
  if (c <= 0) return 0;
  let m = parseInt((c / 60) % 60);
  let s = parseInt(c % 60);
  if (m < 10) m = '0' + m;
  if (s < 10) s = '0' + s;
  return m + '分' + s + '秒';
}

export default {
  name: 'Register',
  components: {
    STATUS,
    ORDER,
    FOOTER,
  },
  data() {
    return {
      orderNo: '',
      signTime: '',
      detail: {},
      num: '',
      status: '1',
      callId: '',
      pays: [],
      ghId: '',
      timeStr: '',
      isFrist: true,
    };
  },
  onLoad(opt) {
    this.orderNo = opt.orderNo;
    this.getDetail();
    this.isFrist = false;
  },
  onShow() {
    if (this.isFrist) return;
    this.getDetail();
  },
  methods: {
    // 获取排队号
    async getNum() {
      let { regId } = this.detail;
      let { data } = await myRegQueue(regId);
      this.num = data;
    },
    // 获取详情
    async getDetail() {
      let { data } = await pregOrderDetailNew({
        orderNo: this.orderNo,
      });
      this.ghId = data.ghId;
      let { status } = data;
      this.status = status;
      this.detail = data;
      if (data.status == 1) {
        timer = setInterval(() => {
          this.timeStr = setTimeStr(data.addTime);
          if (this.timeStr == 0) clearInterval(timer);
        }, 1000);
      }

      this.getNum();
    },
    // 去详情
    toDetail() {
      let { regId, hosId } = this.detail;
      if (!regId) return;
      uni.setStorageSync('hosId', hosId);
      let url = '/pages/personalCenter/diagnosisRecord/detail?id=' + regId;
      uni.navigateTo({ url });
    },
    // 支付
    async toPay() {
      let { payStr, payType, orderMoney, ghId } = this.detail;
      payStr = JSON.parse(payStr);
      if (payType == 1) {
        this.wxPay(payStr);
        return;
      }
      if (payType == 2) {
        uni.navigateTo({
          url:
            '/pages/pay/pay?price=' +
            orderMoney +
            '&ghId=' +
            ghId +
            '&url=' +
            btoa(payStr.url),
        });
        reutrn;
      }
      Toast('确实支付类型参数');
    },
    // 微信支付
    async wxPay(info) {
      try {
        await wxPay(info);
        this.getStatus();
      } catch (error) {
        Toast('取消支付');
      }
    },
    // 查询状态
    async getStatus() {
      if (num <= 0) {
        clearTimeout(timer);
        return;
      }
      num--;
      let {
        data: { regStatus },
      } = await queryRegisterPayStatus({
        ghId: this.ghId,
      });

      if (regStatus == 2) {
        this.getDetail();
        return;
      }

      timer = setTimeout(this.getStatus(), 2000);
    },
  },
};
</script>

<style lang="scss" scoped>
page {
  background: #f5f5f5;
}
.register {
  padding-bottom: 120rpx;

  .price_info {
    background: #fff;
    border-radius: 32rpx;
    padding: 0 32rpx 32rpx;
    width: 90%;
    margin: auto;
    margin-top: -32rpx;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0px 1px 4px  rgba(121, 72, 234, 0.15);
    .title {
      @include flex(lr);
      height: 88rpx;
      font-size: 28rpx;
      font-weight: bold;
      border-bottom: 1px solid #ebebeb;

      .icon {
        width: 64rpx;
        height: 64rpx;
      }
    }

    .info_text {
      padding: 10rpx 0;
      border-bottom: 1px solid #ebebeb;

      .item_info {
        @include flex(lr);
        font-size: 28rpx;
        color: #333;
        height: 60rpx;

        .uni-icons {
          position: relative;
          top: 25rpx;
        }
      }
    }

    .info_price {
      padding-top: 10rpx;

      .item_info {
        @include flex(lr);
        font-size: 28rpx;
        color: #333;
        height: 60rpx;

        .sub {
          color: #666;
        }

        .bold {
          font-weight: bold;
        }

        .num {
          color: #ff5050;
          font-weight: bold;
        }
      }
    }
  }

  .order {
    margin-top: 24rpx;
  }
}
</style>
