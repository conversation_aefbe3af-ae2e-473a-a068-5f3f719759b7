<template>
  <view>
    <view class="page-container">
      <template v-if="questionList.length > 0">
        <scroll-view
          class="content-container"
          scroll-y="true"
          @scrolltolower="getMore"
        >
          <template v-for="(item, index) in questionList">
            <view class="list" @click="getQuestionDetail(item)" :key="index">
              <view class="list-top">
                <view class="question-info">
                  <view>
                    <view class="question-name">{{ item.didName }}</view>
                    <view>创建时间：{{ item.sendTime }}</view>
                  </view>
                  <img
                    src="/static/doc/u-6.png"
                    alt=""
                    style="
                      width: 32px;
                      height: 32px;
                      margin-right: 10px;
                      margin-top: 5px;
                    "
                  />
                </view>
                <view class="isFeedback">
                  <text
                    :class="item.isFeedback == '已填写' ? 'read' : 'un-read'"
                  >
                   <text class="crip"></text> {{ item.isFeedback }}
                  </text>
                </view>
              </view>
              <view class="list-bottom"> 共{{ item.topicCount }}题 </view>
            </view>
          </template>
          <!-- 加载更多 -->
          <view v-if="isShowMore">
            <uni-load-more :status="status"></uni-load-more>
          </view>
        </scroll-view>
      </template>
      <view class="empty_list" v-else>
        <image src="/static/images/question/empty_toast.png" />
        <view> 暂无量表 </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getAllQuestion } from "@/api/question.js";
export default {
  data() {
    return {
      questionList: [],
      listQuery: {
        page: 1,
        limit: 20,
        patientId: "",
        userId: uni.getStorageSync("userId") || "",
      },
      total: "",
      isShowMore: false,
      status: "loading",
    };
  },
  onLoad() {
    this.listQuery.patientId = uni.getStorageSync("patientIdList");
  },
  onShow() {
    this.getAllQuestionFun();
  },
  onPullDownRefresh() {
    this.questionList = [];
    this.listQuery.page = 1;
    this.getAllQuestionFun();
  },
  methods: {
    // 查询量表记录
    async getAllQuestionFun() {
      uni.stopPullDownRefresh();
      let { data } = await getAllQuestion(this.listQuery);
      if (!data) return;
      if (this.listQuery.page === 1) {
        this.questionList = data.rows;
      } else {
        this.questionList = this.questionList.concat(data.rows);
      }
      this.total = data.total;
    },
    // 查看量表详情
    getQuestionDetail(item) {
      if (item.isFeedback == "已填写") {
        uni.navigateTo({
          url:
            "/pages/chatCardDetail/questionnaireRead?param=" +
            JSON.stringify(item),
        });
      } else if (item.isFeedback == "未填写") {
        uni.navigateTo({
          url:
            "/pages/chatCardDetail/questionnaire?action=" +
            "chatRoom" +
            "&param=" +
            JSON.stringify(item),
        });
      }
    },
    // 查看更多
    getMore() {
      this.isShowMore = true;
      // 共几页
      let num = Math.ceil(this.total / this.listQuery.limit);
      if (this.listQuery.page < num) {
        this.listQuery.page += 1;
        this.getAllQuestionFun();
        this.isShowMore = false;
      } else {
        this.status = "noMore";
      }
    },
  },
};
</script>

<style scoped lang="scss">
.page-container {
  box-sizing: border-box;
  background: rgba(240, 242, 252, 1) !important;
}

.content-container {
  height: 100vh;
  padding: 24rpx 32rpx;
  box-sizing: border-box;
}

.list {
  width: 100%;
  border-radius: 16rpx;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px 1px 4px rgba(121, 72, 234, 0.15);
}

.list:not(:first-child) {
  margin-top: 24rpx;
}

.list-top,
.list-bottom {
  padding: 24rpx 46rpx;
  font-size: 28rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: rgba(102, 102, 102, 1);
  line-height: 40rpx;
}

.list-bottom {
  border-top: 1rpx solid #ebebeb;
}
.list-top {
}
.question-info {
  display: flex;
  justify-content: space-between;
}

.question-name {
  font-size: 28rpx;
  font-family: PingFangSC-Semibold, PingFang SC;
  font-weight: bold;
  color: rgba(51, 51, 51, 1);
  line-height: 20rpx;
  margin-bottom: 20rpx;
  flex: 1;
  margin-top: 8px;
}

.read {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  font-family: PingFangSC-Semibold, PingFang SC;
  font-weight: 600;
  color: rgba(131, 106, 255, 1);
  line-height: 44rpx;
  .crip{
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: rgba(131, 106, 255, 1);
    margin-right: 7px;
  }
}

.un-read {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  font-family: PingFangSC-Semibold, PingFang SC;
  font-weight: 600;
  color: rgba(227, 113, 113, 1);
  line-height: 44rpx;
  .crip{
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: rgba(255, 176, 176, 1);
    margin-right: 7px;
  }
}

/* 列表为空提示 */
.empty_list {
  @include flex;
  flex-direction: column;
  font-size: 26rpx;
  font-weight: 400;
  color: $k-info-title;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.empty_list image {
  margin-bottom: 40rpx;
  width: 386rpx;
  height: 324rpx;
}
.isFeedback {
  display: flex;
  justify-content: right;
  margin-top: 15px;

}
</style>
