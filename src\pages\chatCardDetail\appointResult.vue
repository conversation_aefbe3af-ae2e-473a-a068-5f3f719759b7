<template>
  <view class="appiont_result">
    <!-- 状态 -->
    <RESULT :time="appointTime" />

    <!-- 二维码 -->
    <EWM v-if="code" :time="signTime" :code="code" :hosId="hosId" />
  </view>
</template>

<script>
import RESULT from './com/result.vue';
import EWM from './com/resultEwm.vue';

export default {
  name: 'Result',
  components: {
    RESULT,
    EWM,
  },
  data() {
    return {
      code: '',
      // 医院id
      hosId: '',
      // 预约时间
      appointTime: '',
      // 签到时间
      signTime: '',
    };
  },
  onLoad(v) {
    this.code = v.code;
    this.hosId = v.hosId;
    this.appointTime = v.appointTime;
    this.signTime = v.signTime;
  },
};
</script>

<style lang="scss" scoped>
.appiont_result {
  padding: 32rpx;

  .ewm {
    margin-top: 24rpx;
  }
}
</style>
