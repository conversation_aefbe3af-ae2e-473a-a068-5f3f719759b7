<template>
  <view class="status">
    <block v-if="status == 1">
      <view class="h2">待支付</view>
      <view class="status_info">请于{{ time }},到院进行支付</view>
    </block>

    <block v-if="status == 2">
      <view class="h2">待签到</view>
      <view class="status_info">请于{{ time }},进行签到</view>
    </block>

    <block v-if="status == 3">
      <view class="h2">等待检{{ isLis ? '验' : '查' }}中…</view>
      <view class="status_info" v-if="lisType == 1">
        您当前为第<text>{{ queueNumber }}</text
        >位，前面还有{{ num }}位，请耐心等待
      </view>

      <view class="status_info" v-if="lisType == 2">
        样本已签收，等待检验中，请耐心等待
      </view>
    </block>

    <block v-if="status == 7">
      <view class="h2">等待取样中</view>
      <view class="status_info">
        您当前为第<text>{{ queueNumber }}</text
        >位，前面还有{{ num }}位，请耐心等待
      </view>
    </block>

    <block v-if="status == 8">
      <view class="h2">等待配送中</view>
      <view class="status_info">
        采样完成，正在加急向检验中心配送
      </view>
    </block>

    <block v-if="status == 9">
      <view class="h2">等待签收中</view>
      <view class="status_info">
        样本已发送，请耐心等待
      </view>
    </block>

    <block v-if="status == 4">
      <view class="h2">等待上传报告</view>
      <view class="status_info">请耐心等待</view>
    </block>

    <block v-if="status == 5">
      <view class="h2">已出报告</view>
      <view class="status_info">已出报告，点击查看结果，查看报告吧..</view>
    </block>

    <block v-if="status == 6">
      <view class="h2">已退费</view>
      <view class="status_info">已为您退费，请注意查收</view>
    </block>

    <block v-if="status == 0">
      <view class="h2">已失效</view>
      <view class="status_info">您的检{{ isLis ? '验' : '查' }}单已失效</view>
    </block>

    <image class="icon" src="/static/inspection/lis.png" />
  </view>
</template>

<script>
export default {
  name: 'Status',
  props: {
    isLis: {
      type: Boolean,
      default: false,
    },
    status: {
      type: Number | String,
      default: 0,
    },
    time: {
      type: String,
      default: '',
    },
    queueNumber: {
      type: String,
      default: '',
    },
    num: {
      type: String,
      default: '',
    },
    lisType: {
      type: String | Number,
      default: 1,
    },
  },
};
</script>

<style lang="scss" scoped>
.status {
  width: 100%;
  height: 394rpx;
  @include bg_theme;
  color: #fff;
  padding: 32rpx;
  border-radius: 0 0 8rpx 8rpx;

  .h2 {
    font-size: 48rpx;
    font-weight: bold;
  }

  &_info {
    font-size: 28rpx;
    margin-top: 24rpx;
    position: relative;
    z-index: 1;

    text {
      font-size: 36rpx;
      padding: 0 10rpx;
      font-weight: bold;
    }
  }

  .icon {
    width: 100rpx;
    height: 100rpx;
    position: absolute;
    top: 60rpx;
    right: 32rpx;
  }
}
</style>
