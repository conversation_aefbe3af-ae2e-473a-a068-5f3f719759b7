<template>
  <!-- 单个 -->
  <view class="card" @click="toPath">
    <!-- 编号 -->
    <view class="item_title">
      <view class="order-text">
        <img src="/static/doc/order-y.png" alt="">
        <text class="order-text-name">{{name||'挂号'}}</text>
      </view>
      <text class="status" v-if="item.regOrderListNewQueryVO.status == 0"  style="background: rgba(244, 244, 244, 1);color: rgba(134, 138, 141, 1)">{{
        item.regOrderListNewQueryVO.statusName
      }}</text>
      <text
        class="status wait"
        v-if="item.regOrderListNewQueryVO.status == 1"
        >{{ item.regOrderListNewQueryVO.statusName }}</text
      >
      <text
        class="status wait"
        v-if="item.regOrderListNewQueryVO.status == 2"
        >{{ item.regOrderListNewQueryVO.statusName }}</text
      >
      <text class="status" v-if="item.regOrderListNewQueryVO.status == 3">{{
        item.regOrderListNewQueryVO.statusName
      }}</text>
      <text
        class="status wait"
        v-if="item.regOrderListNewQueryVO.status == 4"
        >{{ item.regOrderListNewQueryVO.statusName }}</text
      >
      <text class="status act" v-if="item.regOrderListNewQueryVO.status == 5">{{
        item.regOrderListNewQueryVO.statusName
      }}</text>
      <text class="status" v-if="item.regOrderListNewQueryVO.status == 6"  style="background: rgba(236, 232, 255, 1);color: rgba(131, 106, 255, 1)">{{
        item.regOrderListNewQueryVO.statusName
      }}</text>
    </view>

    <!-- 相关信息 -->
    <view class="item_cont">
      <view class="left_info" v-if="name!='快速续方'" style="justify-content: left;font-size: 14px">
        {{ item.regOrderListNewQueryVO.platformDocName || item.regOrderListNewQueryVO.docName }}
       <text style="font-size: 12px;margin-top: 5px;margin-left: 7px;font-weight: normal">{{
           item.regOrderListNewQueryVO.deptName
         }}</text>
      </view>
      <view class="left_info"> 医院：{{ item.hosName }} </view>
      <view class="left_info">
        号别：{{ item.regOrderListNewQueryVO.dntName }}
        <!-- 右箭头 -->
        <uni-icons type="arrowright" color="#666" size="16"></uni-icons>
      </view>
      <view class="left_info">
        问诊类型：{{ item.regOrderListNewQueryVO.receiveType }}
      </view>
      <view class="left_info">
       <text  class="cus"> 就诊人：{{ item.regOrderListNewQueryVO.patientName }}</text>
      </view>
    </view>

    <!-- 时间 -->
    <view class="item_footer">
      <view class="left">
        <view style="font-size: 12px">
          总价:<text class="order-red"  style="margin-left: 6px;"><text style="font-size: 12px">￥</text>{{ item.totalPay | toFixed }}</text>
        </view>
       <view style="font-size: 12px">
         实付款:<text
           class="bold order-red"
           style="margin-left: 6px;"
       ><text style="font-size: 12px">￥</text>{{ item.orderMoney | toFixed }}</text
       >
       </view>

      </view>
      <view class="button" v-if="item.payStatus == 1">去支付</view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'Register',
  props: ['item', 'remark','name'],
  methods: {
    // 根据支付状态跳转
    toPath() {
      const { hosId, orderNo } = this.item;
      uni.setStorageSync('hosId', hosId);
      let url = '/pages/order/detail/register?orderNo=' + orderNo;
      uni.navigateTo({ url });
    },
  },
};
</script>

<style lang="scss" scoped>
.card {
  padding: 0 32rpx;
  border-radius: 8rpx;
  background: #fff;
  margin-bottom: 24rpx;
  box-shadow: 0 0 20rpx #ddd;

  .item_title {
    height: 88rpx;
    @include flex(lr);
    font-size: 28rpx;
    color: #333;
    font-weight: bold;
    border-bottom: 1px solid #ebebeb;

    .status {
      font-weight: normal;
      color: #333;

      &.wait {
        color: #ff3b30;
      }

      &.act {
        @include font_theme;
      }
    }
  }

  .item_cont {
    padding: 10rpx 0;
    border-bottom: 1px solid #ebebeb;

    .left_info {
      @include flex(lr);
      font-size: 12px;
      color: #666666;
      line-height: 50rpx;

      &:first-child {
        font-weight: bold;
        color: #333;
      }
    }
  }

  .item_footer {
    height: 88rpx;
    @include flex(lr);
    font-size: 28rpx;
    color: #333;

    .left {
      flex: 1;
      display: flex;
      justify-content: space-between;
      text {
        color: red;
        margin-right: 16rpx;
      }
    }

    .button {
      width: 160rpx;
      height: 60rpx;
      border-radius: 30rpx;
      @include flex;
      @include bg_theme;
      color: #fff;
    }
  }
}
</style>
