<template>

  <view class="chat_text">
    <!-- 头像 -->
    <image @click="head" :src="imgUrl || '/static/images/docHead.png'" mode="aspectFill" class="chat_user_img" />
    <view>
      <view class="chatName">{{chatName}}</view>
      <!-- 文本 -->
      <view class="text_cont">
        <view class="drug-buy" @click="handleBuy">点击购药</view>
      </view>
    </view>
  </view>
</template>

<script>
import { findDrugById } from '@/api/base'
import { addShoppingCart, queryShoppingCartGroupByDrugStore, updateShoppingCart } from "@/api/shop"

export default {
  name: 'BuyDrug',
  props: {
    imgUrl: {
      type: String,
      default: ''
    },
    drugId: {
      type: String,
      default: ''
    },
    drugStoreId: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: '药品信息'
    },
    chatName: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      isClick: false
    }
  },
  methods: {
    async handleBuy () {
      if (this.isClick) return
      this.isClick = true

      try {
        // 根据drugId和drugStoreId查询药品详情
        const { data } = await findDrugById(this.drugId, this.drugStoreId, uni.getStorageSync('wxInfo').openId)

        // 获取yfkcId和drugKc
        const yfkcId = data.yfkcId
        const drugKc = data.drugKc

        // 查询购物车列表
        let cartList = await queryShoppingCartGroupByDrugStore()
        const currentShops = cartList.data.drugStoreList.filter(v => v.drugStoreID == this.drugStoreId)

        if (!currentShops.length) {
          // 新增购物车
          await this.addItem({
            yfkcId,
            drugKc
          })
          this.isClick = false
          uni.switchTab({
            url: '/pages/shop/index'
          })
          return
        }

        const findDrug = currentShops[0].shoppingCartList.find(v => v.drugId == this.drugId)
        if (findDrug) {
          const quan = findDrug.quan * 1 + 1
          // 编辑购物车
          await this.upShopList(quan, {
            yfkcId,
            drugKc
          })
        } else {
          // 新增购物车
          await this.addItem({
            yfkcId,
            drugKc
          })
        }

        this.isClick = false
        uni.switchTab({
          url: '/pages/shop/index'
        })
      } catch (e) {
        console.error(e)
        uni.showToast({
          title: '添加购物车失败',
          icon: 'none'
        })
        this.isClick = false
      }
    },

    // 新增购物车
    async addItem (items) {
      const item = {
        quan: 1,
        yfkcId: items.yfkcId,
        drugKc: items.drugKc,
        openid: uni.getStorageSync('wxInfo').openId
      }
      return await addShoppingCart(item)
    },

    // 更新购物车
    async upShopList (quan, items) {
      const item = {
        quan: quan,
        yfkcId: items.yfkcId,
        drugKc: items.drugKc,
        openid: uni.getStorageSync('wxInfo').openId
      }
      return await updateShoppingCart(item)
    }
  }
}
</script>

<style scoped lang="scss">


.chat_text {
  max-width: 600upx;
  box-sizing: border-box;
  @include flex(left);
  padding-bottom: 30upx;
  align-items: flex-start;

  // 头像
  .chat_user_img {
    width: 64upx;
    height: 64upx;
    border-radius: 4upx;
    margin-right: 16upx;
    flex: none;
  }

  .text_cont {
    max-width: 516upx;
    padding: 10upx 20upx;
    background-color: #fff;
    color: #333;
    word-break: break-all;
    font-size: 28upx;
    border: 1px solid #ececec;
    border-radius: 8upx 32upx 32upx 32upx;
  }
}
.drug-buy {
        color: #7a6ce8;
        font-size: 28rpx;
        text-decoration: underline;
        display: inline-block;
        cursor: pointer;
      }
</style> 