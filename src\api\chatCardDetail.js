import http from '../common/request/request.js';

// 查询自定义服务订单信息
export function findCustomService(param = {}) {
    return http({
        url: 'business/proCustomBusiness/findCustomService',
        param,
        method: 'post',
    });
}

// 获取就诊人信息
export function findPatientByPatientId(param = {}) {
    return http({
        url: '/basic/propatient/findPatientByPatientId',
        param,
        method: 'post',
    });
}

// 根据转诊id获取病情资料id
export function getPrIdByReferral(param = {}) {
    return http({
        url: 'business/patientrecords/savePatientRecordsReferral',
        param,
        method: 'post',
    });
}

// 挂号分割(post请求)
export function saveRegister(param = {}) {
    return http({
        url: 'business/paymentBusiness/saveRegister',
        param,
        method: 'post',
    });
}

// 查询群发详情
export function getDocBulkDetails(param = {}) {
    return http({
        url: 'basic/proflocksend/getDocBulkDetails',
        param,
        method: 'post',
    });
}

// 查询咨询小结
export function getInterrogationSummary(param = {}) {
    return http({
        url: 'business/proreceive/getInterrogationSummaryById',
        param,
        method: 'post',
    });
}

// 查询随访计划详情
export function followupQueryPlanDetails(param = {}) {
    return http({
        url: 'basic/docFollowPlan/followupQueryPlanImplementationDetails',
        param,
        method: 'post',
    });
}

// 获取医生信息接口(post请求)
export function getDocInfoById(param = {}) {
    return http({
        url: 'basic/doctor/getDocInfoById',
        param,
        method: 'post',
    });
}

// 查询医生的所有出诊信息，按服务类型
export function getDocAllVisitReal(param = {}) {
    return http({
        url: 'basic/provisitreal/docAllVisitReal',
        param,
        method: 'post',
    });
}

// 查询空白问诊单详情
export function getQuestionDetailEmpty(param = {}) {
    return http({
        url: 'basic/docinquiringdiagnosis/getInquiringDiagnosisDetail',
        param,
        method: 'post',
    });
}

// 保存问诊单
export function saveQuestionAnswer(param = {}) {
    return http({
        url: 'basic/replyInquiringDiagnosis/saveReplyInquiringDiagnosisAnswer',
        param,
        method: 'post',
    });
}

// 保存问诊单
export function saveReplyInquiringDiagnosisAnswerCustom(param = {}) {
    return http({
        url: 'basic/replyInquiringDiagnosis/saveReplyInquiringDiagnosisAnswerCustom',
        param,
        method: 'post',
    });
}

// 患者打开问诊单，将问诊单状态置为已读
export function setQuestionRead(param = {}) {
    return http({
        url: 'basic/replyInquiringDiagnosis/changeHaveReadStatus',
        param,
        method: 'post',
    });
}

// 查询已反馈的问诊单详情
export function getReplyDiagnosisAnswer(param = {}) {
    return http({
        url: 'basic/replyInquiringDiagnosis/getReplyDiagnosisAnswer',
        param,
        method: 'post',
    });
}

// 查询某一个医生给某一个患者发送的所有的量表
export function getAllQuestionByDoc(param = {}) {
    return http({
        url: 'basic/replyInquiringDiagnosis/getDiagnosisListPageByPatientIdAndDocId',
        param,
        method: 'post',
    });
}