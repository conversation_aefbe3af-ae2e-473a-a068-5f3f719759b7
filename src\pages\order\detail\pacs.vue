<template>
  <!-- 检查单订单 -->
  <view class="lis">
    <!-- 顶部状态 -->
    <STATUS
      :time="signTime"
      :queueNumber="detail.queueNumber"
      :num="num"
      :addTime="timeStr"
      :status="status"
    />

    <!-- 价格相关 -->
    <PRICEINFO @click="toDetail" :info="detail" />

    <!-- 订单信息 -->
    <ORDER :info="detail" />

    <FOOTER @click="getNext" v-if="status == 1">去支付</FOOTER>
  </view>
</template>

<script>
import STATUS from './lisCom/status.vue';
import PRICEINFO from './lisCom/price.vue';
import ORDER from './lisCom/order.vue';
import FOOTER from '@/components/footer_button/button.vue';

import { getReceiptWay } from '@/api/order';
import {
  pacsLisOrderDetailNew,
  myPacsQueueNum,
  getAppointPayInfo,
  queryPacsRegisterPayStatus,
} from '@/api/inspect';

import { wxPay } from '@/common/js/pay';

let num = 3;

import { Toast } from '@/common/js/pay.js';

let timer;

function setTimeStr(val) {
  if (!val) return 0;
  val = val.replace(/-/g, '/');
  let start = new Date(val).getTime();
  let end = new Date().getTime();
  let c = 1800 - (end - start) / 1000;
  if (c <= 0) return 0;
  let m = parseInt((c / 60) % 60);
  let s = parseInt(c % 60);
  if (m < 10) m = '0' + m;
  if (s < 10) s = '0' + s;
  return m + '分' + s + '秒';
}

export default {
  name: 'Pacs',
  components: {
    STATUS,
    PRICEINFO,
    ORDER,
    FOOTER,
  },
  data() {
    return {
      // 订单状态
      status: 1,
      orderNo: '',
      detail: {},
      // 签到时间
      signTime: '',
      num: '',
      callId: '',
      pays: [],
      remark: {},
      timeStr: '',
    };
  },
  onLoad(opt) {
    this.orderNo = opt.orderNo;
    this.getDetail();
  },
  methods: {
    toDetail(item) {
      if (this.status == 1) return;
      let {
        remark: { ppiId },
      } = item;
      if (ppiId) {
        uni.navigateTo({
          url: '/pages/inspect/pacsDetails?id=' + ppiId,
        });
      }
    },
    // 获取支付方callId
    async getPayList(dpoId) {
      let { data } = await getReceiptWay({
        subjectId: dpoId,
      });
      this.pays = data;
    },
    // 查询排号
    async getNum() {
      let { ppiId } = this.detail;
      let { data } = await myPacsQueueNum(ppiId);
      if (data) this.num = data;
    },
    // 获取详情
    async getDetail() {
      let { data } = await pacsLisOrderDetailNew({
        orderNo: this.orderNo,
      });
      let { signEndTime, signStartTime } = data;
      // 签到时间
      this.signTime = signStartTime
        ? signStartTime.slice(0, 16) + '-' + signEndTime.slice(11, 16)
        : '';
      if (data.remark) data.remark = JSON.parse(data.remark);
      let remark = data.remark;
      data.ppiId = remark.ppiId;
      this.status = data.payStatus;
      this.detail = data;
      if (data.payStatus == 1) {
        // 获取支付方式
        this.getPayList(remark.dpoId);
        this.setTime();
      }
      this.remark = remark;

      if (data.payStatus == 3) {
        this.getNum();
      }

      if (data.payStatus == 1) {
        timer = setInterval(() => {
          this.timeStr = setTimeStr(data.addTime);
          if (this.timeStr == 0) clearInterval(timer);
        }, 1000);
      }
    },
    // 倒计时
    setTime() {
      this.addTime = this.detail.addTime;
    },
    // 支付
    async getNext() {
      let { ppiId, openid } = this.remark;
      let isReady = 1;
      let payType = 1;
      payType = await this.selePay();
      let callId = this.callId;

      let param = {
        ppiId,
        openid,
        isReady,
        payType,
        callId,
      };

      uni.showLoading({
        mask: true,
      });

      try {
        let { data } = await getAppointPayInfo(param);

        uni.hideLoading();

        // 微信支付
        if (payType == 1) {
          this.wxPay(data);
          return;
        }

        let money = this.detail.orderMoney;

        // 支付宝支付
        if (payType == 2) {
          uni.navigateTo({
            url:
              '/pages/pay/pay?price=' +
              money +
              '&ppiId=' +
              ppiId +
              '&url=' +
              btoa(data.url),
          });
        }
      } catch (error) {
        uni.hideLoading();
      }
    },
    // 微信支付
    async wxPay(info) {
      try {
        await wxPay(info);
        this.getStatus();
      } catch (error) {
        Toast('取消支付');
      }
    },
    // 选择支付方式
    async selePay() {
      let that = this;
      return new Promise((resolve, reject) => {
        // 如果不可选支付
        if (!this.pays.length) {
          Toast('当前没有配置支付方式');
          return reject();
        }
        // 如果在线支付
        uni.showActionSheet({
          itemList: ['微信支付', '支付宝支付'],
          success(res) {
            let index = res.tapIndex + 1;
            let item;
            item = that.pays.filter((it) => it.receiptType == index)[0];
            if (!item) {
              Toast('暂不支持该支付方式');
              return reject();
            }
            that.callId = item.appid;
            resolve(index);
          },
          fail(err) {
            reject(err);
          },
        });
      });
    },
    // 查询支付状态
    async getStatus() {
      let {
        data: { status },
      } = await queryPacsRegisterPayStatus(this.detail.ppiId);
      // 支付成功
      if (status == 2) {
        this.toSuc();
        return;
      }
      if (status == 1) {
        if (num > 0) {
          setTimeout(this.getStatus(), 2000);
          num--;
        }
      }
    },
    // 跳转成功
    toSuc() {
      this.getDetail();
    },
  },
};
</script>

<style lang="scss" scoped>
page {
  background: #f5f5f5;
}
.lis {
  padding-bottom: 120rpx;

  .price_info {
    margin-top: -32rpx;
  }

  .order {
    margin-top: 24rpx;
  }
}
</style>
