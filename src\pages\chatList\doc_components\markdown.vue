<!--
 * @Descripttion: Markdown消息组件
 * @version: 1.0
 * @Author: 
 * @Date: 2025-05-22 16:25:28
 * @LastEditors: zhengyangyang
 * @LastEditTime: 2025-05-22 16:57:39
-->
<template>
  <!-- 文本消息 -->
  <view class="chat_text">
    <!-- 头像 -->
    <image @click="head" :src="imgUrl || '/static/images/docHead.png'" mode="aspectFill" class="chat_user_img" />
    <view>
      <view class="chatName">{{chatName}}</view>
      <!-- 文本 -->
      <view class="markdown_cont" @click="click">
        <rich-text :nodes="parsedContent"></rich-text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    content: {
      type: String,
      default: '',
    },
    imgUrl: {
      type: String,
      default: '',
    },
    chatName: {}
  },
  data() {
    return {
      n: 0,
      parsedContent: ''
    };
  },
  created() {
    this.parseMarkdown();
  },
  watch: {
    content() {
      this.parseMarkdown();
    }
  },
  methods: {
    parseMarkdown() {
      // 简单的Markdown解析逻辑
      let content = this.content || '';
      
      // 解析标题
      content = content.replace(/# (.*?)\n/g, '<h1>$1</h1>');
      content = content.replace(/## (.*?)\n/g, '<h2>$1</h2>');
      content = content.replace(/### (.*?)\n/g, '<h3>$1</h3>');
      
      // 解析加粗
      content = content.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
      
      // 解析斜体
      content = content.replace(/\*(.*?)\*/g, '<em>$1</em>');
      
      // 解析链接
      content = content.replace(/\[(.*?)\]\((.*?)\)/g, '<a href="$2">$1</a>');
      
      // 解析列表
      content = content.replace(/- (.*?)(?:\n|$)/g, '<li>$1</li>');
      
      // 解析换行
      content = content.replace(/\n/g, '<br>');
      
      this.parsedContent = content;
    },
    head() {
      this.$emit('head');
    },
    click() {
      this.n++;
      if (this.n >= 2) {
        this.$emit('double', this.content);
      }
      setTimeout(() => {
        this.n = 0;
      }, 500);
    }
  }
};
</script>

<style scoped lang="scss">
.chat_text {
  max-width: 600upx;
  box-sizing: border-box;
  @include flex(left);
  padding-bottom: 30upx;
  align-items: flex-start;

  // 头像
  .chat_user_img {
    width: 64upx;
    height: 64upx;
    border-radius: 4upx;
    margin-right: 16upx;
    flex: none;
  }

  .markdown_cont {
    max-width: 516upx;
    padding: 10upx 20upx;
    background-color: #fff;
    color: #333;
    word-break: break-all;
    font-size: 28upx;
    border: 1px solid #ececec;
    border-radius: 8upx 32upx 32upx 32upx;
    
    :deep(h1) {
      font-size: 32upx;
      font-weight: bold;
      margin: 16upx 0;
    }
    
    :deep(h2) {
      font-size: 30upx;
      font-weight: bold;
      margin: 14upx 0;
    }
    
    :deep(h3) {
      font-size: 28upx;
      font-weight: bold;
      margin: 12upx 0;
    }
    
    :deep(strong) {
      font-weight: bold;
    }
    
    :deep(em) {
      font-style: italic;
    }
    
    :deep(a) {
      color: #1989fa;
      text-decoration: underline;
    }
    
    :deep(li) {
      margin-left: 20upx;
      position: relative;
      padding-left: 12upx;
      
      &:before {
        content: '•';
        position: absolute;
        left: -10upx;
      }
    }
  }
}
</style> 