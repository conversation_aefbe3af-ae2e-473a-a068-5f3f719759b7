<template>
  <view class="count-up" :style="wrapperStyle">
    <text class="time-item">{{ formatNumber(hours) }}时</text>
    <text class="time-item">{{ formatNumber(minutes) }}分</text>
    <text class="time-item">{{ formatNumber(seconds) }}秒</text>
  </view>
</template>

<script>
export default {
  name: 'CountUp',
  props: {
    // 已等待的秒数，默认为 0
    initialSeconds: {
      type: Number,
      default: 0,
    },
    // 文字颜色
    color: {
      type: String,
      default: '#333',
    },
    // 背景色
    backgroundColor: {
      type: String,
      default: '#FDF8DB',
    },
    // 边框颜色
    borderColor: {
      type: String,
      default: '#FDF8DB',
    },
    // 自定义样式
    customStyle: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  data() {
    return {
      timer: null,
      totalSeconds: this.initialSeconds,
      hours: 0,
      minutes: 0,
      seconds: 0,
    }
  },
  computed: {
    wrapperStyle() {
      return {
        color: this.color,
        'background-color': this.backgroundColor,
        'border-color': this.borderColor,
        display: 'flex',
        'align-items': 'center',
        padding: '2rpx 6rpx',
        'border-radius': '6rpx',
        ...this.customStyle,
      }
    },
  },
  watch: {
    initialSeconds(val) {
      this.totalSeconds = val
      this.calcTime()
    },
  },
  created() {
    this.calcTime()
  },
  mounted() {
    this.start()
  },
  beforeDestroy() {
    this.stop()
  },
  methods: {
    formatNumber(num) {
      return num < 10 ? `0${num}` : `${num}`
    },
    calcTime() {
      const hrs = Math.floor(this.totalSeconds / 3600)
      const mins = Math.floor((this.totalSeconds % 3600) / 60)
      const secs = this.totalSeconds % 60
      this.hours = hrs
      this.minutes = mins
      this.seconds = secs
    },
    start() {
      this.stop() // 确保无重复定时器
      this.timer = setInterval(() => {
        this.totalSeconds += 1
        this.calcTime()
      }, 1000)
    },
    stop() {
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }
    },
  },
}
</script>

<style scoped>
.time-item {
  margin: 0 2rpx;
}
</style> 