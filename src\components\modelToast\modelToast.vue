<template>
	<view class="model-container">
		<view class="wrapper">
			<view class="block">
				<view class="title">
					<slot></slot>
				</view>
				<view class="btn_footer">
					<template v-if=" type== '2'">
						<view class="cancel" @click="modelCancel">取消</view>
						<view @click="modelConfirm">确定</view>
					</template>
					<template v-else>
						<view @click="modelConfirm">确定</view>
					</template>
				</view>
			</view>
		</view>

	</view>
</template>
<script>
	export default ({
    name:"modelToast",
		props: {
			father: {
				type: Object
			},
			type: {
				type: Number,
				default: "1"
			},
		},
		data() {
			return {}
		},
		mounted() {
		},
		methods: {
			modelConfirm() {
				this.father.modeConfirm();
			},
			modelCancel(){
				this.father.modelCancel();
			}
		}
	})
</script>

<style scoped lang="scss">
	.wrapper {
		position: fixed;
		width: 100%;
		height: 100vh;
		left: 0;
		top: 0;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 999;
	}

	.wrapper .block {
		width: 606rpx;
		box-sizing: border-box;
		background: rgba(255, 255, 255, 1);
		border-radius: 16rpx;
		overflow: hidden;
		padding: 94rpx 40rpx 40rpx 40rpx;
	}

	.block .title {
		padding-bottom: 64rpx;
		color: #333333;
		font-size: 32rpx;
		font-weight: 600;
		white-space: normal;
		text-align: center;
	}

	.btn_footer {
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: space-around;
	}

	.btn_footer view {
		width: 242rpx;
		height: 68rpx;
		border-radius: 46rpx;
		color: #ffffff;
		font-weight: 600;
		@include bg_theme;
		font-size: 28rpx;
		text-align: center;
		line-height: 68rpx;
		box-sizing: border-box;
	}

	.btn_footer .cancel {
		@include border_theme;
		@include font_theme;
		background: transparent;
		margin-right: 36rpx;
	}
</style>
