<template>
  <!-- 文本消息 -->
  <view class="user_text">
    <!-- 文本 -->
    <view>
      <view class="patientName">{{ patientName }}</view>
      <view  class="text_cont" @click="click">
        <text v-html="content"></text>
        <view v-if="isLongPress" class="chat-longPress triangle-down" @click="withdraw">
          <view>撤回</view>
        </view>
      </view>
    </view>
    <!-- 头像 -->
    <image
      @click="head"
      :src="imgUrl || '/static/images/docHead.png'"
      mode="aspectFill"
      class="user_img"
    />
  </view>
</template>

<script>
export default {
  props: {
    content: {
      type: String,
      default: '123',
    },
    imgUrl: {
      type: String,
      default: '',
    },
    item:{
      type: Object,
      default(){
        return {}
      }
    },
    isLongPress:{
      type:Boolean,
      default:false
    },
    patientName:{

    }
  },
  data() {
    return {
      n: 0,
    };
  },
  watch: {

  },
  methods: {
    withdraw(){
      this.$emit('withdraw')
    },
    head() {
      this.$emit('click');
    },
    click() {
      this.n++;
      if (this.n >= 2) {
        this.$emit('double', this.content);
      }
      setTimeout(() => {
        this.n = 0;
      }, 500);
    },
  },
};
</script>

<style scoped lang="scss">
.text_cont{
  position: relative;
}
.user_text {
  // max-width: 600upx;
  box-sizing: border-box;
  @include flex(right);
  padding-bottom: 30upx;
  align-items: flex-start;

  // 头像
  .user_img {
    width: 64upx;
    height: 64upx;
    border-radius: 4upx;
    margin-left: 16upx;
    flex: none;
  }

  .text_cont {
    max-width: 516upx;
    padding: 10upx 20upx;
    background-color: #d6f1ff;
    font-size: 28upx;
    box-sizing: border-box;
    word-break: break-all;
    color: #333;
    border: 1px solid #ececec;
    border-radius: 32upx 8upx 32upx 32upx;
  }
}
.chat-longPress{
  white-space: nowrap;
  position: absolute;
  top:-27px;
  left: 50%;
  transform: translateX(-50%);
  background: #3B4144;
  color: white;
  padding: 4px 15px;
  border-radius: 7px;
  font-size: 12px;
}
//.triangle-down {
//  position: relative;
//  width: 0;
//  height: 0;
//  border-left: 5px solid transparent;
//  border-right: 5px solid transparent;
//  border-top: 10px solid black; /* 三角形的颜色 */
//}

.triangle-down::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid  #3B4144; /* 三角形的颜色 */
}
</style>
