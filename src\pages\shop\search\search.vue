<template>
  <!-- 药品搜索界面 -->
  <view class="search_warp">
<!--     <image class="zaixian" src="/static/doc/zaixian.png"></image>-->
    <!-- 搜索 -->
    <view class="search">
      <uni-search-bar
        v-model="search"
        :radius="100"
        cancelButton="none"
        clearButton="auto"
        bgColor="#FFF"
        maxlength="20"
        placeholder="输入药品进行查找"
        @confirm="toSearch"
      ></uni-search-bar>
      <view style="color:rgba(135, 79, 240, 1)" @click="toSearch">
        <text style="margin-right: 12px">|</text>
        搜索
      </view>
    </view>
<!--    <view class="search">-->
<!--      <view class="cont">-->
<!--&lt;!&ndash;        <uni-icons type="search" size="20" color="#C1C1C1"></uni-icons>&ndash;&gt;-->
<!--        <view class="cont_text">-->
<!--          <input-->
<!--            type="text"-->
<!--            v-model="search"-->
<!--            @confirm="toSearch"-->
<!--            confirm-type="search"-->
<!--            maxlength="20"-->
<!--            class="text"-->
<!--            placeholder="搜索您想要购买的商品"-->
<!--          />-->
<!--          <image-->
<!--            src="/static/images/search/btn_del.png"-->
<!--            class="icon_del"-->
<!--            v-show="search"-->
<!--            @click="search = ''"-->
<!--          />-->
<!--        </view>-->
<!--        <view class="but" @click="toSearch">搜索</view>-->
<!--      </view>-->
<!--    </view>-->

    <!-- 搜索历史 -->
    <view class="search_list" v-if="list.length">
      <!-- 标题 -->
      <view class="list_title">
        <view class="left">搜索历史</view>
        <!-- 清空按钮 -->
        <view class="right" @click="showRemove = true">
          <image src="/static/images/search/del.png" />
          <text>清空</text>
        </view>
      </view>
      <!-- 历史 -->
      <view class="list_items">
        <text
          class="item"
          v-for="item in list"
          :key="item"
          @click="toSearchList(item)"
          >{{ item }}</text
        >
      </view>
    </view>

    <!-- 确认弹窗 -->
    <propCenter
      v-if="showRemove"
      @cancelPropCenter="showRemove = false"
      @confirmPropCenter="removeList"
      :type="2"
    >
      确定要清空所有记录吗？
    </propCenter>
  </view>
</template>

<script>
import propCenter from '@/components/propCenter/propCenter.vue';

export default {
  components: {
    propCenter,
  },
  data() {
    return {
      search: '',
      list: [],
      showRemove: false,
    };
  },
  onShow() {
    let list = uni.getStorageSync('search_drug') || false;
    if (list) {
      this.list = list;
    }
  },
  methods: {
    // 搜索
    toSearch() {
      console.log(this.search)
      if (!this.search||!this.search?.value) return;
      let str = this.search.value.trim();
      this.setList(str);
      this.toSearchList(str);
    },
    // 设置搜索历史
    setList(str) {
      // 不为空
      if (this.list.length) {
        let arr = [str, ...this.list];
        this.list = [...new Set(arr)];
      } else {
        this.list = [str];
      }
      uni.setStorageSync('search_drug', this.list);
    },
    // 清空历史
    removeList() {
      this.list = [];
      uni.removeStorageSync('search_drug');
      this.showRemove = false;
    },
    // 去搜索列表
    toSearchList(str) {
      uni.navigateTo({
        url: '../searchList/searchList?str=' + str,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}
.zaixian{
  width: 120rpx;
  height: 60rpx;
}
.search_warp {
  height: 100vh;

  //.search {
  //  height: 88rpx;
  //  padding: 0 24rpx;
  //  @include flex;
  //  flex: none;
  //
  //  .cont {
  //    width: 100%;
  //    padding: 0 24rpx;
  //    height: 60rpx;
  //    border-radius: 30rpx;
  //    background-color: #f5f5f5;
  //    @include flex(lr);
  //
  //    .cont_text {
  //      flex: 1;
  //      padding: 0 20rpx;
  //      @include flex;
  //
  //      .text {
  //        font-size: 28rpx;
  //        flex: 1;
  //      }
  //
  //      .icon_del {
  //        width: 38upx;
  //        height: 38upx;
  //        flex: none;
  //      }
  //    }
  //
  //    .but {
  //      font-size: 28rpx;
  //      @include font_theme;
  //    }
  //  }
  //}

  .search_list {
    width: 100%;
    padding: 0 32upx;

    .list_title {
      @include flex(lr);
      height: 90upx;

      .left {
        font-size: 28rpx;
        color: $k-title;
        font-weight: bold;
      }

      .right {
        @include flex(center);
        font-size: 22upx;
        color: $k-info-title;
        image {
          width: 32upx;
          height: 32upx;
        }
      }
    }

    .list_items {
      @include flex(left);
      flex-wrap: wrap;

      text {
        font-size: 24rpx;
        color: rgba(51, 51, 51, 1);
        height: 64upx;
        @include flex;
        padding: 0 32upx;
        border-radius: 32upx;
        background: rgba(247, 248, 252, 1);
        margin-right: 16upx;
        margin-bottom: 16upx;
      }
    }
  }
}
.search{
  width: 706rpx;
  height: 60rpx;
  opacity: 1;
  border-radius: 30rpx;
  background: rgba(242, 245, 255, 1);
  margin: auto;
  margin-top: 40rpx;
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  .uni-searchbar{
    border: none !important;
    background: none;
    width: 80%;
   /deep/ .uni-searchbar__box{
       border: none !important;
      background: none !important;
      //margin-top: -25rpx;
      justify-content: left !important;
    }
  }
}
</style>
