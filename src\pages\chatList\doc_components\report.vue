<!--
 * @Descripttion: 报告消息卡片组件
 * @version: 1.0
 * @Author: 
 * @Date: 2025-05-22 17:10:28
 * @LastEditors: zhengyangyang
 * @LastEditTime: 2025-05-23 15:50:02
-->
<template>
  <!-- 报告卡片 -->
  <view class="chat_text">
    <!-- 头像 -->
    <image @click="head" :src="imgUrl || '/static/images/docHead.png'" mode="aspectFill" class="chat_user_img" />
    <view>
      <view class="chatName">{{chatName}}</view>
      <!-- 报告卡片 -->
      <view class="report_card" @click="openReport">
        <view class="report_title">
          <image src="/static/ai/report_icon.png" class="report_icon"></image>
          <text>{{title || '健康报告'}}</text>
        </view>
        <view class="report_footer">
          <view class="report_btn">查看详情</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    imgUrl: {
      type: String,
      default: '',
    },
    chatName: {},
    title: {
      type: String,
      default: '健康报告'
    },
    desc: {
      type: String,
      default: '点击查看详细报告内容'
    },
    time: {
      type: String,
      default: ''
    },
    reportId: {
      type: String,
      default: ''
    }
  },
  methods: {
    head() {
      this.$emit('head');
    },
    openReport() {
      // 跳转到报告详情页面
      uni.navigateTo({
        url: `/pages/qcAi/report?id=${this.reportId}`
      });
    }
  }
};
</script>

<style scoped lang="scss">
.chat_text {
  max-width: 600upx;
  box-sizing: border-box;
  @include flex(left);
  padding-bottom: 30upx;
  align-items: flex-start;

  // 头像
  .chat_user_img {
    width: 64upx;
    height: 64upx;
    border-radius: 4upx;
    margin-right: 16upx;
    flex: none;
  }

  .report_card {
    max-width: 516upx;
    background-color: #fff;
    border: 1px solid #ececec;
    border-radius: 8upx 32upx 32upx 32upx;
    overflow: hidden;
    
    .report_title {
      display: flex;
      align-items: center;
      padding: 16upx 20upx;
      border-bottom: 1px solid #f5f5f5;
      background-color: #ffffff;
      
      .report_icon {
        width: 40upx;
        height: 40upx;
        margin-right: 10upx;
      }
      
      text {
        font-size: 28upx;
        font-weight: 500;
        color: #333;
      }
    }
    
    .report_desc {
      padding: 16upx 20upx;
      font-size: 26upx;
      color: #666;
      line-height: 1.5;
    }
    
    .report_footer {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12upx 20upx;
      border-top: 1px solid #f5f5f5;
      
      .report_time {
        font-size: 24upx;
        color: #999;
      }
      
      .report_btn {
        font-size: 24upx;
        color: #1989fa;
        background-color: rgba(25, 137, 250, 0.1);
        padding: 6upx 12upx;
        border-radius: 4upx;
      }
    }
  }
}
</style> 