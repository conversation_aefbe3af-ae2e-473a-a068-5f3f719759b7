<template>
  <!-- 通知公告 -->
  <view class="notice">
    <!-- 列表 -->
    <view class="list">
      <view class="item">
        <!-- 标题时间 -->
        <view class="item_title">
          <text class="title_text">{{ setTitle(detail.ext.type) }}</text>
          <text class="title_time">{{ getTime(detail.time) }}</text>
        </view>
        <!-- 文本内容 -->
        <view class="item_cont">
          {{ detail.content }}
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import date from '@/utils/date';
export default {
  computed: {
    // 公告具体详情
    detail() {
      return this.$store.getters.getNotice;
    },
  },
  data() {
    return {};
  },
  methods: {
    setTitle(val) {
      let title = '';
      switch (val) {
        // 问诊相关
        case 'HZ_WZ_SF':
        case 'HZ_WZ_YSJZ':
        case 'HZ_WZ_JSWZ':
        case 'HZ_WZ_YSTZ':
        case 'HZ_WZ_JJJZ':
          title = '问诊信息';
          break;
        // 系统图标
        case 'HZ_XT_QF':
          title = '系统公告';
        case 'HZ_SF_SF':
          title = '随访';
          break;
        default:
          title = '系统公告';
          break;
      }
      return title;
    },
    getTime(timestamp) {
      let time = '';
      let times = date.DateDifferenceMsgTime(timestamp);
      if (times.days > 0) {
        time = date.getNowDate(Number(timestamp));
      } else if (times.hours > 0) {
        time = times.hours + '小时前';
      } else if (times.minutes > 0) {
        time = times.minutes + '分钟前';
      } else {
        time = '刚刚';
      }
      return time;
    },
  },
};
</script>

<style lang="scss" scoped>
.notice {
  background-color: $k-page-bg-color;
  min-height: 100vh;
  * {
    box-sizing: border-box;
  }

  .list {
    padding: 24upx 32upx;

    .item {
      width: 100%;
      font-size: 28upx;
      padding: 24upx 32upx;
      border-radius: 16upx;
      background-color: #fff;

      .item_title {
        @include flex(lr);
        padding-bottom: 24upx;

        .title_text {
          color: $k-title;
          font-weight: 600;
        }

        .title_time {
          color: $k-sub-title;
        }
      }

      .item_cont {
        color: $k-sub-title;
        line-height: 42upx;
      }
    }
  }
}
</style>
