// 引入amr插件
const BenzAMRRecorder = require('benz-amr-recorder');

var amr;

function AmrPlay(url) {
  // 如果不存在
  if (!amr) {
    amr = new BenzAMRRecorder();
  } else {
    amr.stop();
  }

  return new Promise((resolve, reject) => {
    // 播放结束
    amr.onEnded(function() {
      amr = null;
      resolve();
    });
    // 加载地址播放
    amr.initWithUrl(url).then((res) => {
      amr.play();
    });
  });
}

export default AmrPlay;
