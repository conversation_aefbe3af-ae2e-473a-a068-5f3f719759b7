<template>
  <view class="articles-detail">
    <view class="detail-title">
      <view class="title" >{{ artInfo.title }}</view>
    </view>
    <view class="detail-top">
      <text style="font-size: 14px">{{ artInfo.publisher }}</text>
      <text>发布时间：{{ artInfo.publicTime }}</text>
    </view>
    <view class="headImgUrl">
      <image
          :src="artInfo.headImgUrl? artInfo.headImgUrl: 'https://szhy.dchealthcloud.com/image/default.png'"
          mode="aspectFit"
          class="header_img"
      ></image>
    </view>
    <view class="content">
      <view v-if="artInfo.type===1">
        <rich-text :nodes="artInfo.content" @itemclick="handleRichTextClick"></rich-text>
      </view>
      <view v-if="artInfo.type===2">
        <iframe :src="artInfo.content" frameborder="0" width="100%" height="500"></iframe>
      </view>
    </view>
    <!-- 名医推荐-->
    <view v-if="famousDocList.length" class="fam_list">
      <view class="fam_title">
        <TITLE title="推荐医生" />
      </view>
      <view class="list-box">
        <view class="list-box-1" v-for="(item,index) in famousDocList" :key="index">
          <view class="list-card" @click="toDoc(item)">
            <image class="doc_head" v-if="item.docImgUrl" :src="item.docImgUrl"  />
            <image class="doc_head" v-else src="/static/images/docHead.png"></image>
            <view class="doc_name">
              <text>{{item.docName}}</text>
            </view>
            <view class="desc_two">
              <text>{{item.deptName}}</text>
              <text>{{item.docProf}}</text>
            </view>
            <view class="host">
              <text>{{isShowWorkHosName ? item.workHosName : item.hosName}}</text>
            </view>
            <!-- 标签 -->
            <view class="desc_tag" v-if="item.docLable.length>3">
              <view v-for="(l, lk) in item.docLable.slice(0,3)" class="desc_tag_1">
                <view class="desc_tag_text">{{ l.lableName }}</view>
              </view>
            </view>
            <view class="desc_tag" v-else>
              <view v-for="(l, lk) in item.docLable" class="desc_tag_1">
                <view class="desc_tag_text">{{ l.lableName }}</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <!-- 药品卡片-->
    <view>
      <view class="recommendedMedicine">推荐药品</view>
      <view v-for="(drug,index) in dicArticleDrugs" :key="index">
        <DRUG :item="drug" :index="index" flag="artcle"/>
      </view>
    </view>
  </view>
</template>
<script>
import { labelList, arcTagList, arcTagInfo,findRecommendDocPage } from "@/api/visit.js";
import FAMOUS from "@/pages/index/com/famousDoc.vue";
import TITLE from "@/pages/inspect/com/itemTitle.vue";
import {listPageDicArticleDrug} from "../../api/base";
import DRUG from "@/pages/shop/components/drugItem.vue";
export default({
  components:{FAMOUS,TITLE,DRUG},
  data() {
    return {
      artInfo:{},
      articleId:'',
      famousDocList:[],
      isShowWorkHosName: uni.getStorageSync("isShowWorkHosName") || false,
      dicArticleDrugs:[],
      imageUrls: [] // 存储富文本中的图片URL
    }
  },
  onLoad(options) {
    this.articleId=options.articleId
    this.getInfo()
    this.getFindRecommendDocPage()
    this.getListPageDicArticleDrug()
  },
  methods: {
    // 提取富文本中的图片URL
    extractImageUrls(content) {
      if (!content) return [];
      
      const imgLst = content.match(/<img\s*src=\"([^\"]*?)\"[^>]*>/gi);
      const urls = [];
      
      if (imgLst) {
        for (let i = 0; i < imgLst.length; i++) {
          imgLst[i].replace(/<img [^>]*src=['"]([^'"]+)[^>]*>/gi, (match, capture) => {
            urls.push(capture);
          });
        }
      }
      
      return urls;
    },
    // 处理富文本中图片点击
    handleRichTextClick(e) {
      // 判断是否点击的是图片
      if (e.detail.node && e.detail.node.name === 'img') {
        // 获取图片src
        const imgSrc = e.detail.node.attrs.src;
        
        // 预览图片
        uni.previewImage({
          urls: this.imageUrls,
          current: imgSrc,
          longPressActions: {
            itemList: ['保存图片'],
            success: function(data) {
              if(data.tapIndex === 0) {
                // 保存图片到相册
                uni.saveImageToPhotosAlbum({
                  filePath: imgSrc,
                  success: function () {
                    uni.showToast({
                      title: '保存成功',
                      duration: 2000
                    });
                  },
                  fail: function() {
                    uni.showToast({
                      title: '保存失败',
                      icon: 'none'
                    });
                  }
                });
              }
            }
          }
        });
      }
    },
   async getListPageDicArticleDrug(){
      const res=await listPageDicArticleDrug({
        page:1,
        limit:100,
        articleId:this.articleId,
        sortFlag:"1"
      })
     this.dicArticleDrugs=res.data.rows||[]
    },
    // 去医生主页
    toDoc(item) {
      // 医院id
      // uni.setStorageSync("hosId", item.hosId);
      uni.navigateTo({
        url: "/pages/register/docHomePage/index?docId=" + item.docId,
      });
    },
    getInfo(){
      arcTagInfo({articleId:this.articleId}).then(res=>{
        this.artInfo=res.data
        // 提取文章内容中的图片URL
        if (this.artInfo && this.artInfo.content && this.artInfo.type === 1) {
          this.imageUrls = this.extractImageUrls(this.artInfo.content);
        }
      })
    },
    async getFindRecommendDocPage(){
      const data={
        page:1,
        limit:5,
        articleId:this.articleId,
        isRecommend:1,
        operationType:1
      }
      const res=await findRecommendDocPage(data)
      this.famousDocList=(res.data.rows||[]).map(v=>{
        return {
          ...v,
          docLable:v.docLable||[]
        }
      })
      console.log('res',this.famousDocList)
    }
  },
})


</script>
<style scoped lang="scss">
.title{
  font-size: 23px;
  font-weight: bold;
  padding: 20px;
}
.detail-top{
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid #e1e1e1;
}
.headImgUrl{
  display: flex;
  justify-content: center;
  padding: 20px 0;
}

.content{
  padding: 20px;
  /deep/img{
    width: 100% !important;
  }
}
.fam_list {
  background: #fff;
  border-radius: 8rpx;

  .fam_title {
    padding: 0 32rpx;
    height: 80rpx;
    @include flex(left);
    display: flex;
    justify-content: space-between;
  }
}

.list-box {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  margin-left: 20rpx;
}

.list-card {
  padding: 16rpx;
  background-color: #FBFBFD;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  border-radius: 8rpx;
  height: 360rpx;
  text-align: center;
}

.list-box-1 {
  width: 50%;
  text-align: center;
}

.card_more {
  height: 80rpx;
  @include flex;
  font-size: 28rpx;
  color: #999;
}

.doc_head {
  width: 104rpx;
  height: 104rpx;
  border-radius: 50%;
  flex: none;
  object-fit: cover;

}

.doc_name {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}

.desc_tag {
  margin-top: 16rpx;
  display: flex;
  flex-direction: row;
  justify-content: center;

  // text {
  //   padding: 0 12rpx;
  //   height: 36rpx;
  //   border-radius: 18rpx;
  //   background-color: #e8f6fd;
  //   @include font_theme;
  //   font-size: 22rpx;
  //   @include flex;

  // }
}
.desc_tag_text{
  padding: 0 12rpx;
  height: 36rpx;
  line-height: 36rpx;
  border-radius: 18rpx;
  background-color: #e8f6fd;
  @include font_theme;
  font-size: 22rpx;
  //@include flex;
  overflow: hidden; //块元素超出隐藏
  max-width: 72rpx;
  text-overflow: ellipsis; //文本超出块元素时以省略号的形式显示
  white-space: nowrap; //规定段落中的文本不进行换行
}
.desc_tag_1{
  margin: 0 2rpx;
}

.desc_two {
  margin-top: 8rpx;
  font-size: 24rpx;
  color: #333;

  text:last-child {
    margin-left: 24rpx;
  }
}

.host {
  margin-top: 8rpx;
  font-size: 24rpx;
  color: #a6aab2;
  text-align: center;
  /* 超出部分...代替*/
  //width: 135px;
  width: 100%;
  white-space: nowrap;
  text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
  overflow: hidden;
}
.recommendedMedicine{
  margin-left: 15px;
  font-size: 16px;
  font-weight: bold;
}
</style>