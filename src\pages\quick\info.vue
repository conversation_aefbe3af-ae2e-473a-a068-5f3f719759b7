<template>
  <div class="info">
    <div class="item">
      <span class="label">姓名</span>
      <input
        type="text"
        v-model="name"
        maxlength="10"
        placeholder="请输入您的姓名"
      />
    </div>

    <div class="item">
      <span class="label">手机号</span>
      <input
        type="tel"
        v-model="telNo"
        maxlength="11"
        placeholder="请输入您的手机号"
      />
    </div>

    <FOOTER @click="send">确定</FOOTER>
  </div>
</template>

<script>
import FOOTER from '@/components/footer_button/button.vue';
import { updateOrderPatient } from '@/api/order';

import { patientFastKFCompleteInfo, createFastPrescription } from '@/api/base';

import { Toast } from '@/common/js/pay.js';

export default {
  name: 'Info',
  components: {
    FOOTER,
  },
  data() {
    return {
      // 名称
      name: '',
      // 手机号
      telNo: '',
      // 订单号
      orderNo: '',
      // 模板id
      dpmpId: '',
    };
  },
  onLoad(v) {
    this.orderNo = v.orderNo;
    this.dpmpId = v.dpmpId;
  },
  methods: {
    async send() {
      if (!this.name) {
        Toast('请输入姓名');
        return;
      }
      if (!this.telNo) {
        Toast('请输入手机号');
        return;
      }
      if (this.telNo.length != 11) {
        Toast('手机号格式错误');
        return;
      }

      let obj = {
        name: this.name,
        telNo: this.telNo,
        userId: uni.getStorageSync('userId'),
      };

      try {
        uni.showLoading({
          mask: true,
        });

        let { data } = await patientFastKFCompleteInfo(obj);

        let param = {
          ...data,
          dpmpId: this.dpmpId,
          orderNo: this.orderNo,
        };
        // 直接调用接口 之后到成功页
        await createFastPrescription(param);

        // 更改订单中就诊人id
        await updateOrderPatient({
          orderNo: this.orderNo,
          patientId: data.patientId,
        });

        uni.hideLoading();

        // 存就诊人id
        uni.setStorageSync('patientIdList', [data.patientId]);

        uni.redirectTo({
          url: '/pages/quick/result?orderNo=' + this.orderNo,
        });
      } catch (error) {
        uni.hideLoading();
        console.log(error);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}
page {
  background-color: #f5f5f5;
}
.info {
  padding-top: 86rpx;

  .item {
    margin-bottom: 68rpx;
    padding: 0 64rpx;

    .label {
      font-size: 48rpx;
      font-weight: bold;
    }

    input {
      width: 100%;
      height: 146rpx;
      font-size: 44rpx;
      background-color: #fff;
      border-radius: 8rpx;
      margin-top: 32rpx;
      padding: 0 32rpx;
      font-weight: bold;
    }
  }
}
</style>
