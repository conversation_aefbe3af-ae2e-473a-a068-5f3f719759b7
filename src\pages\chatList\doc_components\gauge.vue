<template>
  <!-- 量表 -->
  <view class="gauge">
    <!-- 头像 -->
    <image
      @click="head"
      :src="imgUrl || '/static/images/docHead.png'"
      mode="aspectFill"
      class="chat_user_img"
    />
    <!-- 卡片 -->
    <view class="gauge_content" @click="click">
      <!-- 标题 -->
      <view class="content_title">量表</view>
      <!-- 内容 -->
      <view class="content_cont">
        <!-- 左侧图标 -->
        <image src="/static/images/chat/lb.png" class="cont_icon" />
        <!-- 右侧文案 -->
        <view class="cont_right">
          <!-- 诊断 -->
          <view class="cont_right_title">量表：{{ title }}</view>
          <!-- 根据状态区分 -->
          <view class="cont_right_info">
            <text>请填写问诊单内容</text>
          </view>
        </view>
      </view>
      <!-- 量表状态 -->
      <view class="gauge_status">
        <!-- 未填写 -->
        <image
          src="/static/images/chat/un-answer.png"
          class="status_icon"
          v-if="isFeedback == 0"
        />
        <!-- 已填写 -->
        <image
          src="/static/images/chat/answer.png"
          class="status_icon"
          v-if="isFeedback == 1"
        />
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    // 头像
    imgUrl: {
      type: String,
      default: '',
    },
    // 量表标题
    title: {
      type: String,
      default: '',
    },
    // 填写状态
    isFeedback: {
      type: [Number, String],
      default: 0,
    },
  },
  methods: {
    head() {
      this.$emit('head');
    },
    click() {
      this.$emit('click');
    },
  },
};
</script>

<style scoped lang="scss">
.gauge {
  max-width: 600upx;
  box-sizing: border-box;
  @include flex(left);
  padding-bottom: 30upx;
  align-items: flex-start;

  // 头像
  .chat_user_img {
    width: 64upx;
    height: 64upx;
    border-radius: 4upx;
    margin-right: 16upx;
    flex: none;
  }

  // 内容
  .gauge_content {
    width: 516upx;
    padding: 24upx;
    background-color: #fff;
    color: #333;
    border: 1px solid #ececec;
    border-radius: 8upx 32upx 32upx 32upx;
    position: relative;

    // 标题
    .content_title {
      font-size: 28upx;
      color: $k-title;
      font-weight: bold;
    }

    // 底部内容
    .content_cont {
      @include flex(left);
      align-items: flex-start;
      margin-top: 20upx;

      // 左侧图标
      .cont_icon {
        width: 80upx;
        height: 80upx;
        border-radius: 8upx;
        margin-right: 24upx;
        flex: none;
      }

      // 右侧文案
      .cont_right {
        flex: 1;
        min-height: 80upx;
        @include flex(lr);
        flex-direction: column;
        align-items: stretch;

        // 标题
        .cont_right_title {
          font-size: 28upx;
          color: $k-title;
          font-weight: bold;
        }

        // 描述
        .cont_right_info {
          font-size: 24upx;
          color: $k-info-title;
        }
      }
    }

    // 处方状态
    .gauge_status {
      width: 100%;
      height: 82upx;
      margin-top: -10upx;
      position: absolute;
      top: 0;
      left: 0;
      padding-right: 32upx;
      @include flex(right);
      box-sizing: border-box;

      // 图标
      .status_icon {
        width: 50upx;
        height: 82upx;
        margin-left: 20upx;
      }
    }
  }
}
</style>
