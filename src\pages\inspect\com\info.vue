<template>
  <!-- 订单信息 -->
  <view class="order_info">
    <!-- 重要 -->
    <view class="order_number">
      <!-- 单号 -->
      <view class="order_bold"
        >检{{ isLis ? '验' : '查' }}单号：{{
          detail.pliCode || detail.ppiCode
        }}</view
      >
      <!-- 医院 -->
      <view class="order_bold">医院名称：{{ detail.hosName }}</view>
    </view>

    <!-- 患者信息 -->
    <view class="user">
      <text>{{ detail.patientName }}</text>
      <text>{{ detail.sex }}</text>
      <text>{{ detail.age }}{{ detail.ageUnit }}</text>
    </view>
    <!-- 开单医生 -->
    <view class="info_item">
      <text>开单医生：{{ detail.docName }}</text>
    </view>
    <!-- 医生科室 -->
    <view class="info_item">
      <text>医生科室：{{ detail.deptName }}</text>
    </view>
    <!-- 临床诊断 -->
    <view class="info_item">
      <text>临床诊断：{{ detail.ppiDiag }}</text>
    </view>
    <!-- 开单时间 -->
    <view class="info_item">
      <text>预约时间：{{ detail.time }}</text>
    </view>

    <!-- 二维码 -->
    <view class="ewm">
      <QRCODE
        :val="isLis ? detail.pliCode : detail.ppiCode"
        :size="300"
        background="#ffffff"
        foreground="#000000"
        pdground="#000000"
        icon="/static/images/logo-img.png"
        :iconSize="45"
        onval
        loadMake
      />

      <!-- 单号 -->
      <view class="ewm_num">{{ isLis ? detail.pliCode : detail.ppiCode }}</view>
    </view>
  </view>
</template>

<script>
import QRCODE from '@/components/tki-qrcode/tki-qrcode.vue';
export default {
  name: 'OrderInfo',
  components: {
    QRCODE,
  },
  props: {
    isLis: {
      type: Boolean,
      default: false,
    },
    detail: {
      type: Object,
      default: () => {},
    },
  },
};
</script>

<style lang="scss" scoped>
.order_info {
  padding: 0 32rpx;
  background: #fff;
  border-radius: 8rpx;
  font-size: 28rpx;

  .order_number {
    height: 148rpx;
    border-bottom: 1px dashed #eee;
    @include flex;
    align-items: stretch;
    flex-direction: column;

    .order_bold {
      font-size: 30rpx;
      color: #333;
      line-height: 60rpx;
      font-weight: bold;
    }
  }

  .user {
    @include flex(lr);
    padding-top: 16rpx;

    text {
      font-size: 30rpx;
      color: #333;
      font-weight: bold;
    }
  }

  .info_item {
    font-size: 28rpx;
    color: #666;
    margin-top: 16rpx;
    line-height: 40rpx;
  }

  .ewm {
    @include flex;
    flex-direction: column;
    padding: 38rpx 0 32rpx;

    .ewm_num {
      font-weight: bold;
      color: #333;
      padding-top: 28rpx;
    }
  }
}
</style>
