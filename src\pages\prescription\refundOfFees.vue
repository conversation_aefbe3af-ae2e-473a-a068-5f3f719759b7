<template>
<div class="refund">
  <u-form :model="form" ref="uForm" label-width="150px">
    <u-form-item label="退费金额" prop="price" >
      <u-input class="price" v-model="form.price" type="number" disabled/>
     <text style="color:red;margin-left: 5px">元</text>
    </u-form-item>
    <u-form-item label="退费原因" prop="refundReason">
      <picker @change="bindPickerChange" range-key="name" :range="array" style="width: 100%;">
        <view style="display: flex;align-items: center;justify-content: space-between;width: 100%;">
          <text class="uni-input">{{ form.reason?form.reason:'请选择退费原因' }}</text>
          <image
              src="../../static/images/question/down.png"
              class="downIcon"
          ></image>
        </view>
      </picker>
    </u-form-item>
    <u-form-item v-if="form.refundReason=='1100.9'" label="申请说明" prop="applyReason">
      <view class="descriptionOfApplication">
        <u-input v-model="form.applyReason" type="textarea" :height="150" :auto-height="true" maxlength="100"/>
        <view class="po">{{form.applyReason.length}}/100</view>
      </view>
    </u-form-item>
  </u-form>
  <FOOTER
      :isDef="false"
      :isCw="true"
      @click="submit"
      :disabled="isSubmitting"
  >
    {{isSubmitting ? '提交中...' : '提交'}}
  </FOOTER>
</div>
</template>
<script>
  import FOOTER from "@/components/footer_button/button.vue";
  import {getSysCodeByType, tradeRefundAndAddYf} from "../../api/shop";
  export default {
    components:{FOOTER},
    data() {
      return {
        form:{
          reason:"",
          price:null,
          applyReason:'',
          refundReason:null
        },
        array:[

        ],
        orderNo:'',
        isSubmitting: false,
        rules: {
          refundReason: [
            {
              required: true,
              message: '请选择退费原因',
              trigger: ['change','blur'],
            }
          ],
          applyReason: [
            {
              required: true,
              message: '请输入申请说明',
              trigger: ['change','blur'],
            }
          ]
        }
      }
    },
    onLoad(options){
      this.form.price= options.totalMoney||0
      this.orderNo=options.orderNo
      this.getReason()
    },
    // 必须要在onReady生命周期，因为onLoad生命周期组件可能尚未创建完毕
    onReady() {
      this.$refs.uForm.setRules(this.rules);
    },
    methods:{
     async getReason(){
        let { data } = await getSysCodeByType('1100');
        let arr = data.map((v) => {
          return {
            name: v.meaning,
            value: v.code,
          };
        });
         this.array=arr
      },
      bindPickerChange(e){
        this.form.reason = this.array[e.detail.value].name;
        this.form.refundReason=this.array[e.detail.value].value
      },
      submit() {
        if (this.isSubmitting) return;
        this.$refs.uForm.validate(valid => {
          if (valid) {
            this.isSubmitting = true;
            const data={
              orderNo:this.orderNo,
              returnOrderNo:this.orderNo,
              refundReason:this.form.refundReason,
              applyReason:this.form.applyReason
            }
            tradeRefundAndAddYf(data).then(res=>{
              uni.showToast({
                title: '提交成功',
                icon: 'none'
              })
              setTimeout(() => {
                this.isSubmitting = false;
                uni.navigateBack({
                  delta: 1
                })
              }, 500);
            }).catch(err => {
              this.isSubmitting = false;
              uni.showToast({
                title: '提交失败',
                icon: 'none'
              })
            })
          } else {
            this.isSubmitting = false;
            console.log('验证失败');
          }
        });
      }
    }
  }
</script>
<style scoped lang="scss">
.refund{
  padding: 20px;
}
.descriptionOfApplication{
  width: 100%;
  position: relative;
  .po{
    position: absolute;
    bottom: -15px;
    right: 10px;
    color: #8a8a8a;
  }
}
.downIcon {
  width: 32rpx;
  height: 32rpx;
  margin-left: 20px;
}
.price{
  text-align: right !important;
  color: red !important;
  ::v-deep .u-input__input{
    color: red !important;
  }
}

</style>