<template>
  <!-- 检验预约结果 -->
  <view class="lis_result">
    <!-- 状态 -->
    <RESULT :time="time" isLis :isSuc="status == 1" />

    <!-- 二维码 -->
    <EWM
      isLis
      v-if="status == 1"
      :code="code"
      :time="time"
      :pliId="pliId"
      :dloId="dpoId"
    />
  </view>
</template>

<script>
import { getProLisInfoByID } from '@/api/inspect';
import RESULT from '../com/result.vue';
import EWM from '../com/resultEwm.vue';

export default {
  name: 'LisResult',
  components: {
    RESULT,
    EWM,
  },
  data() {
    return {
      status: -1,
      address: {},
      time: '',
      code: '',
      pliId: '',
      dpoId: '',
    };
  },
  onLoad(opt) {
    this.status = opt.type || 1;
    this.getDetail(opt.id);
  },
  methods: {
    async getDetail(id) {
      let { data } = await getProLisInfoByID(id);
      let {
        appointDate,
        appointEndTime,
        appointStartTime,
        pliCode,
        pliId,
        dpoId,
      } = data;
      this.time =
        appointDate +
        ' ' +
        appointStartTime.slice(0, 5) +
        '-' +
        appointEndTime.slice(0, 5);
      this.code = pliCode;
      this.pliId = pliId;
      this.dpoId = dpoId;
    },
  },
};
</script>

<style lang="scss" scoped>
.lis_result {
  padding: 32rpx;

  .ewm {
    margin-top: 24rpx;
  }
}
</style>
