<template>
  <view >
    <view class="page-container">
      <scroll-view
        class="content-container"
        scroll-y="true"
        @scrolltolower="loadMoreData"
      >
       <view class="empty" v-if="historyList.length == 0">
          <image
            src="../../static/images/index/box_empty.png"
            class="emptyImg"
          ></image>
          <view>暂无其他信息</view>
        </view>
        <!-- 消息循环 -->
        <view
          v-else="historyList.length>0"
          v-for="(item, index) in historyList"
          :key="index"
          class="admibMsg"
          @click="readMsg(item)"
        >
          <view class="leftImg" style="margin-right: 27px">
            <img src="/static/doc/log.png" class="tips" />
            <view class="unread" v-if="item.status == 'unread'"></view>
          </view>
          <view class="contentDiv" style="flex: 1;">
            <!-- 标题类型 -->
            <view class="title" style="display: flex;align-items: center;justify-content: space-between;flex: 1">
              <block v-if="item.ext.type == 'HZ_XT_QF'">系统公告</block>
              <block v-else-if="item.ext.type == 'HZ_SF_SF'">随访</block>
              <block v-else-if="item.ext.type == 'HZ_WZ_ADD_JZR'">温馨提醒</block>
              <block v-else-if="item.ext.type == 'HZ_PLTS_DIAGNOSIS'">随访提醒</block>
              <block v-else>问诊信息</block>
               <!-- 时间 -->
            <view class="time">{{ getTime(item.time) }}</view>
            </view>
            <!-- 推送详情 -->
            <view class="content">{{ item.content }}</view>
          </view>
        </view>
        <!-- 加载更多 -->
        <view v-if="isShowMore">
          <uni-load-more :status="status"></uni-load-more>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script>
import date from '@/utils/date';
import {getQuestionStatus} from "../../api/chat";
export default {
  name: 'sysTemList',
  data() {
    return {
      list: [],
      loading: false,
      isShowMore: false,
      refreshing: false,
      listQuery: {
        page: 1,
        maxNum: 0,
        limit: 10,
        total: 10,
      },
      historyList: [],
      chatId: '',
      status: 'loading',
    };
  },
  computed: {
    chatList() {
      let chatList = this.$store.getters.getChatList;
      let list = chatList.chatRecordList;
      if (list) {
        list.sort(function(a, b) {
          return b.time - a.time; //时间反序
        });
      } else {
        list = [];
      }
      this.listQuery.total = list.length;
      return list;
    },
  },
  async onShow() {

  },
  onPullDownRefresh() {
    // 清空列表数据
    this.listQuery.maxNum = 0;
    this.historyList = [];
    this.loadMoreData();
  },
  async created() {
    this.chatId = 'admin,admin';
    let obj = {
      chatId: this.chatId,
    };
    await this.$store.dispatch('getChatListId', obj);
    setTimeout(() => {
      this.loadMoreData();
    }, 200);
  },
  methods: {
    setIcon(ext) {
      let path = '';
      switch (ext.type) {
        // 问诊相关
        case 'HZ_WZ_SF': //审方
        case 'HZ_WZ_YSJZ'://医生接诊
        case 'HZ_WZ_JSWZ'://结束问诊
        case 'HZ_WZ_YSTZ'://医生退诊
        case 'HZ_WZ_JJJZ'://拒绝接诊
          path = CONFIG_ENV.VUE_APP_SHARE;
          break;
        // 系统图标
        case 'HZ_XT_QF':
        case 'HZ_SF_SF':
          //path = 'static/images/chat/tips.png';
          path = CONFIG_ENV.VUE_APP_SHARE;
          break;
        default:
          //path = 'static/images/chat/tips.png';
          path = CONFIG_ENV.VUE_APP_SHARE;
          break;
      }
      return path;
    },
    getTime(timestamp) {
      let time = '';
      let times = date.DateDifferenceMsgTime(timestamp);
      if (times.days > 0) {
        time = date.getNowDate(Number(timestamp));
      } else if (times.hours > 0) {
        time = times.hours + '小时前';
      } else if (times.minutes > 0) {
        time = times.minutes + '分钟前';
      } else {
        time = '刚刚';
      }
      return time;
    },
    async readMsg(item) {
      if(item.ext.type==='HZ_PLTS_DIAGNOSIS'){
        let isFeedback=null
        // 判断该量表的状态，是否已填写
        let res = await getQuestionStatus({
          sendId: item.ext.sendId,
          patientId: item.ext.patientId,
        });
        isFeedback = res.data.isFeedback; // 是否反馈 0否，1是
        item.ext.isFeedback = isFeedback;
        let param = {
          sendId:item.ext.sendId,
          patientId:item.ext.patientId,
          docId:item.ext.docId,
        };
        if (isFeedback == "1") {
          uni.navigateTo({
            url:
                "/pages/chatCardDetail/questionnaireRead?param=" +
                JSON.stringify(param),
          });
        } else {
          uni.navigateTo({
            url:
                "/pages/chatCardDetail/questionnaire?action=" +
                "chatRoom" +
                "&param=" +
                JSON.stringify(param),
          });
        }
        return;
      }
      if (item.status == 'unread') {
        var bodyId = item.id; // 需要发送已读回执的消息id
        var ackMsg = new this.$im.message('read', this.$im.conn.getUniqueId());
        ackMsg.set({
          id: bodyId,
          to: item.from,
        });
        this.$im.conn.send(ackMsg.body);
        item.status = 'read';
        this.$store.commit('updateMessageStatus', item);
      }
      if(item.ext.type =='HZ_WZ_ADD_JZR'){
        uni.setStorageSync("patientId",item.ext.patientId)
        uni.navigateTo({
          url: '/pages/personalCenter/patientManage/editorPatient/index',
        });
        return;
      }
      // 不存在扩展消息
      if (!item.ext) return;
      // 系统群发
      if (item.ext.type == 'HZ_XT_QF') {
        uni.navigateTo({
          url: './system/detail?id=' + item.ext.sendId,
        });
        return;
      }

      // 随访
      if (item.ext.type == 'HZ_SF_SF') return;
      // 设置到vuex
      this.$store.commit('SET_NOTICE_DETAIL', item);
      // 跳转页面
      uni.navigateTo({
        url: './notice/notice',
      });
    },
    loadMoreData() {
      uni.stopPullDownRefresh();
      this.isShowMore = true;

      if (this.listQuery.maxNum == this.listQuery.total) {
        this.status = 'noMore';
        return;
      }
      let maxNum = this.listQuery.maxNum + this.listQuery.limit;
      if (maxNum >= this.listQuery.total) {
        maxNum = this.listQuery.total;
      }
      for (var i = this.listQuery.maxNum; i < maxNum; i++) {
        if (i >= this.listQuery.total) {
          break;
        } else {
          this.historyList.push(this.chatList[i]);
        }
      }
      this.listQuery.maxNum = maxNum;
      this.isShowMore = false;
    },
  },
};
</script>

<style scoped lang="scss">
.empty {
  text-align: center;
  color: $k-info-title;
  font-size: 28rpx;
  margin-top: 320rpx;
}
.content-container {
  height: calc(100vh - 100px);
}

.admibMsg {
  width: 90%;
  margin: auto;
  margin-bottom: 32rpx;
  display: flex;
  align-items: center;
  background: #ffffff;
  padding: 32rpx;
  border-radius: 8rpx;
  border-bottom: 1px solid #ebebeb;
  border-radius: 5px;
background: rgba(255, 255, 255, 1);
box-shadow: 0px 1px 4px  rgba(121, 72, 234, 0.15);
  .leftImg {
    position: relative;
    margin-right: 20rpx;
    @include flex;

    .unread {
      width: 14rpx;
      height: 14rpx;
      border-radius: 50%;
      position: absolute;
      right: 0px;
      top: 0px;
      background: #ff5050;
    }
  }

  .contentDiv {
    overflow: hidden;
  }

  .tips {
    width: 80rpx;
    height: 62rpx;
    object-fit: contain;
    border-radius: 8rpx;
  }

  .title {
    color: #333333;
    font-size: 15px;
    font-weight: 600;
  }

  .content {
    font-size: 14px;
    color: #333333;
    @include hide;
    color: rgba(102, 102, 102, 1);
    margin-top: 12rpx;
  }

  .time {
    color: #999999;
    font-size: 12px;
  }
}
.emptyImg {
  width: 286rpx;
  height: 226rpx;
  margin-bottom: 40rpx;
}
</style>
