<template>
  <view>
    <!-- 订单信息 -->
    <ORDER v-if="source == 'gh'" :isConcat="2" :info="ghsInfo" style="margin-top: 40rpx;"/>
    <!-- 订单信息 -->
    <view v-if="source != 'gh'&&orderNo" class="order_info">
      <view class="info_title">订单信息 </view>
      <view class="info_item"> <text>订单编号</text> {{ ghsInfo.orderNo }} </view>

      <view class="info_item"> <text>创建时间</text> {{ ghsInfo.addTime }} </view>

      <view class="info_item" v-if="ghsInfo.payTime">
        <text>支付时间</text> {{ isScan == 1 ? ghsInfo.checkTime || ghsInfo.payTime : ghsInfo.payTime }}
      </view>

      <view class="info_item" v-if="ghsInfo.receiveTime && ghsInfo.payStatus != 9">
        <text>成交时间</text> {{ ghsInfo.receiveTime }}
      </view>

      <view class="info_item" v-if="ghsInfo.returnTime"> <text>退费时间</text> {{ ghsInfo.returnTime }} </view>

      <view class="info_item" v-if="ghsInfo.payStatus == 9"> <text>关闭时间</text> {{ ghsInfo.updateTime }} </view>
    </view>
    <view class="cauCon">
      <view class="titCon">
        <text>手机号</text>
        <u-input v-model="phoneVal" label="手机号：" @blur="telBlur" placeholder="请输入手机号" />
      </view>

      <view class="textCon">
        <view style="font-size: 14px">{{orderNo?'申请描述':'请输入您的建议'}}</view>
        <u-input
          v-model="message"
          autosize
          :border="true"
          type="textarea"
          maxlength="500"
          placeholder="请输入"
          @blur="textBlur"
          show-word-limit
        />
      </view>
      <view class="img-info">
        <view class="title_format" :class="{ x: isRequired }">上传多张图片<text class="font_hint">（9张）</text></view>
        <view class="describe_check_image">
          <view class="uploader">
            <template v-for="(item, index) in fileListLi">
              <view class="img-box">
                <image class="img-list" :src="item.url" mode="aspectFill" @click="previewImg(item.url)"></image>
                <image src="/static/images/question/image-delete.png" class="delete-img" @click="delectImg1(index)"></image>
              </view>
            </template>
            <view v-if="fileListLi.length < 9" class="img-box img_box_shang" @click="chooseImage1">
              <view>
                <image src="/static/images/分组 <EMAIL>"></image>
                <!--                <view>上传图片</view>-->
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="footerBtn">
      <button class="publicBtn" @click="saveCaSuggestion">提交</button>
    </view>
    <u-toast ref="uToast" />
  </view>
</template>
<script>
import myJsTools from '@/common/js/myJsTools.js'
import { uploadImg } from '@/api/oss.js'
import { saveSuggestion } from '@/api/user.js'
import { orderDetailNew } from '@/api/order.js'
import { pregOrderDetailNew } from '@/api/order.js'
import ORDER from '@/pages/order/detail/lisCom/order.vue'
export default {
  components: {
    ORDER
  },
  data() {
    return {
      phoneVal: '',
      message: '',
      fileListLi: [],
      // 字段是否必填
      isRequired: false,
      disease: [], // 提交时，用以缓存图片上传oss后返回的文件名
      diseaseIndex: 0,
      orderNo: '',
      source: '',
      ghsInfo: {}
    }
  },
  onLoad(options) {
    this.orderNo = options.orderNo || ''
    this.source = options.source || ''
    if (!this.orderNo) {
      return
    }
    if (this.source == 'gh') {
      this.getGhsInfo()
    } else {
      this.getDetail()
    }
  },
  methods: {
    async getGhsInfo() {
      let { data } = await pregOrderDetailNew({
        orderNo: this.orderNo
      })
      this.ghsInfo = data
    },
    async getDetail() {
      let { data } = await orderDetailNew({
        orderNo: this.orderNo
      })
      this.ghsInfo = data
    },
    //手机号正则表达式
    telBlur() {
      const re = /^1\d{10}$/
      if (!this.phoneVal) {
        this.$refs.uToast.show({
          title: '手机号不能为空',
          type: 'default'
        })
        return false
      } else if (!re.test(this.phoneVal)) {
        this.$refs.uToast.show({
          title: '请输入正确的手机号',
          type: 'default'
        })
        return false
      } else {
        return true
      }
    },
    textBlur() {
      if (this.message.length <= 5) {
        this.$refs.uToast.show({
          title: '请输入您的建议，不少于5个汉字...',
          type: 'default'
        })
        return false
      }
      return true
    },
    // 提交建议
    saveCaSuggestion() {
      const re = /^1\d{10}$/
      if (!this.phoneVal) {
        this.$refs.uToast.show({
          title: '手机号不能为空',
          type: 'default'
        })
        return
      } else if (!re.test(this.phoneVal)) {
        this.$refs.uToast.show({
          title: '请输入正确的手机号',
          type: 'default'
        })
        return
      }

      // 校验申请描述
      if (!this.message || this.message.trim() === '') {
        this.$refs.uToast.show({
          title: '申请描述不可为空',
          type: 'default'
        })
        return
      }

      if (this.message.trim().length < 5) {
        this.$refs.uToast.show({
          title: '内容需大于5个字',
          type: 'default'
        })
        return
      }
      const that = this
      const pathUrlArr = this.disease.map((item, index) => {
        return {
          url: item,
          fileName: Number(Math.random().toString().substr(3, 18) + Date.now()).toString(36) + '.png'
        }
      })
      console.log(uni.getStorageSync('wxInfo'))
      let paramsObj = {
        content: this.message,
        wxNickname: uni.getStorageSync('wxInfo').nickname,
        terminal: '患者端',
        phoneNumber: this.phoneVal,
        userId: uni.getStorageSync('userId'),
        fileJson: pathUrlArr,
        orderNo: this.orderNo,
        source: this.source
      }
      console.log(paramsObj)
      saveSuggestion(paramsObj).then((res) => {
        if (res.code == 20000) {
          that.$refs.uToast.show({
            title: '提交成功，稍后由客服联系您！',
            type: 'default'
          })
          setTimeout(() => {
            uni.navigateBack()
          }, 1000)
        }
      })
    },
    // 预览图片
    previewImg(img) {
      myJsTools.previewImg(img)
    },
    // 删除照片
    delectImg1(index) {
      this.fileListLi.splice(index, 1)
      this.disease.splice(index, 1)
    },
    // 上传病情图片
    chooseImage1() {
      let _this = this
      uni.chooseImage({
        count: 9, //默认9
        sizeType: ['compressed'],
        sourceType: ['album'], //从相册选择
        success: (res) => {
          let tempFiles = res.tempFiles
          if (tempFiles.length > 0) {
            // 循环压缩图片
            for (let i = 0; i < tempFiles.length; i++) {
              let el = tempFiles[i]
              myJsTools.setImgZip(el, (dataUrl) => {
                // 如果小于9张
                if (_this.fileListLi.length < 9) {
                  // 防止多次选择多张超出9张
                  _this.fileListLi.push({
                    base64: dataUrl.split(',')[1],
                    url: dataUrl,
                    name: ''
                  })
                }
                // 循环完成
                if (i == tempFiles.length - 1) {
                  // 开始上传 病情图片
                  _this.uploadDiseaseImg()
                }
              })
            }
          }
        }
      })
    },
    async uploadDiseaseImg() {
      let fileListLi = this.fileListLi
      let disease = this.disease
      if (fileListLi.length > 0) {
        // 图片上传加载loading
        uni.showLoading({
          title: '图片上传中'
        })
        for (let i = 0; i < fileListLi.length; i++) {
          if (fileListLi[i].base64) {
            let res = await this.uploadImgFun(fileListLi[i])
            fileListLi[i].base64 = ''
            disease.push(res.data.url)
          }
        }
        // 隐藏loading
        uni.hideLoading()
        this.disease = disease
      } else {
        this.patientRecordsInfo.diseaseImg = ''
      }
    },
    uploadImgFun(element) {
      let para = {
        folderType: 14,
        imgBody: element.base64,
        otherId: uni.getStorageSync('patientId')
      }
      return uploadImg(para)
    }
  }
}
</script>
<style lang="scss" scoped>
.footerBtn {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 20px;
  .publicBtn {
    box-sizing: border-box;
    background: linear-gradient(135deg, #516afb 0%, #859bff 99.97%);
    line-height: 44rpx;
    color: #ffffff;
    font-weight: 500;
    font-size: 32rpx;
    width: 100%;
    border-radius: 66rpx;
    padding: 20rpx 0;
  }
}
.cauCon {
  padding: 20px;
  .titCon {
    display: flex;
    align-items: center;
    text {
      padding-right: 20px;
      font-size: 14px;
      color: #333333;
    }
  }
  .textCon {
    margin-top: 20px;
  }
  .textCon /deep/ .u-input {
    background-color: #f0f0f0;
    margin-top: 8px;
  }
  .titCon /deep/ .uni-input-input {
    font-size: 12px !important;
    text-align: right !important;
    color: #999999 !important;
  }

  .titCon /deep/ .uni-input-placeholder {
    font-size: 12px !important;
    color: #999999 !important;
  }
  .img-info {
    .title_format {
      font-size: 14px;
      font-weight: 400;
      color: #333333;
      line-height: 22px;
      margin: 38rpx 0 20rpx 0;
      .font_hint {
        font-size: 14px;
        color: #999;
        font-weight: 500;
      }
    }
    .describe_check_image {
      color: #666666;
      border-radius: 8rpx;
      padding: 18rpx;
      font-size: 28rpx;
      .font_hint_pb {
        font-size: 32rpx;
        color: #999;
      }

      .uploader {
        display: flex;
        align-items: center;
        flex-direction: row;
        flex-wrap: wrap;
      }
      .img_box_shang,
      .img-box {
        width: 198rpx;
        height: 175rpx;
        border-radius: 8px;
        border: 0.5px dashed #836aff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18rpx;
        text-align: center;
        color: #666666;
        margin-top: 20rpx;
        position: relative;
      }

      .img-box:not(:nth-child(3n + 1)) {
        margin-left: 22rpx;
      }

      .img-list {
        display: block;
        width: 100%;
        height: 100%;
      }

      .img_box_shang image {
        width: 40rpx;
        height: 38rpx;
      }

      .delete-img {
        width: 40rpx;
        height: 40rpx;
        position: absolute;
        top: -10rpx;
        right: -10rpx;
      }
    }
  }
}
.order_info {
  position: relative;
  background: #fff;
  padding: 10rpx 32rpx;
  width: 90%;
  margin: auto;
  margin-top: -32rpx;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px 1px 4px  rgba(121, 72, 234, 0.15);
  border-radius: 10px;
  margin-top: 40rpx;
    .info_title {
      font-size: 28rpx;
      font-weight: bold;
      line-height: 68rpx;
    }

    .info_item {
      font-size: 28rpx;
      line-height: 68rpx;

      text {
        display: inline-block;
        width: 140rpx;
      }
    }
  }
</style>
