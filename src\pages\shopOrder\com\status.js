/*
  Author: 王可 (<EMAIL>)
  status.js (c) 2021
  Desc: 判断状态返回文案
  Created:  2021/11/15下午3:29:52
  Modified: 2021/12/27下午5:17:24
*/

/**
 * 判断是否显示 补开发票按钮
 * 若商城订单提交时，未开具发票，后续订单审核通过，但未退费、未交易关闭的都可以补开发票，暂无时间限制
 * 提交补开发票申请后，补开发票按钮不显示
 * @param {object} obj 多状态订单
 * @param {string|number} obj.orderStatus 订单状态
 * @param {string|number} obj.auditStatus 审核状态
 * @param {string|number} obj.payType 支付方式
 * @param {string|number} obj.invoicetitle 发票抬头
 * @return {boolean} 是否显示
 */
export const isShowInvoice = ({
  orderStatus,
  auditStatus,
  payType,
  invoicetitle,
}) => {
  // 如果已填写
  if (invoicetitle) return false;
  // 如果退费 关闭
  if (orderStatus > 5) return false;
  // 非退费关闭
  if (auditStatus == 3) return true;
  // 其他情况
  return false;
};

/**
 * 判断显示 再次购买按钮
 * 将药品添加到购物车，若购物车中存在相同药店的药品，则进行替换，
 * 并进入提交需求页面，将之前订单的信息进行回显，若退出再次进入，无需保留信息
 * @param {object} obj 多状态订单
 * @param {string|number} obj.orderStatus 订单状态
 * @returns {boolean} 是否显示
 */
export const isShowRepeat = ({ orderStatus }) => {
  // 退费 交易关闭
  if (orderStatus > 3) return true;

  return false;
};

/**
 * 判断显示 取消订单按钮
 * @param {object} obj 多状态订单
 * @param {string|number} obj.auditStatus 审核状态
 * @param {string|number} obj.orderStatus 订单状态
 * @param {string|number} obj.refundAuditStatus 退费申请状态
 * @param {string|number} obj.deliverytype 物流方式
 * @returns {boolean} 是否显示
 */
export const isShowRefund = ({ orderStatus, auditStatus, deliverytype }) => {
  // 已退费 或 交易关闭 或 待收货 待取药
  if (orderStatus > 5) return false;

  // 已完成 不可取消
  if (orderStatus == 4) return false;

  // 物流 待收货
  if (deliverytype != 2 && orderStatus == 3) return false;

  // 拒单
  if (auditStatus > 4) return false;

  return true;
};

/**
 * 判断显示 取药码按钮
 */
export const isShowDrugCode = ({
  auditStatus,
  orderStatus,
  deliverytype,
  logisticsstatus,
}) => {
  // 订单失效
  if (orderStatus > 3) return false;

  // 非通过状态
  if (auditStatus != 3) return false;

  // 非自提
  if (deliverytype != 2) return false;

  // 已完成
  if (logisticsstatus > 1) return false;

  return true;
};

/**
 * 判断显示 修改订单按钮
 */
export const isShowEdit = ({ orderStatus, auditStatus, payType }) => {
  // 线上支付 且待支付
  if (payType < 5 && payType > 0 && orderStatus == 1) return false;
  // 退费 交易关闭
  if (orderStatus > 5) return false;
  // 非拒单
  if (auditStatus < 5 && auditStatus != 3) return true;
  return false;
};

/**
 * 判断显示 查看物流按钮
 */
export const isShowLogistics = ({ orderStatus, logisticsstatus }) => {
  // 未交易完成 且为快递 且为收货
  if (orderStatus < 4 && logisticsstatus == 1) return true;
  return false;
};

/**
 * 判断显示 去支付按钮
 */
export const isShowPay = ({ orderStatus, payType }) => {
  // 线上支付
  if (payType < 5 && payType > 0) {
    if (orderStatus == 1) return true;
  }

  return false;
};
