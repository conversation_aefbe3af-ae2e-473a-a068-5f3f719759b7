<!--
 * @Descripttion:
 * @version:
 * @Author: zhengyangyang
 * @Date: 2025-06-13 10:35:35
 * @LastEditors: zhengyangyang
 * @LastEditTime: 2025-07-09 17:08:16
-->
<template>
  <view class="promotion-page">
    <image
      class="promotion-banner"
      :src="bannerImage"
      mode="widthFix"
      @error="handleImageError"
    />
    <!-- <view class="button-container">
      <image
        class="promotion-button"
        :src="buttonImage"
        mode="widthFix"
        @click="handleShare"
        @error="handleButtonError"
      />
    </view> -->
  </view>
</template>

<script>
export default {
  data() {
    return {
      bannerImage: '/static/618/618-banner.jpg', // 618活动banner图
      buttonImage: '/static/618/618-button.png', // 618活动按钮图
    }
  },
  methods: {
    handleImageError() {
      // 图片加载失败处理
      console.error('banner图片加载失败')
      uni.showToast({
        title: 'banner图片加载失败',
        icon: 'none',
      })
    },
    handleButtonError() {
      console.error('按钮图片加载失败')
      uni.showToast({
        title: '按钮图片加载失败',
        icon: 'none',
      })
    },
    handleShare() {
      const openId=uni.getStorageSync('wxInfo').openId
      // 判断当前时间是否在2025年7月16日00:00:00之后
      const now = new Date();
      const targetDate = new Date('2025/07/16 00:00:00');
      console.log(now,targetDate);
      
      if (now > targetDate) {
        // 如果当前时间在目标时间之后，显示活动结束提示
        uni.showToast({
          title: '本次内购活动结束，敬请期待下次活动~',
          icon: 'none',
          duration: 2000
        });
        return;
      }
      
      // 如果当前时间在目标时间之前，继续原有流程
      // 测试
      // window.location.href =
      // //'https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx6683b06aeddb37ac&redirect_uri=https%3A%2F%2Fszhy.dchealthcloud.com%2Fcloud%2FcloudHosPatient%2Findex.html%3Fappid%3Dwx6683b06aeddb37ac%26action%3D618&response_type=code&scope=snsapi_userinfo&state=STATE#wechat_redirect'
      // 正式
      window.location.href =
        'https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx54529fae788f1776&redirect_uri=https%3A%2F%2Fsz.dhchospital.com%2Fcloud%2FcloudHosPatient%2Findex.html%3Fappid%3Dwx54529fae788f1776%26action%3D618&response_type=code&scope=snsapi_userinfo&state=STATE#wechat_redirect'
    },
  },
}
</script>

<style lang="scss" scoped>
.promotion-page {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #ffffff;
  position: relative;
}

.promotion-banner {
  width: 100%;
  height: auto;
}

.button-container {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-top: -40px; // 根据需要调整按钮与banner的间距
  position: absolute;
  bottom: 60rpx;
  z-index: 10;
}

.promotion-button {
  width: 80%; // 根据实际按钮图片调整大小
  max-width: 400px;
  height: auto;
  transition: all 0.3s ease;
}

.promotion-button:active {
  transform: scale(0.95);
}
</style>
