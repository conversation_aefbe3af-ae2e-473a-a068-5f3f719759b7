<template>
  <!-- 健康建议 -->
  <view class="proposal">
    <!-- 标题 -->
    <view class="proposal_head">
      <view class="title">{{ proposal.title }}</view>
      <view class="info">{{ proposal.cont }}</view>
    </view>
    <!-- 图片 -->
    <view class="proposal_imgs" v-if="proposal.urls">
      <block v-for="(item, index) in proposal.urls">
        <img v-img:click="item" class="img_item" />
      </block>
    </view>
  </view>
</template>

<script>
export default {
  name: 'Proposal',
  props: {
    proposal: {
      type: Object,
      default: () => {},
    },
  },
};
</script>

<style lang="scss" scoped>
.proposal {
  background-color: #fff;
  padding: 0 24rpx 24rpx;
  margin-bottom: 32rpx;
  border-radius: 12rpx;
  box-shadow: 0px 0px 16rpx 0px rgba(0, 0, 0, 0.05);

  .proposal_head {
    padding: 32rpx 0;

    .title {
      font-size: 28rpx;
      color: #333;
      font-weight: bold;
    }

    .info {
      font-size: 24rpx;
      color: #999;
      line-height: 36rpx;
      margin-top: 28rpx;
    }
  }

  .proposal_imgs {
    @include flex(left);
    gap: 22rpx;
    flex-wrap: wrap;

    .img_item {
      width: 198rpx;
      height: 198rpx;
      border-radius: 8rpx;
      object-fit: cover;
    }
  }
}
</style>
