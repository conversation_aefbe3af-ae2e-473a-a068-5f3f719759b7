import http from '../common/request/request.js';

//查询预约排班标志
export function getReserveFlag(param = {}) {
  return http({
    url: 'basic/provisitreal/docVisitFlagReserveFlag',
    param,
    method: 'post',
  });
}

// 查询医生某一天的出诊信息
export function getReserveTimeList(param = {}) {
  return http({
    url: 'basic/provisitreal/docVisitReserve',
    param,
    method: 'post',
  });
}

// 预约挂号
export function apponitRegister(param = {}) {
  return http({
    url: 'business/appoint/toMakeAnAppointment',
    param,
    method: 'post',
  });
}

// 取消预约
export function cancelAppointRegister(param = {}) {
  return http({
    url: 'business/paymentBusiness/refund',
    param,
    method: 'post',
  });
}

// 立即签到
export function signConfirm(param = {}) {
  return http({
    url: 'business/appoint/signIn',
    param,
    method: 'post',
  });
}

// 查询是否可复诊
export function judgmentBeforeRegistration(param = {}) {
  return http({
    url: 'business/proreceive/judgmentBeforeRegistration',
    param,
    method: 'post',
  });
}

// 查询我的预约列表
export function selectAppointListPage(param) {
  return http({
    url: 'basic/patientAppoint/selectAppointListPage',
    param,
    method: 'post',
  });
}

// 线下预约挂号
export function toMakeAnOffLineAppointment(param) {
  return http({
    url: 'business/appoint/toMakeAnOffLineAppointment',
    param,
    method: 'post',
  });
}
