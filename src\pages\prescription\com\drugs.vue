<template>
  <!-- 药品信息 -->
  <div class="drugs">
    <p class="drugs_title">处方信息</p>

    <!-- 未支付 且不可见 列表 -->
    <div class="list">
      <!-- 西药方 -->
      <view class="list_item" v-if="x.length">
        <!-- 数量价位 -->
        <view class="item">
          <view class="item_label">{{ x_name }}</view>
          <view class="item_num">共{{ x.length || 0 }}种药品</view>
          <view class="item_price">￥{{ getPriceNum(x) }}</view>
        </view>
      </view>
      <!-- 食品方 -->
      <view class="list_item" v-if="s.length">
        <!-- 数量价位 -->
        <view class="item">
          <view class="item_label">{{ s_name }}</view>
          <view class="item_num">共{{ s.length || 0 }}种药品</view>
          <view class="item_price">￥{{ getPriceNum(s) }}</view>
        </view>
      </view>
      <!-- 中药方 -->
      <view class="list_item" v-if="z.length">
        <!-- 数量价位 -->
        <view class="item">
          <view class="item_label">{{ z_name }}</view>
          <view class="item_num">共{{ z.length }}种药品</view>
          <view class="item_price">￥{{ getZyPrice(z) }}</view>
        </view>
        <!-- 用法用量 -->
        <view class="item_cont" v-if="false">
          <view class="item_label">用法</view>
          <view class="item_detail">{{ zy_use }}</view>
        </view>
      </view>
      <!-- 数量小计 -->
      <view class="list_count" v-if="false"
        >共{{ x.length + z.length + s.length }}种药品</view
      >
      <!-- 价位总计 -->
      <view class="list_price">
        合计：<span>￥{{ total }}</span>
      </view>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Drugs',
  props: {
    list: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      z: [],
      z_name: '',
      x: [],
      x_name: '',
      s: [],
      s_name: '',
      zy_use: '',
    };
  },
  computed: {
    total() {
      return (
        Number(this.getPriceNum(this.x)) +
        Number(this.getPriceNum(this.s)) +
        Number(this.getZyPrice(this.z))
      ).toFixed(2);
    },
  },
  created() {
    if (this.list.length) this.setDrugs(this.list);
  },
  methods: {
    // 获取中药总金额
    getZyPrice(arr) {
      let n = 0;
      // arr.forEach((v) => {
      //   // 单价 数量 付数
      //   n += v.price * 100 * v.quan * v.herbalNum;
      // });
      // return (n / 100).toFixed(2);
      arr.forEach((v) => {//改动
        // 单价 数量 付数
        n += (v.price * v.quan).toFixed(2)* 100* v.herbalNum
      });
      return (n / 100).toFixed(2);
    },
    // 获取西药总金额
    getPriceNum(arr) {
      let n = 0;
      arr.forEach((v) => {
        n += v.price * 100 * v.quan;
      });
      return (n / 100).toFixed(2);
    },
    // 新增中西药拆分数组
    setDrugs(arr) {
      let z = [];
      let z_name = '';
      let x = [];
      let x_name = '';
      let s = [];
      let s_name = '';
      if (!arr.length) return;
      // 循环
      arr.forEach((v) => {
        const type = v.proPrescriptionMasterVO.prescriptionType;
        // 西药
        if (type == '1' || type == '2') {
          x = [...x, ...v.details];
          x_name = v.proPrescriptionMasterVO.prescriptionTypeName;
        }
        // 食品
        if (type == '4') {
          s = [...s, ...v.details];
          s_name = v.proPrescriptionMasterVO.prescriptionTypeName;
        }
        // 中药
        if (type == '3') {
          v.details.forEach((item) => {
            item.herbalNum = v.proPrescriptionMasterVO.herbalNum;
          });
          z = [...z, ...v.details];
          z_name = v.proPrescriptionMasterVO.prescriptionTypeName;

          // 中药用法
          this.zy_use =
            v.proPrescriptionMasterVO.dduName ||
            '口服' +
              '，每日' +
              v.proPrescriptionMasterVO.rc +
              '次，每次' +
              v.proPrescriptionMasterVO.rj +
              '剂，用药' +
              v.proPrescriptionMasterVO.days +
              '天';
        }
        this.z = z;
        this.z_name = z_name;
        this.x = x;
        this.x_name = x_name;
        this.s = s;
        this.s_name = s_name;
        console.log('z',z)
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.drugs {
  background: #fff;
  border-radius: 8rpx;
  padding: 32rpx;

  &_title {
    font-size: 28rpx;
    color: #333;
    font-weight: bold;
  }

  .list {
    font-size: 28upx;

    .list_item {
      margin-top: 24upx;
      background-color: $k-page-bg-color;
      border-radius: 8upx;
      padding: 24upx;
      line-height: 28upx;

      .item {
        @include flex(lr);

        .item_label {
          width: 150upx;
          flex: none;
        }

        .item_num {
          flex: 1;
          text-align: left;
        }

        .item_price {
          color: red;
        }
      }

      .item_cont {
        @include flex(lr);
        padding-top: 28upx;

        .item_label {
          width: 150upx;
          flex: none;
        }

        .item_detail {
          flex: 1;
        }
      }
    }

    .list_count {
      padding-top: 24upx;
      text-align: right;
    }

    .list_price {
      text-align: right;
      margin-top: 12rpx;
      padding-top: 24rpx;
      // border-top: 1px solid #eee;

      span {
        color: red;
      }
    }
  }
}
</style>
