<template>
  <view class="ai-team-container">
    <view class="ai-team-header">
      <text class="ai-team-title">AI医生团队</text>
    </view>

    <view class="ai-team-grid">
      <!-- AI营养师 -->
      <view class="ai-doctor-card" @click="navigateToDoctor('yingyangshi')">
        <view class="avatar-container yellow-bg">
          <image
            class="avatar"
            src="/static/ai/ai-1.png"
            mode="aspectFill"
          ></image>
        </view>
        <view class="doctor-info">
          <text class="doctor-name">AI 营养师</text>
          <text class="doctor-desc">个体化营养守护专员</text>
        </view>
      </view>

      <!-- 正元AI医生 -->
      <view class="ai-doctor-card" @click="navigateToDoctor('zhengyuanAI')">
        <view class="avatar-container teal-bg">
          <image
            class="avatar"
            src="/static/ai/ai-2.png"
            mode="aspectFill"
          ></image>
        </view>
        <view class="doctor-info">
          <text class="doctor-name">正元AI医生</text>
          <text class="doctor-desc">正元AI医生</text>
        </view>
      </view>

      <!-- AI 脑瘤医生 -->
      <view class="ai-doctor-card" @click="navigateToDoctor('zhongliu')">
        <view class="avatar-container green-bg">
          <image
            class="avatar"
            src="/static/ai/ai-logo.png"
            mode="aspectFill"
          ></image>
        </view>
        <view class="doctor-info">
          <text class="doctor-name">AI 脑瘤医生</text>
          <text class="doctor-desc">全周期肿瘤健康管家</text>
        </view>
      </view>

      <!-- AI 免疫医生 -->
      <view class="ai-doctor-card" @click="navigateToDoctor('mianyi')">
        <view class="avatar-container pink-bg">
          <image
            class="avatar"
            src="/static/ai/ai-3.png"
            mode="aspectFill"
          ></image>
        </view>
        <view class="doctor-info">
          <text class="doctor-name">AI 免疫医生</text>
          <text class="doctor-desc">精准免疫调节专家</text>
        </view>
      </view>

      <!-- AI 肝病医生 -->
      <view class="ai-doctor-card" @click="navigateToDoctor('ganbing')">
        <view class="avatar-container peach-bg">
          <image
            class="avatar"
            src="/static/ai/ai-4.png"
            mode="aspectFill"
          ></image>
        </view>
        <view class="doctor-info">
          <text class="doctor-name">AI 肝病医生</text>
          <text class="doctor-desc">AI肝病医生</text>
        </view>
      </view>

      <!-- AI 肾病医生 -->
      <view class="ai-doctor-card" @click="navigateToDoctor('shenbing')">
        <view class="avatar-container orange-bg">
          <image
            class="avatar"
            src="/static/ai/patient.png"
            mode="aspectFill"
          ></image>
        </view>
        <view class="doctor-info">
          <text class="doctor-name">AI 肾病医生</text>
          <text class="doctor-desc">AI肾病医生</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {}
  },
  methods: {
    navigateToDoctor(type) {
      // 根据不同类型的医生导航到对应页面
      if (type) {
        uni.navigateTo({
          url: '/pages/aiAssistant/aiDoctor?type=' + type,
        })
      } else {
        uni.showToast({
          title: `即将推出${this.getDoctorName(type)}功能`,
          icon: 'none',
        })
      }
    },
    getDoctorName(type) {
      const doctorTypes = {
        nutrition: 'AI营养师',
        general: '正元AI医生',
        brain: 'AI脑瘤医生',
        immune: 'AI免疫医生',
        liver: 'AI肝病医生',
        kidney: 'AI肾病医生',
      }
      return doctorTypes[type] || 'AI医生'
    },
  },
}
</script>

<style lang="scss" scoped>
.ai-team-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  padding: 20px;
  box-sizing: border-box;
  position: relative;
  background-image: url('/static/ai/ai-bg.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

/* 创建网格背景效果 */
.ai-team-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  //   background-image: linear-gradient(
  //       rgba(255, 255, 255, 0.1) 1px,
  //       transparent 1px
  //     ),
  //     linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
  z-index: 0;
}

.ai-team-header {
  display: flex;
  justify-content: center;
  margin: 134rpx 0 100rpx;
  position: relative;
  z-index: 1;
}

.ai-team-title {
  font-size: 24px;
  font-weight: bold;
  color: #ffffff;
}

.ai-team-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin: 0 auto;
  width: 95%;
  background-image: url('/static/ai/ai-bg2.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  border-radius: 15px;
  padding: 70rpx 50rpx;
  position: relative;
  z-index: 1;
}

.ai-doctor-card {
  width: 45%;
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.avatar-container {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 10px;
  border: 2px dashed rgba(255, 255, 255, 0.5);
  padding: 2px;
  position: relative;
}

/* 添加虚线边框效果 */
.avatar-container::before {
  content: '';
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border: 1px dashed rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  pointer-events: none;
}

.yellow-bg {
  background-color: #ffff00; // 黄色背景
}

.teal-bg {
  background-color: #00ced1; // 青色背景
}

.green-bg {
  background-color: #4cff00; // 绿色背景
}

.pink-bg {
  background-color: #ff1493; // 粉色背景
}

.peach-bg {
  background-color: #ffdab9; // 桃色背景
}

.orange-bg {
  background-color: #ffa500; // 橙色背景
}

.avatar {
  width: 90px;
  height: 90px;
  border-radius: 50%;
}

.doctor-info {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.doctor-name {
  font-size: 16px;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 5px;
}

.doctor-desc {
  font-size: 12px;
  color: #ffffff;
  opacity: 0.8;
  text-align: center;
}
</style>
