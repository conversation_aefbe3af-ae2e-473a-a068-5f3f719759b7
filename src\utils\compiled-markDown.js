/*
 * @Descripttion: 
 * @version: 
 * @Author: zhengyangyang
 * @Date: 2025-06-11 10:27:02
 * @LastEditors: zhengyangyang
 * @LastEditTime: 2025-06-11 10:46:23
 */
import { marked } from 'marked';
/**
 * 将Markdown文本编译为HTML
 * @param {string} text - 需要编译的Markdown文本
 * @returns {string} 编译后的HTML字符串
 */
export function compiledMarkdown(text) {
    if (text && typeof text === 'string') {
        const departmentRegex = /<div id="department">(.*?)<\/div>/g;
        text = text.replace(departmentRegex, function(match, department) {
            return `<span style="color: #605ce5; cursor: pointer; text-decoration: underline; font-weight: 600; padding: 2px 6px; border-radius: 4px; display: inline-block; transition: all 0.3s ease;"
            onmouseover="this.style.backgroundColor='#e0defb'; this.style.boxShadow='0 1px 3px rgba(0, 0, 0, 0.1)';"
            onmouseout="this.style.backgroundColor='transparent'; this.style.boxShadow='none';"
            id="jumpTDepartment" data="${department}">${department}</span>`;
        });
    }

    const renderer = new marked.Renderer();

    renderer.table = function(header, body) {
        return (
            '<table style="width:100%; border-collapse:collapse; border:1px solid black; margin: 15px 0; scrollbar-width:none;">' +
            '<thead>' +
            header +
            '</thead>' +
            '<tbody>' +
            body +
            '</tbody>' +
            '</table>'
        );
    };

    renderer.tablecell = function(content, flags) {
        const type = flags.header ? 'th' : 'td';
        const style =
            'style="border:1px solid black; padding:8px; text-align:left;' +
            (flags.header ? ' background-color:#f2f2f2; font-weight:bold;' : '') +
            '"';
        return '<' + type + ' ' + style + '>' + content + '</' + type + '>';
    };

    renderer.list = function(body, ordered) {
        const type = ordered ? 'ol' : 'ul';
        if (ordered) {
            return (
                '<ol style="margin: 10px 0; padding-left: 25px;">' + body + '</ol>'
            );
        } else {
            return (
                '<ul style="margin: 10px 0; padding-left: 0; list-style-type: none;">' +
                body +
                '</ul>'
            );
        }
    };

    renderer.listitem = function(text, task, checked) {
        const parentTag = text.includes('<input ') ? 'ul' : this.options.parent;

        if (parentTag === 'ol') {
            return '<li style="margin: 6px 0;">' + text + '</li>';
        } else {
            return (
                '<li style="position: relative; margin: 6px 0; padding-left: 18px; line-height: 1.5;">' +
                '<span style="position: absolute; left: 2px; top: 0.65em; width: 6px; height: 6px; background-color: #605CE5; border-radius: 50%; display: inline-block;"></span>' +
                text +
                '</li>'
            );
        }
    };

    renderer.heading = function(text, level) {
        const fontSize = {
            1: '24px',
            2: '22px',
            3: '20px',
            4: '18px',
            5: '16px',
            6: '15px',
        }[level];

        return `<h${level} style="font-size:${fontSize}; margin:15px 0 10px; font-weight:600; color:#333;">${text}</h${level}>`;
    };

    renderer.paragraph = function(text) {
        if (text.includes('点击购药')) {
            return '<p style="margin: 10px 0; line-height: 1.6;color:blue;cursor: pointer;">' + text + '</p>';
        }
        return '<p style="margin: 10px 0; line-height: 1.6;">' + text + '</p>';
    };

    renderer.code = function(code, language) {
        return `<pre style="background-color:#f6f8fa; border-radius:5px; padding:12px; margin:15px 0; overflow-x:auto; max-width:100%; white-space:pre-wrap; scrollbar-width:none; -ms-overflow-style:none;"><code>${code}</code></pre><style>.chatItemContentText pre::-webkit-scrollbar, .chatItemContentText code::-webkit-scrollbar { display: none; width: 0; }</style>`;
    };

    return marked(text, {
        renderer: renderer,
        headerIds: false,
        mangle: false,
        gfm: true,
        headerPrefix: '',
    });
}