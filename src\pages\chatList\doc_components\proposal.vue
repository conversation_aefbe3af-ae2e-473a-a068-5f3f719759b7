<template>
  <!-- 处方建议 / 处方 -->
  <view class="chat_proposal">
    <!-- 头像 -->
    <image
      @click="head"
      :src="imgUrl || '/static/images/docHead.png'"
      mode="aspectFill"
      class="chat_user_img"
    />
    <view>
      <view class="chatName">{{chatName}}</view>
      <!-- 卡片 -->
      <view class="proposal_content" @click="click">
        <!-- 标题 -->
        <view class="content_title">
          <text v-if="!prescription">处方建议</text>
          <view class="cfTitle" v-if="prescription"
          ><img
              style="width: 14.5px; height: 15.8px; margin-right: 10px"
              src="../../../static/cfimCard.png"
              alt=""
          />
            处方单</view
          >
          <img
              style="width: 5px; height: 10px"
              src="../../../static/rightJt.png"
              alt=""
          />
        </view>
        <!-- 内容 -->
        <view class="content_cont">
          <!-- 左侧图标 -->
          <!--        <image src="/static/images/chat/rp.png" class="cont_icon" />-->
          <!-- 右侧文案 -->
          <view class="cont_right">
            <view class="cont_right_title"
            >患者姓名：{{ preDetail.proBusinessInfo.patientName }}
              {{ preDetail.proBusinessInfo.sex }}
              {{ preDetail.proBusinessInfo.age }}岁</view
            >
            <view class="cont_right_title">医生诊断：{{ diagName }}</view>
            <view
                class="cont_right_title"
                style="padding-bottom: 15upx; border-bottom: 1px solid #666666"
            >开方时间：{{
                preDetail.prescriptions[0].proPrescriptionMasterVO
                    .prescriptionTime
              }}</view
            >
            <!-- 药品列表 -->
            <block v-if="drugList.length > 0">
              <view
                  class="cont_right_title"
                  style="padding-top: 15upx"
                  v-for="(drugItem, index) in drugList"
                  :key="index"
              >
                {{ drugItem.drugName }}
                <text style="color: #666666; font-size: 12px; padding-left: 10upx"
                >{{ drugItem.gg }} {{ drugItem.unit }}</text
                >
                <text style="float: right">x{{ drugItem.quan }}</text>
                <view class="cont_right_title">
                  <text style="color: #666666; font-size: 12px"
                  >{{ drugItem.dduName }}：{{ drugItem.ddufName }} 每次{{
                      drugItem.eachQuan
                    }}{{ drugItem.eachUnit }}
                  </text>
                </view>
              </view>
            </block>

            <view class="payBtn" v-if="hzStstus == 1&&bussType!=8" @click.stop="toPay"
            >立即购买</view
            >

            <!-- 根据状态区分 -->
            <view class="cont_right_info">
              <text v-if="!prescription">开具了处方建议</text>
              <!--            <text v-if="prescription">处方有效为{{ day }}天，请及时购药</text>-->
            </view>
          </view>
          <!-- 处方状态 -->
          <view class="proposal_status" v-if="true">
            <!-- 待审核 -->
            <image
                src="/static/images/chat/un-review.png"
                class="status_icon"
                v-if="hzStstus == 0"
            />
            <!-- 已通过 -->
            <image
                src="/static/images/chat/tg.png"
                class="status_icon"
                style="width: 140upx; height: 140upx"
                v-if="hzStstus == 1"
            />
            <!-- 未通过 -->
            <image
                src="/static/images/chat/un_tg.png"
                class="status_icon"
                v-if="hzStstus == 2"
            />
            <!-- 已失效 -->
            <image
                src="/static/images/chat/invalid.png"
                class="status_icon"
                v-if="hzStstus == 7"
            />
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getPatientPrescriptionBusinessOrderInfo } from "@/api/cf";
export default {
  props: {
    // 头像
    imgUrl: {
      type: String,
      default: "",
    },
    // 诊断
    diagName: {
      type: String,
      default: "",
    },
    // 是否处方
    prescription: {
      type: Boolean,
      default: false,
    },
    // 有效期
    day: {
      type: [Number, String],
      default: 0,
    },
    // 处方状态
    hzStstus: {
      type: [Number, String],
      default: 0,
    },
    // 处方数据
    businessId: {
      type: String,
      default: "",
    },
    bussType:{
      type: [Number, String],
      default: 0,
    },
    chatName:{}
  },
  data() {
    return {
      preDetail: {},
      drugList: [],
    };
  },
  watch: {
    businessId(val) {
      if (val !== "") {
        this.getDetail(val);
      }
    },
  },
  created() {
    console.log("查看处方数据", this.businessId);
    if (this.businessId !== "") {
      this.getDetail(this.businessId);
    }
  },
  methods: {
    async getDetail() {
      let prescriptionQueryVO = {
        businessId: this.businessId,
        splitSingleShowFlag: "1",
      };
      let res = await getPatientPrescriptionBusinessOrderInfo(
        prescriptionQueryVO
      );
      this.preDetail = res.data[0];
      this.drugList = this.preDetail.prescriptions[0].details;
      console.log("查看处方详情数据", res);
      console.log("查看处方详情数据", this.drugList);
    },
    // 去生成订单
    toPay() {
      let url =
        "/pages/prescription/prescriptionDetail?businessId=" +
        this.businessId +
        "&status=0";
      // 跳转
      uni.navigateTo({
        url,
      });
    },
    head() {
      this.$emit("head");
    },
    click() {
      if(this.bussType==8){
        this.$emit("click");
        return
      }
      if (this.hzStstus == 1) return;
      this.$emit("click");
    },
  },
};
</script>

<style scoped lang="scss">
.chat_proposal {
  max-width: 600upx;
  box-sizing: border-box;
  @include flex(left);
  padding-bottom: 60upx;
  align-items: flex-start;

  .payBtn {
    width: 100%;
    height: 72upx;
    border-radius: 457px;
    background: linear-gradient(145.74deg, #7c67e6 0.14%, #c5bbf2 100%);
    line-height: 72upx;
    text-align: center;
    color: #fff;
    margin-top: 20upx;
  }
  // 头像
  .chat_user_img {
    width: 64upx;
    height: 64upx;
    border-radius: 4upx;
    margin-right: 16upx;
    flex: none;
  }

  // 内容
  .proposal_content {
    //width: 516upx;
    width: 100%;
    min-width: 280px;
    background-color: #fff;
    color: #333;
    border: 1upx solid #ececec;
    border-radius: 8upx;
    position: relative;
    box-shadow: 0upx 2upx 4upx #999999;
    // 标题
    .content_title {
      font-size: 28upx;
      color: $k-title;
      font-weight: bold;
      display: flex;
      justify-content: space-between;
      align-items: center;
      background-color: #f5f5ff;
      border-radius: 7upx;
      padding: 15upx 16upx 24upx 11upx;
      .cfTitle {
        display: flex;
        align-items: center;
      }
    }

    // 底部内容
    .content_cont {
      @include flex(left);
      align-items: flex-start;
      padding: 14upx 17upx;

      // 左侧图标
      .cont_icon {
        width: 80upx;
        height: 80upx;
        border-radius: 8upx;
        margin-right: 24upx;
        flex: none;
      }

      // 右侧文案
      .cont_right {
        flex: 1;
        min-height: 80upx;
        @include flex(lr);
        flex-direction: column;
        align-items: stretch;

        // 标题
        .cont_right_title {
          font-size: 24upx;
          color: $k-title;
          font-weight: bold;
        }

        // 描述
        .cont_right_info {
          font-size: 24upx;
          color: $k-info-title;
        }
      }
    }

    // 处方状态
    .proposal_status {
      width: 100%;
      height: 82upx;
      margin-top: -10upx;
      position: absolute;
      top: 0;
      left: 0;
      padding-right: 32upx;
      @include flex(right);
      box-sizing: border-box;

      // 图标
      .status_icon {
        width: 50upx;
        height: 82upx;
        margin-left: 20upx;
      }
    }
  }
}
</style>
