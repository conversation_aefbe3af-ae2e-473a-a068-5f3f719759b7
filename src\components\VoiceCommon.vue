<template>
  <div style="flex:1;">
    <view class="voice-button" :class="{'active': recState !== '0', 'disabled': disFlag}" @touchstart.prevent="startRecord" @touchend.prevent="stopRecord" @touchcancel.prevent="cancelRecord">
      按住说话
    </view>

    <!-- 录音中的遮罩层v-if="recState === '2'" -->
    <view class="recording-mask" v-if="recState === '2'">
      <image src="/static/images/voice-record.png" class="recording-gif" mode="widthFix"></image>
      <view class="recording-text">
        <image src="/static/images/voice-record-text.png" class="recording-text-gif"></image>
        <view class="recording-text-text">松开 识别</view>
      </view>
    </view>

    <!-- 语音识别结果遮罩层 v-if="showResult && rec_text" -->
    <view class="voice-result-mask" v-if="showResult && rec_text">
      <view class="voice-result">
        <textarea 
          class="voice-text" 
          v-model="rec_text" 
          :auto-height="true"
          :show-confirm-bar="false"
          :cursor-spacing="20"
          :adjust-position="true"
          focus
        ></textarea>
      </view>
      <view class="voice-actions">
        <view class="voice-action-btn cancel" @click="cancelVoiceResult">
          <u-icon name="close" color="#ccc" size="32"></u-icon>
        </view>
        <view class="voice-action-btn send" @click="sendVoiceResult">
          <text class="btn-text">发送</text>
        </view>
      </view>
    </view>
  </div>
</template>

<script>
export default {
  data () {
    return {
      wsconnecter: null,
      rec: null,
      sampleBuf: new Int16Array(),
      recState: "0", //录音状态：'0' 没开启或已关闭录音  '1' 正在连接  '2' 连接成功
      offline_text: "", //语音转成的文字
      rec_text: "",
      barHeights: [2, 2, 2, 2], //音量数组
      curIndex: 0,
      touchStartY: 0, // 触摸开始的Y坐标
      isCanceled: false, // 是否取消录音
      showResult: false, // 是否显示结果
      longPressTimer: null, // 长按定时器
      isLongPressing: false, // 是否正在长按
      isWechat: false, // 是否在微信浏览器中
      recorderManager: null, // 微信录音管理器
      isRecording: false, // 是否正在录音中
      reconnectCount: 0, // 重连次数
      maxReconnectCount: 3 // 最大重连次数
    }
  },
  props: {
    oldMsg: {
      type: String,
      default: ''
    },
    disFlag: {
      type: Boolean,
      default: false
    },
    isRealTime: {
      type: Boolean,
      default: false
    }
  },
  created () {
    this.checkEnvironment();
    this.init();
  },
  methods: {
    // 检查运行环境
    checkEnvironment() {
      // 检测是否在微信浏览器中
      const ua = navigator.userAgent.toLowerCase();
      this.isWechat = /micromessenger/i.test(ua);
      if (this.isWechat) {
        console.log('当前在微信浏览器环境中，仍然使用WebSocket方式');
      } else {
        console.log('当前在普通Web环境中');
      }
    },
    
    //初始化数据
    init () {
      try {
        if (!this.wsconnecter) {
          this.wsconnecter = new WebSocketConnectMethod({
            msgHandle: this.getJsonMessage,
            stateHandle: this.getConnState,
            url: 'wss://sz.dhchospital.com/socket/' //'wss://www.funasr.com:10096/'
          });
        }
        
        // 确保Recorder对象的初始化是在用户交互之后
        if (!this.rec) {
          this.rec = Recorder({
            type: "pcm",
            bitRate: 16,
            sampleRate: 16000,
            onProcess: this.recProcess,
            // 添加错误处理回调
            onError: (err) => {
              console.error('Recorder错误:', err);
              uni.showToast({
                title: '录音初始化失败，请重试',
                icon: 'none'
              });
            }
          });
        }
      } catch (error) {
        console.error('初始化失败:', error);
        uni.showToast({
          title: '录音初始化失败，请重试',
          icon: 'none',
          duration: 2000
        });
      }
    },

    // 开始录音（长按触发）
    startRecord (e) {
      if (this.disFlag) return; // 如果禁用状态，不响应
      
      this.touchStartY = e && e.touches && e.touches[0] ? e.touches[0].clientY : 0; // 记录起始Y坐标
      this.isCanceled = false; // 重置取消状态
      this.showResult = false; // 隐藏结果
      this.rec_text = ""; // 清空结果
      this.offline_text = "";
      
      // 在微信环境中可能需要先请求用户授权
      if (this.isWechat) {
        // 如果是实时识别模式，直接尝试连接
        if (this.isRealTime) {
          this.connect();
          return;
        }
      }
      
      // 设置长按计时器，500ms后才开始录音
      clearTimeout(this.longPressTimer);
      this.isLongPressing = true;
      
      if(this.isRealTime) {
        this.connect();
        return;
      }
      
      this.longPressTimer = setTimeout(() => {
        if (this.isLongPressing) {
          // 开始连接并录音
          this.connect();
        }
      }, 500);
    },

    // 停止录音（松开触发）
    stopRecord (e) {
      // 清除长按计时器
      clearTimeout(this.longPressTimer);
      this.isLongPressing = false;
      
      // 如果还没有开始录音，直接返回
      if (this.recState === "0" || this.isCanceled) return;

      // 松开时检查是否应该取消录音
      const touchEndY = e && e.changedTouches && e.changedTouches[0] ? e.changedTouches[0].clientY : 0;
      const moveDistance = this.touchStartY - touchEndY;

      // 如果上滑距离超过50，取消录音
      if (moveDistance > 50) {
        this.cancelRecord();
      } else {
        this.stop(); // 正常停止录音
        // 延迟一点显示结果，等待最后的识别完成
        setTimeout(() => {
          if (this.rec_text.trim()) {
            this.showResult = true;
          }
        }, 300);
      }
    },

    // 取消录音
    cancelRecord () {
      // 清除长按计时器
      clearTimeout(this.longPressTimer);
      this.isLongPressing = false;
      
      this.isCanceled = true;
      this.rec_text = ""; // 清空已识别文本
      this.offline_text = "";

      if (this.rec) {
        this.rec.close();
      }
      if (this.wsconnecter) {
        this.wsconnecter.wsStop();
      }
      this.recState = "0";
    },

    // 发送识别结果给父组件
    sendVoiceResult () {
      if (this.rec_text.trim()) {
        this.$emit("getVoice", this.oldMsg + this.rec_text);
        this.showResult = false;
      }
    },

    // 取消识别结果
    cancelVoiceResult () {
      this.rec_text = "";
      this.showResult = false;
    },

    // 连接语音服务，随时准备可以语音
    connect () {
      this.recState = "1";
      // 清除显示
      this.offline_text = "";
      this.rec_text = "";
      
      try {
        if (!this.wsconnecter) {
          this.init();
        }
        
        // 启动连接前确保Recorder已准备好
        if (!this.rec) {
          this.init();
        }
        
        // 在微信环境中，可能需要特殊处理
        if (this.isWechat) {
          // 可以添加一些微信专用的处理，但仍使用WebSocket
          console.log('微信环境中使用WebSocket连接');
        }
        
        //启动连接
        let ret = this.wsconnecter.wsStart();
        // 1 is ok, 0 is error
        if (ret == 1) {
          //连接成功，开始录音
          this.reconnectCount = 0; // 重置重连次数
          this.record();
        } else { //需要重新连接
          if (this.reconnectCount < this.maxReconnectCount) {
            this.reconnectCount++;
            console.log(`连接失败，尝试重连 ${this.reconnectCount}/${this.maxReconnectCount}`);
            
            // 延迟一点重连
            setTimeout(() => {
              this.connect();
            }, 500);
          } else {
            console.error(`连接失败，已达到最大重连次数 ${this.maxReconnectCount}`);
            this.recState = "0";
            
            // 在微信环境中，提供额外的提示
            if (this.isWechat) {
              uni.showModal({
                title: '提示',
                content: '连接语音服务失败，请确保已允许浏览器录音权限或尝试刷新页面',
                showCancel: false
              });
            } else {
              uni.showToast({
                title: '连接失败，请重试',
                icon: 'none'
              });
            }
          }
        }
      } catch (error) {
        console.error('连接出错:', error);
        this.recState = "0";
        uni.showToast({
          title: '连接出错，请重试',
          icon: 'none'
        });
      }
      
      return this.recState !== "0" ? 1 : 0;
    },

    recProcess (buffer, powerLevel, bufferDuration, bufferSampleRate, newBufferIdx, asyncEnd) {
      if (this.recState === "2") {
        let height = (powerLevel * 1 / 20) * 28;
        height = height > 2 ? height : 2;
        this.$set(this.barHeights, this.curIndex, height);
        this.curIndex = (this.curIndex + 1) % 4;

        let data_48k = buffer[buffer.length - 1];

        let array_48k = new Array(data_48k);
        let data_16k = Recorder.SampleData(array_48k, bufferSampleRate, 16000).data;

        this.sampleBuf = Int16Array.from([...this.sampleBuf, ...data_16k]);
        let chunk_size = 960; // for asr chunk_size [5, 10, 5]
        while (this.sampleBuf.length >= chunk_size) {
          let sendBuf = this.sampleBuf.slice(0, chunk_size);
          this.sampleBuf = this.sampleBuf.slice(chunk_size, this.sampleBuf.length);
          this.wsconnecter.wsSend(sendBuf);
        }
      }
    },
    //开始录音
    record () {
      let _this = this;
      this.$emit("setOldMsg");
      
      try {
        // 使用 Promise 包装录音初始化逻辑，便于错误处理
        new Promise((resolve, reject) => {
          // 在微信环境中，可能需要特别处理录音权限问题
          if (this.isWechat) {
            console.log('微信环境中启动录音');
            // 仍然使用标准的Recorder API
          }
          this.rec.open(resolve, reject);
        }).then(() => {
          this.rec.start();
          this.recState = "2";
        }).catch((e) => {
          console.error('录音初始化失败:', e);
          _this.recState = "0";
          _this.wsconnecter.wsStop();
          
          // 在微信环境中，提供更具体的引导
          if (this.isWechat) {
            uni.showModal({
              title: '录音失败',
              content: '请确保已允许浏览器使用麦克风权限，或尝试刷新页面后重试',
              showCancel: false
            });
          } else {
            uni.showToast({
              title: '录音失败，请允许录音权限',
              icon: 'none',
              duration: 2000
            });
          }
        });
      } catch (error) {
        console.error('录音启动异常:', error);
        _this.recState = "0";
        _this.wsconnecter.wsStop();
      }
    },
    //结束录音
    stop () {
      if (this.recState === "0") return;
      let chunk_size = new Array(5, 10, 5);
      let request = {
        "chunk_size": chunk_size,
        "wav_name": "h5",
        "is_speaking": false,
        "chunk_interval": 10,
        "mode": "2pass"
      };
      if (this.sampleBuf.length > 0) {
        this.wsconnecter.wsSend(this.sampleBuf);
        this.sampleBuf = new Int16Array();
      }
      this.wsconnecter.wsSend(JSON.stringify(request));
      this.recState = "0";
      setTimeout(() => {
        this.wsconnecter.wsStop();
      }, 3000);
      this.rec.close();
    },
    //WebSocket的回调方法
    getJsonMessage (jsonMsg) {
      let rectxt = "" + JSON.parse(jsonMsg.data)['text'];
      let asrmodel = JSON.parse(jsonMsg.data)['mode'];
      let is_final = JSON.parse(jsonMsg.data)['is_final'];
      let timestamp = JSON.parse(jsonMsg.data)['timestamp'];
      console.log(rectxt, '===========rectxt')
      if (asrmodel == "2pass-offline" || asrmodel == "offline") {

        this.offline_text = this.offline_text + rectxt;
        this.rec_text = this.offline_text;
      } else {
        this.rec_text = this.rec_text + rectxt;
      }
      if (this.isRealTime) {
        this.$emit("getVoice", this.rec_text);
      }
      if (is_final == true) {
        this.wsconnecter.wsStop();
      }
    },
    //WebSocket的回调方法
    getConnState (connState) {
      if (connState === 2) {
        this.stop();
        
        // 微信环境中可能需要特殊处理连接问题
        if (this.isWechat) {
          uni.showModal({
            title: '提示',
            content: '连接语音服务失败，请确保网络连接正常并允许使用麦克风权限',
            confirmText: '我知道了',
            showCancel: false,
            success: (res) => {
              if (res.confirm) {
                this.$emit("init");
                this.recState = "0";
                // 在微信环境中不使用plus.runtime.openURL
                this.init();
              }
            }
          });
        } else {
          uni.showModal({
            title: '提示',
            content: '连接语音地址失败, 是否手动授权，再发语音?',
            confirmText: '确定',
            cancelText: '取消',
            success: (res) => {
              if (res.confirm) {
                this.$emit("init");
                this.recState = "0";
                this.init();
              }
            }
          });
        }
      }
    },
    getHotwords () {
      let jsonresult = {
        "阿里巴巴": 20,
        "hello world": 40
      };
      return JSON.stringify(jsonresult);
    },
    //关闭资源
    closeRes () {
      if (!!this.wsconnecter) {
        this.wsconnecter.wsStop();
      }
      if (!!this.rec) {
        this.rec.close();
      }
    }
  },
  beforeDestroy () {
    // 销毁监听，清除计时器
    this.closeRes();
    clearTimeout(this.longPressTimer);
    
    // 如果有微信录音管理器，需要停止录音
    if (this.isWechat && this.recorderManager && this.isRecording) {
      this.recorderManager.stop();
    }
  }
}
</script>

<style scoped="scoped" lang="scss">
  .voice-button {
    width: 100%;
    height: 100%;
    background-color: #ffffff;
    border-radius: 40rpx;
    color: #666;
    font-size: 28rpx;
    text-align: center;
    line-height: 80rpx;
    &.active {
    }
    
    &.disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
  
  /* 录音遮罩层 */
  .recording-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 9999;
  }
  
  .recording-gif {
    width: 400rpx;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  
  .recording-text {
    color: #fff;
    font-size: 30rpx;
    position: fixed;
    bottom:-10rpx;
    left: 0px;
    right: 0px;
    text-align: center;
  }
  
  /* 语音识别结果遮罩层 */
  .voice-result-mask {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    z-index: 999;
    animation: fadeIn 0.2s ease-out;
  }
  
  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
  
  .voice-result {
    background-color: #b2ffa8;
    border-radius: 20rpx;
    padding: 30rpx;
    max-width: 80%;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
    min-width: 60%;
    
    .voice-text {
      font-size: 32rpx;
      color: #333;
      line-height: 1.5;
      word-break: break-all;
      background-color: transparent;
      width: 100%;
      min-height: 80rpx;
      border: none;
      padding: 0;
    }
  }
  
  .voice-actions {
    position: fixed;
    bottom: 100rpx;
    left: 0;
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 20rpx 40rpx;
    box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
    align-items: center;
    
    .voice-action-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 15rpx 30rpx;
      
      &.cancel {
        width: 100rpx;
        height: 100rpx;
        border-radius: 50%;
        background-color: #383838;
        .btn-text {
          color: #999;
          font-size: 28rpx;
          margin-left: 5rpx;
        }
      }
      
      &.send {
        background-color: #ffffff;
        border-radius: 40rpx;
        padding: 15rpx 50rpx;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
        height: 80rpx;
        .btn-text {
          color: #333;
          font-size: 28rpx;
        }
      }
    }
  }
  .recording-text-gif{
    width: 750rpx;
    height: 160rpx;
  }
  .recording-text-text{
    position: absolute;
    top: 120rpx;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    font-size: 32rpx;
    color: black;
    font-weight: bold;
  }
</style> 