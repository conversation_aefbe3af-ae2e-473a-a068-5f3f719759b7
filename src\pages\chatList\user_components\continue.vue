<template>
  <!-- 慢病续方 -->
  <view class="user_continue">
    <view>
      <view class="patientName">{{ patientName }}</view>
      <!-- 卡片 -->
      <view class="continue_content" @click="click">
        <!-- 标题 -->
        <view class="content_title">{{ name }}</view>
        <!-- 内容 -->
        <view class="content_cont">
          <!-- 左侧图标 -->
          <image src="/static/images/chat/continue-cat.png" class="cont_icon" />
          <!-- 右侧文案 -->
          <view class="cont_right">
            <!-- 诊断 -->
            <view class="cont_right_title">诊断：{{ title }}</view>
            <!-- 描述 -->
            <view v-if="name!='快速续方'" class="cont_right_info">
              <text>请点击查看</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    <!-- 头像 -->
    <image
      @click="head"
      :src="imgUrl || '/static/images/docHead.png'"
      mode="aspectFill"
      class="user_img"
    />
  </view>
</template>

<script>
export default {
  props: {
    // 头像
    imgUrl: {
      type: String,
      default: '',
    },
    // 病名
    title: {
      type: String,
      default: '',
    },
    name:{
      type: String,
      default: '慢病续方'
    },
    patientName:{}
  },
  methods: {
    click() {
      this.$emit('click');
    },
    head() {
      this.$emit('head');
    },
  },
};
</script>

<style scoped lang="scss">
.user_continue {
  box-sizing: border-box;
  @include flex(right);
  padding-bottom: 30upx;
  align-items: flex-start;

  // 头像
  .user_img {
    width: 64upx;
    height: 64upx;
    border-radius: 4upx;
    margin-left: 16upx;
    flex: none;
  }

  // 内容
  .continue_content {
    width: 516upx;
    padding: 24upx;
    background-color: #d6f1ff;
    color: #333;
    border: 1px solid #ececec;
    box-sizing: border-box;
    border-radius: 32upx 8upx 32upx 32upx;

    // 标题
    .content_title {
      font-size: 28upx;
      color: $k-title;
      font-weight: bold;
    }

    // 底部内容
    .content_cont {
      @include flex(left);
      align-items: flex-start;
      margin-top: 20upx;

      // 左侧图标
      .cont_icon {
        width: 80upx;
        height: 80upx;
        border-radius: 8upx;
        margin-right: 24upx;
        flex: none;
      }

      // 右侧文案
      .cont_right {
        flex: 1;
        min-height: 80upx;
        @include flex(lr);
        flex-direction: column;
        align-items: stretch;

        // 标题
        .cont_right_title {
          font-size: 28upx;
          color: $k-title;
          font-weight: bold;
        }

        // 描述
        .cont_right_info {
          font-size: 24upx;
          color: $k-info-title;
        }
      }
    }
  }
}
</style>
