<template>
  <!-- 在线购药 -->
  <view class="prescription" @click="toPay">
    <!-- 订单号 -->
    <view class="order_num">
      <view class="order-text">
        <img src="/static/doc/order-w.png" alt="" />
        <text class="order-text-name">处方</text>
      </view>
      <text v-if="item.payStatus == 1"> 提交时间：{{ item.addTime }}</text>
      <text v-if="item.payStatus == 3">
        <block v-if="item.deliveryType == 1">
          支付时间：{{ item.payTime }}
        </block>

        <block v-if="item.deliveryType == 2">
          发货时间：{{ item.payTime }}
        </block>
      </text>
      <text v-if="item.payStatus == 4"> 发货时间：{{ item.deliveryTime }}</text>
      <text v-if="item.payStatus == 7"> 退费时间：{{ item.returnTime }}</text>
      <text v-if="item.payStatus == 10"> 收货时间：{{ item.receiveTime }}</text>
      <text class="status wait" v-if="item.payStatus == 1">待支付</text>
      <text class="status wait" v-if="item.payStatus == 2">已支付</text>
      <text
        class="status wait"
        v-if="item.payStatus == 3"
        style="background: rgba(253, 236, 237, 1); color: rgba(251, 98, 98, 1)"
      >
        <block v-if="item.deliveryType == 1"> 待发货 </block>

        <block v-if="item.deliveryType == 2"> 待取药 </block>
      </text>
      <text class="status wait" v-if="item.payStatus == 4">待收货</text>
      <text class="status close" v-if="item.payStatus == 9">交易关闭</text>
      <text class="status" v-if="item.payStatus == 7">已退费</text>
      <text
        class="status done"
        v-if="item.payStatus == 10"
        style="background: #e8faf4; color: rgba(131, 106, 255, 1)"
        >交易完成</text
      >
    </view>
    <view class="order_num">
      <text>订单编号：{{ item.orderNo }}</text>
    </view>
    <!-- 药店 药品列表 -->
    <view
      class="pharmacy_list"
      v-for="(p, pn) in item.orderDrugStoreListNewVOList"
      :key="pn"
    >
      <view class="pharmacy">
        <!-- 药店名称 -->
        <text
          >{{ p.drugstoreName
          }}{{ p.isProprietary == 1 ? '（自营）' : '' }}</text
        >
      </view>

      <!-- 支付前可见 -->
      <view
        class="durg_list"
        v-if="
          item.beforePayVisible == 1 ||
          item.payStatus == 3 ||
          item.payStatus == 4 ||
          item.payStatus == 10
        "
      >
        <!-- 单个药品 -->
        <view
          class="durg_item"
          v-for="(d, dn) in p.drugShoppingOnlineOrderList"
          :key="dn"
        >
          <img
            v-if="d.drugImg"
            v-img="d.drugImg"
            :data-src="errUrl"
            class="left"
          />
          <image v-else class="left" src="/static/shop/drug.png"></image>
          <!-- 内容 -->
          <view class="right">
            <!-- 药品名称 -->
            <view class="drug_name">{{ d.drugName }}</view>
            <!-- 规格 -->
            <view class="drug_info">规格: {{ d.gg }}</view>
            <!-- 活动 -->
            <view class="drug_red" v-if="d.activeName">
              单品{{ d.activeName }}</view
            >
            <!-- 价位数量 -->
            <view class="right_menu">
              <text class="price"
                >￥{{ d.drugRealMoney | toFixed }}
                <text class="del" v-if="d.drugShouldMoney != d.drugRealMoney"
                  >￥{{ d.drugShouldMoney | toFixed }}</text
                >
              </text>
              <text class="num">x{{ Number(d.quan) }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 支付前不可见 -->
      <view
        class="durg_info"
        v-if="
          item.beforePayVisible == 0 &&
          item.payStatus != 3 &&
          item.payStatus != 4 &&
          item.payStatus != 10
        "
      >
        <view class="info_item" v-if="z">
          <text class="item_name">草药方</text>
          <text class="item_num">共{{ z }}种药品</text>
          <text class="item_price">￥{{ zp | toFixed }}</text>
        </view>

        <view class="info_item" v-if="x">
          <text class="item_name">西药方</text>
          <text class="item_num">共{{ x }}种药品</text>
          <text class="item_price">￥{{ xp | toFixed }}</text>
        </view>

        <view class="info_item" v-if="s">
          <text class="item_name">食品级</text>
          <text class="item_num">共{{ s }}种药品</text>
          <text class="item_price">￥{{ sp | toFixed }}</text>
        </view>
      </view>

      <!-- 根据状态 显示按钮 -->
      <view
        class="status_buts"
        v-if="item.payStatus == 2 && item.deliveryType == 1 && false"
      >
        <text
          class="one"
          v-if="p.logisticsStatus >= 1"
          @click.stop="lockLogist(p)"
          >查看物流</text
        >
        <!--        <text-->
        <!--          v-if="p.logisticsStatus == 1"-->
        <!--          @click.stop="confirmGood(p.merchantsOrderNo)"-->
        <!--          >确认收货</text-->
        <!--        >-->
        <!--        <text v-if="p.logisticsStatus == 0" @click.stop="toast">催物流</text>-->
      </view>
    </view>
    <!-- 统计 -->
    <view class="count">
      <!-- <text class="num"
        >共{{ getCount(item.orderDrugStoreListNewVOList) }}件商品</text
      > -->
      <view class="count_price">
        总价:
        <text class="order-red"
          ><text style="font-size: 12px">￥</text
          >{{ item.orderShouldMoney | toFixed }}</text
        >
      </view>
      <view class="count_price">
        优惠:
        <text class="order-red" style="color: rgba(255, 195, 0, 1)"
          ><text style="font-size: 12px; color: rgba(255, 195, 0, 1)">￥</text
          >{{ item.orderDisMoney | toFixed }}</text
        >
      </view>
      <view class="count_price">
        实付款:
        <text class="order-red"
          ><text style="font-size: 12px">￥</text
          >{{ item.orderRealMoney | toFixed }}</text
        >
      </view>
    </view>
    <!-- 待支付状态 -->
    <view class="pay_buts" v-if="item.payStatus == 1">
      <text @click.stop="toPay">去支付</text>
    </view>
  </view>
</template>

<script>
import { Toast } from '@/common/js/pay.js'
import { getConfigInfoByKey } from '@/api/base.js'
export default {
  name: 'Prescription',
  props: ['item', 'isScan'],
  data() {
    return {
      z: 0,
      zp: 0,
      x: 0,
      xp: 0,
      s: 0,
      sp: 0,
      errUrl: require('../../../static/shop/drug.png'),
    }
  },
  mounted() {
    this.getTypeList()
  },
  methods: {
    // 提示
    toast() {
      Toast('已催物流')
    },
    // 确认收货
    confirmGood(merchantsOrderNo) {
      const { businessId, orderNo } = this.item
      let obj = {
        merchantsOrderNo,
        businessId,
        orderNo,
      }
      this.$emit('confirmGood', obj)
    },
    // 查看物流
    lockLogist(item) {
      let { logisticsCode: code, deliveryTelNo, logisticsName: name } = item
      if (!code) {
        Toast('未查询到物流单号')
        return
      }
      let tel = deliveryTelNo.slice(-4)
      uni.navigateTo({
        url:
          '/pages/personalCenter/myOrder/allOrder/prescriptionOrder/logistics?code=' +
          code +
          '&tel=' +
          tel +
          '&name=' +
          name,
      })
    },
    // 统计
    getCount(list) {
      if (!list.length) return 0
      let n = 0
      list.forEach((v) => {
        v.drugShoppingOnlineOrderList.forEach((k) => {
          n += Number(k.quan)
        })
      })

      return n
    },
    // 区分药品数量 金额
    getTypeList() {
      let z = 0,
        x = 0,
        s = 0
      let zp = 0,
        xp = 0,
        sp = 0
      const list = this.item.orderDrugStoreListNewVOList
      list.forEach((v) => {
        v.drugShoppingOnlineOrderList.forEach((k) => {
          // 西药
          if (k.drugType == 1 || k.drugType == 2) {
            x += 1
            xp += (Number(k.price) * 100 * Number(k.quan)) / 100
          }
          // 草药
          if (k.drugType == 3) {
            z += 1
            zp += (Number(k.price) * 100 * Number(k.quan)) / 100
          }
          // 食品级
          if (k.drugType == 4) {
            s += 1
            sp += (Number(k.price) * 100 * Number(k.quan)) / 100
          }
        })
      })

      this.z = z
      this.x = x
      this.s = s
      this.zp = zp
      this.xp = xp
      this.sp = sp
    },
    // 点击去支付
    toPay() {
      uni.setStorageSync('hosId', this.item.hosId || '')
      getConfigInfoByKey({
        configKey: 'yx_subject_id',
      }).then((ret) => {
        console.log('获取药店id', ret)
        console.log('获取药店id11111', this.item)
        // 圆心药店业务
        if (
          ret.data.configValue ===
          this.item.orderDrugStoreListNewVOList[0]?.drugstoreId
        ) {
          if (
            (this.item.payStatus == 4 || this.item.payStatus == 10) &&
            this.item.deliveryType == 1
          ) {
            uni.navigateTo({
              url:
                '/pages/order/detail/drugStatusSanF?orderNo=' +
                this.item.orderNo +
                '&source=' +
                this.item.source,
            })
          } else {
            uni.navigateTo({
              url:
                '/pages/order/detail/drugStatus?orderNo=' +
                this.item.orderNo +
                '&source=' +
                this.item.source +
                `&isScan=${this.isScan ? 1 : 0}`,
            })
          }
        } else {
          uni.navigateTo({
            url:
              '/pages/order/detail/drugStatus?orderNo=' +
              this.item.orderNo +
              '&source=' +
              this.item.source +
              `&isScan=${this.isScan ? 1 : 0}`,
          })
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.prescription {
  width: 100%;
  padding: 0 24rpx 10rpx;
  background-color: #fff;
  margin-bottom: 24rpx;
  font-size: 28rpx;
  border-radius: 8rpx;
  box-shadow: 0 0 20rpx #ddd;

  .order_num {
    @include flex(lr);
    font-size: 24rpx;
    //font-weight: bold;
    height: 88rpx;
    border-bottom: 1px solid #ebebeb;

    &:first-child {
      border: none;
      height: 60rpx;
      padding-top: 10rpx;
    }

    text::nth-child(2) {
      font-weight: normal;
    }

    .status {
      font-weight: normal;

      &.wait {
        color: red;
      }

      &.done {
        @include font_theme;
      }
    }
  }

  .pharmacy_list {
    border-bottom: 1px dashed #ebebeb;

    &:only-child {
      border: none;
    }

    .pharmacy {
      @include flex(lr);
      font-size: 28rpx;
      font-weight: bold;
      height: 40rpx;
      margin-top: 20rpx;
      //border-bottom: 1px solid #ebebeb;

      text::nth-child(2) {
        font-weight: normal;
      }

      text {
        color: #333;

        &.wait {
          color: #ff5050;
        }

        &.close {
          color: #999;
        }

        &.done {
          @include font_theme;
        }
      }
    }

    .status_buts {
      @include flex(right);
      height: 88rpx;

      text {
        @include border_theme;
        @include flex;
        font-size: 26rpx;
        margin-left: 24rpx;
        width: 158rpx;
        height: 56rpx;
        border-radius: 28rpx;
        @include bg_theme;
        color: #fff;

        &.one {
          background: none;
          @include font_theme;
        }
      }
    }

    .durg_list {
      .durg_item {
        @include flex;
        padding: 24rpx 0;

        &:last-child {
          .right {
            border-bottom: none;
          }
        }

        .left {
          width: 230rpx;
          height: 166rpx;
          margin-right: 24rpx;
          border: 1px solid #eee;
          border-radius: 8rpx;
          flex: none;
        }

        .right {
          flex: 1;
          min-height: 128rpx;
          @include flex(lr);
          flex-direction: column;
          align-items: stretch;
          //border-bottom: 1px solid #eee;

          .drug_name {
            flex: 1;
            font-size: 28rpx;
            font-weight: bold;
          }

          .drug_info {
            flex: 1;
            font-size: 24rpx;
            color: #999;
          }

          .drug_red {
            flex: 1;
            font-size: 24rpx;
            color: red;
          }

          .right_menu {
            flex: 2;
            @include flex(lr);
            align-items: flex-end;

            .price {
              font-size: 28rpx;
              color: #ff3b30;
              font-weight: bold;

              .del {
                font-size: 20rpx;
                color: #999;
                text-decoration: line-through;
                margin-left: 10rpx;
              }
            }

            .num {
              width: 33px;
              height: 23px;
              opacity: 1;
              border-radius: 4px;
              background: rgba(240, 240, 240, 1);
              font-size: 24rpx;
              font-weight: 600;
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }
        }
      }
    }

    .durg_info {
      padding: 24rpx 0;

      .info_item {
        height: 76rpx;
        border-radius: 8rpx;
        background-color: #f5f5f5;
        @include flex;
        padding: 0 24rpx;
        font-size: 28rpx;
        color: #333;
        margin-top: 24rpx;

        &:nth-child(1) {
          margin-top: 0;
        }

        .item_name {
          width: 136rpx;
          flex: none;
        }

        .item_num {
          flex: 1;
        }

        .item_price {
          flex: none;
          color: red;
          flex: none;
        }
      }
    }
  }

  .count {
    @include flex(lr);
    height: 88rpx;
    font-size: 28rpx;
    font-size: 12px;
    font-weight: 400;
    color: rgba(102, 102, 102, 1);
    .count_num {
      color: #333;
    }

    .count_price {
      margin-right: 24rpx;

      text {
        color: #ff3b30;
        padding-left: 10rpx;
        font-weight: bold;
      }
    }
  }

  .pay_buts {
    @include flex(right);

    text {
      @include flex;
      width: 158rpx;
      height: 56rpx;
      border-radius: 28rpx;
      @include bg_theme;
      color: #fff;
      font-size: 26rpx;
    }
  }
}
.status {
  width: 54px;
  height: 22px;
  opacity: 1;
  border-radius: 10px;
  background: rgba(232, 250, 244, 1);
  font-size: 12px;
  font-weight: 600;
  color: rgba(44, 199, 147, 1) !important;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
