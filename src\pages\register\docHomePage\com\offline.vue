<template>
  <!-- 线下排班 -->
  <view class="offline">
    <view class="tab">预约到院</view>

    <!-- 列表 -->
    <view class="list">
      <!-- 单个 -->
      <view
        class="item"
        v-for="(item, index) in list"
        :class="{
          end: !item.remainingNumber,
          zr: item.dntName == '主任号' && item.remainingNumber,
          pz: item.dntName == '普通号' && item.remainingNumber,
          f_zr: item.dntName == '副主任号' && item.remainingNumber,
        }"
        :key="index"
        @click="click(item)"
      >
        <!-- 左侧 -->
        <view class="left">
          <!-- 图 -->
          <image
            src="/static/images/doc/offline1.png"
            v-if="item.remainingNumber && item.dntName == '普通号'"
            class="icon"
          />
          <image
            src="/static/images/doc/offline2.png"
            v-else-if="item.remainingNumber && item.dntName == '主任号'"
            class="icon"
          />
          <image
            src="/static/images/doc/offline3.png"
            v-else-if="item.dntName == '副主任号' && !item.remainingNumber"
            class="icon"
          />
          <image src="/static/images/doc/offline4.png" v-else class="icon" />
        </view>

        <!-- 号别 -->
        <view class="mid">{{ item.dntName }}</view>

        <view class="right">
          <text v-if="item.remainingNumber"
            >剩余号: {{ item.remainingNumber }}</text
          >
          <text v-else>已约满</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'OfflineList',
  props: {
    list: {
      type: Array,
      default: () => [],
    },
  },
  methods: {
    click(item) {
      if (item.remainingNumber == 0) return
      this.$emit('click', item)
    },
  },
}
</script>

<style lang="scss" scoped>
.offline {
  width: 100%;
  background: #fff;
  border-radius: 16rpx;
  font-size: 28rpx;
  border: 8rpx solid #fff;

  .tab {
    font-weight: bold;
    @include font_theme;
    background: #fff;
    font-size: 32rpx;
    height: 88rpx;
    @include flex;
  }

  .list {
    border-radius: 8rpx;
    background-color: #fff;
    padding: 8rpx;

    .item {
      @include flex(lr);
      margin-bottom: 12rpx;
      border-radius: 16rpx;

      &:last-child {
        margin-bottom: 0;
      }

      &.zr {
        background: linear-gradient(
          158.72deg,
          rgba(193, 212, 255, 1) 0%,
          rgba(213, 226, 254, 1) 100%
        );
      }

      &.pz {
        background: linear-gradient(158.72deg, #bee8f7 0%, #e2f5fd 100%);
      }

      &.f_zr {
        background: linear-gradient(158.72deg, #dac8f9 0%, #ece5fc 100%);
      }

      &.end {
        border-color: #eaeaea;
        background-color: #d1d1d1;
      }

      .left {
        @include flex;
        font-size: 28rpx;
        font-weight: bold;
        width: 104rpx;
        height: 104rpx;

        .icon {
          width: 56rpx;
          height: 56rpx;
        }
      }

      .mid {
        flex: 1;
        padding-left: 24rpx;
        color: #333333;
        font-size: 32rpx;
      }

      .right {
        text-align: center;
        font-size: 24rpx;
        color: #2a6cfc;
        border-radius: 0 16rpx 16rpx 0;
        border-radius: 24rpx;
        background: #f2f5ff;
        border: 0.5px solid #9bbafb;
        padding: 2rpx 12rpx;
        margin-right: 30px;
      }
    }
  }
}
</style>
