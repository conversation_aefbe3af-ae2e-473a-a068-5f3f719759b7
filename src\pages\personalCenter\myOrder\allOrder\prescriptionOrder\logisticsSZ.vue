<template>
  <!-- 物流 -->
  <view class="logistics">
    <!-- 引导 -->
    <!-- <Tips v-if="false" /> -->
    <view class="top"> 物流公司：{{ name }} </view>
    <view class="top">
      <text>物流单号： {{ code }} </text>
      <text style="color: #1686ff" @click="copyCode(code)">复制</text></view
    >
    <view class="cont" v-if="list.length">
      <view
        class="item"
        :class="index == 0 ? 'act' : ''"
        v-for="(item, index) in list"
        :key="index"
      >
        <view class="title">{{ item.strace_content }}</view>
        <view class="time"
          >{{ item.strace_time }} {{ getWeek(item.strace_time) }}</view
        >
      </view>
    </view>

    <view class="empt" v-else>
      <image src="/static/images/index/box_empty.png"></image>
      <text>暂无物流信息</text>
    </view>
  </view>
</template>

<script>
import {
  LogisticTrackQuerySanF,
  queryLogisticInfoById,
  getConfigInfoByKey,
  trackQuerySZ,
} from "@/api/base.js";

export default {
  components: {
    // Tips
  },
  data() {
    return {
      code: "",
      tel: "",
      list: [],
      businessId: "",
      subjectId: "", // 药店ID
    };
  },
  onLoad(e) {
    this.subjectId = e.subjectId;
    this.code = e.code;
    this.name = e.name;
    this.orderCode = e.orderNo;
    this.getDetail();
  },
  methods: {
    getWeek(str) {
      if (!str) return;
      let n = new Date(str).getDay();
      switch (n) {
        case 0:
          return "周日";
          break;
        case 1:
          return "周一";
          break;
        case 2:
          return "周二";
          break;
        case 3:
          return "周三";
          break;
        case 4:
          return "周四";
          break;
        case 5:
          return "周五";
          break;
        case 6:
          return "周六";
          break;
        default:
          return "";
          break;
      }
    },
    async getDetail() {
      // let ret = await getConfigInfoByKey({
      //   configKey: "yx_subject_id",
      // });
      // // 圆心药店业务
      // if (ret.data.configValue === this.subjectId) return
      let res = await trackQuerySZ({
        code: this.code,
        orderCode: this.orderCode,
      });
      let arr = res.data;
      // 如果为空
      if (!arr.length) return;
      // // 时间倒序
      // arr.sort((a, b) => {
      //   let end = b.strace_time.replace(/-/g, "/");
      //   let start = a.strace_time.replace(/-/g, "/");
      //   return new Date(end) - new Date(start);
      // });
      this.list = arr;
    },
    // 复制物流单号
    copyCode(textStr) {
      // 要复制的文本内容
      let textToCopy = textStr;
      // 调用微信小程序的 setClipboardData API 进行复制
      uni.setClipboardData({
        data: textToCopy,
        success: () => {
          uni.showToast({
            title: "复制成功",
            icon: "none",
            duration: 2000,
          });
        },
        fail: () => {},
      });
    },
  },
};
</script>

<style scoped lang="scss">
.logistics {
  padding: 24upx 30upx;
  .top {
    font-size: 32upx;
    color: #333;
    padding-left: 30upx;
    margin-bottom: 36upx;
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    // 原点
    //&::before {
    //  content: "";
    //  display: block;
    //  position: absolute;
    //  left: 0;
    //  top: 50%;
    //  margin-top: -8upx;
    //  width: 16upx;
    //  height: 16upx;
    //  border-radius: 50%;
    //  background-color: #1686ff;
    //}
  }
  .cont {
    background-color: #fff;
    padding: 30upx;
    position: relative;
    border-radius: 16upx;

    &::before {
      content: "";
      display: block;
      position: absolute;
      width: 1px;
      top: 64upx;
      left: 38upx;
      height: calc(100% - 120upx);
      background-color: #f3f3f3;
    }

    .top {
      font-size: 32upx;
      color: #333;
      padding-left: 30upx;
      margin-bottom: 36upx;
      position: relative;
      display: flex;
      justify-content: space-between;
      align-items: center;
      // 原点
      //&::before {
      //  content: "";
      //  display: block;
      //  position: absolute;
      //  left: 0;
      //  top: 50%;
      //  margin-top: -8upx;
      //  width: 16upx;
      //  height: 16upx;
      //  border-radius: 50%;
      //  background-color: #1686ff;
      //}
    }

    .item {
      font-size: 28upx;
      color: #999;
      padding-left: 30upx;
      margin-bottom: 36upx;
      position: relative;

      // 原点
      &::before {
        content: "";
        display: block;
        position: absolute;
        left: 0;
        top: 50%;
        margin-top: -8upx;
        width: 16upx;
        height: 16upx;
        border-radius: 50%;
        background-color: #f3f3f3;
      }

      &.act {
        color: #1686ff;

        &::before {
          background-color: #1686ff;
        }
      }

      &:last-child {
        margin-bottom: 0;
      }

      .time {
        font-size: 24upx;
      }
    }
  }

  .empt {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: 28upx;
    color: #999;
    padding-top: 150upx;
  }
}
</style>
