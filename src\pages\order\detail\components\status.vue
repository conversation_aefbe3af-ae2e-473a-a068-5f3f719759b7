<template>
  <!-- 状态 -->
  <view class="status">
    <img class="o-2" src="/static/doc/o-2.png" alt="">
    <block v-if="status == 1">
      <view class="h2">
        <image class="icon" src="/static/pre/1.png" />
        待支付
      </view>

      <view class="info">
        剩余{{ setTime(time) }}，订单将自动取消，请尽快支付哦
      </view>
    </block>

    <block v-if="status == 3">
      <block v-if="deliveryType == 1">
        <view class="h2">
          <image class="icon" src="/static/pre/2.png" />
          待发货
        </view>

        <view class="info"> 正在加急为您发货，请耐心等待 </view>
      </block>

      <block v-if="deliveryType == 2">
        <view class="h2">
          <image class="icon" src="/static/pre/2.png" />
          待取药
        </view>

        <view class="info"> 请尽快到店取药，超时未取，将不会为您保留药品 </view>
      </block>
    </block>

    <block v-if="status == 4">
      <view class="h2">
        <image class="icon" src="/static/pre/2.png" />
        待收货
      </view>

      <view class="info">已为您发货，请注意查收</view>
    </block>

    <block v-if="status == 7">
      <view class="h2">
        <image class="icon" src="/static/pre/4.png" />
        已退费
      </view>
      <view v-if="detail.refundReason" class="info">退费原因： {{detail.applyReason|| detail.refundReason}} </view>
      <view v-else class="info"> 已为您退费，预计15个工作日内到账，请注意查收 </view>
    </block>

    <block v-if="status == 9">
      <view class="h2">
        <image class="icon" src="/static/pre/4.png" />
        交易关闭
      </view>

      <view class="info"> 本次服务已关闭 </view>
    </block>

    <block v-if="status == 10">
      <view class="h2">
        <image class="icon" src="/static/pre/3.png" />
        交易完成
      </view>

      <view class="info"> 本次服务已完成 </view>
    </block>
  </view>
</template>

<script>
export default {
  name: 'Status',
  props: {
    status: {
      type: Number | String,
      default: 1,
    },
    deliveryType: {
      type: String | Number,
      default: 1,
    },
    time: {
      type: String | Number,
      default: '',
    },
    detail:{
      type: Object,
      default: () => {
        return {};
      },
    }
  },
  methods: {
    setTime(n) {
      if (!n) return '';
      // let h = parseInt(n / 60 / 60);
      let m = parseInt((n / 60) % 60);
      let s = parseInt(n % 60);
      // if (h < 10) h = '0' + h;
      if (m < 10) m = '0' + m;
      if (s < 10) s = '0' + s;
      return m + '分' + s + '秒';
    },
  },
};
</script>

<style lang="scss" scoped>
.status {
  height: 216rpx;
  background: #ff5050;
  padding: 32rpx;
  color: #fff;
  box-sizing: border-box;

  .h2 {
    font-size: 48rpx;
    font-weight: bold;
    @include flex(left);

    .icon {
      width: 44rpx;
      height: 44rpx;
      margin-right: 16rpx;
    }
  }

  .info {
    font-size: 24rpx;
    margin-top: 24rpx;

    text {
      font-size: 36rpx;
      font-weight: bold;
      padding: 0 8rpx;
    }
  }
}
</style>
