<template>
  <!-- 预约 -->
  <view>
    <view class="page-container">
      <!-- 医生头部 -->
      <Docter :infoDetail="appointInfoDetail" />

      <view class="img_box">
        <template v-for="item in msgList">
          <image :src="item.url" mode=""></image>
        </template>
      </view>

      <view class="reserve_box">
        <view class="reserve-date">
          <view class="title">
            <text class="line"></text>
            <text>点击选择您要预约的日期</text>
          </view>
          <view class="date-content">
            <view>
              <uni-calendar
                v-if="visitDate"
                :date="visitDate"
                :start-date="timeBettwen.start"
                :end-date="timeBettwen.end"
                :insert="true"
                :lunar="false"
                :selected="visitDates"
                @change="selevtVisitDate"
              />
            </view>
          </view>
        </view>

        <!-- 预约时间 -->
        <view class="reserve-time">
          <view class="timeList">
            <block v-if="reserveTimeList.length">
              <view
                class="list"
                v-for="(item, index) in reserveTimeList"
                :key="index"
                @click="appoint(item)"
              >
                <!-- 单个循环 -->
                <view class="list_item">
                  <!-- 时间 -->
                  <view class="item_time"
                    >{{ item.startTimeShow }}-{{ item.endTimeShow }}</view
                  >
                  <!-- 剩余号 -->
                  <view class="number" v-if="item.appointLimitNum > 0"
                    >剩余号：{{ item.appointLimitNum }}</view
                  >
                  <view class="number" v-else>已约满</view>
                </view>
              </view>
            </block>
            <view v-else>
              <view class="list_empt">无可预约号</view>
            </view>
          </view>
          <view class="more" v-if="getMoreShow">
            <button class="btn-more" @click="getMore">查看更多</button>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { Toast } from '@/common/js/pay.js';
import Docter from '@/components/doctor_header/doctor_header.vue';
import myJsTools from '@/common/js/myJsTools.js';
import { getReserveFlag, getReserveTimeList } from '@/api/appoint.js';
export default {
  components: {
    Docter,
  },
  data() {
    return {
      pageTitle: '视频预约',
      msgList: [], // 图片列表
      appointInfoDetail: {},
      visitDate: '',
      visitDates: [], // 该医生有排班的所有日期
      reserveTimeList: [], // 某一天的出诊排班
      allReserveTimeList: [],
      getMoreShow: false,
      timeBettwen: {
        start: '',
        end: '',
      },
    };
  },
  onLoad() {
    let appointInfoDetail = uni.getStorageSync('appointInfoDetail') || {};
    // 如果存在头像
    if (
      appointInfoDetail.docImg &&
      appointInfoDetail.docImg.indexOf('http') == -1
    ) {
      myJsTools.downAndSaveImg(appointInfoDetail.docImg, (url) => {
        appointInfoDetail.docImg = url;
      });
    }
    this.appointInfoDetail = appointInfoDetail;
    this.initData();
    this.getReserveFlagFun();
  },
  methods: {
    // 初始化页面
    initData() {
      let appointInfoDetail = uni.getStorageSync('appointInfoDetail');
      // 设置头部内容
      if (appointInfoDetail.visitTypeCode == '2') {
        this.pageTitle = '语音预约';
        if (appointInfoDetail.consultationCode == '0') {
          // 咨询
          this.msgList = [
            {
              url: '/static/images/appoint/audio_base.png',
            },
            {
              url: '/static/images/appoint/base.png',
            },
          ];
        } else {
          // 复诊
          this.msgList = [
            {
              url: '/static/images/appoint/audio_base.png',
            },
            {
              url: '/static/images/appoint/base.png',
            },
            {
              url: '/static/images/appoint/medical_doc.png',
            },
            {
              url: '/static/images/appoint/medical.png',
            },
          ];
        }
      } else if (appointInfoDetail.visitTypeCode == '3') {
        this.pageTitle = '电话预约';
      } else {
        this.pageTitle = '视频预约';
        if (appointInfoDetail.consultationCode == '0') {
          // 咨询
          this.msgList = [
            {
              url: '/static/images/appoint/video_base.png',
            },
            {
              url: '/static/images/appoint/base.png',
            },
          ];
        } else {
          // 复诊
          this.msgList = [
            {
              url: '/static/images/appoint/video_base.png',
            },
            {
              url: '/static/images/appoint/base.png',
            },
            {
              url: '/static/images/appoint/medical_doc.png',
            },
            {
              url: '/static/images/appoint/medical.png',
            },
          ];
        }
      }

      this.timeBettwen.start = myJsTools.getDate('day');
      this.timeBettwen.end = myJsTools.getDate('day', 7);
    },
    // 查询有排班的日期
    async getReserveFlagFun() {
      let appointInfoDetail = uni.getStorageSync('appointInfoDetail');
      let appointInfo = {
        consultationCode: appointInfoDetail.consultationCode,
        visitTypeCode: appointInfoDetail.visitTypeCode,
        docId: appointInfoDetail.docId,
      };
      let res = await getReserveFlag(appointInfo);
      let data = res.data;
      let visitDates = [];
      for (let i = 0; i < data.length; i++) {
        visitDates.push({
          date: data[i].visit_date,
          info: '可预约',
        });
      }
      this.visitDates = visitDates; // 排班标志
      if (!visitDates.length) {
        Toast('医生近期无可预约时间');
        this.visitDate = this.timeBettwen.start;
        return;
      }
      this.visitDate = visitDates[0].date;
      this.getReserveTimeListFun();
    },
    // 选择日期
    selevtVisitDate(evt) {
      this.visitDate = evt.fulldate;
      this.getReserveTimeListFun();
    },
    // 查询某一天的出诊排班
    async getReserveTimeListFun() {
      let appointInfoDetail = uni.getStorageSync('appointInfoDetail');
      let appointInfo = {
        consultationCode: appointInfoDetail.consultationCode,
        visitTypeCode: appointInfoDetail.visitTypeCode,
        docId: appointInfoDetail.docId,
        visitDate: this.visitDate,
        hosId: uni.getStorageSync('hosId'),
      };
      let res = await getReserveTimeList(appointInfo);
      let data = res.data;
      let reserveTimeList = [];
      for (var i = 0; i < data.length; i++) {
        let obj = data[i];
        let startTime = obj.startTime;
        let endTime = obj.endTime;
        obj.startTimeShow = startTime.slice(0, 5);
        obj.endTimeShow = endTime.slice(0, 5);
        reserveTimeList.push(obj);
      }
      this.allReserveTimeList = reserveTimeList;
      if (reserveTimeList.length > 3) {
        reserveTimeList = reserveTimeList.slice(0, 3);
        this.getMoreShow = true;
        this.reserveTimeList = reserveTimeList;
      } else {
        this.reserveTimeList = reserveTimeList;
      }
    },
    // 查看更多
    getMore() {
      this.getMoreShow = false;
      this.reserveTimeList = this.allReserveTimeList;
    },
    // 点击预约
    appoint(evt) {
      if (evt.appointLimitNum <= 0) return;

      // 当前日期
      let now = new Date(this.timeBettwen.start).getTime();
      // 当前点击日期
      let item = new Date(evt.visitDate).getTime();

      if (item < now) {
        Toast('当前时间不可预约');
        return;
      }

      // 如果预约是当天
      if (item == now) {
        let h = new Date().getHours();
        let m = new Date().getMinutes();
        let end = evt.endTimeShow.split(':');

        // 当前
        let n = h * 60 + m;
        // 选择
        let s = end[0] * 60 + Number(end[1]);

        // 如果当前时间 大于预约结束时间
        if (n >= s) {
          Toast('当前时间不可预约');
          return;
        }
      }
      uni.setStorageSync('appointReserveInfo', evt);
      uni.navigateTo({
        url:
          '/pages/personalCenter/patientManage/index?action=' +
          'appointSelectPatient',
      });
    },
  },
};
</script>

<style scoped lang="scss">
.page-container {
  box-sizing: border-box;
  overflow-y: scroll;
  background: #f5f6f7;
}

/* 图片提示区 */
.img_box {
  @include flex(left);
  background: #fff;
  margin-top: 24rpx;
  padding: 0 20rpx;
}

.img_box image {
  width: 174rpx;
  height: 232rpx;
  vertical-align: top;
}

/* 排班样式 */
.reserve-date,
.timeList {
  background: #fff;
  margin-top: 24rpx;
}

.reserve_box {
  padding-bottom: 30upx;
}

.reserve-date .title {
  width: 100%;
  height: 92rpx;
  line-height: 92rpx;
  font-size: 28rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: $k-title;
  padding: 0 32rpx;
  box-sizing: border-box;
  @include flex(left);

  .line {
    display: inline-block;
    width: 6rpx;
    height: 28rpx;
    @include bg_theme;
    margin-right: 16rpx;
    // margin-top: 6rpx;
  }
}

.date-content {
  // padding: 0 36rpx;
  box-sizing: border-box;
  overflow-y: auto;
}

/* 日历样式 */
/deep/.uni-calendar {
  width: 100%;
}

/deep/.uni-calendar__backtoday {
  display: none;
}

/deep/.uni-calendar__content {
  width: 100%;
  height: 100%;
}

/deep/.uni-calendar__box {
  width: 100%;
  height: 100%;
}

/deep/.uni-calendar-item__weeks-box-item {
  width: 48px;
  height: 48px;
}

/deep/ .uni-calendar-item--isDay .uni-calendar-item--extra {
  color: #fff;
}

/deep/.uni-calendar-item--checked {
  background: #ff5050;

  .uni-calendar-item__weeks-box-item {
    background: inherit !important;
  }
}

/deep/.uni-calendar-item--isDay {
  background: #ff5050;
}

// 预约时间列表
.list {
  padding: 0 32upx;
  .list_item {
    @include flex(lr);
    height: 112upx;
    border-bottom: 1px solid #ebebeb;

    // 时间段
    .item_time {
      width: 40%;
      flex: none;
      font-size: 32rpx;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: $k-title;
    }

    // 剩余号
    .number {
      width: 40%;
      text-align: center;
      font-size: 28rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: rgba(102, 102, 102, 1);
      line-height: 48rpx;
    }
  }
}

// 没有时间
.list_empt {
  text-align: center;
  font-size: 28upx;
  color: $k-info-title;
  line-height: 100upx;
}

.btn-more {
  width: 686rpx;
  height: 92rpx;
  line-height: 92rpx;
  border-radius: 46rpx;
  @include border_theme;
  margin: 24rpx auto 26rpx;
  font-size: 36rpx;
  font-family: PingFangSC-Semibold, PingFang SC;
  @include font_theme;
}
</style>
