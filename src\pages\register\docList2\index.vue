<template>
  <div class="page">
    <!-- 顶部固定 -->
    <div class="fixed">
      <view class="search-container">
        <uni-search-bar
          placeholder="请输入您想要搜索的内容"
          cancelButton="none"
          @confirm="search"
          @input="changeInput"
        ></uni-search-bar>
      </view>
    </div>
    <!-- 列表 -->
    <template v-if="docList.length">
      <scroll-view
        scroll-y="true"
        class="doc_list_box"
        @scrolltolower="getMore"
      >
        <div class="list_warp">
          <!-- 循环 -->
          <template v-for="item in docList">
            <view class="doc_box" @click="goDocHomePage(item)">
              <div>
                <image
                  :src="
                    item.docImg ? item.docImg : '/static/images/docHead.png'
                  "
                  mode="aspectFill"
                  class="header_img"
                  :class="item.isOnline == 0 ? 'docImg_box_unOnline' : 'docImg_box_Online'"
                >
                </image>
                <div
                    :style="{
                          color: item.isOnline == 0 ? '#ffffff' : '#ffffff',
                          textAlign: 'center',
                          marginTop: '-15px',
                          zIndex: 10,
                          position: 'relative',
                          fontSize: '10px',
                        }"
                >
                  <div v-if="item.isOnline == 0" style=" width: 37px;height: 15px;border-radius: 15px;background: #c7c7c7;margin: auto;display: flex;align-items: center;justify-content: space-evenly;color: #efefef">
                    <span style="font-size: 8px">离线</span>
                  </div>
                  <div v-else style=" width: 37px;height: 15px;border-radius: 15px;background: #0AC2B2;margin: auto;display: flex;align-items: center;justify-content: space-evenly;color: #ffffff">
                    <span style="font-size: 8px">在线</span>
                  </div>
                </div>
              </div>

              <view class="doc_box_right">
                <view class="doc_box_right_top">
                  <view class="doc_box_right_name">
                    <view
                      >{{ item.docName }}
                      <text
                        style="
                          font-size: 12px;
                          color: #666666;
                          padding-left: 10px;
                        "
                        >{{ item.docProf }}</text
                      ></view
                    >
                    <view
                      ><text
                        class="dep"
                        v-for="(deptName, index) in item.dept"
                        >{{ deptName }}</text
                      >
                    </view>
                    <view style="font-size: 12px; color: #999999">
                      {{
                        isShowWorkHosName ? item.workHosName : item.hosName
                      }}</view
                    >
                  </view>
                  <view class="hint_box">
                    <template v-for="element in item.visitType">
                      <view
                        :class="
                          element.visitTypeCode == 1
                            ? 'one'
                            : element.visitTypeCode == 4
                            ? 'three'
                            : 'two'
                        "
                      >
                        {{ element.visitTypeName }}</view
                      >
                    </template>
                  </view>
                </view>

                <!-- 描述 -->
                <view class="doc_box_right_content">
                  <view style="display: flex; align-items: center">
                    <img
                      style="width: 14px; height: 14px; margin-right: 8px"
                      src="../../../static/分组 <EMAIL>"
                      alt=""
                    />
                    <text>接诊量:{{ item.consultationCount }}例</text>
                  </view>

                  <text class="long">|</text>
                  <view style="display: flex; align-items: center">
                    <img
                      style="width: 14px; height: 14px; margin-right: 8px"
                      src="../../../static/路径 <EMAIL>"
                      alt=""
                    />
                    <text>好评数:{{ item.percentage }}%</text>
                  </view>

                  <!--                  <text class="long">|</text>-->
                  <!--                  <text>平均响应:{{ item.responseTime }}分钟</text>-->
                </view>

                <!-- 标签 -->
                <!--                <view class="little_label">-->
                <!--                  <template v-for="element in item.docLable">-->
                <!--                    <text>{{ element.lableName }}</text>-->
                <!--                  </template>-->
                <!--                </view>-->
              </view>
              <view v-if="item.isReferral == '1'" class="zz-logo">转诊</view>
            </view>
          </template>
          <view v-if="isShowMore">
            <uni-load-more :status="status"></uni-load-more>
          </view>
        </div>
      </scroll-view>
    </template>
    <view class="empty_list" v-else>
      <image src="/static/images/index/office_empty.png" />
      <view> 暂无医生 </view>
    </view>
  </div>
</template>

<script>
import {
  findDoctorByDeptID,
  findDoctorByDeptIDFZ,
  findAllVisitType,
  findDoctorProfType,
  getAllDept,
  getDicDisease,
} from "@/api/base.js";

import myJsTools from "@/common/js/myJsTools.js";

import HMfilterDropdown from "@/components/HM-filterDropdown/HM-filterDropdown.vue";
export default {
  components: {
    HMfilterDropdown: HMfilterDropdown,
  },
  data() {
    return {
      docList: [],
      docListQuery: {
        appid: uni.getStorageSync("appId"),
        openid: uni.getStorageSync("wxInfo").openId,
        searchKey: "",
        subjectCode: "",
        page: 1,
        limit: 10,
        bussType: "",
      },
      total: "",
      listQuery: {
        deptName: "",
        page: 1,
        limit: 100,
      },
      total: "",
      isShowMore: false,
      status: "loading",
      filterData: [
        {
          name: "综合排序",
          type: "hierarchy",
          submenu: [
            {
              name: "综合排序",
              value: "",
            },
            {
              name: "好评度",
              value: "0",
            },
            {
              name: "问诊量",
              value: "1",
            },
            {
              name: "平均响应时长",
              value: "2",
            },
          ],
        },
        {
          name: "医师级别",
          type: "filter",
          submenu: [
            {
              name: "",
              value: "",
              submenu: [],
            },
          ],
        },
        {
          name: "问诊方式",
          type: "filter",
          submenu: [
            {
              name: "",
              value: "",
              submenu: [],
            },
          ],
        },
        {
          name: "科室",
          type: "hierarchy",
          submenu: [],
        },
      ],
      defaultSelected: [],
      // 是否搜索疾病
      isDiasease: false,
      isShowWorkHosName: uni.getStorageSync("isShowWorkHosName") || false,
    };
  },
  onShow() {},
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(option) {
    let flag = option.flag;
    uni.setNavigationBarTitle({
      title: flag == 1 ? "健康咨询" : "复诊开方",
    });
    if (flag == 1) {
      //健康咨询
      this.docListQuery.bussType = 0;
    } else {
      this.docListQuery.bussType = 1; //复诊开发
    }
    this.getDocList();
  },
  // 下拉刷新
  onPullDownRefresh() {
    this.docList = [];
    this.docListQuery.visitTypeCode = [];
    this.docListQuery.docProf = [];
    this.docListQuery.orderByKey = "";
    this.docListQuery.diseaseNames = [];
    this.docListQuery.page = 1;
    this.getDocList();
  },
  methods: {
    // 获取医生列表
    async getDocList() {
      console.log(this.docListQuery);
      uni.stopPullDownRefresh();
      this.docListQuery.openid = uni.getStorageSync("wxInfo").openId;
      this.docListQuery.curDateDocType = 1;
      let {
        data: { rows, total },
      } =
        this.docListQuery.bussType == 1
          ? await findDoctorByDeptIDFZ(this.docListQuery)
          : await findDoctorByDeptID(this.docListQuery);
      let docList = rows;
      this.total = total;
      // 循环获取头像
      for (let i = 0; i < docList.length; i++) {
        let obj = docList[i];
        // 存在头像
        if (obj.docImg) {
          myJsTools.downAndSaveImg(obj.docImg, (url) => {
            obj.docImg = url;
          });
        }
      }
      // 拼接数组
      // this.docList = [...this.docList, ...docList];
      this.docList = [...this.docList, ...this.updOCList(docList)];
    },
    //重新格式化医生数组,将重复医生归类
    updOCList(arr) {
      let newArr = [];
      arr.forEach((item, index) => {
        let i = newArr.findIndex((nitem) => {
          return nitem.docId == item.docId;
        });
        if (i == -1) {
          item.dept = [item.deptName];
          newArr.push(item);
        } else {
          newArr[i].dept.push(item.deptName);
        }
      });
      return newArr;
    },
    // 模糊搜索
    search(e) {
      this.docList = [];
      this.docListQuery.page = 1;
      this.docListQuery.searchKey = e.value;
      this.getDocList();
    },
    // 清空搜索框时,重置
    changeInput(e) {
      if (e.value == "") {
        this.docList = [];
        this.docListQuery.page = 1;
        this.docListQuery.searchKey = "";
        this.getDocList();
      }
    },
    // 加载更多
    getMore() {
      this.isShowMore = true;
      // 共几页
      let num = Math.ceil(this.total / this.docListQuery.limit);
      if (this.docListQuery.page < num) {
        this.docListQuery.page += 1;
        this.getDocList();
        this.isShowMore = false;
      } else {
        this.status = "noMore";
      }
    },
    // 去医生主页
    goDocHomePage(e) {
      let { hosId, docId } = e;
      uni.setStorageSync("hosId", hosId);
      uni.navigateTo({
        url: "/pages/register/docHomePage/index?docId=" + docId,
      });
    },
  },
};
</script>

<style scoped lang="scss">
.page {
  display: flex;
  flex-direction: column;
}

.docImg_box_unOnline {
  border: 1px solid #CCCCCC;
  margin-right: 20upx;
  border-radius: 100px;
}
.docImg_box_Online {
  margin-right: 20upx;
  border-radius: 100px;
  border: 1px solid #0AC2B2;
}

/* 搜索框样式 */
.search-container {
  width: 100%;
  position: relative;
  z-index: 14;
}

/deep/.uni-searchbar {
  padding: 0 40upx;
  height: 88rpx;
  border-bottom: 2rpx solid #ebebeb;
}

/deep/.uni-searchbar__box {
  height: 70rpx;
  border: none;
  border-radius: 215px !important;
  background: #f2f5ff;
  margin-top: 20upx;
  padding: 15upx 23upx;
  justify-content: flex-start !important;
}

/deep/ .uni-searchbar__box-icon-search {
  color: #874ff0 !important;
}

.fixed {
  width: 100%;
  position: sticky;
  height: 85rpx;
  flex: none;
  top: 0;
  z-index: 9;
}

.search-container {
  position: relative;
  z-index: 15;
}

/* 筛选条件 */
.query-container {
  width: 100%;
  height: 84rpx;
}

/* 医生列表 */
.doc_list_box {
  // padding: 32rpx;
  box-sizing: border-box;
  overflow-y: auto;
  background: #fff;
  flex: 1;

  .list_warp {
    padding: 32rpx 40rpx 32rpx 52rpx;
  }

  .doc_box {
    border-bottom: 1px solid $k-hr-color;
    @include flex(lr);
    align-items: flex-start;
    padding: 24rpx 0;
    position: relative;

    .header_img {
      width: 90rpx;
      height: 90rpx;
      border-radius: 800upx;
      margin-right: 0 !important;
    }
  }
}

// 标签
.little_label {
  width: 100%;

  text {
    display: inline-block;
    background: #e8f6fd;
    border-radius: 18rpx;
    font-size: 22rpx;
    @include font_theme;
    margin-right: 16upx;
    padding: 0 12rpx;
    height: 36rpx;
    line-height: 36upx;
    font-weight: 400;
  }
}

.doc_box .doc_box_right {
  flex: 1;
}

.doc_box .hint_box {
  display: flex;
}

.doc_box .hint_box view {
  width: 88rpx;
  height: 36rpx;
  border-radius: 8rpx;
  color: #ffffff;
  font-size: 24rpx;
  text-align: center;
  line-height: 36rpx;
}

.doc_box .hint_box view {
  margin-left: 10rpx;
}

.doc_box .hint_box view:first-child {
  margin-left: 0;
}

.doc_box .hint_box .one {
  background-color: #f2f5ff;
  color: #874ff0;
}

.doc_box .hint_box .two {
  background-color: #defceb;
  color: #00baad;
}

.doc_box .hint_box .three {
  background-color: #def4fc;
  color: #0078d7;
}

.doc_box .doc_box_right_top {
  display: flex;
  justify-content: space-between;
}

.doc_box .doc_box_right_name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.doc_box .doc_box_right_name view:last-child {
  font-weight: 500;
  font-size: 28rpx;
  color: #444444;
  padding-top: 10rpx;
}

// 描述
.doc_box_right_content {
  @include flex(lr);
  font-size: 24rpx;
  color: #a6aab2;
  padding: 6upx 0;
}

/* 转诊标志 */
.zz-logo {
  width: 88rpx;
  height: 36rpx;
  border-radius: 8rpx;
  color: #ffffff;
  background: #159f5c;
  font-size: 24rpx;
  text-align: center;
  line-height: 36rpx;
  position: absolute;
  top: 70rpx;
  right: 0;
}

/* 列表为空提示 */
.empty_list {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  font-weight: 400;
  color: rgba(158, 163, 173, 1);
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.dep {
  margin-right: 8rpx;
  font-size: 12px;
  color: #666666;
}

.empty_list image {
  margin-bottom: 40rpx;
  width: 386rpx;
  height: 324rpx;
}
</style>
