<template>
  <div class="detail">
    <!-- 药品卡片 -->
    <div class="card">
      <view style="position: relative">
         <img
            v-if="detail.drugImg"
            v-img:click="detail.drugImg"
            :data-src="errUrl"
            alt=""
           :class="['card_img',detail.drugType=='025.9'?'gx':'']"
        />
        <!-- 商品图片 -->
        <image
          src="/static/shop/drug.png"
          v-else
          mode="aspectFill"
          class="card_img"
        ></image>
        <view v-if="detail.drugType=='025.9'" class="drug-coverUp cfCover">
          处方药 <br>
          依规定不展示包装
        </view>
        <view v-if="detail.drugKc==0||!detail.drugKc"  class="drug-coverUp">
          <img class="card_img" src="/static/ysq.png" alt="">
        </view>
      </view>


      <!-- 右侧 -->
      <div class="right" style="overflow:hidden;">
        <view style="display: flex">
            <view v-if="detail.drugType!='025.8'&&detail.drugType" class="drugTypeName" :style="getDrugTypeColor(detail)">{{ detail.drugTypeName||'' }}</view>
        <text class="name letter">{{ detail.drugName }}</text>
        </view>

        <div v-if="!isScan" class="but">
<!--          <span v-if="detail.otc == 0">处方药</span>-->
          <i ></i>
          <!-- 操作 -->
          <div class="action" v-show="true || (item.quan && item.drugKc)">
            <text class="label">购买数量</text>
            <image
              @click="reduce"
              v-if="item.quan > 0"
              class="icon"
              src="/static/shop/del.png"
            ></image>
            <image v-else class="icon" src="/static/shop/jian.png"></image>
            <text class="num">{{ item.quan }}</text>
            <image @click="add" class="icon" src="/static/shop/add.png"></image>
          </div>
          <!-- 添加 有库存 且未选中 -->
          <div class="action" v-show="false && !item.quan && item.drugKc">
            <image @click="add" class="icon" src="/static/shop/add.png"></image>
          </div>
        </div>
      </div>
    </div>
    <div v-if="isScan!=1" class="cont drugstore">
      <view>{{ getDrugDetailStore.drugstoreName }}</view>
      <view style="color: #606060" @click="toStoreList">切换门店></view>
    </div>
    <!-- 说明 -->
    <div class="cont" style="position: relative">
     <view v-if="detail.drugType=='025.9'" class="cfCover">
       <view style="flex: 1;z-index: 9;font-size: 16px">
          处方药 <br>
        依法规不展示药品详情
       </view>
      </view>
      <div :class="['tip',detail.drugType=='025.9'?'gx':'']">
        <p class="title">药品信息</p>

        <div class="bg" v-if="detail.memo">
          <h4>【功能主治】</h4>
          <p>{{ detail.memo }}</p>
        </div>

        <div class="bg">
          <h4>【用法用量】</h4>
          <p>
            {{ detail.dduName }},每次{{ detail.eachQuan }}{{ detail.jldw }},
            {{ detail.ddufName }}
          </p>
        </div>

        <div class="bg">
          <block v-if="detail.genericName">
            <h4>【通用名称】</h4>
            <p>{{ detail.genericName }}</p>
          </block>
          <block v-if="detail.drugName">
            <h4>【商品名称】</h4>
            <p>{{ detail.drugName }}</p>
          </block>
          <block v-if="detail.chemistryName">
            <h4>【化学名称】</h4>
            <p>{{ detail.chemistryName }}</p>
          </block>
          <block v-if="detail.jx">
            <h4>【药品剂型】</h4>
            <p>{{ detail.jx }}</p>
          </block>
          <block v-if="detail.unit">
            <h4>【包装单位】</h4>
            <p>{{ detail.unit }}</p>
          </block>
          <block v-if="detail.memo">
            <h4>【主治功能】</h4>
            <p>{{ detail.memo }}</p>
          </block>
          <block v-if="detail.component">
            <h4>【成分】</h4>
            <p>{{ detail.component }}</p>
          </block>

          <block v-if="detail.adverseReaction">
            <h4>【不良反应】</h4>
            <p>{{ detail.adverseReaction }}</p>
          </block>

          <block v-if="detail.taboo">
            <h4>【禁忌】</h4>
            <p>{{ detail.taboo }}</p>
          </block>

          <block v-if="detail.storage">
            <h4>【贮藏】</h4>
            <p>{{ detail.storage }}</p>
          </block>

          <block>
            <h4>【规格】</h4>
            <p>{{ detail.gg }}</p>
          </block>

          <block v-if="detail.termOfValidity">
            <h4>【有效期】</h4>
            <p>{{ detail.termOfValidity }}</p>
          </block>

          <block v-if="detail.pzwh">
            <h4>【批准文号】</h4>
            <p>{{ detail.pzwh }}</p>
          </block>

          <block v-if="detail.scqy">
            <h4>【生产企业】</h4>
            <p>{{ detail.scqy }}</p>
          </block>

          <block v-if="detail.reminder">
            <h4>【温馨提示】</h4>
            <p>{{ detail.reminder }}</p>
          </block>

          <block v-if="drugDescriptionPicture.length">
            <h4>【药品说明书】</h4>
              <img v-for="(item,index) in drugDescriptionPicture"
                v-img:click="item" />
            </span>

          </block>
        </div>
      </div>
    </div>

    <!-- 悬浮 -->
    <view v-if="isScan!=1"  class="round" :class="{ act: drugNum }" @click="setShowCart">
      <image v-show="!drugNum" src="/static/shop/car.png"></image>
      <view class="show" v-show="drugNum">
        <image src="/static/shop/cart_act.png"></image>
        <text>去结算</text>
      </view>

      <text class="num" v-show="drugNum">{{ drugNum }}</text>
    </view>

    <CART
      @itemChange="itemChange"
      @clear="clear"
      v-show="showCart"
      ref="cart"
      @close="showCart = false"
    />
  </div>
</template>

<script>
import { findDrugById } from '@/api/base';
import CART from '../components/cart.vue';
import { mapActions, mapGetters } from 'vuex';
import {findOfflineDrugById} from "../../../api/base";
// 获取上一页实例
let prev;

export default {
  name: 'Drug',
  components: {
    CART,
  },
  data() {
    return {
      drugDescriptionPicture:[],
      drugId: '',
      detail: {},
      item: {},
      prev:null,
      // 显示购物车
      showCart: false,
      errUrl: require('../../../static/images/Pharmacy-default.png'),
      drugstoreName:'',
      drugstoreId:'',
      isScan:0
    };
  },
  computed: {
    ...mapGetters(['drugNum', 'shopList',"getDrugDetailStore"]),
  },
  onLoad(v) {
    this.prev = getCurrentPages()[getCurrentPages().length - 2];
    let { id, drugKc, quan, yfkcId, isScan } = v;
    this.drugId = id;
    this.item = {
      drugKc: Number(drugKc),
      quan: Number(quan),
      yfkcId,
    };
    this.isScan=isScan
    this.drugstoreName=v.drugstoreName
    this.drugstoreId=v.drugstoreId
    if(this.isScan == 1){
      this.getFindOfflineDrugById()
      return
    }
    this.getDetail();
  },
  onShow(){
    // this.getDetail();
  },
  methods: {
    ...mapActions('shop', ['addDrugItem', 'reduceDrugItem']),
   async getFindOfflineDrugById(){
      const { data } = await  findOfflineDrugById(this.drugId,this.getDrugDetailStore.drugstoreId,uni.getStorageSync('wxInfo').openId)
       if(data.drugDescriptionPicture) {
         this.drugDescriptionPicture = JSON.parse(data.drugDescriptionPicture)
       }
       this.item = {
         ...this.item,
         ...data
       }
       this.detail = data;
    },
    toStoreList(){
       uni.navigateTo({
        url: `/pages/shop/store?isDrugDetail=1&drugId=${this.drugId}`
      });
    },
        getDrugTypeColor(item){
      const map={
        '025.6': 'rgb(244, 92, 98)',
        '025.7': 'rgb(105, 211, 161)',
        '025.8': '#FFB800',
        '025.9': 'rgb(107, 202, 215)',
      }
      return `background:${map[item.drugType]} !important`;
    },
    // 显示购物车
    setShowCart() {
      if (!this.drugNum) return;
      this.showCart = true;
      this.$refs.cart.showList();
    },
    // 清空
    clear() {
      this.item.quan = 0;
      this.prev.getList();
    },
    async add() {
      let list = this.prev.list;
      const index = list.findIndex((v) => v.yfkcId == this.item.yfkcId);
      const item = list[index];
      console.log(this.prev,item)
      await this.addDrugItem(this.item)
      // await this.prev.add(item, index);
      this.item.quan++;
    },
    async reduce() {
      await this.reduceDrugItem(this.item);
      // this.$set(this.list, index, item);
      // let list = this.prev.list;
      // const index = list.findIndex((v) => v.yfkcId == this.item.yfkcId);
      // const item = list[index];
      //
      // await this.prev.reduce(item, index);
      this.item.quan--;
      if (this.item.quan <= 0) this.item.quan = 0;
    },
    async getDetail() {
      let { data } = await findDrugById(this.drugId,this.getDrugDetailStore.drugstoreId,uni.getStorageSync('wxInfo').openId);
      if(data.drugDescriptionPicture) {
        this.drugDescriptionPicture = JSON.parse(data.drugDescriptionPicture)
      }
      this.item={
        ...this.item,
        ...data
      }
      this.detail = data;
    },
    // 购物车药品变化
    itemChange(item) {
      if (item.yfkcId == this.item.yfkcId) {
        this.item.quan = item.quan;
      }
      // 查找当前列表指定药品的下标
      this.prev.itemChange(item);
    },
  },
};
</script>

<style lang="scss" scoped>
page {
  background-color: #f5f5f5;
}

.detail {
  .card {
    background-color: #fff;
    border-radius: 0 0 16rpx 16rpx;
    @include flex(lr);
    padding: 24rpx;

    .card_img {
      width: 128rpx;
      height: 128rpx;
      border-radius: 8rpx;
      object-fit: cover;
      flex: none;
    }

    .right {
      padding-left: 24rpx;
      height: 128rpx;
      flex: 1;
      @include flex;
      align-items: stretch;
      flex-direction: column;

      .name {
        font-size: 32rpx;
        font-weight: bold;
        margin-bottom: 12rpx;
      }

      .but {
        @include flex(lr);

        span {
          width: 90rpx;
          height: 36rpx;
          @include bg_theme;
          font-size: 24rpx;
          color: #fff;
          @include flex;
          border-radius: 16rpx;
        }

        .action {
          @include flex;

          .label {
            font-size: 28rpx;
            color: #999;
            margin-right: 16rpx;
          }

          .icon {
            width: 40rpx;
            height: 40rpx;
          }

          .num {
            min-width: 60rpx;
            height: 36rpx;
            @include flex;
            font-size: 28rpx;
            background-color: #f5f5f5;
            margin: 0 16rpx;
            border-radius: 4rpx;
          }
        }
      }
    }
  }

  .cont {
    padding: 24rpx;

    .tip {
      border-radius: 16rpx;
      background-color: #fff;
      padding: 0 24rpx 1px;

      .title {
        line-height: 90rpx;
        font-size: 32rpx;
        font-weight: bold;
      }

      .bg {
        background-color: #f5f5f5;
        border-radius: 16rpx;
        margin-bottom: 16rpx;
        padding: 0 24rpx 16rpx;

        h4 {
          line-height: 70rpx;
          font-size: 28rpx;
        }

        p {
          font-size: 28rpx;
          color: #666;
          line-height: 40rpx;
          padding-bottom: 12rpx;
        }
        img {
          width: 610rpx;
          height: 610rpx;
        }
      }
    }
  }

  .round {
    min-width: 88rpx;
    height: 88rpx;
    position: fixed;
    bottom: 154rpx;
    z-index: 2;
    right: 24rpx;
    border-radius: 44rpx;
    background-color: #fafafa;
    @include flex;
    box-shadow: 0 0 20rpx #ddd;

    &.act {
      background-color: #e2f0f7;
    }

    image {
      width: 64rpx;
      height: 64rpx;
    }

    .num {
      min-width: 30rpx;
      height: 30rpx;
      padding: 0 4rpx;
      background-color: #ff5050;
      @include flex;
      font-size: 24rpx;
      color: #fff;
      border-radius: 16rpx;
      line-height: 24rpx;
      position: absolute;
      right: 0;
      top: 0;
    }

    .show {
      @include flex;
      padding: 0 24rpx;

      image {
        width: 44rpx;
        height: 44rpx;
        margin-right: 8rpx;
      }

      text {
        font-size: 28rpx;
        @include font_theme;
      }
    }
  }
}
.drugstore{
  display: flex;
  justify-content: space-between;
}
.drugTypeName{
  white-space: nowrap;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: red;
  color: white;
  padding:0 7px;
  border-radius: 6px;
  margin-right: 5px;
}
.letter{
  // 超出一行自动省略号
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.cfCover{
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  text-align: center;
  font-size: 10px;
  background: rgba(255,255,255,0.7) !important;
  display: flex;
  align-items: center;
  flex: 1;
  z-index: 2;
}
.gx{
   filter: blur(2px);
}
.drug-coverUp{
  width: 128rpx;height: 128rpx;
  background: rgba(255, 255, 255, 0.5);
  position: absolute;
  top: 0;
  left: 0;
}
</style>
