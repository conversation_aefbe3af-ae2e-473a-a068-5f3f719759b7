<template>
  <!-- 回到首页 -->
  <view class="go_home" @click="goHome" v-show="showHome">
    <image src="/static/home.png" class="icon" />
  </view>
</template>

<script>
export default {
  name: 'GoHome',
  data() {
    return {
      showHome: false,
    };
  },
  created() {
    let length = getCurrentPages().length;
    if (length == 1) {
      this.showHome = true;
    } else {
      this.showHome = false;
    }
  },
  methods: {
    // 关闭所有页面 直接去首页
    goHome() {
      uni.reLaunch({
        url: '/pages/index/index',
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.go_home {
  width: 70rpx;
  height: 70rpx;
  border-radius: 50%;
  background-color: rgba($color: #000000, $alpha: 0.5);
  @include flex;
  position: fixed;
  right: 30upx;
  bottom: 60upx;
  z-index: 999;

  .icon {
    width: 50rpx;
    height: 50rpx;
  }
}
</style>
