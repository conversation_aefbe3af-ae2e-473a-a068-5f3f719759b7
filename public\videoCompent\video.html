<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <title></title>
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0">
    <link rel="stylesheet" href="./css/index.css">
    <script src="./js/flexible.js"></script>
    <script src="./js/vue.min.js"></script>
    <script src="./js/vant.min.js"></script>
</head>
<style>
    * {
        list-style: none;
        box-sizing: border-box;
        padding: 0;
        margin: 0;
        width: 100%;
        height: 100%;
    }

    #app {
        width: 100%;
        height: 100%;
    }

    .cont_video {
        width: 100%;
        height: 100%;
        /* height: 200px; */
        object-fit:fill;
    }
</style>

<body>
    <div id="app">
        <video class="cont_video" webkit-playsinline="true" playsinline="true" preload="Metadata"
        
            :poster="videoSrc+ '?x-oss-process=video/snapshot,t_0,f_jpg,w_0,h_0,ar_auto'" webkit-playsinline="true"
             initial-time="1" x5-video-player-type="h5" controls>
            <source :src="videoSrc" type="video/mp4">
            <source :src="videoSrc" type="video/ogg">
            <source :src="videoSrc" type="video/webm">
        </video>
    </div>
</body>

</html>

<script>
    initVue()
    function initVue() {
        var app = new Vue({
            el: '#app',
            data: {
                videoSrc: "https://a1-v2.easemob.com/1102200519107623/cloudhos/chatfiles/3b60fb40-6c6d-11ed-87f0-6feeaf0288c2?em-redirect=true",
            },
            created() {
                this.getUrl("add");
            },
            methods: {
                async getUrl(paras) {
                    var url = location.href;
                    var paraString = url
                        .substring(url.indexOf("?") + 1, url.length)
                        .split("&");
                    var paraObj = {};
                    for (var i = 0, j; (j = paraString[i]); i++) {
                        paraObj[j.substring(0, j.indexOf("=")).toLowerCase()] =
                            decodeURIComponent(j.substring(j.indexOf("=") + 1, j.length));
                    }
                    var returnValue = paraObj[paras.toLowerCase()];
                    if (typeof returnValue == "undefined") {
                        this.videoSrc = "";
                    } else {
                        this.videoSrc = returnValue;
                    }
                    console.log(location.href)
                },


            },
        })

        Vue.use(vant);
    }
</script>