<template>
	<view class="prop-container">
		<view class="wrapper">
			<view class="prop-content">
				<view class="select-lists">
					<template v-for="item in actions">
						<view class="list" @click="confirm(item)">
							{{item.name}}
						</view>
					</template>
				</view>
				<view class="line"></view>
				<view class="cancel-btn" @click="cancel">
					取消
				</view>
			</view>
		</view>
	</view>
</template>
<script>
	export default ({
		props: {
			actions: {
				type: Array,
				default:[]
			},
		},
		data() {
			return {}
		},
		mounted() {
		},
		methods: {
			confirm(value) {
				this.$emit("propConfirm", value);
			},
			cancel() {
				this.$emit("propCancel");
			}
		}
	})
</script>

<style scoped>
	.wrapper{
		position: fixed;
		width: 100%;
		height: 100vh;
		left: 0;
		top: 0;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		z-index: 999;
	}
	.prop-content{
		width: 100%;
		position: absolute;
		bottom: 0;
		left: 0;
		background: #FFFFFF;
		border-radius: 8px 8px 0px 0px;
		overflow: hidden;
	}
	.list{
		width: 100%;
		height: 114rpx;
		line-height: 114rpx;
		background: #FFFFFF;
		text-align: center;
		font-size: 36rpx;
		font-weight: 400;
		color: #333333;
		border-bottom: 2rpx solid #E5E5E5;
	}
	
	.list:last-child{
		border-bottom: none;
	}
	.line{
		width: 100%;
		height: 16rpx;
		background: #F5F5F5;
	}
	.cancel-btn {
		width: 100%;
		height: 114rpx;
		line-height: 114rpx;
		background: #FFFFFF;
		text-align: center;
		font-size: 36rpx;
		font-weight: 400;
		color: #333333;
	}
</style>
