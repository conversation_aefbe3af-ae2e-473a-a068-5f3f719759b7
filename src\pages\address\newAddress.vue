<template>
  <view class="newAddress">
    <view class="addForm">
      <view class="formInfo">
        <view class="tit">请填写收货人姓名</view>
        <input
          class="uni-input"
          placeholder-style="color:#999999;font-size:24rpx"
          v-model="info.deliveryName"
          maxlength="10"
          placeholder="请填写收货人姓名"
        />
      </view>

      <view class="formInfo">
        <view class="tit">请填写收货人手机号</view>
        <input
          class="uni-input"
          placeholder-style="color:#999999;font-size:24rpx"
          v-model="info.telNo"
          maxlength="11"
          type="number"
          placeholder="请填写收货人手机号"
        />
      </view>

      <view class="formInfo">
        <view class="tit">请选择省/市/区</view>
        <Region @change="cityChange" :result="info.addressArea" />
        <view class="right" @click="openSelectArea">
          <uni-icons type="location-filled" size="14" color="#999"></uni-icons>
          <text>定位</text>
        </view>
      </view>

      <view class="formInfo start">
        <view class="tit">街道、楼牌号等</view>
        <textarea
          v-model="info.addressDetail"
          placeholder-style="color:#999999;font-size:24rpx"
          placeholder="街道、楼牌号等"
        />
      </view>

      <!--      <view>-->
      <!--        <template v-if="!info.lableName">-->
      <!--          <view class="label">-->
      <!--            <view>标签</view>-->
      <!--            <text-->
      <!--              v-for="(item, index) in label"-->
      <!--              :key="index"-->
      <!--              class="labelName"-->
      <!--              @click="selectLabel(item.lableName)"-->
      <!--              >{{ item.lableName }}</text-->
      <!--            >-->
      <!--            <text class="labelName" @click="addLabel">-->
      <!--              <image-->
      <!--                src="../../static/images/index/add.png"-->
      <!--                class="labelAdd"-->
      <!--              ></image>-->
      <!--            </text>-->
      <!--          </view>-->
      <!--        </template>-->
      <!--        <template v-else>-->
      <!--          <view class="checked-label" @click="addLabel">-->
      <!--            <view>标签</view>-->
      <!--            <view class="label-content">-->
      <!--              <text class="labelName">{{ info.lableName }}</text>-->
      <!--            </view>-->
      <!--            <uni-icons-->
      <!--              class="icon"-->
      <!--              type="arrowright"-->
      <!--              size="18"-->
      <!--              color="#666"-->
      <!--            ></uni-icons>-->
      <!--          </view>-->
      <!--        </template>-->
      <!--      </view>-->

      <view class="defauleSet">
        <u-checkbox-group @change="switch1Change" style="transform: scale(0.8)">
          <u-checkbox
            v-model="switchVal"
            active-color="#836AFF"
            shape="circle"
          ></u-checkbox>
        </u-checkbox-group>
        <view>设置默认</view>
        <text> 每次下单会默认推荐使用该地址</text>

        <!--        <switch-->
        <!--          :checked="switchVal"-->
        <!--          @change="switch1Change"-->
        <!--          style="transform: scale(0.6)"-->
        <!--        />-->
      </view>
    </view>

    <!-- 选择标签 -->
    <uni-popup ref="popup" type="bottom">
      <view class="addLabelPopup">
        <view class="select-lists">
          <view
            class="customize-list list"
            @click="proupSelectLabel('customize')"
          >
            <image src="/static/images/customize-label.png" mode=""></image
            >自定义标签
          </view>
          <template v-for="item in label">
            <view class="list" @click="proupSelectLabel(item.lableName)">
              {{ item.lableName }}
            </view>
          </template>
        </view>
        <view class="line"></view>
        <view class="cancel-btn" @click="labelCancel"> 取消 </view>
      </view>
    </uni-popup>

    <!-- 添加自定义标签 -->
    <uni-popup ref="customizePopup" type="bottom">
      <view class="customize-popup">
        <view class="title"> 添加标签 </view>
        <view class="content">
          <textarea
            type="text"
            :value="customizeLabel"
            v-model="customizeLabel"
            focus
            placeholder="请输入自定义标签"
            maxlength="6"
            minlength="2"
          />
          <text class="num">{{ n }}</text>
        </view>
        <!-- 按钮 -->
        <view class="footer">
          <text>2-6个汉字</text>
          <view class="btn-list">
            <view class="btn cancel-btn" @click="customizeLabelCancel">
              取消
            </view>
            <view
              class="btn confirm-btn"
              @click="customizeLabelConfirm(customizeLabel)"
            >
              完成
            </view>
          </view>
        </view>
      </view>
    </uni-popup>

    <view class="commit" @click="saveAddress">
      <button>保存</button>
    </view>

    <!-- 弹窗 -->
    <uni-popup type="bottom" ref="cityPopup">
      <view class="city_sele">
        <view class="sele_input">
          <input
            confirm-type="search"
            type="text"
            placeholder="搜索地点"
            @confirm="searchCity"
          />
        </view>
        <!-- 列表 -->
        <view class="sele_city_list">
          <view
            class="city_list_item"
            v-for="item in city_list"
            :key="item.id"
            @click="seleCityItem(item)"
          >
            <view class="item_name">{{ item.title }}</view>
            <view class="item_des">{{ item.address }}</view>
          </view>
          <!-- 翻页 -->
          <view
            class="city_list_more"
            @click="getCityNext"
            v-show="count > city_list.length"
            >加载更多</view
          >
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { Toast } from "@/common/js/pay.js";
import Region from "@/components/region/region_old.vue";
const mapKey = require("@/common/request/config.js").mapKey;

import { findAllLable, dicdeliveryaddressAdd } from "@/api/address";
export default {
  components: {
    Region,
  },
  data() {
    return {
      switchVal: false,
      label: [],
      isEdit: false,
      ddaId: "",
      info: {
        provinceName: "",
        cityName: "",
        areaName: "",
        deliveryName: "",
        telNo: "",
        addressArea: "",
        addressDetail: "",
        isDefault: "",
        lableName: "",
        lableImg: "",
        jumpStatus: "",
        latitude: "",
        longitude: "",
      },
      pageGo: "",
      businessId: "",
      // 自定义标签
      customizeLabel: "",
      action: "", // 从那个页面进入
      // 是否可定位
      isLocation: true,
      // 要搜索的城市
      city: "",
      // 搜索列表
      city_list: [],
      // 搜索总数量
      count: 0,
      page_index: 1,
      keyword: "",
      locate: null,
    };
  },
  computed: {
    // 剩余可输入字符
    n() {
      return 6 - this.customizeLabel.length;
    },
  },
  onLoad(options) {
    this.businessId = options.businessId;
    if (options.action) {
      this.action = options.action;
    }
    this.findAllLable();
  },
  onShow() {
    let add = uni.getStorageSync("selectAddress");
    this.locate = null;
    if (add) {
      this.info.addressArea = add.address;
      this.info.addressDetail = add.name;
      this.info.provinceName = add.item?.ad_info?.province;
      this.info.cityName = add.item?.ad_info?.city;
      this.info.areaName = add.item?.ad_info?.district;
      this.locate = add.item.ad_info;
      // 清空省市区
      // this.info.areaName = this.info.provinceName = this.info.cityName = "";
      if (add.latitude) {
        this.info.latitude = add.latitude.lat;
        this.info.longitude = add.latitude.lng;
      }
    }
  },
  methods: {
    // 选择指定地址
    seleCityItem(item) {
      this.info.addressArea = item.address;
      this.info.addressDetail = item.title;
      this.info.latitude = item.location.lat;
      this.info.longitude = item.location.lng;
      this.$refs.cityPopup.close();
      this.city_list = false;
      this.keyword = "";
    },
    // 请求下一页
    getCityNext() {
      if (this.count > this.city_list.length) {
        this.page_index += 1;
        this.searchCity({ detail: { value: this.keyword } });
      }
    },
    // 根据关键词搜索地点
    searchCity(e) {
      let keyword = e.detail.value;
      this.keyword = keyword;
      let that = this;
      that
        .$jsonp("https://apis.map.qq.com/ws/place/v1/suggestion", {
          page_index: that.page_index,
          page_size: 20,
          region: that.city,
          keyword,
          policy: 1,
          key: mapKey,
          output: "jsonp",
        })
        .then((res) => {
          that.count = res.count;
          if (that.page_index == 1) {
            that.city_list = res.data;
          } else {
            that.city_list = [...that.city_list, ...res.data];
          }
        });
    },
    // 查询标签
    async findAllLable() {
      let res = await findAllLable();
      let data = res.data;
      let arr = [];
      for (let i = 0; i < data.length; i++) {
        arr.push({
          lableName: data[i].lableName,
          lableImg: data[i].lableImg,
        });
      }
      this.label = arr;
    },
    // 设置默认
    switch1Change(e) {
      // this.switchVal = e.value;
    },
    // 添加自定义标签
    addLabel() {
      this.$refs.popup.open();
    },
    // 选择地址标签
    selectLabel(evt) {
      this.info.lableName = evt;
    },
    // 取消选择
    labelCancel() {
      this.$refs.popup.close();
    },
    // 弹框选择标签
    proupSelectLabel(evt) {
      if (evt == "customize") {
        this.$refs.popup.close();
        this.$refs.customizePopup.open();
      } else {
        this.info.lableName = evt;
        this.$refs.popup.close();
      }
    },

    // 自定义标签的取消
    customizeLabelCancel() {
      this.$refs.customizePopup.close();
      this.$refs.popup.open();
    },
    // 自定义标签的确定
    customizeLabelConfirm(evt) {
      if (evt.length < 2 || evt.length > 6) {
        Toast("请输入2-6位的汉字");
        return;
      }
      this.info.lableName = evt;
      this.$refs.customizePopup.close();
    },
    // 城市选择
    cityChange(v) {
      let [province, city, area] = v;

      let obj = {
        provinceName: province.name,
        cityName: city.name,
        areaName: area.name,
      };

      let addressArea = province.name + "" + city.name + "" + area.name;

      this.info = {
        ...this.info,
        ...obj,
        addressArea,
      };
    },
    // 选择地区
    openSelectArea() {
      uni.navigateTo({
        url: "./selectAddress",
      });
    },
    async saveAddress() {
      let obj = this.info;
      if (!obj.deliveryName) {
        Toast("请输入收货人姓名");
        return;
      }
      if (!obj.telNo) {
        Toast("请输入收货人手机号");
        return;
      } else {
        // 暂时不强制校验手机号格式
        var re = /^1[\d]\d{9}$/;
        if (!re.test(obj.telNo)) {
          Toast("请输入正确的手机号");
          return false;
        }
      }
      if (!obj.addressArea) {
        Toast("请选择所在地区");
        return;
      }
      if (!this.info.addressDetail) {
        Toast("请输入详细地址");
        return;
      }
      obj.isDefault = this.switchVal ? "1" : "0";
      obj.userId = uni.getStorageSync("userId");
      obj.lableImg = "";
      uni.removeStorageSync("selectAddress");

      let { addressArea } = obj;

      try {
        uni.showLoading({
          mask: true,
        });

        // 通过省市区
        if (obj.provinceName) {
          let map = await this.$jsonp(
            "https://apis.map.qq.com/ws/geocoder/v1/",
            {
              address: `${addressArea}`,
              key: mapKey,
              output: "jsonp",
            }
          );

          if (map.status) {
            Toast(map.message);
            return;
          }

          let {
            location: { lat, lng },
          } = map.result;

          obj.latitude = lat;
          obj.longitude = lng;
        }

        // 新增
        await dicdeliveryaddressAdd(obj);
        uni.hideLoading();
        Toast("添加成功");
      } catch (error) {
        uni.hideLoading();
        return;
      }

      // 因uni.redirectTo(关闭当前页面,跳转到应用内某个页面),对于H5页面不起作用,因此,都采用uni.navigateBack
      if (this.action == "switchAddress") {
        // 购药切换地址-收货地址列表-新增收货地址
        // 赋值地址  nowAddress.address= 当前新增的收货地址
        let nowobj = uni.getStorageSync("nowAddress");

        if (nowobj) {
          nowobj.address = obj;

          uni.setStorageSync("nowAddress", nowobj);
          uni.setStorageSync("action", "switchAddress");
          uni.navigateBack({
            delta: 1, //想要返回的层级
          });
        } else {
          let nowrObj = {};
          nowrObj.address = obj;
          uni.setStorageSync("nowAddress", nowrObj);
          uni.setStorageSync("action", "switchAddress");
          uni.navigateBack({
            delta: 1, //想要返回的层级
          });
        }
      } else if (this.action == "emptyAddress") {
        let nowobj = uni.getStorageSync("nowAddress");
        if (nowobj) {
          nowobj.address = obj;
          uni.setStorageSync("nowAddress", nowobj);
          uni.setStorageSync("action", "switchAddress");
          uni.navigateBack({
            delta: 1, //想要返回的层级
          });
        } else {
          let nowrObj = {};
          nowrObj.address = obj;
          uni.setStorageSync("nowAddress", nowrObj);
          uni.setStorageSync("action", "switchAddress");
          uni.navigateBack({
            delta: 1, //想要返回的层级
          });
        }
      } else if (this.action == "shop") {
        uni.setStorageSync("shop_address", obj);
        uni.navigateBack({
          delta: 1,
        });
      } else {
        uni.navigateBack({
          delta: 1,
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.addForm {
  background: #ffffff;
  padding: 0 32rpx;

  .formInfo {
    display: flex;
    flex-direction: column;
    padding: 14rpx 0;
    color: #333333;
    font-size: 28rpx;
    border-bottom: 1rpx solid #ebebeb;
    position: relative;

    &.start {
      align-items: flex-start;
    }

    view {
      @include flex(left);
      align-items: center;
      color: #333333;
      font-size: 28rpx;
      line-height: 40rpx;
      flex: none;
      padding-bottom: 10px;
    }

    .tit:before {
      content: "*";
      color: #d43030;
    }

    textarea {
      font-size: 28upx;
      height: 128upx;
    }

    .uni-input {
      flex: 1;
      height: 60rpx;
    }

    .right {
      @include flex;
      height: 60rpx;
      color: #999;
      font-size: 28rpx;
      position: absolute;
      top: 5px;
      right: 0;
    }
  }

  .defauleSet,
  .label {
    display: flex;
    padding: 26rpx 0;
    font-size: 28rpx;
    align-items: center;

    text {
      color: #999999;
      font-size: 24rpx;
      line-height: 34rpx;
      margin-left: 32rpx;
      margin-right: 90rpx;
    }
  }

  .label {
    border-bottom: 1rpx solid #ebebeb;

    view {
      width: 20%;
    }

    text {
      padding: 0 30rpx;
      display: inline-block;
      border-radius: 16rpx;
      border: 1px solid #ebebeb;
      margin: 0;
      margin-right: 20rpx;
    }

    .labelAdd {
      width: 20rpx;
      height: 20rpx;
    }
  }

  // 选中标签的样式
  .checked-label {
    display: flex;
    padding: 26rpx 0;
    font-size: 28rpx;
    align-items: center;
    border-bottom: 1rpx solid #ebebeb;

    view {
      width: 20%;
      flex: none;
    }

    .label-content {
      flex: 1;
      text-align: right;
    }

    .labelName {
      padding: 0 20rpx;
      box-sizing: border-box;
      display: inline-block;
      border-radius: 16rpx;
      margin: 0;
      margin-right: 20rpx;
      color: #fff;
      @include bg_theme;
      font-size: 24rpx;
      line-height: 34rpx;
    }

    .icon {
      width: 40rpx;
    }
  }
}

// 选择标签弹框
.addLabelPopup {
  border-radius: 16rpx 16rpx 0 0;
  overflow: hidden;

  .list {
    width: 100%;
    height: 114rpx;
    line-height: 114rpx;
    background: #ffffff;
    text-align: center;
    font-size: 36rpx;
    font-weight: 400;
    color: #333333;
    border-bottom: 2rpx solid #e5e5e5;
  }

  .list:last-child {
    border-bottom: none;
  }

  .customize-list {
    display: flex;
    justify-content: center;
    align-items: center;
    color: #999999;
  }

  image {
    width: 40rpx;
    height: 40rpx;
    margin-right: 6rpx;
  }

  .line {
    width: 100%;
    height: 16rpx;
    background: #f5f5f5;
  }

  .cancel-btn {
    width: 100%;
    height: 114rpx;
    line-height: 114rpx;
    background: #ffffff;
    text-align: center;
    font-size: 36rpx;
    font-weight: 400;
    color: #333333;
  }
}

.customize-popup {
  padding: 20rpx 32rpx 32rpx 32rpx;
  background: #f5f5f5;
  color: #333333;

  .title {
    font-size: 36rpx;
    line-height: 50rpx;

    text-align: center;
  }

  textarea {
    background-color: #ffffff;
    border-radius: 2rpx;
    width: 100%;
    height: 168rpx;
    padding: 24rpx 32rpx;
    box-sizing: border-box;
    font-size: 28rpx;
    line-height: 40rpx;
    margin-top: 20rpx;
  }

  .content {
    position: relative;

    .num {
      position: absolute;
      right: 0;
      bottom: 0;
      padding: 20upx;
      font-size: 24upx;
      color: $k-info-title;
      line-height: 24upx;
    }
  }

  .footer {
    @include flex(lr);
    margin-top: 20rpx;

    text {
      font-size: 24rpx;
      color: #999999;
    }

    .btn-list {
      display: flex;
    }

    .btn {
      width: 140rpx;
      height: 56rpx;
      border-radius: 38rpx;
      font-size: 28rpx;
      font-weight: 600;
      text-align: center;
      line-height: 56rpx;
    }

    .btn.cancel-btn {
      background: #d1d1d1;
      border: 1px solid #d1d1d1;
      color: #ffffff;
      margin-right: 16rpx;
    }

    .btn.confirm-btn {
      @include bg_theme;
      color: #ffffff;
    }
  }
}

.commit {
  background: #fff;
  width: 100%;
  height: 104rpx;
  position: fixed;
  bottom: 0;
  padding: 0 32upx;
  box-sizing: border-box;
  @include flex;

  button {
    width: 100%;
    height: 84upx;
    font-size: 32upx;
    border-radius: 42upx;
    @include bg_theme;
    color: #fff;
    @include flex;
  }
}

/deep/uni-input {
  font-size: 28rpx;
}

.city_sele {
  height: 70vh;
  width: 100%;
  padding: 30upx;
  background-color: #fff;
  box-sizing: border-box;

  .sele_input {
    width: 100%;
    height: 90upx;
    padding-bottom: 30upx;
    border-radius: 4upx;
    background-color: #fff;
    box-sizing: border-box;

    input {
      width: 100%;
      height: 100%;
      padding-left: 20upx;
      background-color: #f5f5f5;
      box-sizing: border-box;
    }
  }

  .sele_city_list {
    width: 100%;
    height: calc(100% - 90upx);
    overflow-y: scroll;
    box-sizing: border-box;

    .city_list_item {
      padding: 20upx 0;
      border-bottom: 1px solid #f5f5f5;
      box-sizing: border-box;

      .item_name {
        font-size: 28upx;
        color: #333;
      }

      .item_des {
        font-size: 28upx;
        color: #999;
      }
    }
    .city_list_more {
      font-size: 28upx;
      color: #666;
      text-align: center;
      line-height: 80upx;
    }
  }
}
</style>
