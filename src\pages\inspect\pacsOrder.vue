<template>
  <!-- 检查 -->
  <view class="inspect">
    <!-- 详情部分 -->
    <view class="detail">
      <!-- 顶部信息 -->
      <USERINFO :info="detail" />

      <!-- 检查机构 -->
      <INPRCTION v-if="false" />

      <!-- 选择机构 -->
      <SELECT
        :check="checkObj || {}"
        :showRight="!isHaveDpoId"
        @click="toPath('./center/pacsList?id=' + ppiId)"
      />

      <!-- 项目信息 -->
      <PROJECT
        v-if="list.length"
        @help="help"
        @click="
          toPath('./inspectTime?dpoId=' + checkObj.dpoId + '&ppiId=' + ppiId)
        "
        :list="list"
        :isShowTime="!!checkObj.dpoId"
        :time="timeObj.showTime"
      />

      <!-- 选择支付 -->
      <SELEPAY
        v-if="payList.length == 2"
        :list="payList"
        :currAct="payType"
        @change="setPayType"
      />

      <ROW text="支付方式" v-if="payList.length == 1">{{
        payList[0].name
      }}</ROW>

      <!-- 医保类型 -->
      <INSURANCE
        v-if="checkObj"
        :showRight="isSupportMedical == 1"
        :currAct="isSupportMedical == 1 ? support : 0"
        @change="supportChange"
      />
    </view>

    <!-- 底部 -->
    <FOOTER :length="list.length" :cost="detail.cost" @click="getNext">
      提交订单
    </FOOTER>
  </view>
</template>

<script>
// 顶部信息
import USERINFO from './com/userInfo.vue';
// 项目信息
import PROJECT from './com/project.vue';
// 选择机构
import SELECT from './com/check.vue';
// 底部
import FOOTER from './com/footer.vue';
// 检查中心
import INPRCTION from './com/inspection.vue';
// 选择支付
import SELEPAY from './com/selePay.vue';
// 医保类型
import INSURANCE from './com/insurance.vue';
// 行
import ROW from './com/row.vue';

import {
  getProPacsPayInfoByID,
  getPacsListToOrgan,
  getPaceItemPrice,
  getAppointPayInfo,
  queryPacsRegisterPayStatus,
} from '@/api/inspect';

import { wxPay } from '@/common/js/pay';

import { getReceiptWay } from '@/api/order';

import { Toast } from '@/common/js/pay.js';

export default {
  components: {
    USERINFO,
    PROJECT,
    SELECT,
    FOOTER,
    INPRCTION,
    SELEPAY,
    INSURANCE,
    ROW,
  },
  data() {
    return {
      // 检查单id
      ppiId: '',
      // 检查机构id
      dpoId: '',
      // 步骤
      step: 0,
      // 检查项目
      list: [],
      // 详情
      detail: {},
      // 机构
      mechanism: '',
      // 检查机构
      checkObj: '',
      // 预约时间段
      timeObj: {
        showTime: '',
      },
      // 支付类型 1到院 2在线
      payType: -1,
      // 是否可选支付方式
      isSelePay: true,
      // 支持的支付列表
      payList: [],
      // 是否支持医保
      isSupportMedical: '',
      // 选择医保
      support: -1,
      // 是否指定机构
      isHaveDpoId: false,
      // 支付appid
      pays: [],
      callId: '',
    };
  },
  onLoad(opt) {
    this.ppiId = opt.id;
    this.getDetail();
  },
  onShow() {
    // 如果存在机构 并且未指定
    if (this.dpoId && !this.isHaveDpoId) {
      this.getOrgan();
      this.getPrice();
      this.getPayList();
      // 防止二次加载
      this.dpoId = '';
    }
  },
  methods: {
    // 获取详情
    async getDetail() {
      let { data } = await getProPacsPayInfoByID(this.ppiId);
      this.list = data.pacsListDVO;
      this.detail = data;
      if (data.isHaveDpoId == 1) {
        this.isHaveDpoId = true;
        this.dpoId = data.dpoId;

        this.getPrice();
        this.getPayList();
        this.getOrgan();
      }
    },
    // 获取指定机构价位
    async getPrice() {
      let arr = [];
      this.list.forEach((v) => {
        arr.push(v.pliId);
      });
      let obj = {
        dpoId: this.dpoId,
        itemList: arr,
      };
      let { data } = await getPaceItemPrice(obj);
      if (!data.length) return;
      let cost = 0;
      data.forEach((v) => {
        cost += v.price * 100;
        this.detail.pacsListDVO.forEach((k) => {
          if (v.pliId == k.pliId) {
            if (v.price) k.cost = v.price;
          }
        });
      });
      if (cost) this.detail.cost = cost / 100;
      this.list = this.detail.pacsListDVO;
    },
    // 选择医保
    supportChange(n) {
      this.support = n;
    },
    // 选择支付方式
    setPayType(n) {
      this.payType = n;
      if (n == 2) {
        this.support = 0;
        this.isSupportMedical = false;
      } else if (this.checkObj.isSupportMedical == 1) {
        this.isSupportMedical = true;
      }
    },
    // 获取机构信息
    async getOrgan() {
      this.timeObj = {};
      let { data } = await getPacsListToOrgan({
        dpoId: this.dpoId,
        ppiId: this.ppiId,
      });
      // 是否支持医保 支付类型（逗号分隔）
      let { isSupportMedical, paymentType } = data[0];
      this.isSupportMedical = isSupportMedical != 1 ? false : true;
      // 如果不支持医保
      if (!isSupportMedical) {
        // 默认 0 自费
        this.support = 0;
      }
      // 支持的支付方式
      paymentType = paymentType.split(',');
      // 如果两种支付方式
      if (paymentType.length == 1) {
        this.payType = Number(paymentType[0]);
        this.isSelePay = false;
        if (this.payType == 1) {
          this.payList = [{ name: '到院支付', id: 1 }];
        } else {
          this.payList = [{ name: '在线支付', id: 2 }];
          this.support = 0;
        }
      } else {
        // 默认在线支付
        this.payType = 2;
        this.isSelePay = true;
        this.isSupportMedical = false;
        this.support = 0;
        paymentType.forEach((v, i) => {
          let obj = {
            id: Number(v),
          };
          if (v == 1) {
            obj.name = '到院支付';
          } else {
            obj.name = '在线支付';
          }
          paymentType[i] = obj;
        });
        this.payList = paymentType;
      }

      // 检查机构信息
      this.checkObj = data[0];
    },
    // 获取支付方callId
    async getPayList() {
      let { data } = await getReceiptWay({
        subjectId: this.dpoId,
      });
      this.pays = data;
    },
    // 跳转页面
    toPath(path) {
      uni.navigateTo({
        url: path,
      });
    },
    // 下一步
    async getNext() {
      // 检验机构id
      let { dpoId } = this.checkObj;

      if (!dpoId) {
        Toast('请选择检查机构');
        return;
      }

      // 预约相关
      let { appointDate, appointEndTime, appointStartTime } = this.timeObj;

      if (!appointDate) {
        Toast('请选择预约时间');
        return;
      }

      // 是否医保
      let isMedicare = this.support;

      if (isMedicare == -1) {
        Toast('请选择医保类型');
        return;
      }

      // 检查单id
      let ppiId = this.ppiId;

      // 用户openid
      let openid = uni.getStorageSync('wxInfo').openId;

      // 支付金额
      let payCost = this.detail.cost;

      // 支付方式 0 无需支付 1 微信 2 支付宝 3 业务平台 4 线下支付
      let payType = 4;

      // 支付类型 1 到院（线下） 2 线上支付
      let paymentType = this.payType;

      // 项目列表
      let projectDTOList = [];

      this.list.forEach((v) => {
        projectDTOList.push({
          cost: v.cost,
          ppdId: v.ppdId,
        });
      });

      payType = await this.selePay();

      let callId = this.callId;

      // 组合参数
      let param = {
        dpoId,
        appointDate,
        appointEndTime,
        appointStartTime,
        isMedicare,
        callId,
        ppiId,
        openid,
        payCost,
        payType,
        paymentType,
        projectDTOList,
      };

      uni.showLoading({
        mask: true,
      });

      try {
        let { data } = await getAppointPayInfo(param);
        uni.hideLoading();

        // 线下支付
        if (payType == 4) {
          this.toSuc();
          return;
        }

        // 无需支付
        if (payType == 0 && data.success == 1) {
          this.toSuc();
          return;
        }

        // 微信支付
        if (payType == 1) {
          this.wxPay(data);
          return;
        }

        // 支付宝支付
        if (payType == 2) {
          uni.navigateTo({
            url:
              '/pages/pay/pay?price=' +
              payCost +
              '&ppiId=' +
              this.ppiId +
              '&url=' +
              btoa(data.url),
          });
        }
      } catch (error) {
        uni.hideLoading();
      }
    },
    // 微信支付
    async wxPay(info) {
      try {
        await wxPay(info);
        this.getStatus();
      } catch (error) {
        Toast('取消支付');
        uni.redirectTo({
          url: './pay/pacs?id=' + this.ppiId,
        });
      }
    },
    // 选择支付方式
    async selePay() {
      let that = this;
      return new Promise((resolve, reject) => {
        // 如果到院支付
        if (this.payType == 1) {
          return resolve(4);
        }
        // 如果无需支付
        if (this.detail.cost == 0) {
          return resolve(0);
        }
        // 如果不可选支付
        if (!this.pays.length) {
          Toast('当前没有配置支付方式');
          return reject();
        }
        // 如果在线支付
        uni.showActionSheet({
          itemList: ['微信支付', '支付宝支付'],
          success(res) {
            let index = res.tapIndex + 1;
            let item;
            item = that.pays.filter((it) => it.receiptType == index)[0];
            if (!item) {
              Toast('暂不支持该支付方式');
              return reject();
            }
            that.callId = item.appid;
            resolve(index);
          },
          fail(err) {
            reject(err);
          },
        });
      });
    },
    // 查询支付状态
    async getStatus() {
      let {
        data: { status },
      } = await queryPacsRegisterPayStatus(this.ppiId);
      // 支付成功
      if (status == 2) {
        this.toSuc();
        return;
      }
      if (status == 1) {
        if (num > 0) {
          setTimeout(this.getStatus(), 2000);
          num--;
        }
      }
    },
    // 跳转成功
    toSuc() {
      uni.redirectTo({
        url: './result/pacsResult?id=' + this.ppiId,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.inspect {
  padding-bottom: 108rpx;

  .detail {
    padding: 32rpx;
  }
}
</style>
