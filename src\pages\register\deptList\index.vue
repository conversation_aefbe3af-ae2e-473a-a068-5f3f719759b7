<template>
  <view>
    <view class="page-container">
      <view class="search-container">
        <uni-search-bar
          placeholder="请输入您想要搜索的内容"
          cancelButton="none"
          @confirm="search"
          @input="changeInput"
        ></uni-search-bar>
      </view>
      <template v-if="deptList.length">
        <scroll-view
          scroll-y="true"
          class="section_doc"
          @scrolltolower="getMore"
        >
          <!-- 科室列表 -->
          <template v-for="item in deptList">
            <view class="section_doc_list" @click="goDocList(item.deptId)">
              <view class="left">
                <text>{{ item.deptName }}</text>
                <!-- 标志 -->
                <text class="tag" v-if="item.isInternet == 1">互联网</text>
              </view>

              <uni-icons type="arrowright" size="22"></uni-icons>
            </view>
          </template>
          <!-- 加载更多 -->
          <view v-if="isShowMore">
            <uni-load-more :status="status"></uni-load-more>
          </view>
        </scroll-view>
      </template>
      <view class="empty_list" v-else>
        <image src="/static/images/index/office_empty.png" />
        <view> 暂无科室 </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getAllDept } from '@/api/base.js';
export default {
  data() {
    return {
      deptList: [],
      listQuery: {
        deptName: '',
        page: 1,
        limit: 120,
      },
      total: 0,
      isShowMore: false,
      status: 'loading',
    };
  },
  onShow() {},
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    this.getAllDept();
  },
  onPullDownRefresh() {
    this.deptList = [];
    this.listQuery.deptName = '';
    this.listQuery.page = 1;
    this.getAllDept();
  },
  methods: {
    getMore() {
      this.isShowMore = true;
      // 共几页
      let num = Math.ceil(this.total / this.listQuery.limit);
      if (this.listQuery.page < num) {
        this.listQuery.page += 1;
        this.getAllDept();
        this.isShowMore = false;
      } else {
        this.status = 'noMore';
      }
    },

    goBack() {
      uni.navigateBack({
        delta: 1,
      });
    },
    async getAllDept() {
      uni.stopPullDownRefresh();
      let res = await getAllDept(this.listQuery);
      this.total = res.data.total;
      this.deptList = this.deptList.concat(res.data.rows);
    },
    search(e) {
      this.deptList = [];
      this.listQuery = {
        deptName: e.value,
        page: 1,
        limit: 20,
      };
      this.getAllDept();
    },
    changeInput(e) {
      // 清空输入框内容时查询
      if (e.value == '') {
        this.deptList = [];
        this.listQuery = {
          deptName: '',
          page: 1,
          limit: 20,
        };
        this.getAllDept();
      }
    },
    goDocList(e) {
      let deptId = e;
      uni.navigateTo({
        url: '/pages/register/docList/index?deptId=' + deptId,
      });
    },
  },
};
</script>

<style scoped lang="scss">
.page-container {
  background-color: #fff;
  padding-bottom: 30rpx;
}

.search-container {
  position: sticky;
  top: 0;
  z-index: 3;
}

/* 搜索框样式 */
/deep/.uni-searchbar {
  padding: 0;
  height: 88rpx;
  border-bottom: 2rpx solid #ebebeb;
}

/deep/.uni-searchbar__box {
  height: 88rpx;
  background: #ffffff !important;
  border-radius: 0px !important;
  border: none;
}

/* 科室列表 */
.section_doc {
  padding: 0 32rpx;
  height: calc(100% - 90rpx);
  box-sizing: border-box;
}

.section_doc_list {
  @include flex(lr);
  color: $k-title;
  font-size: 30rpx;
  height: 88rpx;
  background: #e6ebee;
  border-radius: 8rpx;
  margin-top: 16rpx;
  padding: 0 32rpx;
  font-weight: 400;

  .left {
    @include flex;

    .tag {
      width: 104rpx;
      height: 36rpx;
      @include flex;
      @include border_theme;
      border-radius: 4rpx;
      font-size: 24rpx;
      background: #fff;
      @include font_theme;
      margin-left: 12rpx;
    }
  }
}

/* 列表为空提示 */
.empty_list {
  @include flex(center);
  flex-direction: column;
  font-size: 26rpx;
  font-weight: 400;
  color: rgba(158, 163, 173, 1);
  position: absolute;
  top: 40%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.empty_list image {
  margin-bottom: 40rpx;
  width: 386rpx;
  height: 324rpx;
}
</style>
