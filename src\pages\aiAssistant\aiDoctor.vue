<template>
  <view class="ai-doctor-container">
    <!-- 顶部区域：头像和描述 -->
    <view class="doctor-info">
      <view class="avatar-container">
        <image
          class="avatar"
          src="/static/ai/ai-logo.png"
          mode="aspectFill"
        ></image>
        <text class="doctor-name">{{ docInfoHtml.name }}</text>
      </view>

      <view class="tag">
        <image
          class="tag-icon"
          src="/static/ai/shanchang.png"
          mode="aspectFit"
        ></image>
        <text class="tag-text">擅长</text>
      </view>

      <view class="description">
        {{ docInfoHtml.description }}
      </view>
    </view>

    <!-- 底部功能区 -->
    <view class="function-area">
      <view class="function-buttons">
        <view class="function-button" @click="navigateToConsult">
          <image
            class="button-icon"
            src="/static/ai/ai-1.png"
            mode="aspectFit"
          ></image>
          <text class="button-text">健康咨询</text>
        </view>
        <view class="function-button" @click="navigateToQA">
          <image
            class="button-icon"
            src="/static/ai/ai-2.png"
            mode="aspectFit"
          ></image>
          <text class="button-text">智能问诊</text>
        </view>
        <view class="function-button" @click="navigateToGuide">
          <image
            class="button-icon"
            src="/static/ai/ai-3.png"
            mode="aspectFit"
          ></image>
          <text class="button-text">用药指导</text>
        </view>
      </view>

      <!-- 底部输入区 -->
      <view class="input-area">
        <view class="input-left">
          <image
            class="voice-icon"
            src="/static/ai/keyboard.png"
            mode="aspectFit"
            @touchstart="startRecording"
            @touchend="stopRecording"
          ></image>
        </view>
        <view class="input-center" @click="goAI">
          <text class="input-placeholder">按住 说话</text>
        </view>
        <view class="input-right">
          <image
            class="more-icon"
            src="/static/ai/ai-4.png"
            mode="aspectFit"
          ></image>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      isRecording: false,
      docType: 'yingyangshi',
      docInfo: {
        yingyangshi: {
          avatar: '/static/ai/ai-logo.png',
          name: '营养师',
          tag: '擅长',
          description:
            '收集基本信息和身体状况，根据用户动态变化，快速计算每日所需能量，同时会有每日打卡提醒，上传每餐食物图片，利用图像识别插件识别食物，推荐营养食谱以及上传每日体重，依托大模型计算 BMI 、缺失能量等指标，分析用户健康状况与营养需求，实时调整健康管理方案，并给予情感关怀。',
        },
        zhongliu: {
          avatar: '/static/ai/ai-logo.png',
          name: '脑瘤医生',
          tag: '擅长',
          description:
            '分析病理报告、基因检测（如突变检查）、影像（PET-CT/MRI）数据、辅助制定个性化治疗方案、临床试验/药物治疗的不良反应（如自身免疫、皮疹）、提供肿瘤干预建议、整合多学科数据，制定认知诊断策略的全周期计划，包括结直肠癌与多学科支持，解决疑难病症的临床研究，快速匹配患者特征进行治疗方法，提升诊疗效率与满意度。',
        },
        mianyi: {
          avatar: '/static/ai/ai-logo.png',
          name: '免疫医生',
          tag: '擅长',
          description:
            '结合病情活动度、靶点特征生成治疗建议（如生物制剂、免疫抑制剂），优化用药剂量与疗程，预测器官损伤、感染风险，提示复查指标（如炎症因子、免疫球蛋白）及疗效评估时机，解答日常护理问题（如避光防晒、关节保护），科普免疫疾病诱因与缓解期注意事项，动态追踪免疫指标变化，辅助医生调整方案，提升慢性病全程管理效率。',
        },
        zhengyuanAI: {
          avatar: '/static/ai/ai-logo.png',
          name: '正元AI医生',
          tag: '擅长',
          description:
            '基于患者肿瘤类型、放化疗方案及结合体质（如脾肾两虚程度）调整用药剂量，联合其他中成药或西药时优化搭配，提升放化疗耐受性，通过症状改善数据（如体力评分、血红蛋白水平）动态评估药效，提示是否需增减疗程或更换方案，解答用药周期、与其他补益类药物的区别等问题，辅助医生制定长期调理计划。',
        },
        ganbing: {
          avatar: '/static/ai/ai-logo.png',
          name: '肝病医生',
          tag: '擅长',
          description:
            '基于病情，分析肝功能、影像（超声 / CT 等）数据，辅助鉴别病毒性肝炎、脂肪肝、肝硬化等，解答肝病日常管理问题（如饮酒影响、用药禁忌、运动指导等），提供科普知识，动态跟踪病情变化，辅助医生制定长期诊疗计划，提升肝病管理效率。',
        },
        shenbing: {
          avatar: '/static/ai/ai-logo.png',
          name: '肾病医生',
          tag: '擅长',
          description:
            '结合病因，分析尿常规、肾功能（血肌酐 / 尿素氮）、肾活检等数据，辅助鉴别肾炎、肾病综合征、肾衰竭等，分期生成治疗建议（如免疫抑制、透析方案）、饮食管理（低盐 / 优质蛋白），解答肾病日常疑问（如水肿护理、药物副作用），科普疾病进展与预防知识，动态跟踪病情趋势，辅助医生调整治疗方案，优化慢性肾病长期管理流程。',
        },
      },
    }
  },
  computed: {
    docInfoHtml() {
      return this.docInfo[this.docType]
    },
  },
  created() {
    console.log(this.$route.query.type)
    this.docType = this.$route.query.type
  },
  methods: {
    startRecording() {
      this.isRecording = true
      // 这里可以添加录音相关逻辑
      // uni.showToast({
      //   title: '开始录音',
      //   icon: 'none',
      // })
    },
    stopRecording() {
      this.isRecording = false
      // 这里可以添加停止录音相关逻辑
      // uni.showToast({
      //   title: '录音结束',
      //   icon: 'none',
      // })
    },
    goAI() {
      if (this.docType == 'yingyangshi') {
        uni.navigateTo({
          url: '/pages/qcAi/chat',
        })
      }
    },
    navigateToConsult() {
      if (this.docType == 'yingyangshi') {
        uni.navigateTo({
          url: '/pages/qcAi/chat',
        })
      }
    },
    navigateToQA() {
      uni.showToast({
        title: '智能问诊功能',
        icon: 'none',
      })
    },
    navigateToGuide() {
      uni.showToast({
        title: '用药指导功能',
        icon: 'none',
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.ai-doctor-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: linear-gradient(0deg, #c82090, #6a14d1);
}

// 顶部医生信息区域
.doctor-info {
  padding: 80px 20px;
  display: flex;
  flex-direction: column;
}

.avatar-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 15px;
  position: relative;
}

.avatar {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background-color: #4cff00; // 绿色背景
  border: 2px dashed rgba(255, 255, 255, 0.5); // 添加虚线边框
  padding: 2px; // 内边距使虚线边框有一定间距
}

.doctor-name {
  margin-top: 10px;
  font-size: 39rpx;
  font-weight: bold;
  color: #f0daff;
}

.tag {
  display: flex;
  align-items: center;
  padding: 4px 12px;
  color: #ffffff;
  font-size: 39rpx;
  font-weight: bold;
  margin-bottom: 10px;
  .tag-icon {
    width: 40rpx;
    height: 40rpx;
    margin-right: 10rpx;
  }
}

.description {
  color: #ffffff;
  font-size: 28rpx;
  line-height: 2;
  text-align: justify;
  padding: 0 10px;
}

// 底部功能区域
.function-area {
  margin-top: auto;
  background-color: #ffffff;
  padding: 20px 20px 30px;
}

.function-buttons {
  display: flex;
  justify-content: space-around;
  margin-bottom: 25px;
}

.function-button {
  display: flex;
  align-items: center;
  border: 1px solid #e0e0e0;
  border-radius: 30rpx;
  padding: 10rpx 30rpx;
}

.button-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 10rpx;
}

.button-text {
  font-size: 14px;
  color: #000000;
}

// 底部输入区域
.input-area {
  display: flex;
  align-items: center;
  height: 50px;
}

.input-left,
.input-right {
  width: 50px;
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.input-center {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f7f7f7;
  height: 61rpx;
  border-radius: 8rpx;
}

.voice-icon,
.more-icon {
  width: 24px;
  height: 24px;
}

.input-placeholder {
  color: #000000;
  font-size: 28rpx;
}
</style>
