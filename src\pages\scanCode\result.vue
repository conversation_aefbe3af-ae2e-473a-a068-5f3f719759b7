<template>
  <div class="result">
    <view class="sele_warp">
      <!-- 患者信息 -->
      <view class="user_info" @click="toPatient">
        <!-- 头像 -->
        <img
          v-img="patientInfo.patientImg"
          v-if="patientInfo.patientImg"
          data-src="/static/images/docHead.png"
          alt=""
          class="user_head"
        />
        <image
          src="/static/images/docHead.png"
          v-else
          alt=""
          class="user_head"
        />
        <!-- 信息 -->
        <view class="user_desc">
          <view class="user_name">{{ patientInfo.patientName }}</view>
          <view class="user_other" v-if="patientInfo.sexCode">
            <image
              v-show="patientInfo.sexCode == 1"
              src="/static/shop/nan.png"
            ></image>
            <image
              v-show="patientInfo.sexCode == 2"
              src="/static/shop/nv.png"
            ></image>
            <text>{{ patientInfo.age }}岁</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 药店 药品列表 -->
    <view class="pharmacy_list" v-for="(p, pn) in list" :key="pn">
      <view class="pharmacy">
        <!-- 药店名称 -->
        <text
          >{{ p.drugstoreName
          }}{{ p.isProprietary == 1 ? '（自营）' : '' }}</text
        >
      </view>

      <!-- 药品列表 -->
      <view class="durg_list">
        <!-- 单个药品 -->
        <view
          class="durg_item"
          v-for="(d, dn) in p.drugShoppingOnlineOrderList"
          :key="dn"
        >
          <img
            v-if="d.drugImg"
            v-img="d.drugImg"
            :data-src="errUrl"
            class="left"
          />
          <image v-else class="left" src="/static/shop/drug.png"></image>
          <!-- 内容 -->
          <view class="right">
            <!-- 药品名称 -->
            <view class="drug_name">{{ d.drugName }}</view>
            <!-- 规格 -->
            <view class="drug_info">规格: {{ d.gg }}</view>
            <!-- 优惠 -->
            <view class="drug_red" v-if="d.activeName"
              >单品{{ d.activeName }}</view
            >
            <!-- 价位数量 -->
            <view class="right_menu">
              <view class="price"
                >￥{{ d.drugRealMoney || d.price | toFixed }}
                <text class="del" v-if="d.drugShouldMoney != d.drugRealMoney"
                  >￥{{ d.drugShouldMoney | toFixed }}</text
                >
              </view>
              <text class="num">x{{ d.quan }}</text>
            </view>
          </view>
        </view>
      </view>
      <!-- 合计 -->
      <view class="count">
        <!-- <text>共{{ p.drugShoppingOnlineOrderList.length }}件商品</text> -->
        <view>
          合计: <text class="red">￥{{ p.merchantsTotalMoney | toFixed }}</text>
        </view>
        <view>
          优惠:
          <text class="red"
            >￥{{ p.merchantsPreferentialAmount | toFixed }}</text
          >
        </view>
        <view>
          实付:<text class="red">￥{{ p.merchantsOrderMoney | toFixed }}</text>
        </view>
      </view>
    </view>

    <!-- 自提 -->
    <DURGSTORE :detail="drugStore" v-if="deliveryType == 2" />

    <!-- 物流 -->
    <view class="logist" v-if="deliveryType == 1">
      <view class="title">
        <text>物流方式</text>
        <text>统一配送</text>
      </view>

      <view class="item">
        <text>{{ logisticsName }}</text>
        <text class="red"> ¥{{ logisticsCost | toFixed }} </text>
      </view>
    </view>

    <!-- 收货地址 -->
    <view class="address" v-if="deliveryType == 1">
      <view class="label"> {{ address.deliveryAddressDetail }} </view>

      <view class="user">
        <text>{{ address.deliveryName }}</text>
        {{ phone(address.deliveryTelNo) }}
      </view>
    </view>

    <view class="footer">
      <view class="left">
        合计：<text>¥{{ orderMoney | toFixed }}</text>
      </view>
      <!-- 按钮 -->
      <view class="right">
        <view class="but pay" @click="showPay">好友付</view>
        <view class="but" @click="pay">去支付</view>
      </view>
    </view>

    <!-- 引导 -->
    <Tips v-show="showTip" @click="showTip = false" />
  </div>
</template>

<script>
import {
  orderDetailNew,
  getReceiptWay,
  scanCodeOrderUpdatePayType,
} from '@/api/order.js';
import { queryMallOrderStatus } from '@/api/shop.js';
import { findPatientInfoByPatientId, findDrugStoreDetail } from '@/api/base';
import myJsTools from '@/common/js/myJsTools';
// 药店
import DURGSTORE from '@/pages/prescription/com/drugStore.vue';
import { wxPay, selePay } from '@/common/js/pay';
import payment from '@/mixins/wx';
let timer;
export default {
  name: 'Result',
  mixins: [payment],
  components: {
    DURGSTORE,
  },
  data() {
    return {
      orderNo: '',
      patientInfo: {},
      drugStore: {},
      list: [],
      orderMoney: 0,
      // 药品金额
      drugPrice: 0,
      address: {},
      logisticsCost: 0,
      // 物流名称
      logisticsName: '',
      deliveryType: '',
      payList: [],
      num: 3,
      // 优惠金额
      orderDisMoney: 0,
      errUrl: require('../../static/shop/drug.png'),
    };
  },
  onLoad(v) {
    this.orderNo = v.orderNo;
    this.getDetail();
  },
  methods: {
    async getDetail() {
      let {
        data: {
          patientId,
          drugstoreId,
          deliveryType,
          logisticsCost,
          deliveryAddressDetail,
          deliveryName,
          deliveryTelNo,
          orderDrugStoreListNewVOList: list,
          orderRealMoney,
          drugMoney,
          orderDisMoney,
        },
      } = await orderDetailNew({ orderNo: this.orderNo, source: 3 });
      this.orderMoney = orderRealMoney;
      this.logisticsCost = logisticsCost;
      // 药品金额
      this.drugPrice = drugMoney;
      // 优惠金额
      this.orderDisMoney = orderDisMoney;
      list.forEach((v) => {
        v.drugShoppingOnlineOrderList.forEach((k) => {
          k.quan = Number(k.quan);
        });
      });
      this.list = list;
      // 获取就诊人信息
      this.getPatientInfo(patientId);
      // 自提
      if (deliveryType == 2) {
        // 获取药店
        this.getDrugStore(drugstoreId);
        this.getPayInfo(drugstoreId);
      } else {
        this.logisticsName =
          list[0].logisticsCustomName || list[0].logisticsName || '普通物流';
        this.address = {
          deliveryAddressDetail,
          deliveryName,
          deliveryTelNo,
        };
        // 查询支付方式
        this.getPayInfo(list[0].drugstoreId);
      }
      this.deliveryType = deliveryType;
    },
    // 获取就诊人
    async getPatientInfo(id) {
      let { data } = await findPatientInfoByPatientId(id);
      this.patientInfo = data;
    },
    // 获取药店信息
    async getDrugStore(drugstoreId) {
      let { data } = await findDrugStoreDetail({
        drugstoreId,
      });
      this.drugStore = data;
    },
    // 好友付
    async showPay() {
      this.setShare(this.orderMoney, this.orderNo, 3);
      this.showTip = true;
    },
    // 支付
    async pay() {
      if (!this.payList.length) {
        Toast('未配置支付方式');
        return;
      }

      let { index, item } = await selePay(this);

      try {
        uni.showLoading({
          mask: true,
        });

        let { data } = await scanCodeOrderUpdatePayType({
          openid: uni.getStorageSync('wxInfo').openId,
          callId: item.appid,
          orderNo: this.orderNo,
          payType: index,
        });

        // 微信
        if (index == 1) {
          this.toWx(data);
        } else {
          uni.navigateTo({
            url:
              '/pages/pay/pay?price=' +
              this.orderMoney +
              '&orderNo=' +
              this.orderNo +
              '&url=' +
              btoa(data.url),
          });
        }
        uni.hideLoading();
      } catch (err) {
        uni.hideLoading();
      }
    },
    // 获取支付方信息
    async getPayInfo(subjectId) {
      let res = await getReceiptWay({
        subjectId,
      });
      this.payList = res.data;
    },
    // 微信支付
    async toWx(info) {
      await wxPay(info);
      // 请求支付状态
      this.getStatus(this.orderNo);
    },
    // 查询支付状态
    async getStatus(orderNo) {
      // 根据订单号查询 目前缺少订单号
      let res = await queryMallOrderStatus(orderNo);
      this.num--;

      if (res.data.orderStatus == 2) {
        clearInterval(timer);
        timer = null;
        this.num = 3;
        uni.reLaunch({
          url: './success',
        });
      }

      // 查询过三次
      if (this.num <= 0) {
        clearInterval(timer);
        // 应该跳转到失败
        if (res.data.orderStatus == 1) {
          uni.navigateTo({
            url: './error',
          });
        }
      }
    },
    phone(n) {
      return myJsTools.phone(n);
    },
  },
};
</script>

<style lang="scss" scoped>
page {
  background-color: #f5f5f5;
}
.result {
  padding: 24rpx 32rpx 120rpx;

  * {
    box-sizing: border-box;
  }

  .sele_warp {
    width: 100%;
    background-color: #fff;
    border-radius: 8rpx;
    font-size: 28rpx;
    padding: 0 24rpx;
    margin-bottom: 24rpx;

    .sele_info {
      @include flex;
      align-items: flex-start;
      padding-bottom: 20rpx;

      text {
        flex: none;
        width: 110rpx;

        &.red {
          color: red;
          flex: 1;
        }
      }

      .info_text {
        flex: 1;
        color: #999;
      }
    }

    .logistics {
      width: 100%;
      padding: 24rpx 0;

      .item {
        height: 76rpx;
        padding: 0 24rpx;
        @include flex(lr);
        background-color: #f5f5f5;
        border-radius: 8rpx;

        &:last-child {
          margin-top: 24rpx;
        }

        .name {
          font-size: 28rpx;
        }

        .right {
          @include flex;

          text {
            font-size: 28rpx;
            color: red;
            padding-right: 30rpx;
          }

          image {
            width: 32rpx;
            height: 32rpx;
          }
        }
      }
    }

    .user_info {
      @include flex(lr);
      padding: 18rpx 0;

      .user_head {
        width: 128rpx;
        height: 128rpx;
        border-radius: 8rpx;
        flex: none;
      }

      .user_desc {
        flex: 1;
        padding-left: 22rpx;

        .user_name {
          font-size: 32rpx;
          font-weight: bold;
        }

        .user_other {
          @include flex(left);
          padding-top: 10rpx;

          image {
            width: 32rpx;
            height: 32rpx;
          }

          text {
            padding-left: 10rpx;
            color: #666;
            font-size: 24rpx;
          }
        }
      }
    }
  }

  .pharmacy_list {
    background-color: #fff;
    padding: 0 24rpx;

    &:only-child {
      border: none;
    }

    .pharmacy {
      @include flex(lr);
      font-size: 28rpx;
      font-weight: bold;
      height: 88rpx;
      border-bottom: 1px solid #ebebeb;

      text::nth-child(2) {
        font-weight: normal;
      }

      text {
        color: #333;

        &.wait {
          color: #ff5050;
        }

        &.close {
          color: #999;
        }

        &.done {
          @include font_theme;
        }
      }
    }

    .durg_list {
      .durg_item {
        @include flex;
        padding: 24rpx 0;

        .left {
          width: 128rpx;
          height: 128rpx;
          margin-right: 24rpx;
          border: 1px solid #eee;
          border-radius: 8rpx;
          flex: none;
        }

        .right {
          flex: 1;
          min-height: 128rpx;
          @include flex(lr);
          flex-direction: column;
          align-items: stretch;
          border-bottom: 1px solid #eee;

          .drug_name {
            flex: 1;
            font-size: 28rpx;
            font-weight: bold;
          }

          .drug_info {
            flex: 1;
            font-size: 24rpx;
            color: #999;
          }

          .drug_red {
            flex: 1;
            font-size: 24rpx;
            color: red;
          }

          .right_menu {
            flex: 2;
            @include flex(lr);
            align-items: flex-end;

            .price {
              font-size: 28rpx;
              color: #ff3b30;
              font-weight: bold;

              .del {
                font-size: 20rpx;
                color: #999;
                margin-left: 10rpx;
                text-decoration: line-through;
              }
            }

            .num {
              font-size: 28rpx;
            }
          }
        }
      }
    }

    .time {
      font-size: 24rpx;
      color: #ff3b30;
      height: 60rpx;
      @include flex(left);
    }

    .count {
      @include flex(right);
      font-size: 28rpx;
      padding-bottom: 24rpx;

      view {
        margin-left: 24rpx;
      }

      .red {
        color: red;
        padding-left: 10rpx;
      }
    }
  }

  .logist {
    margin-top: 24rpx;
    background-color: #fff;
    border-radius: 8rpx;
    padding: 0 24rpx 24rpx;

    .title {
      @include flex(lr);
      font-size: 28rpx;
      color: #333;
      height: 80rpx;
    }

    .item {
      height: 76rpx;
      background-color: #f5f5f5;
      padding: 0 24rpx;
      border-radius: 8rpx;
      @include flex(lr);
      font-size: 28rpx;

      .red {
        color: red;
      }
    }
  }

  .address {
    background-color: #fff;
    padding: 24rpx;
    border-radius: 8rpx;
    margin-top: 24rpx;
    font-size: 28rpx;

    .label {
      line-height: 40rpx;
    }

    .user {
      font-size: 26rpx;
      color: #666;
      margin-top: 12rpx;

      text {
        margin-right: 24rpx;
      }
    }
  }

  .footer {
    width: 100%;
    height: 104rpx;
    background-color: #fff;
    @include flex(lr);
    padding: 0 32rpx;
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 2;

    .left {
      font-size: 28rpx;
      color: #333;

      text {
        color: red;
      }
    }

    .right {
      @include flex;

      .but {
        width: 160rpx;
        height: 60rpx;
        color: #fff;
        @include flex;
        @include bg_theme;
        font-size: 28rpx;
        border-radius: 30rpx;

        &.pay {
          background-color: #fff;
          @include font_theme;
          @include border_theme;
          margin-right: 32rpx;
        }
      }
    }
  }
}
</style>
