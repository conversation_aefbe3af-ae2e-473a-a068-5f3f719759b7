<template>
  <view class="info">
    <!-- 重要 -->
    <view class="order_number">
      <view class="n1">
        <img class="n2" src="/static/doc/no.png" alt="" style="margin-right: 7px;">
      </view>
      <!-- 单号 -->
      <view class="order_bold">
        <view>挂号编号：{{ detail.regCode }}</view>
        <view class="order_bold" style="font-weight: normal">医院名称：{{ detail.hosName }}</view>
      </view>
      <!-- 医院 -->
    </view>

    <!-- 患者信息 -->
    <view class="user">
      <text>{{ detail.patientName }}</text>
      <view class="user-n">
        <img class="n2" src="/static/doc/n5.png" alt="" style="margin-right: 7px;">
        <text>{{ detail.sex }}</text>
      </view>
      <view class="user-n" style="width: 50%;">
        <img class="n2" src="/static/doc/n4.png" alt="" style="margin-right: 7px;">
        <text>{{ detail.age }}{{ detail.ageUnit }}</text>
      </view>
    </view>
    <view class="user-q">
      <!-- 医生姓名 -->
      <view class="info_item">
        <text>医生姓名：{{ detail.platformDocName || detail.docName }}</text>
      </view>
      <!-- 医生科室 -->
      <view class="info_item">
        <text>医生科室：{{ detail.deptName }}</text>
      </view>
      <!-- 医生号别 -->
      <view class="info_item">
        <text>医生号别：{{ detail.dntName }}</text>
      </view>
      <!-- 问诊类型 -->
      <view class="info_item">
        <text>问诊类型：{{ detail.receiveType }} </text>
      </view>
      <!-- 问诊方式 -->
      <view class="info_item" v-if="detail.isOffLine == 0">
        <view style="display: flex;align-items: center;">
          <text>问诊方式：</text>
          <text class="custom-n">{{ detail.visitTypeName }}</text>
        </view>
      </view>
      <!-- 开单时间 -->
      <view class="info_item" v-if="detail.isAppoint == 1">
        <text>预约时间：{{ detail.appointTime }}</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    detail: {
      type: Object,
      default: () => {},
    },
  },
};
</script>

<style lang="scss" scoped>
.user-q{
  display: flex;
  flex-wrap: wrap;
  .info_item{
    width: 50%;
    color: rgba(153, 153, 153, 1) !important;
    font-size: 12px !important;
  }
  .custom-n{
    width: 34px;
    height: 18px;
    opacity: 1;
    border-radius: 1px;
    background: rgba(242, 245, 255, 1);
    color: rgba(135, 79, 240, 1);
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.user-n{
  display: flex;
  align-items: center;
}
.info {
  padding: 10rpx 32rpx 24rpx;
  background: #fff;
  border-radius: 8rpx;
  font-size: 28rpx;
  box-shadow: 0px 1px 4px  rgba(121, 72, 234, 0.15);
  .n1{
    height: 100%;
    padding-top: 30rpx;
  }
  .order_number {
    height: 148rpx;
    border-bottom: 1px dashed #eee;
    @include flex;
    flex-direction: row;
    justify-content: left;
    .order_bold {
      font-size: 24rpx;
      color: #333;
      line-height: 60rpx;
      font-weight: bold;
    }
  }

  .user {
    @include flex(lr);
    padding-top: 16rpx;

    text {
      font-size: 30rpx;
      color: #333;
      font-weight: bold;
    }
  }

  .info_item {
    font-size: 28rpx;
    color: #666;
    margin-top: 16rpx;
    line-height: 40rpx;
  }
}
</style>
