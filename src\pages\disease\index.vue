<template>
  <div class="disease">
    <view class="head">
      <HMfilterDropdown
        :menuTop="80"
        :filterData="filterData"
        :defaultSelected="defaultSelected"
        :updateMenuName="true"
        @confirm="confirm"
        dataFormat="Object"
      ></HMfilterDropdown>
    </view>

    <!-- 列表 -->
    <LIST :list="list" v-if="list.length" @click="toDetail" />

    <view class="empty_list" v-else>
      <image src="/static/images/question/empty_toast.png" />
      <view> 暂无处方记录 </view>
    </view>
  </div>
</template>

<script>
import { getAllDept } from '@/api/base.js';
import { findIsEnabledPatientByUserId } from '@/api/user.js';
import myJsTools from '@/common/js/myJsTools.js';
import HMfilterDropdown from '@/components/HM-filterDropdown/HM-filterDropdown.vue';
import { getDocPatientPreListHomePage } from '@/api/cf';

import LIST from './com/list.vue';

export default {
  name: 'Disease',
  components: {
    HMfilterDropdown,
    LIST,
  },
  data() {
    return {
      query: {
        limit: 10,
        page: 1,
        // 就诊人ids
        patientId: '',
        deptId: '',
        startTime: '',
        endTime: '',
        userId: uni.getStorageSync('userId'),
      },
      total: 0,
      list: [],
      filterData: [
        {
          name: '科室',
          type: 'hierarchy',
          submenu: [],
        },
        {
          name: '时间',
          type: 'hierarchy',
          submenu: [
            {
              name: '七天内',
              value: '0',
            },
            {
              name: '一月内',
              value: '1',
            },
            {
              name: '三月内',
              value: '2',
            },
            {
              name: '半年内',
              value: '3',
            },
          ],
        },
        {
          name: '就诊人',
          type: 'hierarchy',
          submenu: [],
        },
      ],
      defaultSelected: [],
    };
  },
  onLoad() {
    this.getAllPatient();
    this.getAllDept();
    this.getList();
  },
  onPullDownRefresh() {
    this.query.page = 1;
    this.getList();
  },
  onReachBottom() {
    if (this.list.length >= this.total) return;
    this.query.page++;
    this.getList();
  },
  methods: {
    // 获取就诊人列表
    async getAllPatient() {
      let { data } = await findIsEnabledPatientByUserId({
        userId: uni.getStorageSync('userId'),
      });
      if (!data) return;
      let submenu = [];
      for (let i = 0; i < data.length; i++) {
        submenu.push({
          value: data[i].patientId,
          name: data[i].patientName,
        });
      }
      this.filterData[2].submenu = submenu;
    },
    // 获取所有科室列表
    async getAllDept() {
      let {
        data: { rows },
      } = await getAllDept({
        deptName: '',
        page: 1,
        limit: 100,
      });
      let submenu = [];
      for (let i = 0; i < rows.length; i++) {
        submenu.push({
          value: rows[i].deptId,
          name: rows[i].deptName,
        });
      }
      this.filterData[0].submenu = submenu;
    },
    // 获取列表
    async getList() {
      let {
        data: { rows, total },
      } = await getDocPatientPreListHomePage(this.query);
      if (this.query.page > 1) {
        this.list = [...this.list, ...rows];
      } else {
        this.list = rows;
      }
      this.total = total;
      uni.stopPullDownRefresh();
    },
    // 筛选
    confirm(e) {
      let valueArr = e.value;
      this.query.page = 1;
      let deptId = valueArr[0];
      if (deptId[0]) {
        this.query.deptId = deptId[0];
      } else {
        this.query.deptId = '';
      }
      let time = valueArr[1];
      if (time[0]) {
        this.query.endTime = myJsTools.getDate(new Date()) + ' 23:59:59';
        let timeValue = time[0];

        let startTime;
        if (timeValue == 0) {
          startTime = myJsTools.getDate('day', -7);
        } else if (timeValue == 1) {
          startTime = myJsTools.getDate('month', -1);
        } else if (timeValue == 2) {
          startTime = myJsTools.getDate('month', -3);
        } else if (timeValue == 3) {
          startTime = myJsTools.getDate('month', -6);
        }
        this.query.startTime = startTime + ' 00:00:00';
      } else {
        this.query.startTime = '';
        this.query.endTime = '';
      }

      let patients = valueArr[2];
      if (patients[0]) {
        this.query.patientId = patients[0];
      } else {
        this.query.patientId = '';
      }
      this.getList();
    },
    // 去详情
    toDetail(item) {
      let { businessId, docId, patientId } = item;
      uni.navigateTo({
        url:
          '/pages/continuedPrescription/myFormulaDetail?businessId=' +
          businessId +
          '&docId=' +
          docId +
          '&patientId=' +
          patientId,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}

page {
  background: #f5f5f5;
}

.disease {
  .head {
    position: sticky;
    top: 0;
    z-index: 1;
  }

  .empty_list {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 26rpx;
    font-weight: 400;
    color: rgba(158, 163, 173, 1);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);

    image {
      margin-bottom: 40rpx;
      width: 386rpx;
      height: 324rpx;
    }
  }
}
</style>
