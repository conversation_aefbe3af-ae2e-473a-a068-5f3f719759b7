<template>
  <!-- 检查 -->
  <view class="inspect">
    <!-- 顶部状态 -->
    <STATUS
      isLis
      :lisType="detail.lisType"
      :status="status"
      :num="num"
      :queueNumber="detail.queueNumber"
      :time="signTime"
    />

    <!-- 详情部分 -->
    <view class="detail">
      <!-- 摘要 -->
      <INFO isLis :detail="detail" />

      <!-- 时间线 -->
      <TIMELINE
        isLis
        :addTime="detail.addTime"
        :signTime="detail.signTime"
        :sampleTime="detail.sampleTime"
        :ditributionTime="detail.ditributionTime"
        :receiveTime="detail.receiveTime"
        :payTime="detail.payTime"
        :verificationTime="detail.verificationTime"
        :uploadReportTime="detail.uploadReportTime"
        :invalidTime="detail.invalidTime"
        :returnTime="detail.returnTime"
      />

      <!-- 项目信息 -->
      <PROJECT
        :showBtn="detail.status == 5"
        isLis
        :list="list"
        :price="detail.payCost"
      />

      <!-- 检查机构 -->
      <INPRCTION
        isLis
        v-if="detail.dpoId || detail.dsoId"
        :dloId="detail.lisType == 2 ? detail.dsoId : detail.dpoId"
        :pliId="pliId"
      />

      <!-- 物流信息 -->
      <LOGISTICS v-if="detail.status == 9" :detail="detail" />

      <!-- 挂号信息 -->
      <ROW text="挂号信息" @click="toRegister" />
    </view>
  </view>
</template>

<script>
// 状态
import STATUS from './com/status.vue';
// 摘要
import INFO from './com/info.vue';
// 项目信息
import PROJECT from './com/project.vue';
// 检查中心
import INPRCTION from './com/inspection.vue';
// 行
import ROW from './com/row.vue';
// 时间线
import TIMELINE from './com/timeLine.vue';
// 物流信息
import LOGISTICS from './com/logistics.vue';

import { getProLisInfoByID, myLisQueueNum } from '@/api/inspect';

export default {
  components: {
    STATUS,
    ROW,
    INFO,
    PROJECT,
    INPRCTION,
    TIMELINE,
    LOGISTICS,
  },
  data() {
    return {
      pliId: '',
      status: -1,
      // 预约时间
      time: '',
      // 项目
      list: [],
      // 详情
      detail: {},
      // 签到时间
      signTime: '',
      // 排队号
      num: '',
    };
  },
  onLoad(opt) {
    this.pliId = opt.id;
    this.getDetail();
  },
  methods: {
    // 获取详情
    async getDetail() {
      let { data } = await getProLisInfoByID(this.pliId);
      let {
        lisListDVO,
        appointDate,
        appointEndTime,
        appointStartTime,
        signStartTime,
        signEndTime,
        status,
      } = data;
      // 预约时间
      data.time = appointDate
        ? appointDate +
          ' ' +
          appointStartTime.slice(0, 5) +
          '-' +
          appointEndTime.slice(0, 5)
        : '';
      // 签到时间
      this.signTime = signStartTime
        ? signStartTime.slice(0, 16) + '-' + signEndTime.slice(11, 16)
        : '';

      // 处理项目报告
      // lisListDVO.forEach((v) => {
      //   if (v.reportOssUrl) v.reportOssUrl = JSON.parse(v.reportOssUrl);
      // });

      // 项目
      this.list = lisListDVO;
      // 详情
      this.detail = data;
      // 状态
      this.status = status;
      // 排队
      if (status == 3 || status == 7) {
        this.getNum();
      }
    },
    // 查询排号
    async getNum() {
      let { data } = await myLisQueueNum(this.pliId);
      if (data) this.num = data;
    },
    // 挂号详情
    toRegister() {
      let { hosId, regId } = this.detail;
      uni.setStorageSync('hosId', hosId);
      let url = '/pages/personalCenter/diagnosisRecord/detail?id=' + regId;
      uni.redirectTo({ url });
    },
  },
};
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}
page {
  background-color: #f5f5f5;
}
.inspect {
  .detail {
    padding: 32rpx;
    margin-top: -240rpx;
  }
}
</style>
