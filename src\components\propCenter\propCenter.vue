<template>
  <view class="model-container">
    <view class="wrapper">
      <view class="block">
        <view class="title">
          <slot></slot>
        </view>
        <view class="btn_footer">
          <template v-if="type == '2'">
            <view class="cancel" @click.prevent="modelCancel">取消</view>
            <view @click.prevent="modelConfirm" class="confirm">确定</view>
          </template>
          <template v-else-if="type == '100'">
            <view @click.prevent="modelConfirm" class="confirm">阅读并同意</view>
          </template>
          <template v-else-if="type == '500'">
            <slot name="content"></slot>
          </template>
          <template v-else>
            <view
              class="confirm"
              @click.prevent="modelConfirm"
              :class="{ forbid: buttontype == true }"
            >
              {{
                buttonText + (buttontype ? "(" + buttonCount + "s" + ")" : "")
              }}
            </view>
          </template>
        </view>
      </view>
    </view>
  </view>
</template>
<script>
export default {
  props: {
    type: {
      type: Number,
      default: 1,
    },
    buttontype: {
      type: Boolean,
      default: false,
    },
    buttonCount: {
      type: Number,
      default: 5,
    },
    buttonText: {
      type: String,
      default: "确定",
    },
  },
  data() {
    return {};
  },
  mounted() {},
  methods: {
    modelConfirm() {
      // this.father.modeConfirm();
      this.$emit("confirmPropCenter");
    },
    modelCancel() {
      // this.father.modelCancel();
      this.$emit("cancelPropCenter");
    },
  },
};
</script>

<style scoped lang="scss">
.wrapper {
  position: fixed;
  width: 100%;
  height: 100vh;
  left: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.5);
  @include flex(center);
  z-index: 999;

  .block {
    width: 592upx;
    min-height: 352upx;
	// height: 352upx;//之前高度样式 先注释了 怕之后会出现问题
    box-sizing: border-box;
    background: #fff;
    border-radius: 16upx;
    overflow: hidden;
    padding: 32upx;
    @include flex(center);
    flex-direction: column;

    .title {
      width: 100%;
      flex: 1;
      color: $k-title;
      font-size: 32upx;
      font-weight: 600;
      @include flex(center);
      white-space: normal;
      text-align: center;
    }
  }
}

.btn_footer {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.btn_footer view {
  width: 242rpx;
  height: 68rpx;
  border-radius: 46rpx;
  color: #ffffff;
  font-weight: 600;

  font-size: 28rpx;
  text-align: center;
  line-height: 68rpx;
  box-sizing: border-box;
}

.btn_footer .cancel {
  @include border_theme;
  @include font_theme;
  background: transparent;
  margin-right: 36rpx;
}

.btn_footer .confirm {
  @include bg_theme;
}
.forbid {
  background: #888888 !important;
}
</style>
