<template>
  <view class="dept_list">
    <view class="dept__title">
      <TITLE title="诊疗科室" />
      <view class="card_more" @click="toDeptList()">
        查看全部科室
<!--        <uni-icons type="arrowright" color="#999" size="14"></uni-icons>-->

      </view>
    </view>
    <view class="container">
      <view class="item" @click="toDeptList(item)" v-for="item in deptList" :key="item.id">
        <img class="doc_head" :src="item.deptImgUrl||'/static/images/index/dept.png'"/>
        <view class="doc_name">
          <text>{{ item.subjectName }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import TITLE from "@/pages/inspect/com/itemTitle.vue";
import {
  findDiagTreatmentSubject
} from "@/api/base.js";
export default {
  components: {
    TITLE,
  },
  data() {
    return {
      deptList: []
    }
  },
  mounted() {
    this.findDiagTreatmentSubject()
  },
  methods: {
    async findDiagTreatmentSubject() {
      let { data } = await findDiagTreatmentSubject();
      this.deptList = data.length > 0 && data.filter((item) => { return item.homeDisplayFlag == '1' })
      this.deptList=this.deptList.map(v=>{
        if(v.deptImg&&v.deptImg.includes('http')){
          v.deptImgUrl=v.deptImg
        }
        return {
          ...v,
          deptImgUrl:v.deptImgUrl
        }
      })
      console.log(this.deptList)
    },
    toDeptList(item) {
      uni.navigateTo({ url: `/pages/register/mainDeptList/index?subjectCode=${item ? item.subjectCode : ''}` });
    },
  }
}
</script>

<style scoped lang="scss">
.dept_list {
  background: #fff;
  border-radius: 8rpx;
  margin-bottom: 10px;

  .dept__title {
    //padding: 0 32rpx;
    padding: 0 12rpx;
    height: 80rpx;
    @include flex(left);
    display: flex;
    justify-content: space-between;
  }
.container {
  width: 100%;
  display: grid;
  grid-template-columns: repeat(4, 1fr); /* 每行4个元素 */
  gap: 20rpx; /* 固定间距 */
  justify-items: center; /* 居中对齐内容 */
  align-items: center; /* 垂直居中内容 */
}

.item {
  width: 150rpx;
  height: 150rpx;
  display: flex;
  flex-direction: column; /* 垂直排列图标和文字 */
  justify-content: center;
  align-items: center;
  background: rgba(247, 248, 252, 1);
  border-radius: 8px; /* 可选：设置圆角 */
  .doc_head{
    width: 50rpx;
    height: 50rpx;
  }
  .doc_name{
    font-size: 24rpx;
  }
}
  .list-box {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 21rpx; /* 设置元素间的间距 */
    .list-card {
      width: 160rpx;
      height: 160rpx;
      border-radius: 4px;
      background: rgb(247, 248, 252);
      text-align: center;
      padding: 10px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }
  }

  .list-box-1 {
    width: 50%;
    text-align: center;
  }

  .card_more {
    height: 80rpx;
    @include flex;
    font-size: 24rpx;
    color:  rgba(135, 79, 240, 1);
  }

  .doc_head {
    width: 104rpx;
    height: 104rpx;
    border-radius: 50%;
    flex: none;
    object-fit: cover;

  }

  .doc_name {
    margin-top: 16rpx;
    color: rgba(51, 51, 51, 1);
    text-align: center;
    font-weight: 400;
  }
}
</style>