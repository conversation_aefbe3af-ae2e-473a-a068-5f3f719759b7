<template>
  <!-- 状态 -->
  <view class="status">
    <img class="o-2" src="/static/doc/o-2.png" alt="">
    <block v-if="status == 0">
      <view class="h2">
        <image class="icon" src="/static/inspection/x.png" />
        交易关闭
      </view>

      <view class="info"> 订单已关闭 </view>
    </block>

    <block v-if="status == 1">
      <view class="h2">
        <image class="icon" src="/static/doc/o-1.png" />
        待支付
      </view>

      <view class="info">
        剩余{{ addTime }}，订单将自动取消，请尽快支付哦
      </view>
    </block>

    <block v-if="status == 2">
      <view class="h2">
        <image class="icon" src="/static/inspection/rl.png" />
        待签到
      </view>

      <view class="info"> 请于{{ time }},进行签到 </view>
    </block>

    <block v-if="status == 3">
      <view class="h2">
        <image class="icon" src="/static/inspection/x.png" />
        已退费
      </view>

      <view v-if="detail.refundReason" class="info">退费原因： {{detail.applyReason|| detail.refundReason}} </view>
      <view v-else class="info"> 已为您退费，预计15个工作日内到账，请注意查收 </view>
    </block>

    <block v-if="status == 4">
      <view class="h2">
        <image class="icon" src="/static/inspection/wait.png" />
        待接诊
      </view>

      <view class="info">
        您当前为第<text>{{ queueNumber }}</text> 位，前面还有
        {{ num }} 位，请耐心等待</view
      >
    </block>

    <block v-if="status == 5">
      <view class="h2">
        <image class="icon" src="/static/register/jz.png" />
        接诊中
      </view>

      <view class="info"> 正在问诊中... </view>
    </block>

    <block v-if="status == 6">
      <view class="h2">
        <image class="icon" src="/static/inspection/done.png" />
        交易完成
      </view>

      <view class="info"> 本次服务已完成 </view>
    </block>
  </view>
</template>

<script>
export default {
  name: 'Status',
  props: {
    status: {
      type: Number | String,
      default: 1,
    },
    time: {
      type: String,
      default: '',
    },
    addTime: {
      type: String,
      default: '',
    },
    num: {
      type: String | Number,
      default: '',
    },
    queueNumber: {
      type: String | Number,
      default: '',
    },
    detail:{
      type: Object,
      default: () => {
        return {};
      },
    }
  },
};
</script>

<style lang="scss" scoped>
.status {
  height: 216rpx;
  background: #ff5050;
  padding: 32rpx;
  color: #fff;
  box-sizing: border-box;

  .h2 {
    font-size: 16px;
    font-weight: 500;
    @include flex(left);

    .icon {
      width: 44rpx;
      height: 44rpx;
      margin-right: 16rpx;
    }
  }

  .info {
    font-size: 24rpx;
    margin-top: 24rpx;
    margin-left: 28px;
    text {
      font-size: 36rpx;
      font-weight: bold;
      padding: 0 8rpx;
    }
  }
}
</style>
