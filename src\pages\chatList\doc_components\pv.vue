<template>
  <!-- 视频通话邀请 -->
  <view class="pv_call">
    <!-- 头像 -->
    <image
      @click="head"
      :src="imgUrl || '/static/images/docHead.png'"
      mode="aspectFill"
      class="chat_user_img"
    />
    <view>
      <view class="chatName">{{chatName}}</view>
      <!-- 卡片 -->
      <view class="pv_content" @click="click">
        <!-- 标题 -->
        <view class="content_title">{{
            ext.isVideo == 0 ? '语音问诊' : '视频问诊'
          }}</view>
        <!-- 内容 -->
        <view class="content_cont">
          <!-- 左侧图标 -->
          <image src="/static/images/chat/zz.png" class="cont_icon" />
          <!-- 右侧文案 -->
          <view class="cont_right">
            <!-- 标题 -->
            <view class="cont_right_title">{{ content }}</view>
            <!-- 描述 -->
            <view class="cont_right_info">
              <text>{{ content }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { Toast } from '@/common/js/pay.js';
// 接听前调用
import { getTimeRemaining } from '@/api/pv.js';

export default {
  props: {
    // 头像
    imgUrl: {
      type: String,
      default: '',
    },
    docInfo: {
      type: Object,
      default: {},
    },
    content: String,
    // 扩展字段
    ext: {
      type: Object,
      default: {},
    },
    chatName:{}
  },
  methods: {
    // 头像点击
    head() {
      this.$emit('head');
    },
    // 点击卡片
    async click() {
      // let isNext = this.ext.isNext;
      // if (!isNext) {
      //   Toast('当前通话已失效');
      //   return;
      // }
      // 请求时长
      //let regId = this.ext.regId;
      let data={
        regId:this.ext.regId,
        isShoppingRegId:this.ext.isPrepool
      }
      //const res = await getTimeRemaining(regId);
      const res = await getTimeRemaining(data);
      let time = res.data.surplusDuration;
      if (time <= 0) {
        // 更新本地值 isNext 改为 false
        this.$emit('setIsNext');
        Toast('当前通话已失效');
        return;
      }

      // 判断是否声网
      if (this.ext.channel) {
        const { channel, regId } = this.ext;

        this.toAgora({
          channel,
          regId,
        });
        return;
      }
      // 以下为环信
      // 成功 跳转接听页面
      let obj = {
        conferenceId: this.ext.conferenceId,
        password: this.ext.password,
        regId,
        time,
      };
      // 存到vuex
      // this.$store.commit('SET_VIDEOOBJ', obj);
      // 执行跳转
      this.toVideo(obj);
    },
    // 跳转页面
    toVideo({ conferenceId, regId }) {
      uni.navigateTo({
        url:
          '/pages/chatList/videoCall?conferenceId=' +
          conferenceId +
          '&regId=' +
          regId +
          '&userId=' +
          uni.getStorageSync('userId') +
          '&token=' +
          uni.getStorageSync('proPfInfo').token +
          '&docId=' +
          this.docInfo.docId +
          '&deptId=' +
          this.docInfo.deptId +
          '&isVideo=' +
          this.ext.isVideo+
          '&isPrepool=' +
          this.ext.isPrepool,
      });
    },
    toAgora({ regId }) {
      uni.navigateTo({
        url:
          '/pages/chatList/videoCallAgora?regId=' +
          regId +
          '&userId=' +
          uni.getStorageSync('userId') +
          '&token=' +
          uni.getStorageSync('proPfInfo').token +
          '&docId=' +
          this.docInfo.docId +
          '&deptId=' +
          this.docInfo.deptId +
          '&isVideo=' +
          this.ext.isVideo+
          '&isPrepool=' +
          this.ext.isPrepool,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.pv_call {
  max-width: 600upx;
  box-sizing: border-box;
  @include flex(left);
  padding-bottom: 30upx;
  align-items: flex-start;

  // 头像
  .chat_user_img {
    width: 64upx;
    height: 64upx;
    border-radius: 4upx;
    margin-right: 16upx;
    flex: none;
  }

  // 内容
  .pv_content {
    width: 516upx;
    padding: 24upx;
    background-color: #fff;
    color: #333;
    border: 1px solid #ececec;
    border-radius: 8upx 32upx 32upx 32upx;
    position: relative;

    // 标题
    .content_title {
      font-size: 28upx;
      color: $k-title;
      font-weight: bold;
    }

    // 底部内容
    .content_cont {
      @include flex(left);
      align-items: flex-start;
      margin-top: 20upx;

      // 左侧图标
      .cont_icon {
        width: 80upx;
        height: 80upx;
        border-radius: 8upx;
        margin-right: 24upx;
        flex: none;
      }

      // 右侧文案
      .cont_right {
        flex: 1;
        min-height: 80upx;
        @include flex(lr);
        flex-direction: column;
        align-items: stretch;

        // 标题
        .cont_right_title {
          font-size: 28upx;
          color: $k-title;
          font-weight: bold;
        }

        // 描述
        .cont_right_info {
          font-size: 24upx;
          color: $k-info-title;
        }
      }
    }
  }
}
</style>
