<template>
  <view>
    <view class="page-container">
      <view class="patient-info">
        <!-- 标题 -->
        <view class="info_title">就诊人信息</view>
        <!-- 头像 -->
        <view class="input-box" @click="uploadTx">
          <text>头像</text>
          <view class="img-box">
            <image
              :src="
                patientInfo.patientImg
                  ? patientInfo.patientImg
                  : '/static/images/docHead.png'
              "
              mode="aspectFill"
              class="txClass"
            ></image>
          </view>
          <uni-icons class="icon" type="arrowright" size="20"></uni-icons>
        </view>
        <!-- 姓名 -->
        <view class="input-box">
          <text>姓名</text>
          <input
            type="text"
            :value="patientInfo.patientName"
            placeholder=""
            disabled
          />
        </view>
        <!-- 身份证 -->
        <view class="input-box">
          <text>身份证号</text>
          <input
            type="text"
            v-model="patientInfo.idNo"
            placeholder="请输入身份证号"
            maxlength="18"
            :disabled="!isEdit"
            @blur="cardIdBlur"
          />
        </view>
        <view class="input-box">
          <text>性别</text>
          <input
            type="text"
            :value="patientInfo.sex"
            placeholder="-"
            disabled
          />
        </view>
        <view class="input-box">
          <text>年龄</text>
          <input
            type="text"
            :value="patientInfo.age"
            placeholder="-"
            disabled
          />
        </view>
        <view class="input-box">
          <text>民族</text>
          <view @click="openNation" class="displayFlex">
            <input
              type="text"
              disabled
              class="disabled"
              v-model="patientInfo.nation"
            />
            <uni-icons class="icon" type="arrowright" size="20"></uni-icons>
          </view>
        </view>
        <view class="input-box" v-if="patientInfo.guardianIdNo">
          <text>监护人</text>
          <input
            type="text"
            :value="patientInfo.guardianIdNo"
            placeholder=""
            disabled
          />
        </view>
        <view class="input-box" @click="editorTelNo">
          <text>手机号</text>
          <input
            type="text"
            :value="patientInfo.telNo"
            v-model="patientInfo.telNo"
            placeholder="请输入手机号"
            disabled
          />
          <uni-icons class="icon" type="arrowright" size="20"></uni-icons>
        </view>
        <view class="input-box" @click="selectYbCardFun">
          <text>医保卡</text>
          <input
            type="text"
            :value="medicareTypeName"
            v-model="medicareTypeName"
            placeholder="请选择医保卡类型"
            disabled
            class="disabled"
          />
          <uni-icons class="icon" type="arrowright" size="20"></uni-icons>
        </view>
        <view v-if="ybCardShow" class="input-box">
          <text></text>
          <input
            type="text"
            :value="patientInfo.medicareNum"
            v-model="patientInfo.medicareNum"
            placeholder="请填写医保卡卡号"
          />
        </view>
        <view v-if="!ybCardShow" class="line"></view>
        <view class="input-box">
          <text>家人关系</text>
          <view @click="openLbPicker" class="displayFlex">
            <input
              type="text"
              disabled
              class="disabled"
              v-model="patientInfo.spuRelation"
            />
            <uni-icons class="icon" type="arrowright" size="20"></uni-icons>
          </view>
        </view>
        <!--        二次开发需求-->
        <view v-if="!ybCardShow" class="line"></view>
        <view class="input-box">
          <text>首次复诊时间</text>
          <view class="displayFlex">
            <uni-datetime-picker
              type="date"
              :value="patientInfo.firstFollowupDate"
              start="2021-3-20"
              end=""
              @change="change"
            />
          </view>
        </view>
        <view v-if="!ybCardShow" class="line"></view>
        <view class="input-box">
          <text>健康信息</text>
          <view @click="goToHealth" class="displayFlex">
            <input
              type="text"
              disabled
              class="disabled"
              v-model="showLongTit"
            />
            <uni-icons class="icon" type="arrowright" size="20"></uni-icons>
          </view>
        </view>
        <view v-if="!ybCardShow" class="line"></view>
        <view class="input-box">
          <text>病历资料</text>
          <view @click="goToCase" class="displayFlex">
            <input
              type="text"
              disabled
              class="disabled"
              v-model="showLongTit"
            />
            <uni-icons class="icon" type="arrowright" size="20"></uni-icons>
          </view>
        </view>
      </view>

      <view
        hidden="hidden"
        class="patient-info"
        style="margin-top: 24rpx; padding-bottom: 24rpx"
      >
        <view class="input-box">
          <text>身高</text>
          <input
            type="number"
            maxlength="3"
            :value="patientInfo.height"
            v-model="patientInfo.height"
            placeholder="请输入就诊人身高"
          />
          <view class="unit"> CM </view>
        </view>
        <view class="input-box">
          <text>体重</text>
          <input
            type="number"
            maxlength="3"
            :value="patientInfo.weight"
            v-model="patientInfo.weight"
            placeholder="请输入就诊人体重"
          />
          <view class="unit"> KG </view>
        </view>

        <!-- 用不到 -->
        <view hidden="hidden">
          <view class="title_format">药物过敏史</view>
          <view class="content">
            <textarea
              placeholder="请输入就诊人药物过敏史"
              maxlength="200"
              :value="patientInfo.drugAllergyHistory"
              v-model="patientInfo.drugAllergyHistory"
              class="other-content"
            />
          </view>
          <view class="title_format">既往病史</view>
          <view class="content">
            <textarea
              placeholder="请输入就诊人既往病史"
              maxlength="200"
              value="patientInfo.previousHistory"
              v-model="patientInfo.previousHistory"
              class="other-content"
            />
          </view>
          <view class="title_format">遗传病史</view>
          <view class="content">
            <textarea
              placeholder="请输入就诊人遗传病史"
              maxlength="200"
              :value="patientInfo.geneticHistory"
              v-model="patientInfo.geneticHistory"
              class="other-content"
            />
          </view>
          <view class="title_format">家族病史</view>
          <view class="content">
            <textarea
              placeholder="请输入就诊人家族病史"
              maxlength="200"
              :value="patientInfo.familyMedicalHistory"
              v-model="patientInfo.familyMedicalHistory"
              class="other-content"
            />
          </view>
        </view>
      </view>
    </view>
    <view class="btns">
      <button class="btn delete-patient" @click="showConfirm = true">
        删除就诊人
      </button>
      <button class="btn editor-patient" @click="editorPatient">
        确认修改
      </button>
    </view>
    <propBottom
      v-if="selectYbType"
      :actions="actions"
      @propConfirm="propConfirm"
      @propCancel="propCancel"
    ></propBottom>
    <lb-picker
      :list="relationship"
      ref="lbpicker"
      v-model="patientInfo.spuRelation"
      mode="multiSelector"
      :level="2"
      @confirm="handleConfirm"
    ></lb-picker>
    <lb-picker
      :list="nationList"
      ref="nation"
      v-model="patientInfo.nation"
      mode="multiSelector"
      :level="1"
      @confirm="handleConfirmnation"
    ></lb-picker>
    <!-- 询问框 -->
    <Confirm
      @confirmPropCenter="del"
      :type="2"
      @cancelPropCenter="showConfirm = false"
      v-if="showConfirm"
    >
      是否要删除此就诊人？
    </Confirm>
  </view>
</template>

<script>
import {
  getPatientInfo,
  deletePatient,
  editorPatient,
  findPatientRelationDict,
} from "@/api/user.js";

import Confirm from "@/components/propCenter/propCenter.vue";
import { Toast } from "@/common/js/pay.js";
import LbPicker from "@/components/lb-picker";
import { uploadImg } from "@/api/oss.js";
import myJsTools from "@/common/js/myJsTools.js";
import regex from "@/common/js/regex.js";
import { getSysCodeByType } from "@/api/shop";
export default {
  components: {
    LbPicker,
    Confirm,
  },
  data() {
    return {
      showDateTime: false,
      showConfirm: false,
      infopatientImg: "",
      patientId: "",
      patientInfo: {},
      relationship: [],
      nationList: [],
      patientImg: "",
      medicareTypeName: "",
      selectYbType: false,
      ybCardShow: false,
      actions: [
        {
          name: "就诊人无本市医保卡",
          ybCardType: 0,
        },
        {
          name: "就诊人持有本市医保卡",
          ybCardType: 1,
        },
        {
          name: "就诊人持有外地医保卡",
          ybCardType: 2,
        },
      ],
      // 是否可编辑
      isEdit: false,
      showLongTit: "待完善",
    };
  },
  onLoad() {
    uni.removeStorageSync('newTelNo')
    this.patientId = uni.getStorageSync("patientId");
    // this.getPatientDetail();
    this.findPatientRelationDict();
  },
  onShow() {
    this.getSysCodeByType();
    this.getPatientDetail();
  },
  methods: {
    // 跳转健康信息界面
    goToHealth() {
      uni.navigateTo({
        url:
          "/pages/personalCenter/patientManage/editorPatientOtherInfo/index?patientInfo=" +
          JSON.stringify(this.patientInfo) +
          "&patientId=" +
          this.patientId,
      });
    },
    // 跳转病历信息界面
    goToCase() {
      uni.navigateTo({
        url:
          "/pages/personalCenter/patientManage/editorPatientCaseInfo/index?patientId=" +
          this.patientId,
      });
    },
    // 日期选择回调
    change(e) {
      this.patientInfo.firstFollowupDate = e;
      console.log("-change事件:", e);
    },
    async getSysCodeByType() {
      let { data } = await getSysCodeByType("085");
      //  console.log(data)
      let list = [];
      data.forEach((item) => {
        list.push({ label: item.meaning, value: item.code, children: [] });
      });
      // console.log(list);
      this.nationList = list;
    },
    // 校验身份证号,并获取用户性别,年龄
    cardIdBlur(e) {
      let cardId = e.detail.value;
      if (!cardId) return;
      let form = this.patientInfo;
      if (regex.idNoBlur(cardId)) {
        if (regex.disCriCard(cardId, "sex") == 2) {
          form.sex = "女";
        } else {
          form.sex = "男";
        }
        form.sexCode = regex.disCriCard(cardId, "sex");
        form.age = regex.disCriCard(cardId, "age");
      }
    },
    // 获取就诊人详情
    async getPatientDetail() {
      let { data } = await getPatientInfo({
        patientId: this.patientId,
      });
      this.infopatientImg = data.patientImg || "";

      // 可编辑
      if (data.idNo == "******************") {
        this.isEdit = true;
      }
      if (!data.sexCode) {
        data.sexCode = "";
      }
      if (data.sex == "男") data.sexCode = "1";
      if (data.sex == "女") data.sexCode = "2";

      this.patientInfo = data;
      const newTel=uni.getStorageSync('newTelNo')
      if(newTel){
        this.patientInfo.telNo=newTel
      }
      let isMedicareType = data.isMedicareType;
      for (let i = 0; i < this.actions.length; i++) {
        if (isMedicareType == this.actions[i].ybCardType) {
          this.medicareTypeName = this.actions[i].name;
        }
      }
      if (data.medicareNum) {
        this.ybCardShow = true;
      }
      if (this.patientInfo.patientImg) {
        myJsTools.downAndSaveImg(this.patientInfo.patientImg, (url) => {
          this.patientInfo.patientImg = url;
        });
      }
    },
    // 修改手机号
    editorTelNo() {
      uni.navigateTo({
        url: "/pages/personalCenter/patientManage/editorTelNo/index",
      });
    },
    // 删除就诊人
    deletePatient() {
      let that = this;
      uni.showModal({
        title: "提示",
        content: "是否删除此就诊人?",
        success(res) {
          if (res.confirm) {
            // 调用删除
            that.del();
          }
        },
      });
    },
    async del() {
      let patientId = this.patientId;
      let userId = uni.getStorageSync("userId");
      await deletePatient({ patientId, userId });
      this.showConfirm = false;
      wx.navigateBack({
        delta: 1,
      });
    },
    // 修改就诊人医保类型
    selectYbCardFun() {
      this.selectYbType = true;
    },
    // 监听就诊人医保卡类型的选择
    propConfirm(data) {
      this.selectYbType = false;
      var patientInfo = this.patientInfo;
      var isMedicareType = patientInfo.isMedicareType;
      let ybCardType = data.ybCardType;

      if (ybCardType != isMedicareType) {
        // 只有在该次选择与上次选择的结果不一致的情况下才会赋值
        this.patientInfo.isMedicareType = ybCardType;
        this.patientInfo.medicareNum = "";
        this.medicareTypeName = data.name;
        if (ybCardType == "1" || ybCardType == "2") {
          this.ybCardShow = true;
        } else {
          this.ybCardShow = false;
        }
      }
    },
    propCancel() {
      this.selectYbType = false;
    },
    // 编辑就诊人
    async editorPatient() {
      uni.removeStorageSync('newTelNo')
      // 解构参数
      let { idNo, spuRelation, nation } = this.patientInfo;
      if (!regex.idNoBlur(idNo)) return;
      // 判断
      if (!spuRelation) {
        Toast("请选择家人关系");
        return false;
      }
      // if (!nation) {
      //   Toast("请选择民族");
      //   return false;
      // }
      // 整合参数
      let para = {
        ...this.patientInfo,
        isDefault: 0,
        patientId: this.patientId,
        userId: uni.getStorageSync("userId"),
      };
      // 存在需要上传的图片
      if (this.patientImg) {
        let uploadImg = await this.upoladImgFun(this.patientImg);
        console.log(uploadImg);
        para.patientImg = uploadImg.data.url;
      } else {
        para.patientImg = this.infopatientImg;
      }

      // return;
      await editorPatient(para);
      uni.navigateBack({
        delta: 1,
      });
    },
    async findPatientRelationDict() {
      let { data } = await findPatientRelationDict();
      this.relationship = data;
    },
    openLbPicker() {
      this.$refs.lbpicker.show();
    },
    openNation() {
      this.$refs.nation.show();
    },
    handleConfirm(e) {
      this.patientInfo.spuRelation = e.item[1].label;
    },
    handleConfirmnation(e) {
      // console.log(e.item[0].label);
      this.patientInfo.nation = e.item[0].label;
    },
    uploadTx() {
      let _this = this;
      uni.chooseImage({
        count: 1,
        sourceType: ["album", "camera"],
        success: (res) => {
          let tempFiles = res.tempFiles[0];
          myJsTools.setImgZip(tempFiles, (dataUrl) => {
            _this.patientImg = {
              base64: dataUrl.split(",")[1],
              url: dataUrl,
              name: "",
            };
            _this.patientInfo.patientImg = dataUrl;
          });
        },
      });
    },
    upoladImgFun(obj) {
      let para = {
        folderType: 1,
        imgBody: obj.base64,
      };
      return uploadImg(para);
    },
  },
};
</script>

<style scoped>
.displayFlex /deep/ .uni-date__x-input {
  font-size: 28rpx !important;
  padding-left: 36rpx !important;
}
.displayFlex /deep/ .uni-date-x--border {
  border: none !important;
}
</style>

<style scoped lang="scss">
page {
  background-color: #f5f5f5;
}
.page-container {
  padding: 24rpx 32rpx 160rpx;
  box-sizing: border-box;
}

.patient-info {
  border-radius: 8rpx;
  background: #ffffff;
  padding-left: 32rpx;
  border-radius: 8rpx;

  .info_title {
    @include flex(left);
    height: 92upx;
    width: 100%;
    font-size: 28upx;
    color: $k-title;
    font-weight: 500;
    border-bottom: 1px solid #ebebeb;

    &::before {
      content: "";
      width: 6upx;
      height: 28upx;
      display: inline-block;
      @include bg_theme;
      margin-right: 16upx;
    }
  }
}

.input-box {
  width: 100%;
  display: flex;
  height: 92rpx;
  border-radius: 4rpx;
  box-sizing: border-box;
  line-height: 92rpx;
  border-bottom: 1rpx solid #ebebeb;
  padding-right: 32upx;
}
.page-container .input-box:last-child {
  border-bottom: none;
}
.input-box text {
  display: inline-block;
  width: 168rpx;
  flex: none;
  font-size: 28rpx;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
}

.input-box .icon {
  width: 40rpx;
}

.input-box .unit {
  font-size: 28rpx;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
}

.input-box input {
  flex: 1;
  height: 100%;
  line-height: 100%;
  padding-left: 40rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: rgba(102, 102, 102, 1);
}
.input-box .img-box {
  flex: 1;
  @include flex(left);
}
.line {
  width: 100%;
  height: 1rpx;
  background-color: #ebebeb;
}

.title_format {
  font-size: 28rpx;
  color: rgba(51, 51, 51, 1);
  line-height: 92rpx;
}

.content {
  padding-right: 32upx;
}

.other-content {
  width: 100%;
  box-sizing: border-box;
  border-radius: 8rpx;
  border: 1px solid rgba(229, 229, 229, 1);
  font-size: 28rpx;
  line-height: 40rpx;
  padding: 16rpx;
  height: 240rpx;
}

/* 删除就诊人，修改 */
.btns {
  width: 100%;
  box-sizing: border-box;
  background-color: #fbfbfb;
  position: fixed;
  left: 0;
  bottom: 0;
  padding: 24rpx 32rpx;
  display: flex;
  justify-content: space-around;
}

.btn {
  display: block;
  width: 314rpx;
  height: 92rpx;
  border-radius: 46rpx;
  color: #fff;
  font-size: 32rpx;
  line-height: 92rpx;
}

.delete-patient {
  @include border_theme;
  background: #fff;
  @include font_theme;
}
.editor-patient {
  @include bg_theme;
}

.txClass {
  width: 70upx;
  height: 70upx;
  border-radius: 16upx;
  margin-left: 40upx;
}
.disabled {
  pointer-events: none;
}

.displayFlex {
  display: flex;
  width: 80%;
  align-items: center;
  justify-content: space-between;
  input {
    width: 80%;
  }
}
</style>
