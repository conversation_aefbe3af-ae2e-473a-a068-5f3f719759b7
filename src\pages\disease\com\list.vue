<template>
  <div class="list">
    <!-- 单个 -->
    <view
      class="card"
      v-for="(item, index) in list"
      :key="index"
      @click="click(item)"
    >
      <!-- 编号 -->
      <view class="item_title">
        <text>处方编号：{{ item.businessCode }}</text>
      </view>

      <!-- 相关信息 -->
      <view class="item_cont">
        <view class="left_info"> 诊断：{{ item.diagNames }} </view>
        <view class="left_info"> 医院：{{ item.hosName }} </view>
        <view class="left_info">
          就诊人：{{ item.patientName }}
          <!-- 右箭头 -->
          <uni-icons type="arrowright" color="#666" size="16"></uni-icons>
        </view>
        <view class="left_info"> 医生姓名：{{ item.docName }} </view>
        <view class="left_info"> 医生科室：{{ item.deptName }} </view>
      </view>

      <!-- 时间 -->
      <view class="item_time"> 开方时间：{{ item.addTime }} </view>
    </view>
  </div>
</template>

<script>
export default {
  name: 'List',
  props: {
    list: {
      type: Array,
      default: () => [],
    },
  },
  methods: {
    click(item) {
      this.$emit('click', item);
    },
  },
};
</script>

<style lang="scss" scoped>
.list {
  padding: 32rpx;

  .card {
    padding: 0 32rpx;
    border-radius: 8rpx;
    background: #fff;
    margin-bottom: 24rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .item_title {
      height: 88rpx;
      @include flex(lr);
      font-size: 28rpx;
      color: #333;
      font-weight: bold;
      border-bottom: 1px solid #ebebeb;
    }

    .item_cont {
      padding: 10rpx 0;
      border-bottom: 1px solid #ebebeb;

      .left_info {
        @include flex(lr);
        font-size: 28rpx;
        color: #666666;
        line-height: 50rpx;

        &:first-child {
          font-weight: bold;
          color: #333;
        }
      }
    }

    .item_time {
      height: 88rpx;
      @include flex(lr);
      font-size: 28rpx;
      color: #333;
    }
  }
}
</style>
