<template>
  <div class="logistics">
    <p class="title">收货信息</p>
    <p class="info">
      {{ address }}
    </p>
    <p class="last">
      <span>{{ name }}</span> {{ phone(telNo) }}
    </p>
  </div>
</template>

<script>
import myJsTools from '@/common/js/myJsTools';
export default {
  name: 'Logistics',
  props: {
    name: String,
    telNo: String,
    address: String,
  },
  methods: {
    phone(val) {
      if (!val) return val;
      return myJsTools.phone(val);
    },
  },
};
</script>

<style lang="scss" scoped>
.logistics {
  background-color: #fff;
  padding: 0 32rpx;

  .title {
    height: 88rpx;
    @include flex(left);
    font-size: 28rpx;
    font-weight: bold;
    border-bottom: 1px solid #f5f5f5;
  }

  .info {
    padding: 20rpx 0;
    font-size: 28rpx;
    color: #333;
  }

  .last {
    padding-bottom: 24rpx;
    font-size: 28rpx;
    color: #999;

    span {
      padding-right: 12rpx;
    }
  }
}
</style>
